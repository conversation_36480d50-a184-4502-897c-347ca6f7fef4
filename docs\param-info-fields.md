# 表演方案参数信息字段扩展

## 概述

为表演方案和表演方案附加参数的`paramInfo`字段增加了两个新字段：`isFixed`和`defaultValue`。

## 新增字段

### isFixed
- **类型**: Boolean
- **默认值**: false
- **描述**: 标识参数是否为固定参数
- **用途**: 当设置为`true`时，前端应该将该参数设置为只读状态，不允许用户修改

### defaultValue
- **类型**: String
- **默认值**: null
- **描述**: 参数的默认值
- **用途**: 前端可以使用此值初始化表单字段

## paramInfo JSON结构示例

```json
{
  "id": "param-001",
  "name": "text1",
  "type": "text",
  "description": "文本参数",
  "isFixed": false,
  "defaultValue": "默认文本内容",
  "required": true,
  "options": null,
  "constraints": null,
  "order": 1
}
```

## 使用现有接口保存数据

数据通过现有的表演方案保存接口进行保存：

```bash
PUT http://**************/v1.0/c/basic_data/performance_plans/{id}
```

请求体示例：
```json
{
  "id": "68905f043fe80daad88f701c",
  "name": "示例表演方案",
  "description": "这是一个示例表演方案",
  "paramInfo": [
    {
      "id": "param-001",
      "name": "text1",
      "type": "text",
      "description": "第一个文本参数",
      "isFixed": false,
      "defaultValue": "默认文本",
      "required": true,
      "order": 1
    },
    {
      "id": "param-002",
      "name": "image1",
      "type": "image",
      "description": "图片参数",
      "isFixed": true,
      "defaultValue": null,
      "required": false,
      "order": 2
    }
  ]
}
```

## 表演方案附加参数

表演方案附加参数也支持相同的字段扩展，通过以下接口保存：

```bash
PUT http://**************/v1.0/c/basic_data/performance_additionals/{id}
```

## 向后兼容性

- 新字段有默认值，不会影响现有功能
- 现有的paramInfo数据仍然可以正常使用
- 如果paramInfo中没有新字段，系统会自动使用默认值

## 前端使用建议

1. **固定参数处理**: 检查`isFixed`字段，如果为`true`则将输入框设置为只读
2. **默认值处理**: 使用`defaultValue`字段初始化表单
3. **数据保存**: 通过现有的PUT接口保存完整的表演方案数据

## 示例代码

### JavaScript前端处理
```javascript
function renderParamForm(paramInfoList) {
  paramInfoList.forEach(param => {
    const input = document.createElement('input');
    input.name = param.name;
    input.value = param.defaultValue || '';
    input.readOnly = param.isFixed || false;
    
    if (param.isFixed) {
      input.classList.add('fixed-param');
    }
    
    // 添加到表单
    form.appendChild(input);
  });
}
```
