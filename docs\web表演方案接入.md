# web 表演方案接入

## 方案实现的脚本信息

```json
// 涉及到轨道的信息
{
  "ui_segment": [
    {
      "time": "03:01.013",
      "end_time": "03:01.013",
      "resource": "B03D1B624BB9E93771DD5C85E46757FB",
      "type": "MetaUI",
      "reason": "web表演方案接入",
      "instanceId": "uuid",
      "custom": {
        "Properties": [
          {
            "name": "Url",
            "value": "https://ai-courseware-2d-player.sdp.101.com/#/load?preload=<encoded_json_data>",
            "type": "string"
          }
        ],
        "Parameters": {
          "template": "pictures-comparison",
          "config": {
            "duration": 10,
            "resources": [
              {
                "name": "picture",
                "type": "image",
                "url": "https://gcdncs.101.com/v0.1/static/app_karaoke/ai-courseware-2d-player/PicturesComparison/picture.png",
                "content": "通电螺线管的磁场线通电螺线管的磁场线通电螺线管的磁场线",
                "startTime": 0,
                "events": [
                  {
                    "event": "show",
                    "startTime": 0
                  }
                ]
              },
              {
                "name": "picture",
                "type": "image",
                "url": "https://gcdncs.101.com/v0.1/static/app_karaoke/ai-courseware-2d-player/PicturesComparison/picture.png",
                "content": "通电螺线管的磁场线通电螺线管的磁场线通电螺线管的磁场线",
                "startTime": 0,
                "events": [
                  {
                    "event": "show",
                    "startTime": 0
                  }
                ]
              }
            ]
          }
        }
      }
    }
  ]
}
```

其中：

- `time`：表示该轨道的开始时间。
- `end_time`：表示该轨道的结束时间。
- `resource`：表示该轨道对应的资源 ID。
- `type`：表示该轨道的类型，这里是 `MetaUI`。
- `reason`：表示该轨道的原因，这里是 `web表演方案接入`。
- `instanceId`：表示该轨道的实例 ID，通常是一个唯一标识符。
- `custom`：包含自定义属性和参数。
  - `Properties`：属性列表，包含一个名为 `Url` 的属性，其值为加载的 URL。
  - `Parameters`：包含方案的模板名称和配置。
    - `template`：表示使用的模板名称。
    - `config`：包含方案的配置参数。
      - `duration`：表示方案的持续时间。
      - `resources`：资源列表，包含多个资源对象。
        - `name`：资源名称，必须与模板中的资源名称一致。
        - `type`：资源类型，必须为 `image`、`video` 或 `text`。
        - `url`：资源的完整 URL 地址，仅在资源类型为 `image` 或 `video` 时必填。
        - `content`：文字内容，仅在资源类型为 `text` 时必填。
        - `startTime`：资源的开始时间。
        - `events`：事件列表，包含事件类型和开始时间。



## 方案参数说明

1. `encoded_json_data` 是一个通用的数据，格式如下

```json
[
  {
    "template": "模板名称1",
    "resources": [
      {
        "name": "资源名称", // 必填：资源的标识名称（对应模板中的资源名称）
        "type": "资源类型", // 必填：'video' | 'image' | 'text'
        "url": "资源URL地址", // URL资源必填：资源的完整URL（适用于video和image类型）
        "content": "文字内容", // 文字资源必填：文字内容（适用于text类型）
        "fullLoad": false // 可选：是否全量加载，默认false（仅适用于URL资源）
      }
    ]
  }
]
```

- `template`：模板名称，表示该资源所属的模板。
- `resources`：资源列表，包含多个资源对象。资源列表是根据 Parameter 中数据进行确认的

  - `type`：资源类型，必须为 `video`、`image` 或 `text`。
  - `url`：资源的完整 URL 地址，仅在资源类型为 `video`
    或 `image` 时必填。
  - `content`：文字内容，仅在资源类型为 `text` 时必填。
  - `fullLoad`：是否全量加载，默认为 `false`。如果设置为 `true`，则表示该资源需要在加载时进行全量加载。

- `name`：资源名称，必须与模板中的资源名称一致。

2. `ui_segment`中的 `Parameters`

- parameters 中的 `template`：表示使用的模板名称。
- parameters 中的 `config`：包含方案的配置参数。
- config 中使用的 resources 是根据不同的模板传入的数据具体的参数。



## 方案接入步骤 


在WEB前端选择了具体某个表演方案后，并填充了对应的数据后请求接口生成对应的表演脚本，生成的数据有如下 ：
- 表演方案名称
- 表演方案的别名（用于匹配 performanceTypeEnum 中的表演方案名称)
- 表演方案的参数配置
- 表演方案的配置信息（模板ID等信息）

原有的逻辑
  - 原有的逻辑是前端传入一个参数名称如  display_image ，在前端有配置文件配置的是类型是图片，则前端控制中选择的就是图片的数据，给到服务端的数据是 {"display_image":"图片的URL地址"}
  - 服务端对应的 PerformanceParameter 中会有对应的一个字段是 display_image，用来接受这个参数，然后根据脚本的逻辑，对这个数据进行组装，到具体的脚本 segment 中

需要调整的逻辑

 - 前端传入一个参数名称如  display_image 且要带上具体的 type 类型，在前端有配置文件配置的是类型是图片，则前端控制中选择的就是图片的数据，给到服务端的数据是 {"display_image":"图片的URL地址", "type": "image"}，type 的值是 image、video、text 中的一个
 - 服务端【通用WEB表演】这个表演方案对应的参数，不是一个已定义的参数，而是由前端传入的数据反序列化为 PerformanceParameter 对象，解析出字段的名称和类型，然后根据脚本的逻辑，对这个数据进行组装，到具体的脚本 segment 中

 针对【通用WEB表演】这个表演方案的实现，参照上面的表演方案参数说明

 - 表演方案的参数，是 Parameters 这个数据
 - 参数中如果是image, video 类型的，则需要将数据放到 preload中
 - 表演方案的配置信息（模板ID等信息），是脚本中对应的 template 和 config 数据


## 总结 


将现有的表演方案从枚举+工厂模式，变成枚举+工厂模式之外 ，增加一个通用的配置型表演方案，由前端传入参数，服务端解析参数，组装脚本数据。依赖配置而不依赖枚举了。