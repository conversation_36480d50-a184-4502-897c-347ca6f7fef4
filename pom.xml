<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nd.gaea</groupId>
        <artifactId>waf-rest-parent</artifactId>
        <version>2.11.3</version>
    </parent>

    <groupId>com.nd.aic</groupId>
    <artifactId>aic-service</artifactId>
    <packaging>war</packaging>
    <version>0.1.0</version>
    <name>aic-service Maven Webapp</name>

    <properties>
        <uc-sdk.version>2.7.10</uc-sdk.version>
        <ndr-sdk.version>2.1.24.3</ndr-sdk.version>
        <cs-sdk.version>3.3.62.10</cs-sdk.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nd.uc</groupId>
            <artifactId>waf-uc-auth-sdk</artifactId>
            <version>${uc-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nd.uc</groupId>
            <artifactId>uc-sdk</artifactId>
            <version>${uc-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nd.uc</groupId>
            <artifactId>uc-sdk-org</artifactId>
            <version>${uc-sdk.version}</version>
        </dependency>
        <!-- ndr -->
        <dependency>
            <groupId>com.nd.component.java</groupId>
            <artifactId>ndr-sdk</artifactId>
            <version>${ndr-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nd.component.java</groupId>
            <artifactId>sdpcs_java_sdk</artifactId>
            <version>${cs-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>  <!-- 或最新版本 -->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>4.3.1</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-mongo</artifactId>
            <version>4.3.1</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.37</version>
        </dependency>

        <dependency>
            <groupId>org.sejda.imageio</groupId>
            <artifactId>webp-imageio</artifactId>
            <version>0.1.6</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/ffmpeg_linux/**/*.*</exclude>
                    <exclude>**/ffmpeg_linux/*</exclude>
                    <exclude>**/ffmpeg_win/*</exclude>
                    <exclude>**/bgm/*.mp3</exclude>
                    <exclude>**/bgm/*.wav</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/ffmpeg_linux/**/*.*</include>
                    <include>**/ffmpeg_linux/*</include>
                    <include>**/ffmpeg_win/*</include>
                    <include>**/bgm/*.mp3</include>
                    <include>**/bgm/*.wav</include>
                </includes>
            </resource>
        </resources>
    </build>

    <distributionManagement>
        <repository>
            <id>java-releases</id>
            <name>ND java releases nexus mirror.</name>
            <url>http://nexus.sdp.nd/nexus/content/repositories/java/</url>
        </repository>
        <snapshotRepository>
            <id>java-snapshots</id>
            <name>ND java snapshots nexus mirror.</name>
            <url>http://nexus.sdp.nd/nexus/content/repositories/java_snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
