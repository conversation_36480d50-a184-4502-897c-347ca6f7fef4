package com.nd.aic;

import com.nd.gaea.rest.config.WafWebSecurityConfigurerAdapter;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

@Configuration
@EnableWebSecurity
public class ApiSecurityConfig extends WafWebSecurityConfigurerAdapter {
    @Override
    protected void onConfigure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                // 不需要鉴权的api
                .antMatchers("/test/**", "/v1.0/guest/**", "/index.html", "/v1.0/c/courseware_flows/**", "/v1.0/ndr/token", "/v1.0/c/monitors/actions/im/**", "/**/elastic_scaling_info")
                .permitAll()
                .anyRequest()
                .authenticated();

    }
}
