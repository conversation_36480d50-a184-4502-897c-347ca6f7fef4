package com.nd.aic;

import com.nd.aic.base.repository.BaseRepositoryImpl;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.stereotype.Repository;

@EnableMongoRepositories(value = "com.nd", includeFilters = {@ComponentScan.Filter(Repository.class)}, repositoryBaseClass = BaseRepositoryImpl.class)
@Configuration
public class MongoRepositoryConfig {
}
