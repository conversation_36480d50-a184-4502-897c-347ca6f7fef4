package com.nd.aic;

import com.mongodb.MongoClient;
import com.nd.aic.common.UserHandler;
import com.nd.gaea.rest.WafApplication;
import com.nd.gaea.rest.support.WafContext;
import com.nd.uc.sdk.context.AuthInfoContext;
import com.nd.uc.sdk.context.ContextHandler;

import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.mongo.MongoLockProvider;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskDecorator;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.CustomConversions;
import org.springframework.data.mongodb.core.convert.DbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@EnableSchedulerLock(defaultLockAtMostFor = "10m")
@EnableFeignClients
@EnableScheduling
@EnableAsync
@SpringBootApplication
public class WebApplication extends WafApplication {

    @Bean
    public MappingMongoConverter mappingMongoConverter(MongoDbFactory factory, MongoMappingContext context, CustomConversions conversions) {
        DbRefResolver dbRefResolver = new DefaultDbRefResolver(factory);
        MappingMongoConverter mappingConverter = new MappingMongoConverter(dbRefResolver, context);
        // 移除_class
        mappingConverter.setTypeMapper(new DefaultMongoTypeMapper(null));

        mappingConverter.setCustomConversions(conversions);
        return mappingConverter;
    }

    @Bean
    public LockProvider mongoLockProvider(MongoClient mongo, MongoTemplate mongoTemplate) {
        return new MongoLockProvider(mongo, mongoTemplate.getDb().getName());
    }

    @Bean
    public ThreadPoolTaskScheduler taskScheduler(@Value("${task.scheduler.poolSize:8}") int poolSize) {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(poolSize);
        scheduler.setThreadNamePrefix("task-scheduler-");
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        return scheduler;
    }

    @Primary
    @Bean
    public ThreadPoolTaskExecutor taskExecutor(@Value("${task.executor.poolSize:8}") int poolSize) {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(poolSize);
        taskExecutor.setThreadNamePrefix("task-executor-");
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.setTaskDecorator(new AsyncTaskDecorator());
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);

        return taskExecutor;
    }

    private static class AsyncTaskDecorator implements TaskDecorator {

        @SuppressWarnings({"rawtypes", "unchecked"})
        @Override
        public Runnable decorate(final Runnable runnable) {
            // 获取当前线程的MDC数据
            final Map<String, String> contextMap = MDC.getCopyOfContextMap();
            final String user = UserHandler.getUser(null);
            final AuthInfoContext authInfoContext = ContextHandler.getAuthInfo();
            final HashMap wafThreadHashMap = new HashMap(WafContext.getLocal());
            return () -> {
                try {
                    // 在异步线程中恢复MDC数据
                    MDC.setContextMap(contextMap);
                    // 在异步线程中恢复Waf线程变量
                    WafContext.setLocal(wafThreadHashMap);
                    // 在异步线程中恢复用户数据
                    UserHandler.setUser(user);
                    // 在异步线程中恢复组织服务线程变量
                    ContextHandler.setAuthInfo(authInfoContext);
                    runnable.run();
                } finally {
                    MDC.clear();
                    WafContext.setLocal(null);
                    UserHandler.setUser(null);
                    ContextHandler.setAuthInfo(null);
                }
            };
        }

    }

    public static void main(String[] args) {
        SpringApplication.run(WebApplication.class, args);
    }
}
