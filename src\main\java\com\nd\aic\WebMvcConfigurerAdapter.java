package com.nd.aic;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.base.query.ListParamHandlerMethodArgumentResolver;
import com.nd.aic.config.AicInterceptor;
import com.nd.gaea.rest.config.WafWebMvcConfigurerAdapter;
import com.nd.gaea.util.WafJsonMapper;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import java.util.List;

@Configuration
public class WebMvcConfigurerAdapter extends WafWebMvcConfigurerAdapter {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        super.addInterceptors(registry);
        registry.addInterceptor(new AicInterceptor());
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new ListParamHandlerMethodArgumentResolver());
        super.addArgumentResolvers(argumentResolvers);
    }

    @Override
    public void customMediaTypeSupport(List<HttpMessageConverter<?>> converters) {
        super.customMediaTypeSupport(converters);
        WafJsonMapper.getMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Bean
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        resolver.setMaxUploadSize(52428800); // 50MB
        resolver.setMaxInMemorySize(8192);  // 设置内存中的最大文件大小
        return resolver;
    }
}
