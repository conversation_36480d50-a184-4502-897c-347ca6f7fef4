package com.nd.aic.base.domain;


import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * Created by <PERSON>(150429) on 2016/2/4.
 */
@Setter
@Getter
public abstract class BizDomain<I extends Serializable> extends BaseDomain<I> {

    @CreatedDate
    private Date createTime;

    @LastModifiedDate
    private Date updateTime;

    private boolean deleted;

}
