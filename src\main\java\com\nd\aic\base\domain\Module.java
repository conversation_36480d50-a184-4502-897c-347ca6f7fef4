package com.nd.aic.base.domain;


import com.nd.aic.base.exception.ErrorCode;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.utils.I18NUtils;
import com.nd.gaea.WafException;

import org.springframework.http.HttpStatus;

import lombok.Getter;

/**
 * 模块
 * Created by <PERSON>(150429) on 2016/2/15.
 */
@Getter
public class Module {

    private final String code;
    private final String i18nModuleCode;

    public Module(String code) {
        this.code = code;
        this.i18nModuleCode = "module.code." + code;
    }

    /**
     * 自动根据当前环境获取i18n的模块名称
     */
    public String getModuleName() {
        return I18NUtils.getDefaultI18NMsg(this.i18nModuleCode, this.code);
    }

    public WafException notFound() {
        String errorCode = ErrorCode.PREFIX + this.code + "_NOT_FOUND";
        return WafI18NException.of(errorCode, "module.not.found", HttpStatus.NOT_FOUND, i18nModuleCode);
    }

    public WafException notFound(String message) {
        String errorCode = ErrorCode.PREFIX + this.code + "_NOT_FOUND";
        return WafI18NException.of(errorCode, message, HttpStatus.NOT_FOUND);
    }

    public WafException existed() {
        String errorCode = ErrorCode.PREFIX + this.code + "_EXISTED";
        return WafI18NException.of(errorCode, "module.existed", HttpStatus.CONFLICT, i18nModuleCode);
    }

    /**
     * 名称已存在
     *
     * @param value 名称
     * @return 相应的异常
     */
    public WafException nameExisted(String value) {
        String errorCode = ErrorCode.PREFIX + this.code + "_NAME_EXISTED";
        return WafI18NException.of(errorCode, "module.name.existed", HttpStatus.CONFLICT, i18nModuleCode, value);
    }

    /**
     * 被关联
     *
     * @param module2 被关联的模块
     * @return 相应的异常
     */
    public WafException associatedWith(Module module2) {
        String errorCode = ErrorCode.PREFIX + this.code + "_ASSOCIATED_WITH_" + module2.code;
        return WafI18NException.of(errorCode, "module.associated.with.module2",
                HttpStatus.NOT_ACCEPTABLE, i18nModuleCode, module2.i18nModuleCode);
    }

    public WafException nullId() {
        return WafI18NException.of("module.null.id", ErrorCode.INVALID_ARGUMENT, i18nModuleCode);
    }
}
