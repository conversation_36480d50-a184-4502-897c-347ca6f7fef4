package com.nd.aic.base.exception;

import org.springframework.http.HttpStatus;

/**
 * List error messages of server internal exception or error
 *
 * @since 1.0
 */
public enum ErrorCode implements IErrorCode {

    // 应用路由相关
    APP_TEMPORARY_UNAVAILABLE(HttpStatus.PAYMENT_REQUIRED, "APP_TEMPORARY_UNAVAILABLE", "error.code.app.temporary.unavailable"),
    MISSING_TENANT_ID(HttpStatus.BAD_REQUEST, "MISSING_TENANT_ID", "error.code.missing.tenant.id"),
    MISSING_BIZ_TYPE(HttpStatus.BAD_REQUEST, "MISSING_BIZ_TYPE", "error.code.missing.biz.type"),

    // 租户和用户相关
    UNSUPPORTED_ACCOUNT_TYPE(HttpStatus.FORBIDDEN, "UNSUPPORTED_ACCOUNT_TYPE", "error.code.unsupported.account.type"),
    OUT_OF_SERVICE(HttpStatus.PAYMENT_REQUIRED, "OUT_OF_SERVICE", "error.code.out.of.service"),
    MISSING_ORG_ID(HttpStatus.BAD_REQUEST, "MISSING_ORG_ID", "error.code.missing.org.id"),
    MISSING_SUID(HttpStatus.BAD_REQUEST, "MISSING_SUID", "error.code.missing.suid"),
    USER_NOT_EXIST(HttpStatus.NOT_FOUND, "USER_NOT_EXIST", "error.code.user.not.exist"),
    USER_NOT_LOGIN(HttpStatus.UNAUTHORIZED, "USER_NOT_LOGIN", "error.code.user.not.login"),
    NO_PERMISSION(HttpStatus.FORBIDDEN, "NO_PERMISSION", "error.code.no.permission"),
    WRONG_AUTH_TYPE(HttpStatus.FORBIDDEN, "WRONG_AUTH_TYPE", "error.code.wrong.auth.type"),
    INVALID_HEADER_SERVICE_TENANT_ID(HttpStatus.FORBIDDEN, "INVALID_HEADER_SERVICE_TENANT_ID", "error.code.invalid.header.service.tenant.id"),
    CREATE_TENANT_ASSOCIATE_OP_NG(HttpStatus.BAD_REQUEST, "CREATE_TENANT_ASSOCIATE_OP_NG", "error.code.create.tenant.associate.op.ng"),
    GET_USER_INFO_FAIL(HttpStatus.BAD_REQUEST, "GET_USER_INFO_FAIL", "error.code.get.user.info.fail"),
    VORG_NOT_EXIST(HttpStatus.BAD_REQUEST, "VORG_NOT_EXIST", "error.code.vorg.not.exist"),
    ORG_NOT_EXIST(HttpStatus.BAD_REQUEST, "ORG_NOT_EXIST", "error.code.org.not.exist"),
    INSTITUTION_NOT_EXIST(HttpStatus.BAD_REQUEST, "INSTITUTION_NOT_EXIST", "error.code.institution.not.exist"),

    // 模板相关
    CONFIG_TEMPLATE_UNDEFINED(HttpStatus.BAD_REQUEST, "CONFIG_TEMPLATE_UNDEFINED", "error.code.config.template.undefined"),
    CONFIG_TEMPLATE_DATA_NONEXISTS(HttpStatus.BAD_REQUEST, "CONFIG_TEMPLATE_DATA_NONEXISTS", "error.code.config.template.data.nonexists"),
    CONFIG_TEMPLATE_INIT_ERROR(HttpStatus.BAD_REQUEST, "CONFIG_TEMPLATE_INIT_ERROR", "error.code.config.template.init.error"),
    SAVE_CONFIG_TEMPLATE_NG(HttpStatus.BAD_REQUEST, "SAVE_CONFIG_TEMPLATE_NG", "error.code.save.config.template.ng"),

    //MQ任务调度相关
    TASKSCHEDULEMQ_ADD_CRON_FAILED(HttpStatus.BAD_REQUEST, "TASKSCHEDULEMQ_ADD_CRON_FAILED", "error.code.taskschedulemq.add.cron.error"),

    TASKSCHEDULEMQ_DELETE_CRON_FAILED(HttpStatus.BAD_REQUEST, "TASKSCHEDULEMQ_DELETE_CRON_FAILED", "error.code.taskschedulemq.delete.cron.error"),

    TASKSCHEDULEMQ_GETALL_CRON_FAILED(HttpStatus.BAD_REQUEST, "TASKSCHEDULEMQ_GETALL_CRON_FAILED", "error.code.taskschedulemq.getall.cron.error"),

    TASKSCHEDULEMQ_PUSH_ERROR(HttpStatus.BAD_REQUEST, "TASKSCHEDULEMQ_PUSH_ERROR", "error.code.taskschedulemq.push.error"),

    TASKSCHEDULEMQ_PUSHALL_ERROR(HttpStatus.BAD_REQUEST, "TASKSCHEDULEMQ_PUSHALL_ERROR", "error.code.taskschedulemq.pushall.error"),

    TASKSCHEDULEMQ_PUSHALL_PARTIAL_ERROR(HttpStatus.BAD_REQUEST, "TASKSCHEDULEMQ_PUSHALL_PARTIAL_ERROR", "error.code.taskschedulemq.pushall.partial.error"),

    IMMESSAGE_DEFAULT_TEMPLATE_NOTFOUND_ERROR(HttpStatus.NOT_FOUND, "IMMESSAGE_DEFAULT_TEMPLATE_NOTFOUND_ERROR", "error.code.immessage.default.template.notfound.error"),

    IMMESSAGE_TEMPLATE_NOTFOUND_ERROR(HttpStatus.NOT_FOUND, "IMMESSAGE_TEMPLATE_NOT_EXISTED", "error.code.immessage.template.notfound.error"),

    IMMESSAGE_TEMPLATE_REPLACE_NOTFOUND_ERROR(HttpStatus.NOT_FOUND, "IMMESSAGE_TEMPLATE_REPLACE_NOT_EXISTED", "error.code.immessage.template.replace.notfound.error"),

    IMMESSAGE_NOT_ALLOW_TO_CREATE_DEFAULT_TEMPLATE(HttpStatus.BAD_REQUEST, "IMMESSAGE_NOT_ALLOW_TO_CREATE_DEFAULT_TEMPLATE", "error.code.immessage.template.not.allow.to.create.default.template"),


    // 请求相关
    INVALID_HEADER(HttpStatus.BAD_REQUEST, "INVALID_HEADER", "error.code.invalid.header"),
    INVALID_ARGUMENT(HttpStatus.BAD_REQUEST, "INVALID_ARGUMENT", "error.code.invalid.argument"),
    REQUIRE_ARGUMENT(HttpStatus.BAD_REQUEST, "REQUIRE_ARGUMENT", "error.code.require.argument"),
    INVALID_OPERATOR(HttpStatus.NOT_ACCEPTABLE, "INVALID_OPERATOR", "error.code.invalid.operator"),

    //ListParam相关
    INVALID_QUERY(HttpStatus.BAD_REQUEST, "INVALID_QUERY", "error.code.invalid.query"),
    FIELD_NOT_FOUND(HttpStatus.BAD_REQUEST, "FIELD_NOT_FOUND", "error.code.field.not.found"),

    // 没有数据
    DATA_NOT_FOUND(HttpStatus.NOT_FOUND, "DATA_NOT_FOUND", "error.code.data.not.found"),

    //配置相关
    CONFIG_LOADING_FAIL(HttpStatus.INTERNAL_SERVER_ERROR, "CONFIG_LOADING_FAIL", "error.code.config.loading.fail"),
    CONFIG_MISSING(HttpStatus.INTERNAL_SERVER_ERROR, "CONFIG_MISSING", "error.code.config.missing"),
    CONFIG_MISSING_ITEM(HttpStatus.INTERNAL_SERVER_ERROR, "CONFIG_MISSING_ITEM", "error.code.config.missing.item"),

    // 内容服务相关
    CS_SESSION_NG(HttpStatus.BAD_REQUEST, "CS_SESSION_NG", "error.code.cs.session.ng"),
    CS_DISABLE(HttpStatus.NOT_FOUND, "CS_DISABLE", "error.code.cs.disable"),

    //程序错误
    FAIL(HttpStatus.INTERNAL_SERVER_ERROR, "FAIL", "error.code.fail"),

    BIZ_TYPE_CONFLICT(HttpStatus.BAD_REQUEST, "BIZ_TYPE_CONFLICT", "error.code.biz.type.conflict"),
    CLONE_FAIL(HttpStatus.BAD_REQUEST, "CLONE_FAIL", "error.code.clone.fail"),
    REQ_ORGANIZATION_FAIL(HttpStatus.BAD_REQUEST, "REQ_ORGANIZATION_FAIL", "error.code.req.organiztion.fail"),
    ;


    private final HttpStatus httpStatus;
    private final String code;
    private final String message;
    public static final String PREFIX = "AIC";

    ErrorCode(HttpStatus httpStatus, String code, String message) {
        this.httpStatus = httpStatus;
        this.code = code;
        this.message = message;
    }

    ErrorCode(ErrorCode errorCode, String addtionMessage) {
        this.httpStatus = errorCode.getHttpStatus();
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage() + addtionMessage;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return this.httpStatus;
    }

    @Override
    public String getCode() {
        return PREFIX + this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
