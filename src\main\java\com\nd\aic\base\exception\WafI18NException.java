package com.nd.aic.base.exception;


import com.nd.aic.base.utils.I18NUtils;
import com.nd.gaea.WafException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.util.Arrays;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

/**
 * Waf国际化异常
 * Create By <PERSON>(150429)
 */
@Slf4j
public class WafI18NException extends WafException {

    private static final long serialVersionUID = 8463172359156983900L;

    private final String code;

    private final String message;

    private final HttpStatus status;

    private final String[] args;

    protected WafI18NException(String code, String message, HttpStatus status, Throwable cause, String... args) {
        super(code, message, status, cause);
        this.code = code;
        this.message = message;
        this.status = status;
        this.args = args;
        //modify by 201112 异常不要写日志，由最外层处理，要不然会重复写日志
        //        if (status.value() >= 500) {
        //            log.error("Request Failed, HttpCode:" + status.toString() + ", Code:" + code, this);
        //        } else {
        //            log.warn("Request Failed, HttpCode:" + status.toString() + ", Code:" + code, this);
        //        }
    }

    /**
     * code 为 "WAF/INTERNAL_SERVER_ERROR";
     * HttpStatus 为 HttpStatus.INTERNAL_SERVER_ERROR (500);
     * message 为 message
     *
     * @param message 错误消息描述
     */
    public static WafI18NException of(String message, String... args) {
        return of("WAF/INTERNAL_SERVER_ERROR", message, HttpStatus.INTERNAL_SERVER_ERROR, null, args);
    }

    /**
     * code 为 "WAF/INTERNAL_SERVER_ERROR";
     * HttpStatus 为 HttpStatus.INTERNAL_SERVER_ERROR (500);
     * message 为 message
     *
     * @param message 错误消息描述
     */
    public static WafI18NException of(String message, Throwable cause, String... args) {
        return of("WAF/INTERNAL_SERVER_ERROR", message, HttpStatus.INTERNAL_SERVER_ERROR, cause, args);
    }

    public static WafI18NException of(String message, IErrorCode errorCode, String... args) {
        return of(errorCode.getCode(), message, errorCode.getHttpStatus(), null, args);
    }

    public static WafI18NException of(IErrorCode errorCode, String... args) {
        return of(errorCode.getCode(), errorCode.getMessage(), errorCode.getHttpStatus(), null, args);
    }

    public static WafI18NException of(String code, String message, HttpStatus status, String... args) {
        return of(code, message, status, null, args);
    }

    public static WafI18NException of(String message, IErrorCode errorCode, Throwable cause, String... args) {
        if (StringUtils.isBlank(message)) {
            message = errorCode.getMessage();
        }
        return of(errorCode.getCode(), message, errorCode.getHttpStatus(), cause, args);
    }

    public static WafI18NException of(IErrorCode errorCode, Throwable cause, String... args) {
        return of(errorCode.getCode(), errorCode.getMessage(), errorCode.getHttpStatus(), cause, args);
    }

    public static WafI18NException of(String code, String message, HttpStatus status, Throwable cause, String... args) {
        String[] i18nArgs = new String[args.length];
        for (int i = 0; i < args.length; i++) {
            i18nArgs[i] = I18NUtils.getDefaultI18NMsg(args[i], args[i]);
        }
        message = I18NUtils.getDefaultI18NMsg(message, message, i18nArgs);
        return new WafI18NException(code, message, status, cause);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        WafI18NException that = (WafI18NException) o;

        return Objects.equals(this.code, that.code) && Objects.equals(this.message, that.message)
                && Objects.equals(this.status, that.status) && Arrays.equals(args, that.args);

    }

    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (args != null ? Arrays.hashCode(args) : 0);
        return result;
    }
}

