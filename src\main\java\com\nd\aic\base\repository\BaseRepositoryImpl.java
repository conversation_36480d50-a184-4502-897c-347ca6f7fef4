package com.nd.aic.base.repository;


import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.base.exception.ErrorCode;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.Condition;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;

import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.repository.query.MongoEntityInformation;
import org.springframework.data.mongodb.repository.support.SimpleMongoRepository;

import java.io.Serializable;
import java.util.List;

/**
 * Dao基础实现
 * Created by <PERSON>(150429) on 2016/4/12.
 */
public class BaseRepositoryImpl<T extends BaseDomain<I>, I extends Serializable> extends SimpleMongoRepository<T, I> implements BaseRepository<T, I> {

    private final MongoOperations mongoOperations;
    private final MongoEntityInformation<T, I> entityInformation;

    public BaseRepositoryImpl(MongoEntityInformation<T, I> metadata, MongoOperations mongoOperations) {
        super(metadata, mongoOperations);
        this.entityInformation = metadata;
        this.mongoOperations = mongoOperations;
    }

    @Override
    public Items<T> list(ListParam<T> listParam) {
        Criteria criteria = buildCriteria(listParam);
        Query query = new Query(criteria).with(listParam).skip(listParam.getOffset());
        List<String> excludeFields = listParam.getExcludeFields();
        if (null != excludeFields && !excludeFields.isEmpty()) {
            for (String excludeField : excludeFields) {
                query.fields().exclude(excludeField);
            }
        }


        long count = 0;
        if (listParam.isCount()) {
            count = mongoOperations.count(new Query(criteria), entityInformation.getJavaType());
            if (listParam.getLimit() == -1) {
                return Items.of(null, count);
            }
        }

        if (listParam.getLimit() == -1) {
            listParam.setLimit(20);
        }

        if (!listParam.isSupportUnlimit()) {
            if (listParam.getLimit() <= 0) {
                listParam.setLimit(20);
            }
            query.limit(listParam.getLimit());
        }

        List<T> list = mongoOperations.find(query, entityInformation.getJavaType());

        if (listParam.isCount()) {
            return Items.of(list, count);
        }
        return Items.of(list);
    }

    private Criteria buildCriteria(ListParam<T> listParam) {
        List<Condition> conditions = listParam.getConditions();
        if (conditions.isEmpty()) {
            return new Criteria();
        }
        int conditionSize = conditions.size();
        Criteria criteria = buildCriteria(conditions.get(0));
        if (conditionSize > 1) {
            Criteria[] otherCriteria = new Criteria[conditionSize - 1];
            for (int i = 1; i < conditionSize; i++) {
                otherCriteria[i - 1] = buildCriteria(conditions.get(i));
            }
            criteria.andOperator(otherCriteria);
        }
        return criteria;
    }

    private Criteria buildCriteria(Condition condition) {
        Criteria criteria = Criteria.where(condition.getField());
        Object value = condition.getValue();
        switch (condition.getOperator()) {
            case EQ:
                criteria = criteria.is(value);
                break;
            case NE:
                criteria = criteria.ne(value);
                break;
            case GT:
                criteria = criteria.gt(value);
                break;
            case GE:
                criteria = criteria.gte(value);
                break;
            case LT:
                criteria = criteria.lt(value);
                break;
            case LE:
                criteria = criteria.lte(value);
                break;
            case LIKE:
                value = value.toString().replaceAll("%", ".*").replaceAll("_", ".");
                criteria = criteria.regex(value.toString(), "i");
                break;
            case IN:
                criteria = criteria.in((Object[]) value);
                break;
            case NIN:
                criteria = criteria.not().in((Object[]) value);
                break;
            default:
                throw WafI18NException.of("非法或不支持的操作符", ErrorCode.INVALID_QUERY);
        }
        return criteria;
    }

}