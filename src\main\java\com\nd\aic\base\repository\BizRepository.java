package com.nd.aic.base.repository;

// import com.nd.social.common.base.domain.BizDomain;

import com.nd.aic.base.domain.BizDomain;

import org.springframework.data.repository.NoRepositoryBean;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON>(150429) on 2016/1/8.
 */
@NoRepositoryBean
public interface BizRepository<T extends BizDomain<I>, I extends Serializable> extends BaseRepository<T, I> {

    @SuppressWarnings("hiding")
    <T extends BizDomain<I>> List<T> findByDeletedIsFalse();

}
