package com.nd.aic.base.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.base.domain.Module;
import com.nd.aic.base.exception.ErrorCode;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.base.utils.ValidatorUtils;
import com.nd.gaea.util.WafJsonMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * BaseService
 * Created by closer on 2016/1/5.
 */
public abstract class BaseService<T extends BaseDomain<I>, I extends Serializable> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseService.class);

    protected final ObjectMapper objectMapper = WafJsonMapper.getMapper();

    @Autowired
    private BaseRepository<T, I> baseRepository;

    protected abstract Module module();

    public T add(T t) {

        if (null != t.getId() && exists(t.getId())) {
            throw module().existed();
        }

        ValidatorUtils.validateAndThrow(t);

        beforeAdd(t);
        return baseRepository.save(t);
    }

    protected void beforeAdd(T t) {

    }

    public T save(T t) {

        ValidatorUtils.validateAndThrow(t);

        beforeSave(t);
        return baseRepository.save(t);
    }

    protected void beforeSave(T t) {

    }

    public T delete(I id) {

        T t = findStrictOne(id);
        return delete(t);
    }

    protected T delete(T t) {

        if (t == null) {
            throw module().notFound();
        }

        beforeDelete(t);
        baseRepository.delete(t);
        return t;
    }

    protected void beforeDelete(T t) {

    }

    public T update(I id, Map<String, Object> map) {

        T oldDomain = findStrictOne(id);
        T newDomain = createAndValidateDomainFromOldAndMap(oldDomain, map);

        beforeUpdate(oldDomain, newDomain);
        return update(newDomain);
    }

    protected void beforeUpdate(T oldDomain, T newDomain) {

    }

    /**
     * 更新操作<br/>
     * 应该尽力保护该方法，外部如果需要修改局部字段，应该在Service方法中增加相应的方法，<br/>
     * 底层调用update方法,而不是直接暴露update方法
     *
     * @param t 需要更新的实体实例
     * @return 更新后的实体实例
     */
    protected T update(T t) {

        if (!exists(t.getId())) {
            throw module().notFound();
        }

        return baseRepository.save(t);
    }

    protected void revisePatchBody(Map<String, Object> map) {

        map.remove("id");
    }

    protected T createAndValidateDomainFromOldAndMap(T oldDomain, Map<String, Object> map) {

        revisePatchBody(map);

        T newDomain = createDomainFromOldAndMap(oldDomain, map);

        ValidatorUtils.validateAndThrow(newDomain);

        return newDomain;
    }

    protected T createDomainFromOldAndMap(T oldDomain, Map<String, Object> map) {

        try {
            @SuppressWarnings("unchecked") T newDomain = (T) oldDomain.getClass().newInstance();
            BeanUtils.copyProperties(oldDomain, newDomain);
            String json = objectMapper.writeValueAsString(map);
            return objectMapper.readerForUpdating(newDomain).readValue(json);
        } catch (IllegalArgumentException | IOException e) {
            LOGGER.error("read for update error!", e);
            throw WafI18NException.of(ErrorCode.INVALID_ARGUMENT);
        } catch (InstantiationException | IllegalAccessException e) {
            String message = "类[" + oldDomain.getClass().getCanonicalName() + "]找不到可访问无参构造器";
            LOGGER.error(message, e);
            throw WafI18NException.of(message, ErrorCode.FAIL);
        }
    }

    /**
     * 严格的详情获取，当数据不存在是，会报出 ErrorCode.DATA_NOT_FOUND
     *
     * @param id 主键
     * @return 详情
     */
    public T findStrictOne(I id) {

        T t = findOne(id);

        if (t == null) {
            throw module().notFound();
        }

        return t;
    }

    public T findOne(I id) {

        checkId(id);

        return baseRepository.findOne(id);
    }

    protected void checkId(I id) {

        if (id == null) {
            throw module().nullId();
        }
    }

    public List<T> findAll() {

        return baseRepository.findAll();
    }

    public Items<T> list(ListParam<T> listParam) {

        return baseRepository.list(listParam);
    }

    public boolean exists(I id) {

        return findOne(id) != null;
    }

}
