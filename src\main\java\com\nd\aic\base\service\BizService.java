package com.nd.aic.base.service;

import com.nd.aic.base.domain.BizDomain;
import com.nd.aic.base.query.Condition;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.repository.BizRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 业务Service基类
 * Created by <PERSON>(150429) on 2016/1/8.
 */
public abstract class BizService<T extends BizDomain<I>, I extends Serializable> extends BaseService<T, I> {

    private static Condition deletedCondition = Condition.eq("deleted", false, Boolean.class);;

    @Autowired
    private BizRepository<T, I> bizRepository;

    @Override
    public T add(T t) {

        reviseDomain(t);

        return super.add(t);
    }

    protected void reviseDomain(T t) {

        // 这里明确赋值，是为了防止外部赋值
        // 当id为外部指定时@CreateDate无法自动设置
        t.setCreateTime(new Date());
        t.setUpdateTime(null);
        t.setDeleted(false);
    }

    @Override
    public T save(T t) {

        reviseDomain(t);

        return super.save(t);
    }

    @Override
    protected T delete(T t) {

        if (t == null || t.isDeleted()) {
            throw module().notFound();
        }

        checkId(t.getId());

        beforeDelete(t);
        t.setDeleted(true);
        return bizRepository.save(t);
    }

    public T realDelete(I id) {

        T t = findStrictOne(id);
        return realDelete(t);
    }

    protected T realDelete(T t) {

        return super.delete(t);
    }

    @Override
    protected void revisePatchBody(Map<String, Object> map) {

        map.remove("create_time");
        map.remove("createTime");
        map.remove("update_time");
        map.remove("updateTime");
        map.remove("deleted");
        super.revisePatchBody(map);
    }

    @Override
    public T findOne(I id) {

        T t = super.findOne(id);

        if (t == null || t.isDeleted()) {
            return null;
        }

        return t;
    }

    public T findOneWithDeleted(I id) {

        return super.findOne(id);
    }

    public T findStrictOneWithDeleted(I id) {

        return super.findStrictOne(id);
    }

    @Override
    public List<T> findAll() {

        return bizRepository.findAll();
    }

    public List<T> findAllWithDeleted() {

        return super.findAll();
    }

    @Override
    public Items<T> list(ListParam<T> listParam) {

        listParam.addCondition(0, deletedCondition());
        listParam.addDefaultSort(new Sort(Sort.Direction.DESC, "createTime"));
        return super.list(listParam);
    }

    public Items<T> listWithDeleted(ListParam<T> listParam) {

        listParam.addDefaultSort(new Sort(Sort.Direction.DESC, "createTime"));
        return super.list(listParam);
    }

    public static Condition deletedCondition() {
        return deletedCondition;
    }
}
