package com.nd.aic.base.utils;

import com.google.common.annotations.VisibleForTesting;

import com.nd.aic.base.exception.ErrorCode;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.gaea.client.ApplicationContextUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

/**
 * <AUTHOR>
 */
public class ValidatorUtils {

    private volatile static Validator validator;

    private ValidatorUtils() {
    }

    public static String getErrorMessageStr(BindingResult result) {
        if (result == null || !result.hasErrors()) {
            return "";
        }
        StringBuilder errorMessage = new StringBuilder();
        boolean start = true;
        for (FieldError fieldError : result.getFieldErrors()) {
            if (start) {
                start = false;
            } else {
                errorMessage.append(',');
            }
            errorMessage.append(fieldError.getDefaultMessage());
        }
        return errorMessage.toString();
    }

    private static Validator getValidator() {
        if (null == validator) {
            synchronized (ValidatorUtils.class) {
                if (null != validator) {
                    return validator;
                }
                if (ApplicationContextUtil.getApplicationContext() != null
                        && ApplicationContextUtil.getApplicationContext().containsBean("validator")) {
                    validator = ApplicationContextUtil.getApplicationContext()
                            .getBean("validator", ValidatorFactory.class)
                            .getValidator();
                } else {
                    ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
                    validator = factory.getValidator();
                }
            }
        }
        return validator;
    }

    public static <T> String validate(T t) {
        StringBuilder errorMessage = new StringBuilder();
        Set<ConstraintViolation<T>> constraintViolations = getValidator().validate(t);
        boolean start = true;
        for (ConstraintViolation<T> constraintViolation : constraintViolations) {
            if (start) {
                start = false;
            } else {
                errorMessage.append(',');
            }
            errorMessage.append(constraintViolation.getMessage());
        }
        return errorMessage.toString();
    }

    public static <T> String validateAndThrow(T t) {
        String validateError = validate(t);
        if (StringUtils.isNotBlank(validateError)) {
            throw WafI18NException.of(validateError, ErrorCode.INVALID_ARGUMENT);
        }
        return validateError;
    }

    @VisibleForTesting
    public static void mock(Validator mockValidator) {
        validator = mockValidator;
    }

    // @VisibleForTesting
    // public static void mock() {
    //     validator = Mockito.mock(Validator.class);
    // }
}