package com.nd.aic.client;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.pojo.aihub.AIHubTaskId;
import com.nd.aic.client.pojo.aihub.AIHubTaskReq;
import com.nd.aic.client.pojo.aihub.AIHubTaskResult;
import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@WafFeignClientAuth
@FeignClient(url = "${app.aihub.host:https://ai-hub-server.sdpsg.101.com}", name = "AIHubFeignClient")
public interface AIHubFeignClient {

    String BOT_APP_ID = "c3e7ea8f-cd9d-419f-a01a-deab3fd50e33";
    // AI课件AI生产号(10019402)
    String DEFAULT_USER_ID = "10019402";

    /**
     * 文字表演视频生成
     * */
    @PostMapping(
            value = "/async_task/api/tasks",
            headers = {
                    "X-App-Id=" + BOT_APP_ID,
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
                    "X-Project-Key=pk-bJE6eZ56p2HDMTAuQaHebs-fHc3Kfczn",
            }
    )
    AIHubTaskId createTextEffectGenerateTask(@RequestHeader(value = "Userid", defaultValue = DEFAULT_USER_ID) String userId, @RequestBody AIHubTaskReq body);

    /**
     * 文字表演视频生成
     * */
    @Retryable(backoff = @Backoff(value = 5000, multiplier = 2, maxDelay = 20000))
    @GetMapping(
            value = "/async_task/api/tasks/{async_task_id}",
            headers = {
                    "X-App-Id=" + BOT_APP_ID,
                    "Userid=" + DEFAULT_USER_ID,
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
            }
    )
    AIHubTaskResult queryTextEffectGenerateTask(@PathVariable("async_task_id") String id);

    /**
     * 通用AIHUB异步任务提交
     * */
    @PostMapping(
            value = "/async_task/api/tasks",
            headers = {
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
                    "X-Project-Key=pk-bJE6eZ56p2HDMTAuQaHebs-fHc3Kfczn",
            }
    )
    AIHubTaskId createTask(@RequestHeader(value = "X-App-Id") String botId, @RequestBody AIHubTaskReq body);

    /**
     * 通用AIHUB异步任务结果查询
     * */
    @Retryable(backoff = @Backoff(value = 5000, multiplier = 2, maxDelay = 20000))
    @GetMapping(
            value = "/async_task/api/tasks/{async_task_id}",
            headers = {
                    "Userid=" + DEFAULT_USER_ID,
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
            }
    )
    AIHubTaskResult queryTask(@RequestHeader(value = "X-App-Id") String botId, @PathVariable("async_task_id") String id);

    @GetMapping(
            value = "/async_task/api/tasks/{async_task_id}",
            headers = {
                    "X-App-Id=" + BOT_APP_ID,
                    "Userid=" + DEFAULT_USER_ID,
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
            }
    )
    JSONObject queryTaskInfo(@PathVariable("async_task_id") String id);

    /**
     * 创建知识库文档（通过文本）
     */
    @PostMapping(
            value = "/v1/datasets/{dataset_id}/document/create_by_text",
            headers = {
                    "Content-Type=application/json"
            }
    )
    JSONObject createDatasetDocumentByText(@PathVariable("dataset_id") String datasetId, @RequestBody JSONObject body);

    /**
     * 创建知识库文档片段
     */
    @PostMapping(
            value = "/v1/datasets/{dataset_id}/documents/{document_id}/segments",
            headers = {
                    "Content-Type=application/json"
            }
    )
    JSONObject createDocumentSegments(@PathVariable("dataset_id") String datasetId, @PathVariable("document_id") String documentId, @RequestBody JSONObject body);

    /**
     * 删除知识库文档片段
     */
    @RequestMapping(
            method = RequestMethod.DELETE,
            value = "/v1/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}",
            headers = {
                    "Content-Type=application/json"
            }
    )
    JSONObject deleteDocumentSegment(@PathVariable("dataset_id") String datasetId, @PathVariable("document_id") String documentId, @PathVariable("segment_id") String segmentId);

    /**
     * 知识库文档查询
     */
    @PostMapping(
            value = "/v1/datasets/{dataset_id}/hit-testing",
            headers = {
                    "Content-Type=application/json"
            }
    )
    JSONObject datasetHitTesting(@PathVariable("dataset_id") String datasetId, @RequestBody JSONObject body);

    // // https://ai-hub-server.sdpsg.101.com/console/api/apps/9bf36b45-8fd6-4756-97f5-810a2cd4fc21/chat-messages?conversation_id=22d4e75b-ad63-442b-8992-2c1aed2a30bd&limit=10
    // @GetMapping(
    //         value = "/console/api/apps/{bot_app_id}/chat-messages",
    //         headers = {
    //                 "X-App-Id=" + BOT_APP_ID,
    //                 "Userid=" + DEFAULT_USER_ID,
    //                 "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
    //         }
    // )
    // AIHubChatMessages queryTaskChatMessages(@PathVariable("bot_app_id") String botAppId, @RequestParam(value = "conversation_id") String conversationId, @RequestParam(value = "limit") int limit);
    //
    // // https://ai-hub-server.sdpsg.101.com/console/api/apps/9bf36b45-8fd6-4756-97f5-810a2cd4fc21/chat-conversations/22d4e75b-ad63-442b-8992-2c1aed2a30bd
    // @GetMapping(
    //         value = "/console/api/apps/{bot_app_id}/chat-conversations/{conversation_id}",
    //         headers = {
    //                 "X-App-Id=" + BOT_APP_ID,
    //                 "Userid=" + DEFAULT_USER_ID,
    //                 "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
    //         }
    // )
    // AIHubChatConversation queryTaskChatConversation(@PathVariable("bot_app_id") String botAppId, @PathVariable(value = "conversation_id") String conversationId);
    //
    // // https://ai-hub-server.sdpsg.101.com/console/api/apps/9bf36b45-8fd6-4756-97f5-810a2cd4fc21/workflow-runs/5d3ae059-f8e8-4b6c-9962-8f2c8d5ae35b/node-executions
    // @GetMapping(
    //         value = "/console/api/apps/{bot_app_id}/workflow-runs/{workflow_run_id}/node-executions",
    //         headers = {
    //                 "X-App-Id=" + BOT_APP_ID,
    //                 "Userid=" + DEFAULT_USER_ID,
    //                 "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
    //         }
    // )
    // AIHubExecutions queryTaskExecutions(@PathVariable("bot_app_id") String botAppId, @PathVariable(value = "workflow_run_id") String workflowRunId);
}
