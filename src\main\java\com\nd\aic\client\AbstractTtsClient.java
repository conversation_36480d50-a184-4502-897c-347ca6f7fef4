package com.nd.aic.client;

import com.google.common.collect.Lists;

import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.service.AudioResultService;
import com.nd.aic.service.CsService;
import com.nd.aic.service.MediaService;
import com.nd.aic.util.CacheKeyUtils;
import com.nd.aic.util.TextUtils;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * */
@Slf4j
public abstract class AbstractTtsClient {

    private final CsService csService;
    private final MediaService mediaService;
    private final AudioResultService audioResultService;

    protected AbstractTtsClient(CsService csService, MediaService mediaService, AudioResultService audioResultService) {
        this.csService = csService;
        this.mediaService = mediaService;
        this.audioResultService = audioResultService;
    }

    protected int maxWord() {
        return 500;
    }

    @SneakyThrows
    public File mergeTts(String text, String voiceType) {
        TtsSegment ttsSegment = new TtsSegment()
                .setVoiceId(voiceType)
                .setText(text);
        return mergeTts(ttsSegment);
    }

    @SneakyThrows
    public File mergeTts(TtsSegment ttsSegment) {
        int ttsMaxWordLength = maxWord();
        List<String> textSegments = TextUtils.splitText(ttsSegment.getText(), ttsMaxWordLength);
        List<File> fileList = Lists.newArrayList();
        for (String textSegment : textSegments) {
            ttsSegment.setText(textSegment);
            File tempFile = cacheableTts(textSegment, ttsSegment);
            fileList.add(tempFile);
        }
        if (fileList.isEmpty()) {
            File silentFile = mediaService.createSilent(0.1f);
            fileList.add(silentFile);
        }
        File file;
        if (fileList.size() > 1) {
            try {
                file = mediaService.concatMp3(fileList);
            } finally {
                fileList.forEach(FileUtils::deleteQuietly);
            }
        } else {
            file = fileList.get(0);
        }

        return file;
    }

    @SneakyThrows
    public File cacheableTts(String text, TtsSegment ttsSegment) {
        String cacheKey = cacheKey(text, ttsSegment);
        String resourceId = audioResultService.queryAudio(cacheKey, "CS");
        if (resourceId != null) {
            log.info("audio cache found: {}", resourceId);
            String mp3Url = "https://cdncs.101.com/v0.1/download?dentryId=" + resourceId;
            return mediaService.download(mp3Url, String.format("%s.mp3", resourceId), true);
        }

        File tempFile = callTts(text, ttsSegment);

        Dentry dentry = csService.uploadFile(147507L, "tts_mp3/", tempFile.getName(), tempFile);
        audioResultService.saveAudio(cacheKey, dentry.getName(), dentry.getDentryId(), null, "CS");
        return tempFile;
    }

    public String cacheKey(String text, TtsSegment ttsSegment) {
        return CacheKeyUtils.audioCacheKey(text, ttsSegment);
    }

    protected abstract File callTts(String text, TtsSegment ttsSegment) throws IOException, InterruptedException;
}
