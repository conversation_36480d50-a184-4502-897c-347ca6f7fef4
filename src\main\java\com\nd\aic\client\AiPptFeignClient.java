package com.nd.aic.client;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.pojo.aippt.TaskDetailDto;
import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import com.nd.aic.common.BizConstant;

import java.util.List;
import java.util.Map;

@WafFeignClientAuth
@FeignClient(url = "${aic.host:https://ioa-aippt.sdp.101.com/}", name = "AIPPTFeignClient")
public interface AiPptFeignClient {

    /**
     * 获取推荐的图片
     */
    @GetMapping(value = "/v0.1/tasks/{id}/actions/all_detail", headers = {"sdp-biz-type=" + BizConstant.AIPPT_BIZ_TYPE_CRAFT, "sdp-app-id=8331e61b-f0b0-45fe-8a91-************"})
    List<TaskDetailDto> taskDetail(@PathVariable(value = "id") Long id, @RequestParam(value = "suid") String userId);

    @PostMapping(value = "/v0.1/tools/callback/update_databus")
    JSONObject updateDataBus(@RequestParam(value = "sdp-app-id") String sdpAppId,
                             @RequestParam(value = "sdp-biz-type") String bizType,
                             @RequestParam(value = "suid") String userId,
                             @RequestParam(value = "task_id") Long taskId,
                             @RequestBody Map<String, String> map);

    @PostMapping(value = "/v0.1/class_hours/{id}/actions/update_status")
    JSONObject updateClassHourStatus(@RequestHeader(value = "sdp-app-id") String sdpAppId,
                                     @RequestHeader(value = "sdp-biz-type") String bizType,
                                     @RequestParam(value = "suid") String userId,
                                     @PathVariable(value = "id") String taskId,
                                     @RequestBody Map<String, Object> map);
}
