package com.nd.aic.client;

import com.nd.aic.base.query.Items;
import com.nd.aic.entity.RenderTask;
import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@WafFeignClientAuth
@FeignClient(url = "${aic.host:https://aic-service.sdp.ndaeweb.com/}", name = "AicFeignClient")
public interface AicFeignClient {

    /**
     * 获取推荐的图片
     */
    @Retryable(maxAttempts = 1, backoff = @Backoff(value = 3000, multiplier = 2, maxDelay = 20000))
    @GetMapping(value = "/v1.0/c/render/task_list")
    Items<RenderTask> listRenderTask(@RequestParam(value = "$filter") String filter, @RequestParam(value = "count") Boolean count);

    @Retryable(maxAttempts = 1, backoff = @Backoff(value = 3000, multiplier = 2, maxDelay = 20000))
    @GetMapping(value = "/v1.0/c/render/task_detail/{id}")
    RenderTask renderTaskDetail(@PathVariable(value = "id") String id);
}
