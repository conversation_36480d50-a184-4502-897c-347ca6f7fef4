package com.nd.aic.client;

import com.nd.aic.client.pojo.aigc.AigcRequest;
import com.nd.aic.client.pojo.aigc.AigcResponse;
import com.nd.aic.client.pojo.aigc.ResReq;
import com.nd.aic.client.pojo.aigc.ResResp;
import com.nd.aic.client.pojo.aigc.ResTask;
import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@WafFeignClientAuth
@FeignClient(url = "${aigc.host:https://aiplayer-ai.sdp.101.com}", name = "AigcFeignClient")
public interface AigcFeignClient {

    /**
     * 获取推荐的图片
     */
    @PostMapping(value = "/v1/aigc/ai_teacher/recommend", headers = {"Content-Type=application/json",
            "TenantId=aihub",
            "Authorization=${aigc.auth:sk-qYzO4YbS3MtYFVVcSc1GAjqrDormYrQKBDiaHQ6Ocwrh0vEt}"
    })
    AigcResponse recommendImage(AigcRequest aigcRequest);

    /**
     * AIGC生成图片
     */
    @PostMapping(value = "/v1/aigc/img/txt2img", headers = {"Content-Type=application/json",
            "TenantId=aihub",
            "Authorization=${aigc.auth:sk-qYzO4YbS3MtYFVVcSc1GAjqrDormYrQKBDiaHQ6Ocwrh0vEt}"
    })
    ResTask txt2img(ResReq resReq);

    /**
     * AIGC生成视频
     */
    @PostMapping(value = "/v1/aigc/video/txt2video", headers = {"Content-Type=application/json",
            "TenantId=aihub",
            "Authorization=${aigc.auth:sk-qYzO4YbS3MtYFVVcSc1GAjqrDormYrQKBDiaHQ6Ocwrh0vEt}"
    })
    ResTask txt2video(ResReq resReq);

    /**
     * AIGC任务查询
     */
    @GetMapping(value = "/v1/aigc/task/task", headers = {"Content-Type=application/json",
            "TenantId=aihub",
            "Authorization=${aigc.auth:sk-qYzO4YbS3MtYFVVcSc1GAjqrDormYrQKBDiaHQ6Ocwrh0vEt}"
    })
    ResResp result(@RequestParam("task_id") String taskId);
}
