package com.nd.aic.client;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.pojo.bcs.AutomateLessonForm;
import com.nd.aic.client.pojo.bcs.LackResourceReq;
import com.nd.aic.config.BcsFeignConfig;
import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@WafFeignClientAuth
@FeignClient(url = "${app.feign.bcs.host:https://bcs-app-service.sdp.101.com}", name = "BcsFeignClient", configuration = BcsFeignConfig.class)
public interface BcsFeignClient {

    /**
     * 获取推荐的图片
     */
    @PostMapping(
            value = "/v0.1/external_events/operation/dc822fd2a2c74d2a98f04e4ce1458dfc",
            headers = {
                    "Content-Type=application/json",
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
                    "bcs-app-id=dc822fd2a2c74d2a9accd58334c20a4a",
            }
    )
    JSONObject submitLackRequirement(@RequestParam(value = "suid") String suid,
                                     @RequestBody LackResourceReq lackResourceReq);

    /**
     * 提交录课颗粒流程表单(批量)
     */
    @PostMapping(
            value = "/v0.1/external_events/operation/e9b2d2822ea046d5836511c71deb2784",
            headers = {
                    "Content-Type=application/json",
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
                    "bcs-app-id=e9b2d2822ea046d5804ed8b1b27d4df5",
            }
    )
    JSONObject submitLessonActivityForm(@RequestParam("bcs-request-id") String reqId,
                                        @RequestParam(value = "suid") String suid,
                                        @RequestBody AutomateLessonForm automateLessonForm);

    /**
     * 提交录课颗粒流程表单
     */
    //TODO: Update with correct bcs-app-id
    @PostMapping(
            value = "/v0.1/external_events/operation/a5386d4135cf4559a80de79a13870ab7",
            headers = {
                    "Content-Type=application/json",
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
                    "bcs-app-id=a5386d4135cf45599fd9f1e7374fe5d8",
            }
    )
    JSONObject markLessonFlowStepCompleted(@RequestParam("bcs-request-id") String reqId,
                                           @RequestParam(value = "suid") String suid,
                                           @RequestBody Map<String, Object> body);

    /**
     * 标记录课流程对应的流程表单完成
     */
    @PostMapping(
            value = "/v0.1/external_events/operation/a5386d4135cf4559a80de79a13870ab7",
            headers = {
                    "Content-Type=application/json",
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
                    "bcs-app-id=a5386d4135cf45599fd9f1e7374fe5d8",
            }
    )
    JSONObject markLessonFlowCompleted(@RequestParam("bcs-request-id")String string, @RequestParam(value = "suid") String user, @RequestBody Map<String, Object> body);

    /**
     * 标记录课颗粒对应的流程表单完成
     */
    @PostMapping(
            value = "/v0.1/external_events/operation/a5386d4135cf455981e7441eed665895", // Replace with actual operation ID
            headers = {
                    "Content-Type=application/json",
                    "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133",
                    "bcs-app-id=a5386d4135cf45599fd9f1e7374fe5d8",
            }
    )
    JSONObject markTeachingActivityCompleted(@RequestParam("bcs-request-id") String reqId,
                                             @RequestParam(value = "suid") String suid,
                                             @RequestBody Map<String, Object> body);
}
