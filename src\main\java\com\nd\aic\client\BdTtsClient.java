package com.nd.aic.client;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.pojo.tts.BdTtsRequest;
import com.nd.aic.client.pojo.tts.BdTtsResponse;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.service.AudioResultService;
import com.nd.aic.service.CsService;
import com.nd.aic.service.MediaService;
import com.nd.aic.util.JsonUtils;
import com.nd.aic.util.TextUtils;
import com.nd.gaea.WafException;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.client.http.WafHttpClient;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BdTtsClient extends AbstractTtsClient {

    public static final String HOST = "openspeech.bytedance.com";
    public static final String API_URL = "https://" + HOST + "/api/v1/tts";
    public static final String ACCESS_TOKEN = "ClOxCV5pwZOkZJaKc8i3_R8kgnlBazcw";

    private static final Map<String, String> EMOTION_MAP = Maps.newHashMap();
    private static final Map<String, List<String>> VOICER_EMOTION_MAP = Maps.newHashMap();

    @Value("${app.tts.bytedance.appId:2032697502}")
    private String appId;
    @Value("${app.tts.bytedance.token:ClOxCV5pwZOkZJaKc8i3_R8kgnlBazcw}")
    private String token;

    private final Semaphore semaphore = new Semaphore(3);
    // private final RateLimiter rateLimiter = RateLimiter.create(3);

    private static final WafHttpClient wafHttpClient = new WafHttpClient(60 * 1000 * 5, 60 * 1000 * 20);
    private final MediaService mediaService;

    static {
        EMOTION_MAP.put("开心", "happy");
        // EMOTION_MAP.put("兴奋", "happy");
        EMOTION_MAP.put("悲伤", "sad");
        EMOTION_MAP.put("生气", "angry");
        // EMOTION_MAP.put("鼓励", "surprised");
        EMOTION_MAP.put("惊讶", "surprised");
        EMOTION_MAP.put("惊叹", "surprised");
        // EMOTION_MAP.put("赞叹", "surprised");
        EMOTION_MAP.put("恐惧", "fear");
        EMOTION_MAP.put("厌恶", "hate");
        EMOTION_MAP.put("激动", "excited");
        EMOTION_MAP.put("激情", "excited");
        EMOTION_MAP.put("冷漠", "coldness");
        EMOTION_MAP.put("中性", "neutral");

        VOICER_EMOTION_MAP.put("zh_male_beijingxiaoye_emo_v2_mars_bigtts", Arrays.stream(StringUtils.split("生气，惊讶，恐惧，激动，冷漠，中性", "，")).collect(Collectors.toList()));
        VOICER_EMOTION_MAP.put("zh_female_roumeinvyou_emo_v2_mars_bigtts", Arrays.stream(StringUtils.split("开心，悲伤，生气，惊讶，恐惧，厌恶，激动，冷漠，中性", "，")).collect(Collectors.toList()));
        VOICER_EMOTION_MAP.put("zh_male_yangguangqingnian_emo_v2_mars_bigtts", Arrays.stream(StringUtils.split("开心，悲伤，生气，恐惧，激动，冷漠，中性", "，")).collect(Collectors.toList()));
        VOICER_EMOTION_MAP.put("zh_female_meilinvyou_emo_v2_mars_bigtts", Arrays.stream(StringUtils.split("悲伤，恐惧，中性", "，")).collect(Collectors.toList()));
        VOICER_EMOTION_MAP.put("zh_female_shuangkuaisisi_emo_v2_mars_bigtts", Arrays.stream(StringUtils.split("开心，悲伤，生气，惊讶，激动，冷漠，中性", "，")).collect(Collectors.toList()));
    }

    public BdTtsClient(CsService csService, MediaService mediaService, AudioResultService audioResultService) {
        super(csService, mediaService, audioResultService);
        this.mediaService = mediaService;
    }

    @Override
    protected int maxWord() {
        return 300;
    }

    @Override
    public File mergeTts(TtsSegment ttsSegment) {
        // 移除停顿标记
        String text = TextUtils.clearBreak(ttsSegment.getText());
        ttsSegment.setText(text);
        return super.mergeTts(ttsSegment);
    }

    @Override
    protected File callTts(String text, TtsSegment ttsSegment) throws IOException, InterruptedException {
        // rateLimiter.acquire();
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            semaphore.acquire();
            BdTtsClient ttsClient = ApplicationContextUtil.getApplicationContext().getBean(BdTtsClient.class);
            BdTtsResponse ttsResponse = ttsClient.retryableTts(appId, token, text, ttsSegment);
            byte[] audioData = Base64.getDecoder().decode(ttsResponse.getBase64Data());
            // File tempFile = new File(mediaService.getTempDirectory(), String.format("%s.mp3", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"))));
            File tempFile = File.createTempFile("tts_", ".mp3", mediaService.getTempDirectory());
            log.info("bytedance temp tts file path: {}", tempFile.getAbsolutePath());
            FileUtils.writeByteArrayToFile(tempFile, audioData);
            return tempFile;
        } finally {
            semaphore.release();
            log.info("bytedance tts cost: {} ms, text: {}", stopwatch.stop(), text);
        }
    }

    @Retryable(value = {Exception.class})
    public BdTtsResponse retryableTts(String appId, String token, String text, TtsSegment ttsSegment) {
        return tts(appId, token, text, ttsSegment);
    }

    public static BdTtsResponse tts(String appId, String token, String text, TtsSegment ttsSegment) {
        log.warn("火山TTS: {}", JsonUtils.toJson(ttsSegment));
        text = TextUtils.clearBreak(text);
        String voiceType = StringUtils.replace(ttsSegment.getVoiceId(), "@bd", "");
        BdTtsRequest ttsRequest = new BdTtsRequest(text);
        ttsRequest.getApp().setAppid(appId);
        if (StringUtils.isNotBlank(voiceType)) {
            ttsRequest.getAudio().setVoiceType(voiceType);
        }
        if (null != ttsSegment.getVoiceSpeed()) {
            ttsRequest.getAudio().setSpeedRatio(ttsSegment.getVoiceSpeed());
        }
        // if (StringUtils.isNotBlank(ttsSegment.getEmotion())) {
        //     String emotion = EMOTION_MAP.getOrDefault(ttsSegment.getEmotion(), null);
        //     List<String> supportEmotions = VOICER_EMOTION_MAP.getOrDefault(voiceType, Lists.newArrayList()).stream().map(EMOTION_MAP::get).collect(Collectors.toList());
        //     if (VOICER_EMOTION_MAP.containsKey(voiceType) && StringUtils.isNotBlank(emotion) && supportEmotions.contains(emotion)) {
        //         log.warn("支持的情感: {}, 语音: {}, emotion: {}", ttsSegment.getEmotion(), voiceType, emotion);
        //         if (StringUtils.isNotBlank(emotion)) {
        //             ttsRequest.getAudio().setEnableEmotion(true);
        //             ttsRequest.getAudio().setEmotion(emotion);
        //             ttsRequest.getAudio().setEmotionScale(1.0f);
        //         }
        //     } else {
        //         log.warn("不支持的情感: {}, 语音: {}", ttsSegment.getEmotion(), voiceType);
        //     }
        // }
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Authorization", "Bearer; " + token);
        HttpEntity<BdTtsRequest> request = new HttpEntity<>(ttsRequest, headers);
        JSONObject resp = wafHttpClient.executeForObject(API_URL, HttpMethod.POST, request, JSONObject.class);
        int sequence = resp.getIntValue("sequence");
        if (sequence >= 0) {
            log.info("火山TTS合成失败：{}", JsonUtils.toJson(ttsSegment));
            throw new WafException("合成失败");
        }
        JSONObject addition = resp.getJSONObject("addition");
        long duration = addition.getLong("duration");
        String base64Audio = resp.getString("data");
        return BdTtsResponse.builder().base64Data(base64Audio).duration(duration).build();
    }

    public static void main(String[] args) throws IOException {
        String text = "我真是受够了！你们这些所谓的\"智能客服\"除了复制粘贴官方话术还会什么？每次遇到问题就让我重启手机、清除缓存，解决不了就装死！我花五千多买的新机才用三天就黑屏，你们居然让我自己找维修点？这就是你们吹上天的售后服务？行啊，明天我就去消费者协会投诉，咱们看看到底是谁耗不起！";
        TtsSegment ttsSegment = new TtsSegment().setVoiceId("zh_male_beijingxiaoye_emo_v2_mars_bigtts").setVoiceSpeed(1f).setText(text);
        BdTtsResponse ttsResponse = tts("2032697502", ACCESS_TOKEN, text, ttsSegment);
        byte[] audioData = Base64.getDecoder().decode(ttsResponse.getBase64Data());
        File tempFile = File.createTempFile("tts_", ".mp3");
        FileUtils.writeByteArrayToFile(tempFile, audioData);
        log.info("temp tts file path: {}", tempFile.getAbsolutePath());
    }
}
