package com.nd.aic.client;

import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.service.AudioResultService;
import com.nd.aic.service.CsService;
import com.nd.aic.service.MediaService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 组合TTS
 */
@Slf4j
@Service
@Primary
public class CombinationTtsClient extends AbstractTtsClient {

    private final MmTtsClient mmTtsClient;
    private final NdTtsClient ndTtsClient;
    private final BdTtsClient bdTtsClient;

    public CombinationTtsClient(CsService csService, MediaService mediaService, AudioResultService audioResultService,
                                MmTtsClient mmTtsClient, NdTtsClient ndTtsClient, BdTtsClient bdTtsClient) {
        super(csService, mediaService, audioResultService);
        this.mmTtsClient = mmTtsClient;
        this.ndTtsClient = ndTtsClient;
        this.bdTtsClient = bdTtsClient;
    }

    @Override
    public File mergeTts(TtsSegment ttsSegment) {
        if ("dj".equalsIgnoreCase(ttsSegment.getVoiceId()) || StringUtils.endsWithIgnoreCase(ttsSegment.getVoiceId(), "@nd")) {
            return ndTtsClient.mergeTts(ttsSegment);
        } else if (StringUtils.endsWithIgnoreCase(ttsSegment.getVoiceId(), "@bd")) {
            return bdTtsClient.mergeTts(ttsSegment);
        }
        return mmTtsClient.mergeTts(ttsSegment);
    }

    @SneakyThrows
    @Override
    protected File callTts(String text, TtsSegment ttsSegment) throws IOException {
        return null;
    }

}
