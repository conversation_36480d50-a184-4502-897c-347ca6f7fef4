package com.nd.aic.client;

import com.nd.aic.client.pojo.chat.ChatCompletion;
import com.nd.aic.client.pojo.chat.ChatCompletionResponse;
import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(url = "${fastGpt.host:https://fastgpt-v2.sdp.ndaeweb.com/}", name = "FastGptClient")
public interface FastGptFeignClient {

    /**
     * 获取资源配置
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.resourceConfig.token:Bearer fastgpt-aUMLDFXor77xMWoJyJJcYX8PHsTytrMZAGVyoa10rfZ4UePiLRhA}"
    })
    ChatCompletionResponse askForResourceConfig(ChatCompletion chatCompletion);

    /**
     * 获取资源配置(测试用)
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.resourceConfig.debug.token:Bearer fastgpt-yhLvrDhsDhNisaGpnu0uoTYoR3JTMix4ucKQvybWQpATySFD2JoXpZ}"
    })
    ChatCompletionResponse askForResourceConfig4Debug(ChatCompletion chatCompletion);

    /**
     * 获取表演方案参数配置
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.resourceConfig.token:Bearer fastgpt-a4mqbwDHJA6Zk5VBwho0jjLv1Z1KI2WJeUXCfWpb05UIiA4uihhl}"
    })
    ChatCompletionResponse askForPerformanceConfig(ChatCompletion chatCompletion);

    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.keyword.token:Bearer fastgpt-efAIrvTt0a2wcV61TJZJQyyBFvFJlbSMGW7OhlbIe9BJKqkO8UGCrvhlo}"
    })
    ChatCompletionResponse askForKeyword(ChatCompletion chatCompletion);


    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json"})
    ChatCompletionResponse ask(@RequestHeader("Authorization") String authorization, @RequestBody ChatCompletion chatCompletion);


    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json"})
    ChatCompletionResponse askForMedia(@RequestHeader("Authorization") String authorization, @RequestBody ChatCompletion chatCompletion);


    /**
     * 全屏播放 PPT 时间
     * @param chatCompletion 聊天内容
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.fullscreen.token:Bearer fastgpt-aLuWVLVRTsgmn5J64ApX5i4mcQboHjN3CA0xuJe5LvnGvKbcb84PFheQwE}"})
    ChatCompletionResponse askForFullScreenTimeTick(@RequestBody ChatCompletion chatCompletion);


    /**
     * 发音标注AI智能体
     * @param chatCompletion 聊天内容
     * @return 发音标注
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.tts.pronunciation.token:Bearer fastgpt-evLvXd7qKRESKzxWQkaE0IXiSBbcvfV6ks7Q2OjaLYtmpizazaUyXppxX2t}"})
    ChatCompletionResponse askForTTSPronunciationAnnotation(@RequestBody ChatCompletion chatCompletion);



    /**
     * 发音停顿的AI智能体
     * @param chatCompletion 聊天内容
     * @return 发音标注
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.tts.pause.token:Bearer fastgpt-dEfvsxpN8PK9ClkP4Ak4jl18TbryfMwBP3zEddGiN9ocs8LKu5mxMWx3Fw}"})
    ChatCompletionResponse askForTTSPauseAnnotation(@RequestBody ChatCompletion chatCompletion);


    /**
     * 发音停顿的AI智能体
     * @param chatCompletion 聊天内容
     * @return 发音标注
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.words.check.token:Bearer fastgpt-pAIGo2v18Rhfk0vkhS2GxyaHMAldLs3tgZH7Nko35ejsNO2OPXDENQI9KtZFZ6m}"})
    ChatCompletionResponse askForWordsCheck(@RequestBody ChatCompletion chatCompletion);



    /**
     * Story rewrite
     * @param chatCompletion chat completion
     * @return output
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.words.check.token:Bearer fastgpt-g6M06zfPAhdIkPfvLwsA5JFkthCPd97Oe1e0HGUOKJEkk069XErr57cX1ltSp9}"})
    ChatCompletionResponse askForStoryWrite(@RequestBody ChatCompletion chatCompletion);



    /**
     * Story rewrite
     * @param chatCompletion chat completion
     * @return output
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.words.check.token:Bearer fastgpt-p2YTFUqznU3HaHU7xB2YVZClSbb3lPDiCPkcFoynBLN5svyQUzfo}"})
    ChatCompletionResponse askForLanguage(@RequestBody ChatCompletion chatCompletion);

    /**
     * interaction question design
     * @param chatCompletion chat completion
     * @return output
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=${fastGpt.words.check.token:Bearer fastgpt-rbZtmhQcAnS5XNmaVVy0x9a3VvLv9AGoi2pME56WZuTX4kA2C77KgLj8n3h4Q}"})
    ChatCompletionResponse askForInteractionQuestion(@RequestBody ChatCompletion chatCompletion);


    /**
     * IM模板
     */
    @PostMapping(value = "/api/v1/chat/completions", headers = {"Content-Type=application/json",
            "Authorization=Bearer fastgpt-prDgUhko8i6egLXIHJ7Kl3c6E5fe667qoeh1W9c0gkULBQ7Q1pLUUK7BFAE2uf"
    })
    ChatCompletionResponse askForImTemplate(@RequestBody ChatCompletion chatCompletion);


}
