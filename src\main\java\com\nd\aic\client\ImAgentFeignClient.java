package com.nd.aic.client;


import com.alibaba.fastjson.JSONObject;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(url = "${imagent.host:https://im-agent.sdp.101.com}", name = "ImAgentFeignClient")
public interface ImAgentFeignClient {

    @PostMapping(value = "/v0.2/api/agents/users/tokens", headers = {"Content-Type=application/json"})
    JSONObject login(JSONObject reqBody);

    @PostMapping(value = "/v0.2/api/agents/messages", headers = {"Content-Type=application/json", "sdp-app-id=b4fb92a0-af7f-49c2-b270-8f62afac1133"})
    JSONObject send(@RequestHeader("Authorization") String token, String reqBody);

}
