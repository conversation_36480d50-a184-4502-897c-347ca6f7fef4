package com.nd.aic.client;

import com.nd.aic.entity.VideoInteraction;
import com.nd.aic.pojo.interactive.ActiveEventVO;
import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@WafFeignClientAuth
@FeignClient(url = "${interaction.host:https://eb-ai-courseware-api.sdp.101.com/}", name = "InteractionFeignClient")
public interface InteractionFeignClient {

    @GetMapping(value = "v1.0/courseware/ref_resource_id/{resource_id}?container_id=4f7a4a2c-9dac-411f-9a15-1851f8326247")
    VideoInteraction queryInteraction(@PathVariable("resource_id") String resourceId);

    @GetMapping(value = "v1.0/courseware/ref_resource_id/{resource_id}")
    VideoInteraction queryInteraction(@PathVariable("resource_id") String resourceId, @RequestParam("container_id") String containerId);

    @PutMapping(value = "/v1.0/admin/courseware/{id}")
    VideoInteraction submitInteraction(@PathVariable("id") String id, @RequestBody VideoInteraction interaction);

    @GetMapping("/v1.0/active_events/{active_event_id}")
    ActiveEventVO findActiveEventById(@PathVariable("active_event_id") String activeEventId);

    @PostMapping("/v1.0/active_events")
    ActiveEventVO createActiveEventById(@RequestBody ActiveEventVO activeEventVO);


}
