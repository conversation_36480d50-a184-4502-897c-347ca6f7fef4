package com.nd.aic.client;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.client.pojo.tts.MmTtsRequest;
import com.nd.aic.client.pojo.tts.MmTtsResponse;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.service.AudioResultService;
import com.nd.aic.service.CsService;
import com.nd.aic.service.MediaService;
import com.nd.gaea.client.http.WafHttpClient;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * MiniMax
 * */
@Slf4j
@Service
public class MmTtsClient extends AbstractTtsClient implements InitializingBean {

    public static final String HOST = "api.minimax.chat";
    public static final String API_URL = "https://" + HOST + "/v1/t2a_v2";
    public static final String GROUP_ID = "1834772057259262142";
    public static final String ACCESS_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************.aX9ssavswkLduUYuDIA4qM6uawFiY7ZNhplVheErZ-y3-a14RwhGyxsH6-OFpf26dNygyMgv1UhC9heQYVbZeslrEkY_BBWCC7YUKiRaIoFCI64LJdbke2gwZbrYoRy5mR3N-81XZIfnH2LSOx79s2nEUzwge_4G0-mZBQkc6Lx6lEe4sTAgzcppIlkRgc9rZR6N74zBo6ADqd08os_kJRJ-gsZQiGtAdkbshFeFJoJyOXLrnXaMYh_EpkBNGDuG2u24nwTTxRMD4zDwWUy4wc4s8rO8ag0pPri7iy5wBfKmG3YlNW8qHmXvZ-1kzbnmrsTca0aQUOGMrB3b2Z3pmg";

    // private final Semaphore semaphore = new Semaphore(1);
    private final RateLimiter rateLimiter = RateLimiter.create(8);
    private final Map<String, Float> voiceSpeedMap = Maps.newHashMap();

    private final WafHttpClient wafHttpClient = new WafHttpClient(60 * 1000 * 5, 60 * 1000 * 20);
    private final MediaService mediaService;

    public MmTtsClient(MediaService mediaService, CsService csService, AudioResultService audioResultService) {
        super(csService, mediaService, audioResultService);
        this.mediaService = mediaService;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        voiceSpeedMap.put("lovely_girl", 1.05f);
        voiceSpeedMap.put("cartoon_pig", 1.05f);
    }

    @Override
    public File mergeTts(TtsSegment ttsSegment) {
        throw WafI18NException.of("TTS_PROVIDER_NOT_SUPPORT", "当前TTS提供商不再支持，请选择其它配音选项", HttpStatus.BAD_REQUEST);
    }

    @SneakyThrows
    @Override
    protected File callTts(String text, TtsSegment ttsSegment) throws IOException, InterruptedException {
        rateLimiter.acquire();
        MmTtsResponse ttsResponse = tts(text, ttsSegment);
        if (!Objects.equals(ttsResponse.getBaseResp().getStatusCode(), 0)) {
            throw new RuntimeException(ttsResponse.getBaseResp().getStatusMsg());
        }
        byte[] audioData = Hex.decodeHex(ttsResponse.getData().getAudio().toCharArray());
        File tempFile = File.createTempFile("tts_", ".mp3", mediaService.getTempDirectory());
        // tempFile = mediaService.convertAudio(mediaService.getTempDirectory(), tempFile, new File(tempFile.getParentFile(), String.format("_%s", tempFile.getName())));
        log.info("temp minimax tts file path: {}", tempFile.getAbsolutePath());
        FileUtils.writeByteArrayToFile(tempFile, audioData);
        return tempFile;
    }

    public MmTtsResponse tts(String text, TtsSegment ttsSegment) {
        String url = API_URL + "?GroupId=" + GROUP_ID;
        MmTtsRequest ttsRequest = new MmTtsRequest();
        ttsRequest.setText(text);
        ttsRequest.setPronunciationDict(ttsSegment.getPronunciationDict());
        ttsRequest.getVoiceSetting().setVoiceId(ttsSegment.getVoiceId());

        if (null != ttsSegment.getVoiceSpeed()) {
            ttsRequest.getVoiceSetting().setSpeed(ttsSegment.getVoiceSpeed());
        } else {
            Float voiceSpeed = voiceSpeedMap.get(ttsSegment.getVoiceId());
            if (voiceSpeed != null) {
                ttsRequest.getVoiceSetting().setSpeed(voiceSpeed);
            }
        }

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Authorization", "Bearer " + ACCESS_TOKEN);

        HttpEntity<MmTtsRequest> request = new HttpEntity<>(ttsRequest, headers);
        return wafHttpClient.executeForObject(url, HttpMethod.POST, request, MmTtsResponse.class);
    }
}
