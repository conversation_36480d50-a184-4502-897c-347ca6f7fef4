package com.nd.aic.client;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.service.AudioResultService;
import com.nd.aic.service.CsService;
import com.nd.aic.service.MediaService;
import com.nd.aic.util.JsonUtils;
import com.nd.aic.util.TextUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.client.http.WafHttpClient;
import com.nd.sdp.cs.utils.MD5Util;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static java.lang.Thread.sleep;

/**
 * MiniMax
 */
@Slf4j
@Service
public class NdTtsClient extends AbstractTtsClient {

    public static final String SUBMIT_API_URL = "https://tts.101.com/api/v1/sovits/add-task";
    public static final String QUERY_API_URL = "https://tts.101.com/api/v1/sovits/get-info";

    private final RateLimiter rateLimiter = RateLimiter.create(8);

    private static final WafHttpClient wafHttpClient = new WafHttpClient(60 * 1000 * 5, 60 * 1000 * 20);
    private final MediaService mediaService;

    private static final Map<Integer, Integer> modelRefAudioMapping = Maps.newHashMap();

    static {
        // 精英男性
        modelRefAudioMapping.put(20, 57);
        // 女性
        modelRefAudioMapping.put(21, 66);
        // 萌萌女童
        modelRefAudioMapping.put(22, 77);
        // 霓
        modelRefAudioMapping.put(43, 122);
        // 芭芭拉
        modelRefAudioMapping.put(42, 120);
        // 港普空姐
        modelRefAudioMapping.put(100, 153);
    }

    public NdTtsClient(CsService csService, MediaService mediaService, AudioResultService audioResultService) {
        super(csService, mediaService, audioResultService);
        this.mediaService = mediaService;
    }

    // @SneakyThrows
    // public File cacheableTts(String text, TtsArgument ttsArgument) {
    //     String cacheKey = "cacheableTts:" + ttsArgument.getVoiceType() + ":" + text;
    //     Object tone = MapUtils.getObject(ttsArgument.getPronunciationDict(), "tone");
    //     if (null != tone) {
    //         cacheKey += JsonUtils.toJson(tone);
    //     }
    //     cacheKey = UUID.nameUUIDFromBytes(cacheKey.getBytes(StandardCharsets.UTF_8)).toString();
    //     String resourceId = audioResultService.queryAudio(cacheKey, "CS");
    //     if (resourceId != null) {
    //         log.info("audio cache found: {}", resourceId);
    //         String mp3Url = "https://cdncs.101.com/v0.1/download?dentryId=" + resourceId;
    //         return mediaService.download(mp3Url, String.format("%s.mp3", resourceId), true);
    //     }
    //
    //     File tempFile = callTts(text, ttsArgument);
    //
    //     Dentry dentry = csService.uploadFile(147507L, "tts_mp3/", tempFile.getName(), tempFile);
    //     audioResultService.saveAudio(cacheKey, dentry.getName(), dentry.getDentryId(), "CS");
    //     return tempFile;
    // }


    @Override
    public File mergeTts(TtsSegment ttsSegment) {
        // 移除停顿标记
        String text = TextUtils.clearBreak(ttsSegment.getText());
        ttsSegment.setText(text);
        return super.mergeTts(ttsSegment);
    }

    @SneakyThrows
    @Override
    protected File callTts(String text, TtsSegment ttsSegment) throws IOException {
        rateLimiter.acquire();
        NdTtsClient ttsClient = ApplicationContextUtil.getApplicationContext().getBean(NdTtsClient.class);
        File wavFile = null;
        try {
            wavFile = ttsClient.retryableTts(text, ttsSegment);
            File tempFile = new File(mediaService.getTempDirectory(), String.format("%s.mp3", wavFile.getName()));
            tempFile = mediaService.convertAudio(mediaService.getTempDirectory(), wavFile, tempFile);
            long duration = mediaService.durationMills(tempFile);
            log.info("temp nd tts file path: {}, duration: {}", tempFile.getAbsolutePath(), duration);
            return tempFile;
        } finally {
            if (wavFile != null && wavFile.exists() && wavFile.isFile()) {
                FileUtils.deleteQuietly(wavFile);
            }
        }
    }

    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public File retryableTts(String text, TtsSegment ttsSegment) throws InterruptedException, IOException {
        // 统计耗时
        long duration = 0;
        long costMs = 0;
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            JSONObject ttsResponse = tts(text, ttsSegment);
            costMs = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            String downloadUrl = ttsResponse.getJSONObject("data").getString("result_audio");
            String taskId = ttsResponse.getJSONObject("data").getString("id");
            File wavFile = mediaService.download(downloadUrl, String.format("tts_nd_%s.wav", taskId), true);
            duration = mediaService.durationMills(wavFile);
            return wavFile;
        } finally {
            if (duration > 0) {
                log.info("nd tts cost time: {}, duration: {}, text: {}", costMs, duration, text);
            }
        }
    }

    public static JSONObject tts(String text, TtsSegment ttsSegment) throws InterruptedException {
        final String appId = "D982E9D2";
        final String appKey = "UqlJVIyN5r8Ws1c4";
        int type = 1;
        int model = 1;
        int refAudio = 1;
        String language = "zh";
        int speed = 1;
        long reqTime = Instant.now().getEpochSecond();
        Map<String, Object> voiceSetting = null;
        if (StringUtils.endsWithIgnoreCase(ttsSegment.getVoiceId(), "@nd")) {
            String voiceId = StringUtils.removeEndIgnoreCase(ttsSegment.getVoiceId(), "@nd");
            String[] modelParam = StringUtils.split(voiceId, ":");
            if (modelParam.length > 2) {
                model = Integer.parseInt(modelParam[0]);
                refAudio = Integer.parseInt(modelParam[1]);
                voiceSetting = Maps.newHashMap();
                voiceSetting.put("voice_id", modelParam[2]);
            } else if (modelParam.length > 1) {
                model = Integer.parseInt(modelParam[0]);
                refAudio = Integer.parseInt(modelParam[1]);
            } else {
                model = Integer.parseInt(voiceId);
                refAudio = modelRefAudioMapping.getOrDefault(model, 1);
            }
        }

        Map<String, Object> body = Maps.newHashMap();
        body.put("app_id", appId);
        body.put("ai_type", type);
        body.put("model", model);
        body.put("content", text);
        body.put("speed", speed);
        body.put("language", language);
        body.put("ref_audio", refAudio);
        body.put("req_time", reqTime);
        if (null != voiceSetting) {
            body.put("voice_setting", voiceSetting);
        }

        List<String> signParams = Lists.newArrayList();
        List<String> keys = Lists.newArrayList(body.keySet());
        Collections.sort(keys);
        for (String key : keys) {
            Object value = body.get(key);
            if (!value.getClass().getName().startsWith("java.lang")) {
                value = JsonUtils.toJson(value);
            }
            signParams.add(String.format("%s=%s", key, value));
        }
        String signContent = String.join("&", signParams) + "&sign_key=" + appKey;

        // String signContent = String.format("ai_type=%d&app_id=%s&content=%s&language=%s&model=%d&ref_audio=%d&req_time=%d&speed=%d&sign_key=%s", type, appId, text, language, model, refAudio, reqTime, speed, appKey);
        body.put("sign", MD5Util.md5(signContent));
        JSONObject task = wafHttpClient.postForObject(SUBMIT_API_URL, body, JSONObject.class);

        long startTime = System.currentTimeMillis();
        JSONObject result = null;
        while (true) {
            int taskId = task.getJSONObject("data").getIntValue("id");
            log.info("tts task: {}", task);
            reqTime = Instant.now().getEpochSecond();
            signContent = String.format("app_id=%s&id=%d&req_time=%d&sign_key=%s", appId, taskId, reqTime, appKey);
            body = Maps.newHashMap();
            body.put("app_id", appId);
            body.put("id", taskId);
            body.put("req_time", reqTime);
            body.put("sign", MD5Util.md5(signContent));
            result = wafHttpClient.postForObject(QUERY_API_URL, body, JSONObject.class);

            int state = result.getJSONObject("data").getIntValue("state");
            if (1 == state) {
                // 成功
                log.info("tts success: {}", result);
                break;
            } else if (3 == state) {
                // 失败
                log.error("tts failed: {}", result);
                throw WafI18NException.of("TTS_ERROR", result.getJSONObject("data").getString("result_msg"), HttpStatus.BAD_REQUEST);
            }
            if (0 != startTime && System.currentTimeMillis() - startTime > 600 * 1000) {
                log.error("tts timeout: {}s, response: {}", (System.currentTimeMillis() - startTime) / 1000, result);
                startTime = 0;
            }
            sleep(500);
        }
        return result;
    }

    public static void main(String[] args) throws InterruptedException {
        TtsSegment ttsSegment = new TtsSegment();
        ttsSegment.setVoiceId("100:1:Chinese (Mandarin)_Soft_Girl@nd");
        ttsSegment.setText("测试内容");
        ttsSegment.setVoiceSpeed(1f);
        JSONObject result = tts("这边试下了99U也是读成九十九U，可以在99之间加个标点符号", ttsSegment);
        System.out.println(result.toString());
    }
}
