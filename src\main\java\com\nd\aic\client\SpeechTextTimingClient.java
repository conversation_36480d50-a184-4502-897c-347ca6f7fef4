package com.nd.aic.client;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.pojo.asr.AsrTask;
import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.service.CsService;
import com.nd.gaea.WafException;
import com.nd.gaea.client.WafResourceAccessException;
import com.nd.gaea.client.http.WafHttpClient;
import com.nd.gaea.util.WafJsonMapper;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.File;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class SpeechTextTimingClient {

    @Value("${app.asr_ata.appId:6416769719}")
    private String appId;
    @Value("${app.asr_ata.token:moiOE3XJSRZ1wdhHEqTfEv_2V5kjyxxC}")
    private String token;
    static final String APPID = "6416769719";
    static final String TOKEN = "moiOE3XJSRZ1wdhHEqTfEv_2V5kjyxxC";

    static final String SUBTITLE_APPID = "9557510582";
    static final String SUBTITLE_TOKEN = "JbW35T6FuPTPVQt1Gi52bFC5rs2y3djy";

    static final String ATA_SUBMIT_URL = "https://openspeech.bytedance.com/api/v1/vc/ata/submit?appid=%s&caption_type=speech&sta_punc_mode=3";
    static final String ATA_QUERY_URL = "https://openspeech.bytedance.com/api/v1/vc/ata/query?appid=%s&id=%s";

    private static final String VC_SUBMIT_URL = "https://openspeech.bytedance.com/api/v1/vc/submit?appid=%s&language=%s&max_lines=1&words_per_line=30&with_speaker_info=True";
    private static final String VC_QUERY_URL = "https://openspeech.bytedance.com/api/v1/vc/query?appid=%s&id=%s";
    private static final String LANGUAGE = "zh-CN";

    private final WafHttpClient httpClient = new WafHttpClient(60 * 1000 * 5, 60 * 1000 * 20);
    private final CsService csService;

    private final RateLimiter rateLimiter = RateLimiter.create(2);

    /**
     * 自动字幕打轴提交-二进制
     */
    @Retryable(value = {Exception.class}, maxAttempts = 2, backoff = @Backoff(value = 5000, multiplier = 2, maxDelay = 20000))
    public AsrTask ataSubmit(byte[] audioData, String audioText) {
        int waitMinutes = 2;
        if (!rateLimiter.tryAcquire(waitMinutes, TimeUnit.MINUTES)) {
            throw new WafException("SpeechTextTimingClient.ataSubmit rate limit");
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
        httpHeaders.add("Accept", "*/*");
        httpHeaders.add("Authorization", String.format("Bearer; %s", token));
        httpHeaders.add("Connection", "keep-alive");

        // 设置音频文件的内容类型
        HttpHeaders audioHeaders = new HttpHeaders();
        audioHeaders.setContentType(MediaType.parseMediaType("audio/mp3"));
        HttpEntity<byte[]> audioEntity = new HttpEntity<>(audioData, audioHeaders);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("audio-text", audioText);
        body.add("data", audioEntity);
        body.add("sta_punc_mode", "3");

        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(body, httpHeaders);
        try {
            return httpClient.executeForObject(String.format(ATA_SUBMIT_URL, appId), HttpMethod.POST, httpEntity, AsrTask.class);
        } catch (Exception e) {
            if (e instanceof WafResourceAccessException) {
                WafResourceAccessException wafException = (WafResourceAccessException) e;
                log.error("ataSubmit responseBody: {}\ndetail: {}", wafException.getResponseBody(), wafException.getDetail());
                Map<String, Object> detail = Maps.newHashMap();
                detail.put("audioText", audioText);
                File mp3File = null;
                File jsonFile = null;
                try {
                    long ct = System.currentTimeMillis();
                    mp3File = File.createTempFile("ataSubmitError", ".json");
                    FileUtils.writeByteArrayToFile(mp3File, audioData);
                    Dentry mp3 = csService.uploadFile(147507L, "logs/ataSubmitError", String.format("%s.mp3", Long.toHexString(ct)), mp3File);

                    detail.put("audioId", mp3.getDentryId());
                    jsonFile = File.createTempFile("ataSubmitError", ".json");
                    FileUtils.writeStringToFile(jsonFile, WafJsonMapper.toJson(detail));
                    Dentry json = csService.uploadFile(147507L, "logs/ataSubmitError", String.format("%s.json", Long.toHexString(ct)), jsonFile);
                    log.info("ataSubmitError: {}; responseBody: {}", json.getDentryId(), wafException.getResponseBody());
                } catch (Exception ignore) {
                } finally {
                    FileUtils.deleteQuietly(mp3File);
                    FileUtils.deleteQuietly(jsonFile);
                }
            }
            throw e;
        }
    }

    /**
     * 自动字幕打轴提交
     */
    @Retryable(value = {Exception.class}, maxAttempts = 2, backoff = @Backoff(value = 5000, multiplier = 2, maxDelay = 20000))
    public AsrTask ataSubmit(String audioUrl, String audioText) {
        int waitMinutes = 2;
        if (!rateLimiter.tryAcquire(waitMinutes, TimeUnit.MINUTES)) {
            throw new WafException("SpeechTextTimingClient.ataSubmit rate limit");
        }
        audioUrl = StringUtils.replace(audioUrl, "https://gcdncs.101.com", "https://cdncs.101.com");
        JSONObject body = new JSONObject();
        body.put("url", audioUrl);
        body.put("audio_text", audioText);
        body.put("sta_punc_mode", "3");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Accept", "*/*");
        httpHeaders.add("Authorization", String.format("Bearer; %s", token));
        httpHeaders.add("Connection", "keep-alive");
        httpHeaders.add("content-type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(body.toString(), httpHeaders);
        return httpClient.executeForObject(String.format(ATA_SUBMIT_URL, appId), HttpMethod.POST, httpEntity, AsrTask.class);
    }

    /**
     * 自动字幕打轴查询
     */
    @Retryable(value = {Exception.class}, maxAttempts = 2, backoff = @Backoff(value = 1500, multiplier = 2))
    public AtaResult ataQuery(String jobId) {
        int waitMinutes = 2;
        if (!rateLimiter.tryAcquire(waitMinutes, TimeUnit.MINUTES)) {
            throw new WafException("SpeechTextTimingClient.ataQuery rate limit");
        }
        String url = String.format(ATA_QUERY_URL, appId, jobId);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Accept", "*/*");
        httpHeaders.add("Authorization", String.format("Bearer; %s", token));
        httpHeaders.add("Connection", "keep-alive");
        HttpEntity<String> httpEntity = new HttpEntity<>(httpHeaders);
        return httpClient.executeForObject(url, HttpMethod.GET, httpEntity, AtaResult.class);
    }

    /**
     * 音视频字幕生成提交
     */
    public AsrTask vcSubmit(String voiceUrl) {
        String url = String.format(VC_SUBMIT_URL, SUBTITLE_APPID, LANGUAGE);
        JSONObject body = new JSONObject();
        body.put("url", voiceUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer; " + SUBTITLE_TOKEN);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(body, headers);
        AsrTask resp = httpClient.executeForObject(url, HttpMethod.POST, httpEntity, AsrTask.class);
        if (resp == null) {
            throw new RuntimeException("提交接口请求失败！");
        }
        if (!Objects.equals(resp.getCode(), 0) || StringUtils.isBlank(resp.getId())) {
            throw new RuntimeException("提交处理失败！" + resp);
        }
        return resp;
    }

    /**
     * 音视频字幕生成查询
     * 接口说明见 https://www.volcengine.com/docs/6561/80909 0:成功， 2000:进行中，  其它:失败
     */
    public AtaResult vcQuery(String jobId) {
        String url = String.format(VC_QUERY_URL, SUBTITLE_APPID, jobId);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer; " + SUBTITLE_TOKEN);
        HttpEntity<String> httpEntity = new HttpEntity<>(headers);
        AtaResult resp = httpClient.executeForObject(url, HttpMethod.GET, httpEntity, AtaResult.class);
        if (Objects.equals(resp.getCode(), 2000) || Objects.equals(resp.getCode(), 2001)) {
            return null;
        }
        if (!Objects.equals(resp.getCode(), 0)) {
            throw new WafException("查询接口请求失败！" + resp);
        }
        return resp;
    }
}
