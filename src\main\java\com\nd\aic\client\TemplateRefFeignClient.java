package com.nd.aic.client;

import com.nd.gaea.client.feign.WafFeignClientAuth;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@WafFeignClientAuth
@FeignClient(url = "${performance_template.host:http://*************:3001}", name = "PerformanceTemplateFeignClient")
public interface TemplateRefFeignClient {

    @GetMapping(value = "/api/templates")
    Object listTemplates();

    @GetMapping(value = "/api/template/{id}")
    Object getTemplate(@PathVariable("id") String id);

    @PostMapping(value = "/api/tasks")
    Object createTask(@RequestBody Map<String, Object> task);

    @GetMapping(value = "/api/task/{id}")
    Object getTask(@PathVariable("id") String id);
}
