package com.nd.aic.client.pojo.aigc;

import org.hibernate.validator.constraints.NotBlank;
import org.joda.time.LocalDateTime;

import lombok.Data;

@Data
public class ResReq {
    // 业务分类
    @NotBlank
    private String topic;
    // 批注内容
    @NotBlank
    private String content;
    // 视频的提示词
    // private String prompt;
    private String provideType = "Response";
    private Option option;
    private Production production;

    @Data
    public static class Option {
        // 期望宽，部分模型无法指定具体的分辨率，那么会当做宽的比
        private Integer width;
        // 期望高，部分模型无法指定具体的分辨率，那么会当做高的比
        private Integer height;
    }

    @Data
    public static class Production {
        // 生产线代码
        private String productionCategoryCode;
    }

    public static void main(String[] args) {
        long duration = LocalDateTime.now().toDate().getTime() / 1000 - 1734522399;
        System.out.println(duration);
    }
}
