package com.nd.aic.client.pojo.aigc;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResResp {

    private Info info;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Info {
        private String taskId;
        private String productionId;
        private String content;
        private String refImg;
        private String aImodel;
        private String status;
        private String prompts;
        private String negativePrompts;
        private String videoPrompts;
        private String drawType;
        private ImgOption imgOption;
        private int maxRetry;
        private int currRetry;
        private List<GenInfo> genInfos;
        private String createTime;
        private String updateTime;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImgOption {
        private int width;
        private int height;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GenInfo {
        private String resourceId;
        private String resourceType;
        private String url;
    }
}
