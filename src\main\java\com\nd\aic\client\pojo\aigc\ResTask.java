package com.nd.aic.client.pojo.aigc;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Map;

import lombok.Data;

@Data
public class ResTask {
    private String taskId;
    private String taskStatus;

    @JsonIgnore
    private Map<String, Object> props = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return props;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        props.put(key, value);
    }
}
