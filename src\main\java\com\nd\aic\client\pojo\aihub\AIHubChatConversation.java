package com.nd.aic.client.pojo.aihub;

import java.util.List;

import lombok.Data;

// https://ai-hub-server.sdpsg.101.com/console/api/apps/9bf36b45-8fd6-4756-97f5-810a2cd4fc21/chat-conversations/22d4e75b-ad63-442b-8992-2c1aed2a30bd
@Data
public class AIHubChatConversation {
    private String id;
    private String status;
    private String fromSource;
    private String fromEndUserId;
    private String fromAccountId;
    private long createdAt;
    private boolean annotated;
    private String introduction;
    private ModelConfig modelConfig;
    private int messageCount;
    private UserFeedbackStats userFeedbackStats;
    private AdminFeedbackStats adminFeedbackStats;

    // Getters and Setters
    @Data
    public static class ModelConfig {
        private String openingStatement;
        private List<String> suggestedQuestions;
        private String model;
        private String userInputForm;
        private String prePrompt;
        private String agentMode;

        // Get<PERSON> and Setters
    }

    @Data
    public static class UserFeedbackStats {
        private int like;
        private int dislike;

        // <PERSON><PERSON> and Set<PERSON>
    }

    @Data
    public static class AdminFeedbackStats {
        private int like;
        private int dislike;

        // Getters and Setters
    }
}

