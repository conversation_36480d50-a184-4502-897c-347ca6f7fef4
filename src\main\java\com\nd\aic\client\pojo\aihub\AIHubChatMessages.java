package com.nd.aic.client.pojo.aihub;

import java.util.List;
import java.util.Map;

import lombok.Data;

// https://ai-hub-server.sdpsg.101.com/console/api/apps/9bf36b45-8fd6-4756-97f5-810a2cd4fc21/chat-messages?conversation_id=22d4e75b-ad63-442b-8992-2c1aed2a30bd&limit=10
@Data
public class AIHubChatMessages {
    private int limit;
    private boolean hasMore;
    private List<Data> data;

    // Getters and Setters
    @lombok.Data
    public static class Data {
        private String id;
        private String conversationId;
        private Inputs inputs;
        private String query;
        private String message;
        private int messageTokens;
        private String answer;
        private int answerTokens;
        private double providerResponseLatency;
        private String fromSource;
        private String fromEndUserId;
        private String fromAccountId;
        private List<Object> feedbacks;
        private String workflowRunId;
        private String annotation;
        private String annotationHitHistory;
        private long createdAt;
        private List<Object> agentThoughts;
        private List<Object> messageFiles;
        private Map<String, Object> metadata;
        private String status;
        private String error;
        private String parentMessageId;
        private boolean activeStatus;
        private List<Object> retryAnswers;

        // Getters and Setters
        @lombok.Data
        public static class Inputs {
            private Object pptFile;
            private String id;

            // Getters and Setters
        }
    }
}

