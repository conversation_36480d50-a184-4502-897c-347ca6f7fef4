package com.nd.aic.client.pojo.aihub;

import java.util.List;
import java.util.Map;

import lombok.Data;

// https://ai-hub-server.sdpsg.101.com/console/api/apps/9bf36b45-8fd6-4756-97f5-810a2cd4fc21/workflow-runs/5d3ae059-f8e8-4b6c-9962-8f2c8d5ae35b/node-executions
@Data
public class AIHubExecutions {
    private List<ExecutionData> data;

    // Getters and Setters

    // Nested Class for ExecutionData
    @Data
    public static class ExecutionData {
        private String id;
        private int index;
        private String predecessorNodeId;
        private String nodeId;
        private String nodeType;
        private String title;
        private Map<String, Object> inputs;
        private Object processData;
        private Map<String, Object> outputs;
        private String status;
        private String error;
        private double elapsedTime;
        private Object executionMetadata;
        private Map<String, Object> extras;
        private long createdAt;
        private String createdByRole;
        private CreatedByAccount createdByAccount;
        private Object createdByEndUser;
        private long finishedAt;

        // Getters and Setters
        // Nested Class for CreatedByAccount
        @Data
        public static class CreatedByAccount {
            private String id;
            private String name;
            private String email;

            // Getters and Setters
        }
    }
}

