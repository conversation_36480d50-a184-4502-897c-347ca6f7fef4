package com.nd.aic.client.pojo.aihub;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class AIHubTaskReq {
    private String query;
    private Map<String, Object> inputs;
    private String responseMode = "streaming";
    private String userId;
    private String conversationId = "";


    // - type (string) 支持类型：图片 image，文档document 。
    // - transfer_method (string) 传递方式:
    // - remote_url: 图片地址。
    // - local_file: 上传文件。
    // - url 图片地址。（仅当传递方式为 remote_url 时）。
    // - upload_file_id 上传文件 ID。（仅当传递方式为 local_file 时）。
    private List<Map<String, Object>> files = Lists.newArrayList();
    private boolean autoGenerateName = true;

}
