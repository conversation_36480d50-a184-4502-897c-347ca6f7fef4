package com.nd.aic.client.pojo.aihub;

import com.nd.aic.base.exception.WafI18NException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class AIHubTaskResult {
    public static final String STATUS_INIT = "init";
    public static final String STATUS_RUNNING = "running";
    public static final String STATUS_SUCCEEDED = "succeeded";
    public static final String STATUS_FAILED = "failed";
    public static final String STATUS_STOPPED = "stopped";

    private String id;
    private String appId;
    private String bizType;
    private String bizId;
    private RequestArgs requestArgs;
    /**
     * init / running / succeeded / failed / stopped
     */
    private String status;
    private Answer answer;
    private long createdAt;

    @Data
    public static class RequestArgs {
        private Map<String, Object> inputs;
        private String query;
        private Object files;
        private String responseMode;
        private String conversationId;
        private String retrieverFrom;
        private boolean autoGenerateName;
        private String retryMessageId;
        private String callbackUrl;
    }

    @Data
    public static class Answer {
        private String event;
        private String conversationId;
        private String messageId;
        private long createdAt;
        private String taskId;
        private String id;
        private Metadata metadata;
        private String answer;
        private String errorMessage;
        private AnswerData data;

        @Data
        public static class Metadata {
            private Usage usage;

            @Data
            public static class Usage {
                private int promptTokens;
                private String promptUnitPrice;
                private String promptPriceUnit;
                private String promptPrice;
                private int completionTokens;
                private String completionUnitPrice;
                private String completionPriceUnit;
                private String completionPrice;
                private int totalTokens;
                private String totalPrice;
                private String currency;
                private double latency;

            }
        }
    }

    @Data
    public static class AnswerData {
        private String id;
        private String workflowId;
        private String status;
        private Map<String, Object> outputs;
        private double elapsedTime;
        private int totalTokens;
        private int totalSteps;
        private long createdAt;
        private long finishedAt;
        private List<String> files;

    }

    public static Answer getAnswerOrRaise(AIHubTaskResult aiHubTaskResult) {
        String status = aiHubTaskResult.getStatus();
        if (null != aiHubTaskResult.getAnswer().getData()) {
            status = StringUtils.defaultString(aiHubTaskResult.getAnswer().getData().getStatus(), status);
        }
        if (StringUtils.equals(status, "failed") || StringUtils.equals(status, "stopped")) {
            throw WafI18NException.of("AIHUB_TASK_FAILED", "AE智能体执行失败", HttpStatus.BAD_REQUEST);
        }
        return aiHubTaskResult.getAnswer();
    }
}

