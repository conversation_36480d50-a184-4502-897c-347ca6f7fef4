package com.nd.aic.client.pojo.aihub;

import java.util.List;

import lombok.Data;

/**
 * AI搜索响应DTO
 */
@Data
public class AISearchResponseDTO {

    /**
     * 查询信息
     */
    private QueryInfo query;

    /**
     * 搜索记录列表
     */
    private List<SearchRecord> records;

    /**
     * 查询信息
     */
    @Data
    public static class QueryInfo {
        /**
         * 查询内容
         */
        private String content;
    }

    /**
     * 搜索记录
     */
    @Data
    public static class SearchRecord {
        /**
         * 文档片段
         */
        private Segment segment;

        /**
         * 相似度分数
         */
        private Double score;
    }

    /**
     * 文档片段
     */
    @Data
    public static class Segment {
        /**
         * 片段ID
         */
        private String id;

        /**
         * 位置
         */
        private Integer position;

        /**
         * 文档ID
         */
        private String documentId;

        /**
         * 内容
         */
        private String content;

        /**
         * 答案
         */
        private String answer;

        /**
         * 字数
         */
        private Integer wordCount;

        /**
         * 令牌数
         */
        private Integer tokens;

        /**
         * 关键词列表
         */
        private List<String> keywords;

        /**
         * 命中次数
         */
        private Integer hitCount;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 状态
         */
        private String status;

        /**
         * 创建者
         */
        private String createdBy;

        /**
         * 创建时间
         */
        private Long createdAt;

        /**
         * 索引时间
         */
        private Long indexingAt;

        /**
         * 完成时间
         */
        private Long completedAt;

        /**
         * 错误信息
         */
        private String error;
    }
}
