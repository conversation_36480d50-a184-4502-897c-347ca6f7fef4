package com.nd.aic.client.pojo.aippt;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class BaseForm {

    private Long id;
    //@Column(columnDefinition = "bigint(20) COMMENT'任务ID'")
    private Long taskId;

    //@Column(columnDefinition = "bigint(20) COMMENT'执行人'")
    private Long executeUid;
    //@Column(columnDefinition = "bigint(20) COMMENT'审核人'")
    private Long approveUid;

    /**
     * 该环节当前状态 -2：暂缓，-1：未准备 0：未认领，1：任务制作（已认领）2：已执行（待审核） 3.待审核认领 4：已审核
     */
    //@Column(columnDefinition = "int COMMENT'当前状态'")
    private Integer status;

    //@Column(columnDefinition = "varchar(50) COMMENT'子状态'")
    private String subStatus;

    /**
     * 0.2版本新增 认领时间。最终完成时间。环节名。
     */
    //@Column(columnDefinition = "datetime COMMENT'生产认领时间'")
    private Date executeTime;
    //@Column(columnDefinition = "datetime COMMENT'审核认领时间'")
    private Date approveTime;
    //@Column(columnDefinition = "datetime COMMENT'预计完成时间'")
    private Date preFinalTime;
    //@Column(columnDefinition = "datetime COMMENT'最终完成时间'")
    private Date finalTime;
    //@Column(columnDefinition = "datetime COMMENT'生产截止时间'")
    private Date deadLine;
    //@Column(columnDefinition = "datetime COMMENT'审核截止时间'")
    private Date approveDeadLine;
    //@Column(columnDefinition = "int COMMENT'审批次数'")
    private Integer approveNum;
    //@Column(columnDefinition = "varchar(255) COMMENT'环节名'")
    private String formName;
    //@Column(columnDefinition = "int not null COMMENT'表单类型'")
    private Integer formCode;

    //@Column(columnDefinition = "LONGTEXT COMMENT'扩展信息'")
    private String extInfo;

    //@Column(columnDefinition = "datetime COMMENT'审核创建时间'")
    private Date approveCreateTime;

    //@Column(columnDefinition = "varchar(128) COMMENT'生产线编码'")
    private String productionLineCode;

    //@Column(columnDefinition = "varchar(64) COMMENT'当前任务uuid'")
    private String openTool;

    public void setOpenTool(String value){
        openTool=value;
    }

    //@Column(columnDefinition = "int COMMENT'是否跳过'")
    private Integer isSkip;

    //@Column(columnDefinition = "int COMMENT'跨环节驳回次数'")
    private Integer crossRejectNum;

    /**
     * 0/null-合格， 1-不合格
     */
    //@Column(columnDefinition = "int COMMENT'是否不合格'")
    private Integer unqualified;

    //@Column(columnDefinition = "int COMMENT'是否审核通过'")
    private Integer approvePass;

    //@Column(columnDefinition = "varchar(50) COMMENT'版本号'")
    private String version;

    //@Column(columnDefinition = "datetime COMMENT '冻结时间'")
    private Date freezeTime;

    //@Column(columnDefinition = "TINYINT COMMENT '是否解冻'")
    private Boolean isFreeze;

    //@Column(columnDefinition = "bigint(20) COMMENT'事务ID'")
    private Long affairId;

    ////@Transient
    private String toolCode;

    //@Column(columnDefinition = "varchar(400) COMMENT '多重审核人数据'")
    private Object multApproveData;


    //@Column(columnDefinition = "int COMMENT '当前是第几个审核人审核'")
    private Integer approveRound;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '生产预计报酬'")
    private Float productMoney;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '审核预计报酬'")
    private Float approveMoney;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '生产预计工时'")
    private Float productWorkHour;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '审核预计工时'")
    private Float approveWorkHour;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '生产预计最大报酬'")
    private Float productMaxMoney;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '审核预计最大报酬'")
    private Float approveMaxMoney;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '生产预计最大工时'")
    private Float productMaxWorkHour;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '审核预计最大工时'")
    private Float approveMaxWorkHour;

    //@Column(columnDefinition = "datetime COMMENT '暂缓时间'")
    private Date pauseTime;

    //@Column(columnDefinition = "varchar(500) COMMENT '暂缓原因'")
    private String pauseReason;

    //@Column(columnDefinition = "bigint(20) COMMENT'暂缓人'")
    private Long pauseUid;

    //@Column(columnDefinition = "int COMMENT'返工次数'")
    private Integer reworkNum;

    //@Column(columnDefinition = "TINYINT COMMENT '是否挂起'")
    private Boolean isPending;

    //@Column(columnDefinition = "TINYINT COMMENT '是否存在事务单'")
    private Boolean existAffair;

    //@Transient
    private Boolean affairPoolGrade;

    //@Transient
    private JSONObject dataBus;


}
