package com.nd.aic.client.pojo.aippt;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor

/**
 *主体信息表
 * */ public class Task {

    private Long id;
    /**
     * 该环节输入信息
     */
    @NotEmpty(message = "课程编码不能为空")
    // @Column(columnDefinition = "varchar(255) COMMENT'课程编码'")
    private String courseCode;

    // @Column(columnDefinition = "varchar(255) COMMENT'课时编码'")
    private String courseTimeCode;

    // @Column(columnDefinition = "varchar(1000) COMMENT'教材名称'")
    private String textbook;

    // @Column(columnDefinition = "varchar(1000) COMMENT'章节'")
    private String chapter;

    // @Column(columnDefinition = "varchar(500) COMMENT'章节Id'")
    private String chapterId;

    // @Column(columnDefinition = "varchar(1000) COMMENT'课程名称'")
    private String courseName;

    // @Column(columnDefinition = "varchar(1000) COMMENT'课时名称'")
    private String courseTimeName;

    // @Column(columnDefinition = "varchar(36) COMMENT '关联外键'", updatable = false)
    private String relationKey;
    /**
     * 该任务所进行到的环节 1-7
     */
    // @Column(columnDefinition = "int COMMENT'当前进行环节'")
    private int status;

    //    @NotEmpty
    //     @Column(columnDefinition = "varchar(255) COMMENT'项目名'")
    private String projectName;

    //    @NotNull
    //     @Column(columnDefinition = "bigint COMMENT'项目id'")
    private Long projectId;

    // @Column(columnDefinition = "int COMMENT'返工标志'")
    private Integer rework;

    // @Column(columnDefinition = "LONGTEXT COMMENT'数据总线'")
    private String dataBus;

    // @Column(columnDefinition = "TEXT COMMENT'数据总线状态'")
    private String busStatus;
    // @Column(columnDefinition = "varchar(255) COMMENT'跳过的节点code'")
    private String skipFormCode;
    // @Column(columnDefinition = "TEXT COMMENT'确定输入输出属性(cs/ndr/text)'")
    private String propertyTypeJson;
    // @Column(columnDefinition = "varchar(128) COMMENT'生产线编码'")
    private String productionLineCode;

    @Length(max = 50)
    // @Column(columnDefinition = "varchar(50) COMMENT '外部数据类型'")
    private String refType;

    @Length(max = 1000)
    // @Column(columnDefinition = "varchar(1000) COMMENT '外嵌地址'")
    private String refUrl;

    @JsonIgnore
    @Length(max = 50000)
    // @Column(columnDefinition = "text COMMENT '扩展信息'")
    private String extendInfo;

    /**
     * 1-事务池下单人为affairSourceId
     */
    // @Column(columnDefinition = "int COMMENT '事务池下单人员类型'")
    private Integer affairSourceType;

    // @Column(columnDefinition = "bigint COMMENT '事务池下单人员ID'")
    private Long affairSourceId;

    // //@Transient
    @JsonProperty(value = "extend_info")
    private Map<String, Object> extendInfoObject;

    /**
     * 已实装到数据库
     */
    // @Column(columnDefinition = "longtext COMMENT '资源审核状态'")
    private String resourceStatusList;

    // @Column(columnDefinition = "varchar(200) COMMENT '任务优先级'")
    private String priority;

    //@Column(columnDefinition = "text COMMENT '额外字段'")
    private String taskInfo;

    //@Column(columnDefinition = "text COMMENT '其他资源'")
    private String otherData;

    //@Column(columnDefinition = "bigint COMMENT '来源taskId'")
    private Long sourceTask;

    //@Column(columnDefinition = "bigint COMMENT '配置组id'")
    private Long configGroupId;

    //@Column(columnDefinition = "varchar(1000) COMMENT '指派人员列表'")
    private String assignUid;

    //@Column(columnDefinition = "varchar(32) COMMENT '创建人id'")
    private String createUid;

    /**
     * 工具版本，对象结构：工具code：版本号
     */
    //@Column(columnDefinition = "varchar(200) COMMENT '工具版本信息'")
    private String toolVersion;

    /**
     * 关联的taskId，下游关联上游，子级关联父级
     */
    //@Column(columnDefinition = "bigint COMMENT '关联taskId'")
    private Long relationTaskId;

    //@Column(columnDefinition = "decimal(10,2) COMMENT '排期优先级'")
    private BigDecimal schedulePriority;

    /**
     * 父子任务生产线新增字段
     */
    //@Column(columnDefinition = "bigint COMMENT '父任务taskId'")
    private Long parentTaskId;

    /**
     * 父子任务生产线新增字段 子任务数
     */
    //@Column(columnDefinition = "int COMMENT '子任务数'")
    private Integer subTaskNum;

    //@Column(columnDefinition = "varchar(300) COMMENT '学习目标'")
    private String learnObjective;

    //@Column(columnDefinition = "varchar(500) COMMENT '教学步骤类型名称'")
    private String teachMethodName;

    //@Column(columnDefinition = "varchar(2048) COMMENT '教学步骤类型定义'")
    private String teachMethodDefinition;

    //@Column(columnDefinition = "varchar(300) COMMENT '教学步骤套路名称'")
    private String teachTrickName;

    //@Column(columnDefinition = "varchar(255) COMMENT '7.0课时编码'")
    private String classHourCode;

    //@Column(columnDefinition = "int COMMENT '项目官网id'")
    private Integer officialProjectId;

    //@Column(columnDefinition = "datetime COMMENT '需求提交时间'")
    private Date proposedTime;

    //@Column(columnDefinition = "int COMMENT '关联表单编码'")
    private Integer relationFormCode;

    //@Column(columnDefinition = "int COMMENT '成本挂靠项目官网id'")
    private Integer costOfficialProjectId;

    //@Column(columnDefinition = "tinyint(1) COMMENT'是否完成'")
    private Boolean isFinished;

    //@Column(columnDefinition = "varchar(255) COMMENT '成本挂靠项目名'")
    private String costProjectName;

    /**
     * 课程负责人， 查询详情时返回
     */
    //@Transient
    private Long principalId;

    /**
     * 结束时需要发布的生产线
     */
    //@Transient
    private String childLineCode;

    //@Transient
    private List<Long> childTaskIds;

    public void clear() {
        setRework(0);
    }

    @JsonAnyGetter
    public Map<String, Object> getter() {
        Map<String, Object> map = new HashMap<>(2);
        if (null == extendInfoObject && StringUtils.isNotEmpty(extendInfo)) {
            JSONObject jsonObject = JSON.parseObject(extendInfo);
            if (null != jsonObject) {
                extendInfoObject = Maps.newHashMap();
                extendInfoObject.putAll(jsonObject);
            }
        }
        return map;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        if (key.startsWith("extend_info_")) {
            if (null == extendInfoObject) {
                extendInfoObject = Maps.newHashMap();
            }
            extendInfoObject.put(key.substring("extend_info_".length()), value);
        }
    }


}
