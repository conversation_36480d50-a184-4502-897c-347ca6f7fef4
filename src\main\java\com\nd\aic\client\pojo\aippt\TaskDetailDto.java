package com.nd.aic.client.pojo.aippt;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 193658
 * date: 2023/6/7 10:21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDetailDto {

    private Task task;

    // private List<DataTemplate> dataTemplate;

    private List<BaseForm> formList;

    // private List<PropertyDto> properties;

    // private List<FormTemplate> formTemplates;

    private Long relationTaskId;

    private Integer relationFormCode;
}
