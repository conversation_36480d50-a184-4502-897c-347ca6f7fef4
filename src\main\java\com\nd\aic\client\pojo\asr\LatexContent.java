package com.nd.aic.client.pojo.asr;

import com.nd.aic.util.LatexUtils;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
public class LatexContent {
    private String input;
    private String output;
    private List<LatexMapping> latexMappings;
    // private Map<String, String> latexMap;

    public Map<String, String> getLatexMap() {
        return LatexUtils.convertLatexMap(latexMappings);
    }
}
