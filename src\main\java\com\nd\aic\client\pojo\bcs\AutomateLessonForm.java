package com.nd.aic.client.pojo.bcs;

import com.google.common.collect.Lists;

import java.util.List;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class AutomateLessonForm {
    private String recordingProdOperationsId;
    private String recordingProdBatchId;
    private List<Activity> activities = Lists.newArrayList();

    @Data
    public static class Activity {
        // 课时ID
        private String resourceId;
        // 课时名称
        private String resourceName;
        // 需求提交人工号
        private List<Long> submitter;
        // // 需求提交人姓名
        // private List<Long> submitterName;
        // 教学活动ID（指向录课颗粒ID）
        private String operationsId;
        // // 录课颗粒地址
        // private String operationsUrl;
    }
}
