package com.nd.aic.client.pojo.bcs;

import java.util.Date;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class LackResourceReq {
    // 生产线类型
    private String type = "AI课件生产线";
    // 生产颗粒名称
    private String name;
    // 生产颗粒ID
    private String id;
    // 生产线链接
    private String url = "https://ai-hub.sdp.101.com/explore/installed/9bf36b45-8fd6-4756-97f5-810a2cd4fc21";
    // 生产成品链接
    private String productUrl = "https://aic-editor.sdp.101.com/#/courseware-config/task";
    // 触发问题节点
    private String node;
    // 表演设计详情
    private String details;
    // 缺失资源类型（场景、角色、动作、镜头、bgm、音效、文字、载具、灯光、转场）
    private String sourceType;
    // 缺失资源标签
    private String tags;
    // 触发BUG日期
    private Date date = new Date();
}
