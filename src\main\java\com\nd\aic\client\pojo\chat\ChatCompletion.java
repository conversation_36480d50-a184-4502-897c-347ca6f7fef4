package com.nd.aic.client.pojo.chat;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.codehaus.jackson.annotate.JsonProperty;

@ToString
@Getter
@Setter
public class ChatCompletion {
    @JsonProperty("chatId")
    private String chatId = UUID.randomUUID().toString();
    private double temperature;
    private boolean stream;
    private String model;
    private List<ChatMessage> messages;
    private Map<String, Object> variables;
}
