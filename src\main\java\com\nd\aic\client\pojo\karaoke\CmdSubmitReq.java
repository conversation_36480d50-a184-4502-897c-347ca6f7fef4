package com.nd.aic.client.pojo.karaoke;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nd.gaea.util.WafJsonMapper;

import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.ToString;

@ToString
@Getter
@Setter
public class CmdSubmitReq {
    private String command = "cmd_text_effects_generate";
    private Params paramsObj = new Params();

    @SneakyThrows
    @JsonAnyGetter
    public Map<String, Object> getParams() {
        String params = WafJsonMapper.toJson(paramsObj);
        Map<String, Object> ret = Maps.newHashMap();
        ret.put("params", params);
        return ret;
    }

    @Getter
    @Setter
    public static class Params {
        private String templateId;
        private Integer inputIndex = 0;
        private Integer outputIndex = 0;
        private Map<String, Object> params = Maps.newHashMap();
    }
}
