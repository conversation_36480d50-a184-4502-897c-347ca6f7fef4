package com.nd.aic.client.pojo.tts;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.UUID;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BdTtsRequest {

    public static final String APP_ID = "2032697502";
    public static final String CLUSTER = "volcano_tts";

    private App app = new App();
    private User user = new User();
    private Audio audio = new Audio();
    private Request request = new Request();

    public BdTtsRequest(String text) {
        this.request.setText(text);
    }

    @Getter
    @Setter
    public static class App {
        private String appid = APP_ID;
        private String token = "access_token"; // 目前未生效，填写默认值：access_token
        private String cluster = CLUSTER;
    }

    @Getter
    @Setter
    public static class User {
        private String uid = "388808087185088"; // 目前未生效，填写一个默认值就可以
    }

    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Audio {
        // 音色类型
        private String voiceType = "BV056_streaming";
        private String encoding = "mp3";
        private int rate = 24000;
        private int compressionRate = 1;
        // 语速 [0.2,3]，默认为1，通常保留一位小数即可
        private float speedRatio = 0.85f;
        // 音量 [0.1, 3]，默认为1，通常保留一位小数即可
        private float volumeRatio = 2.1f;
        private float loudnessRatio = 2;
        // 音高 [0.1, 3]，默认为1，通常保留一位小数即可
        private float pitchRatio = 1;
        // 开启音色情感
        private Boolean enableEmotion = false;
        // 情感/风格
        private String emotion;
        private Float emotionScale;
    }

    @Getter
    @Setter
    public static class Request {
        private String reqid = UUID.randomUUID().toString();
        private String text;
        private String textType = "plain";
        private String operation = "query";
        private Integer withFrontend = 1;
        private String frontendType = "unitTson";
        private Boolean disableMarkdownFilter = true;
        private Boolean enableLatexTn = true;
    }
}
