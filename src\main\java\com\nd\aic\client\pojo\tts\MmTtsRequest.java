package com.nd.aic.client.pojo.tts;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MmTtsRequest {

    private String model = "speech-01-turbo";
    private String text;
    private Boolean stream = false;

    private VoiceSetting voiceSetting = new VoiceSetting();
    private AudioSetting audioSetting = new AudioSetting();

    private Map<String, List<String>> pronunciationDict;

    @Getter
    @Setter
    public static class VoiceSetting {
        private String voiceId = "male-qn-jingying";
        private Float speed = 0.9f;
        private Float vol = 1f;
        private Integer pitch = 0;
    }

    @Getter
    @Setter
    public static class AudioSetting {
        private Integer sampleRate = 24000;
        private Integer bitrate = 128000;
        private Integer channel = 1;
        private String format = "mp3";
    }
}
