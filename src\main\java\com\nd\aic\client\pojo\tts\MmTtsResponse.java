package com.nd.aic.client.pojo.tts;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MmTtsResponse {

    private Data data;
    private ExtraInfo extraInfo;
    private String traceId;
    private BaseResp baseResp;

    @Getter
    @Setter
    public static class Data {
        private String audio;
        private Integer status;
    }

    @Getter
    @Setter
    public static class ExtraInfo {
        // 音频时长，精确到毫秒。
        private Integer audioLength;
        // 采样率。默认为24000，如客户请求参数进行调整，会根据请求参数生成。
        private Integer audioSampleRate;
        // 音频大小。单位为字节。
        private Integer audioSize;
        // 比特率。默认为168000，如客户请求参数进行调整，会根据请求参数生成。
        private Integer audioBitrate;
        // 生成音频声道数。1：单声道，2：双声道。
        private Integer audioChannel;
        private Integer wordCount;
        // 非法字符占比。非法字符不超过10%（包含10%），音频会正常生成并返回非法字符占比；最大不超过0.1（10%），超过进行报错。
        private Integer invisibleCharacterRatio;
        // 生成音频文件的格式。取值范围mp3/pcm/flac。
        private String format;
        // 计费字符数。本次语音生成的计费字符数。
        private Integer usageCharacters;
    }

    @Getter
    @Setter
    public static class BaseResp {
        // 状态码。1000，未知错误；1001，超时；1002，触发限流；1004，鉴权失败；1039，触发TPM限流；1042，非法字符超过10%；2013，输入格式信息不正常。
        private Integer statusCode;
        // 状态详情。
        private String statusMsg;
    }
}
