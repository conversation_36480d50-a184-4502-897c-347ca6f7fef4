package com.nd.aic.client.pojo.tts;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class TtsSegment {
    private String title;
    private String voiceId;
    private Float voiceSpeed;
    private String text;
    private String emotion;
    /**
     * 发音标注
     */
    private Map<String, List<String>> pronunciationDict;
    /**
     * TTS分段发音定制
     * */
    private List<ChildTtsSegment> ttsSegments;
}
