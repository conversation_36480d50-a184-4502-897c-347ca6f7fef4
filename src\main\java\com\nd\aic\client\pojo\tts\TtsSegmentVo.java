package com.nd.aic.client.pojo.tts;

import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class TtsSegmentVo {
    // 指定音频文件名称？
    private String title;
    @NotBlank(message = "发音人ID不能为空")
    private String voiceId;
    // 语速（0.5-2）
    private Float voiceSpeed;
    @NotBlank(message = "说话内容不能为空")
    private String text;
    private String emotion;
    /**
     * 发音批注
     */
    private Map<String, List<String>> pronunciationDict;

    /**
     * TTS分段发音定制
     * */
    private List<ChildTtsSegment> ttsSegments;

    public void setVoiceType(String voiceType) {
        this.voiceId = voiceType;
    }
}
