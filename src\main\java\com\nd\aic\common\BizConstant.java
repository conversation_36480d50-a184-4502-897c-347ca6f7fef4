package com.nd.aic.common;

public final class BizConstant {

    public static final String DEFAULT_COURSEWARE_MODEL_TYPE = "courseware_mv";

    public static final String DEFAULT_COURSEWARE_MODEL_VERSION = "v1.0";

    public static final String DEFAULT_COURSEWARE_MODE_BIZ_TYPE = "ai_courseware";

    public static final String BIZ_FLAG_VLAB = "vlab";
    public static final String BIZ_FLAG_AIG = "aig";

    public static final String BGM_TYPE_RECOMMEND = "recommend";
    public static final String BGM_TYPE_NONE = "none";
    public static final String BGM_TYPE_UPLOAD = "upload";

    public static final int TIME_STRETCH = 2000;

    public static final int V_ANIM_L2R = 1;
    public static final int V_ANIM_R2L = 1 << 1;
    public static final int V_ANIM_T2B = 1 << 2;
    public static final int V_ANIM_B2T = 1 << 3;

    public static final String SOURCE_BCS_WORKFLOW = "bcs_workflow";

    public static final String SOURCE_FLOW_PREFIX = "flow:";
    public static final String SOURCE_AIPPT_PREFIX = "aippt:";
    public static final String AIPPT_BIZ_TYPE_CRAFT = "ai_course_craft";

    /**
     * 角色处理方式常量
     */
    public static final String CHARACTER_HANDLE_MODE_HIDE = "hide";
    public static final String CHARACTER_HANDLE_MODE_DESTROY = "destroy";
    public static final String CHARACTER_HANDLE_MODE_REMAIN = "remain";

}
