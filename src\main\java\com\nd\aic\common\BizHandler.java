package com.nd.aic.common;

import org.springframework.core.NamedThreadLocal;

public class BizHandler {

    private static final ThreadLocal<String> bizFlag = new NamedThreadLocal<>("bizFlag");

    public static String getBizFlag() {
        return bizFlag.get();
    }

    public static void setBizFlag(String user) {
        bizFlag.set(user);
    }

    public static void removeBizFlag() {
        bizFlag.remove();
    }
}
