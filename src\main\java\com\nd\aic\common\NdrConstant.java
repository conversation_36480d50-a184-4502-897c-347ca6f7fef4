package com.nd.aic.common;

public final class NdrConstant {

    public static final String KARAOKE_TENANT_ID = "1314";

    public static final String KARAOKE_COURSEWARE_CONTAINER_ID = "karaoke_courseware";

    public static final String AIGC_TENANT_ID = "1";

    public static final String AIGC_COURSEWARE_CONTAINER_ID = "1a993ab1-ca09-4e90-84b3-40f00455341c";


    public static final String AIMV_TENANT_ID = "1321";

    public static final String AIMV_COURSEWARE_CONTAINER_ID = "bebab070-5478-4870-a0ac-760c36a3f3a2";

    public static final String AIMV_MATERIAL_CONTAINER_ID = "e478e8d5-4471-4357-9b59-31dd9e52bcee";
    // AI课件项目库（主库）
    public static final String AI_COURSEWARE_MAIN_CONTAINER_ID = "4f7a4a2c-9dac-411f-9a15-1851f8326247";

    // ---NDR资源标签---

    /**
     * AI课件标签
     */
    public static final String KARAOKE_COURSEWARE_TAG = "$COURSEWARE";
    /**
     * 不限版本
     */
    public static final String VERSION_UNLIMITED_TAG = "2036397025";


    // ---NDR资源属性---

    /**
     * 歌曲时长
     */
    public static String SONG_DURATION_PROP = "song_duration";


    // ---NDR资源类型---

    public static String ASSETS_RESOURCE_TYPE = "assets";
    public static String KARAOKE_RESOURCE_TYPE = "karaoke";
}
