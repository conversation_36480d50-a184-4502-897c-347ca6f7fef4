package com.nd.aic.common;

import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.NamedThreadLocal;

public class UserHandler {

    private static final ThreadLocal<String> userThreadLocal = new NamedThreadLocal<>("user");

    public static String getUser(String defaultUser) {
        String userId = userThreadLocal.get();

        if (StringUtils.isEmpty(userId)) {
            userId = WafContext.getCurrentAccountId();
            if (StringUtils.isEmpty(userId)) {
                userId = defaultUser;
            }
            userThreadLocal.set(userId);
        }
        return userId;
    }

    public static void setUser(String user) {
        userThreadLocal.set(user);
    }

    public static void clear() {
        userThreadLocal.remove();
    }
}
