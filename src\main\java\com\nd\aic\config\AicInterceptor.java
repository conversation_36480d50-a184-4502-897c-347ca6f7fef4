package com.nd.aic.config;

import com.nd.gaea.rest.security.authens.WafAbstractAuthenticationToken;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AicInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        String authType = getAuthType();
        if (StringUtils.equalsIgnoreCase(AicTokenAuthenticationProvider.AUTH_TYPE, authType)) {
            AicAuth aicAuth = findAicAuthAnnotation((HandlerMethod) handler);
            if (aicAuth == null) {
                throw new AccessDeniedException("Access Denied");
            }
        }
        return true;
    }

    private String getAuthType() {
        WafAbstractAuthenticationToken wafAbstractAuthenticationToken = WafContext.getWafAuthenticationToken();
        if (wafAbstractAuthenticationToken == null) {
            return null;
        }
        return wafAbstractAuthenticationToken.getAuthType();
    }

    private AicAuth findAicAuthAnnotation(HandlerMethod handlerMethod) {
        AicAuth aicAuth = AnnotationUtils.findAnnotation(handlerMethod.getMethod(), AicAuth.class);
        if (aicAuth == null) {
            aicAuth = AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), AicAuth.class);
        }
        return aicAuth;
    }
}
