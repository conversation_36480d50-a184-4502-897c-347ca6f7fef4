package com.nd.aic.config;

import com.nd.gaea.rest.security.WafAuthenticationProvider;
import com.nd.gaea.rest.security.authens.Token;
import com.nd.gaea.rest.security.authens.UserCenterUserDetails;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Order
@Component
public class AicTokenAuthenticationProvider implements WafAuthenticationProvider {

    public static final String AUTH_TYPE = "aic";

    @Override
    public UserCenterUserDetails authenticate(String authenticationValue, HttpServletRequest request) {
        String[] values = StringUtils.split(authenticationValue, " ");
        if (values.length > 0) {
            String authType = StringUtils.lowerCase(StringUtils.trim(values[0]));
            String authValue = values.length > 1 ? StringUtils.trim(values[1]) : StringUtils.EMPTY;
            if (!StringUtils.equals(AUTH_TYPE, authType)) {
                return null;
            }
            return new UserCenterUserDetails(authValue, null, new AicToken(AUTH_TYPE));
        }
        return null;
    }

    static class AicToken implements Token {

        private final String authType;

        public AicToken(String authType) {
            this.authType = authType;
        }

        @Override
        public String getAuthType() {
            return authType;
        }
    }
}
