package com.nd.aic.config;


import com.nd.gaea.client.feign.CompositeWafFeignRequestInterceptor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import feign.RequestInterceptor;

public class BcsFeignConfig {
    @Bean
    public RequestInterceptor bcsRequestInterceptor(CompositeWafFeignRequestInterceptor compositeWafFeignRequestInterceptor) {
        return new BcsRequestInterceptor(compositeWafFeignRequestInterceptor);
    }
}
