package com.nd.aic.config;

import com.nd.gaea.client.feign.CompositeWafFeignRequestInterceptor;
import com.nd.gaea.client.support.CommonHeader;
import com.nd.gaea.client.support.WafContext;

import feign.RequestInterceptor;
import feign.RequestTemplate;

public class BcsRequestInterceptor implements RequestInterceptor {

    private final CompositeWafFeignRequestInterceptor compositeWafFeignRequestInterceptor;

    public BcsRequestInterceptor(CompositeWafFeignRequestInterceptor compositeWafFeignRequestInterceptor) {
        this.compositeWafFeignRequestInterceptor = compositeWafFeignRequestInterceptor;
    }

    @Override
    public void apply(RequestTemplate template) {
        CommonHeader commonHeader = WafContext.getCommonHeader();
        try {
            WafContext.setCommonHeader(new CommonHeader());
            compositeWafFeignRequestInterceptor.apply(template);
        } finally {
            WafContext.setCommonHeader(commonHeader);
        }
    }
}
