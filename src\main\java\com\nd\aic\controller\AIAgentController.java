package com.nd.aic.controller;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nd.aic.entity.basic.Pronunciation;
import com.nd.aic.pojo.ai.FastGptRequest;
import com.nd.aic.pojo.ai.InteractionQuestionReq;
import com.nd.aic.pojo.ai.StylizationScript;
import com.nd.aic.pojo.ai.TTSPauseAnnoReq;
import com.nd.aic.pojo.ai.WordsCheck;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.basic.PronunciationService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/ai/agent")
public class AIAgentController {

    private final FastGptRetryService fastGptRetryService;
    private final PronunciationService pronunciationService;

    /**
     * 发音标注
     * @param content 发音的内容
     * @return 发音标注结果
     */
    @PostMapping(value = "/actions/pronunciation_annotation")
    public List<Pronunciation> pronunciation(@RequestBody String content) {
        List<Pronunciation> pronunciations = Lists.newArrayList();
        List<Pronunciation> gptPronunciations = fastGptRetryService.askForTTSPronunciation("", content);

        String text = content;
        // GPT发音标注
        if (CollectionUtils.isNotEmpty(gptPronunciations)) {
            for (Pronunciation gptPronunciation : gptPronunciations) {
                text = StringUtils.replace(text, gptPronunciation.getText(), "");
            }
            pronunciations.addAll(gptPronunciations);
        }
        // 用户发音标注
        List<Pronunciation> userPronunciations = pronunciationService.list();
        if (CollectionUtils.isNotEmpty(userPronunciations)) {
            for (Pronunciation userPronunciation : userPronunciations) {
                if (text.contains(userPronunciation.getText())) {
                    text = StringUtils.replace(text, userPronunciation.getText(), "");
                    pronunciations.add(userPronunciation);
                }
            }
        }
        return pronunciations;
    }

    /**
     * 暂停标注
     * @param req 请求体
     * @return 暂停标注结果
     */
    @PostMapping(value = "/actions/pause_annotation")
    public JSONArray pause(@Valid @RequestBody TTSPauseAnnoReq req) {
        return fastGptRetryService.askForPauseAnnotation(req.getStyle(), req.getContent());
    }

    /**
     * Check words
     * @param wordsCheck input sources
     * @return check results
     */
    @PostMapping(value = "/actions/word_check")
    public JSONObject checkWords(@Valid @RequestBody WordsCheck wordsCheck){
        return fastGptRetryService.askForWordsCheck(wordsCheck.getText());
    }


    /**
     * story rewrite
     * @param text input text
     * @return story rewrite
     */
    @PostMapping(value = "/actions/story_rewrite")
    public JSONObject askForStoryRewrite(@Valid @RequestBody String text){
        return fastGptRetryService.askForStoryRewrite(text);
    }


    /**
     * ask for keyword
     * @param text input text
     * @return language type
     */
    @PostMapping(value = "/actions/ask_for_language_type")
    public String askForLanguageType(@Valid @RequestBody String text){
        return fastGptRetryService.askForLanguageType(text);
    }

    /**
     * ask for interaction question
     * @param interactionQuestionReq interaction question request
     * @return interaction question
     */
    @PostMapping(value = "/actions/ask_for_interaction_question")
    public Object askForInteractionQuestion(@Valid @RequestBody InteractionQuestionReq interactionQuestionReq){
        return fastGptRetryService.askForInteractionQuestion(interactionQuestionReq);
    }

    /**
     * 获取可用资源配置
     * @return 可用资源配置
     */
    @PostMapping(value = "/actions/courseware_resources")
    public Object coursewareResource() {
        return fastGptRetryService.askForResourceConfig();
    }


    /**
     * 获取可用资源配置
     * @return 可用资源配置
     */
    @PostMapping(value = "/actions/query_configs")
    public Object getConfigData(@RequestParam("gpt_key") String gptKey, @RequestBody FastGptRequest fastGptRequest) {
        return fastGptRetryService.queryConfigData(fastGptRequest.getText(), gptKey, fastGptRequest.getVariables());
    }

    @PostMapping(value = "/actions/stylize")
    public Object stylize(@RequestBody @Valid StylizationScript stylizationScript) {
        return fastGptRetryService.stylize(stylizationScript);
    }
}
