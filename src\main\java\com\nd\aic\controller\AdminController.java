package com.nd.aic.controller;

import com.nd.aic.entity.SgTask;
import com.nd.aic.entity.SpeechTimingResult;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/admin")
public class AdminController {
    private final MongoTemplate mongoTemplate;

    @DeleteMapping("/speech_timing_result")
    public void deleteAllSpeechTimingResult() {
        mongoTemplate.dropCollection(SpeechTimingResult.class);
    }

    @DeleteMapping("/sg_task")
    public void deleteSgTask() {
        mongoTemplate.dropCollection(SgTask.class);
    }
}
