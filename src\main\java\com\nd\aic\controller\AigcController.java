package com.nd.aic.controller;


import com.nd.aic.client.pojo.aigc.ResResp;
import com.nd.aic.client.pojo.aigc.ResTask;
import com.nd.aic.pojo.ai.AigcRequest;
import com.nd.aic.service.AigcService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping({"/v1.0/c/aigc", "/v1.0/guest/aigc"})
public class AigcController {

    private final AigcService aigcService;


    /**
     * 文生图
     */
    @PostMapping("/actions/txt2img")
    public ResTask txt2img(@Valid @RequestBody AigcRequest aigcReq) {
        return aigcService.txt2img(aigcReq);
    }

    /**
     * 文生视频
     */
    @PostMapping("/actions/txt2video")
    public ResTask txt2video(@Valid @RequestBody AigcRequest aigcReq) {
        return aigcService.txt2video(aigcReq);
    }

    /**
     * AIGC任务查询
     */
    @PostMapping("/actions/query_task")
    public ResResp taskResult(@Valid @RequestBody ResTask resReq) {
        return aigcService.taskResult(resReq.getTaskId());
    }

}
