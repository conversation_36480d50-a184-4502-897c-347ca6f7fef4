package com.nd.aic.controller;


import com.google.common.collect.Maps;

import com.nd.aic.client.CombinationTtsClient;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.client.pojo.tts.TtsSegmentVo;
import com.nd.aic.service.AudioResourceService;
import com.nd.aic.service.AudioResultService;
import com.nd.aic.service.SpeechTimingService;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping({"/v1.0/c/tts", "/v1.0/c/asr"})
public class AsrController {

    private final CombinationTtsClient combinationTtsClient;
    private final AudioResourceService audioResourceService;
    private final SpeechTimingService speechTimingService;
    private final AudioResultService audioResultService;

    /**
     * tts并返回MP3文件
     */
    @PostMapping("/actions/dialogue_synthesis")
    public Object dialogueAudio(@Valid @RequestBody TtsSegmentVo ttsRequest) {
        TtsSegment ttsSegment = new TtsSegment();
        BeanUtils.copyProperties(ttsRequest, ttsSegment);
        String dentryId = audioResourceService.mergeTts(ttsSegment).getAudioId();
        Map<String, Object> ret = Maps.newHashMap();
        ret.put("audio_id", dentryId);
        return ret;
    }

    /**
     * tts并返回MP3文件
     */
    @PostMapping("/actions/trial_listening")
    public void trialListening(@Valid @RequestBody TtsSegmentVo ttsRequest, HttpServletResponse response) throws IOException {
        File mp3File = null;
        try {
            mp3File = combinationTtsClient.mergeTts(ttsRequest.getText(), ttsRequest.getVoiceId());
            String fileNameWithoutExt = StringUtils.defaultIfBlank(ttsRequest.getTitle(), "audio");
            // 设置响应头
            response.setContentType("audio/mp3");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileNameWithoutExt, "UTF-8") + ".mp3");
            response.setContentLength((int) mp3File.length());

            // 将音频数据写入响应输出流
            response.getOutputStream().write(FileUtils.readFileToByteArray(mp3File));
            response.getOutputStream().flush();
        } finally {
            FileUtils.deleteQuietly(mp3File);
        }
    }

    /**
     * 查询打轴数据
     */
    @GetMapping("/actions/query_ata_result")
    public Object queryAtaResult(@RequestParam(value = "id") String id) {
        return speechTimingService.queryAtaResult(id);
    }

    /**
     * 查询打轴数据
     */
    @DeleteMapping("/actions/invalidate_audio_cache")
    public Object invalidateAudioCache(@RequestParam(value = "id") String id) {
        return audioResultService.delete(id);
    }
}
