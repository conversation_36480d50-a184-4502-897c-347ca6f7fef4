package com.nd.aic.controller;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.config.AicAuth;
import com.nd.aic.entity.AutomateCourseware;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.pojo.dto.PlotInteractionDTO;
import com.nd.aic.service.AutomateCoursewareProductService;
import com.nd.aic.service.AutomateCoursewareService;
import com.nd.aic.service.AutomateTransactionService;
import com.nd.gaea.rest.support.WafContext;

import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = {"/v1.0/c/automate_productions", "/v1.0/c/automate_coursewares"})
public class AutomateCoursewareController {

    private final AutomateCoursewareService automateCoursewareService;
    private final AutomateTransactionService automateTransactionService;
    private final AutomateCoursewareProductService automateCoursewareProductService;

    @GetMapping("")
    public Items<AutomateCourseware> list(ListParam<AutomateCourseware> listParam) {
        return automateCoursewareService.list(listParam);
    }

    @PostMapping("")
    public AutomateCourseware add(@RequestBody @Valid AutomateCourseware automateCourseware) {
        automateCourseware.setId(null);
        return automateCoursewareService.add(automateCourseware);
    }

    @PostMapping("/actions/submit_ai_task")
    public AutomateCourseware addAndSubmit(@RequestBody @Valid AutomateCourseware automateCourseware) {
        return automateCoursewareService.addAndSubmitAiProductionTask(automateCourseware);
    }

    @AicAuth
    @PutMapping("{id}")
    public AutomateCourseware update(@PathVariable("id") String id, @RequestParam(value = "ignore_not_found", required = false, defaultValue = "false") boolean ignoreNotFound, @RequestBody AutomateCourseware automateCourseware) {
        automateCourseware.setId(id);
        return automateCoursewareService.update(automateCourseware, ignoreNotFound);
    }

    @PostMapping("{id}/actions/copy")
    public AutomateCourseware copy(@PathVariable("id") String id) {
        AutomateCourseware oldOne = automateCoursewareService.findOne(id);
        if (oldOne == null) {
            throw WafI18NException.of("NOT_FOUND", "指定课件不存在", HttpStatus.NOT_FOUND);
        }
        AutomateCourseware newOne = new AutomateCourseware();
        BeanUtils.copyProperties(oldOne, newOne);
        newOne.setId(null);
        return automateCoursewareService.add(newOne);
    }

    @DeleteMapping("{id}")
    public AutomateCourseware delete(@PathVariable("id") String id) {
        return automateCoursewareService.deleteOne(id);
    }

    @GetMapping("{id}")
    public AutomateCourseware get(@PathVariable("id") String id) {
        return automateCoursewareService.findStrictOne(id);
    }

    @PostMapping("/actions/generate_courseware")
    public AutomateCourseware generateCourseware(@Valid @RequestBody AutomateCourseware automateCourseware) {
        return automateCoursewareProductService.genCoursewarePerformance(automateCourseware);
    }

    @PostMapping("/actions/generate_courseware_async")
    public AutomateTransaction generateCoursewareAsync(@Valid @RequestBody AutomateCourseware automateCourseware) {
        AutomateTransaction automateTransaction = new AutomateTransaction();
        BeanUtils.copyProperties(automateCourseware, automateTransaction);
        automateTransaction.setId(null);
        automateTransaction.setCreateAt(new Date());
        automateTransaction.setCreator(WafContext.getCurrentAccountId());
        automateTransaction.setJobFlag("generate_courseware_async");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        automateCoursewareProductService.genCoursewarePerformanceAsync(automateCourseware, automateTransaction);
        return automateTransaction;
    }

    @GetMapping(value = "/actions/generate_courseware_result")
    public AutomateTransaction generateCoursewareResult(@RequestParam("id") String id) {
        return automateCoursewareProductService.queryAutomateTransaction(id);
    }

    //-------------------------------------------------------------------------------------------//

    /**
     * 视频文字稿内容提取
     */
    @PostMapping("/actions/split_audio")
    public Object splitAudio(@Valid @RequestBody AutomateTransaction automateTransaction) {
        automateTransaction.setJobFlag("split_audio");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        automateCoursewareProductService.splitAudio(automateTransaction);
        return automateTransaction;
    }

    /**
     * 视频文字稿内容提取
     */
    @PostMapping("/actions/extract_text")
    public Object extractText(@Valid @RequestBody AutomateTransaction automateTransaction) {
        automateTransaction.setJobFlag("extract_text");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        automateCoursewareProductService.extractText(automateTransaction);
        return automateTransaction;
    }

    /**
     * AI拆解教学活动/事件
     */
    @PostMapping("/actions/annotate_teaching_events")
    public Object annotateTeachingEvents(@Valid @RequestBody AutomateTransaction automateTransaction) {
        automateTransaction.setJobFlag("annotate_teaching_events");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        automateCoursewareProductService.annotateTeachingEvents(automateTransaction);
        return automateTransaction;
    }

    /**
     * AI拆解教学活动/事件
     */
    @PostMapping("/actions/disassemble_teaching_activity")
    public Object disassemblePerformanceDetail(@Valid @RequestBody AutomateTransaction automateTransaction) {
        automateTransaction.setJobFlag("disassemble_teaching_activity");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        automateCoursewareProductService.disassembleTeachingActivity(automateTransaction);
        return automateTransaction;
    }

    /**
     * AI关联表演方案
     */
    @PostMapping("/actions/related_performance_plan")
    public Object generatePerformancePlan(@Valid @RequestBody AutomateTransaction automateTransaction) {
        automateTransaction.setJobFlag("related_performance_plan");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        automateCoursewareProductService.generatePerformancePlan(automateTransaction);
        return automateTransaction;
    }

    @PostMapping("/actions/automate_transaction_async")
    public AutomateTransaction automateTransactionProcessAsync(@Valid @RequestBody AutomateTransaction automateTransaction) {
        automateCoursewareProductService.validate(automateTransaction);
        automateTransaction.setId(null);
        automateTransaction.setCreateAt(new Date());
        automateTransaction.setCreator(WafContext.getCurrentAccountId());
        automateTransaction.setJobFlag("automate_transaction_async");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        automateCoursewareProductService.automateTransactionAsync(automateTransaction);
        return automateTransaction;
    }

    @RequestMapping(value = "/actions/automate_transaction_retry", method = {RequestMethod.POST})
    public AutomateTransaction automateTransactionRetry(@RequestParam("id") String id) {
        return automateCoursewareProductService.automateTransactionRetry(id);
    }

    @RequestMapping(value = "/actions/automate_transaction_result", method = {RequestMethod.GET, RequestMethod.POST})
    public AutomateTransaction automateTransactionResult(@RequestParam("id") String id) {
        return automateCoursewareProductService.queryAutomateTransaction(id);
    }

    @PostMapping("/actions/generate_interactions")
    public Object generateInteractions(@Valid @RequestBody List<PlotInteractionDTO> dialogues) {
        return automateCoursewareProductService.generateInteractions(dialogues);
    }
}
