package com.nd.aic.controller;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.config.AicAuth;
import com.nd.aic.entity.AutomateCourseware;
import com.nd.aic.entity.AutomateLesson;
import com.nd.aic.entity.VideoConcat;
import com.nd.aic.pojo.AutomateTask;
import com.nd.aic.service.AutomateLessonService;

import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = {"/v1.0/c/automate_lessons", "/v1.0/guest/automate_lessons"})
public class AutomateLessonController {

    private final AutomateLessonService automateLessonService;

    @GetMapping("")
    public Items<AutomateLesson> list(ListParam<AutomateLesson> listParam) {
        return automateLessonService.list(listParam);
    }

    @PostMapping("")
    public AutomateLesson add(@RequestBody @Valid AutomateLesson automateLesson) {
        automateLesson.setId(null);
        return automateLessonService.add(automateLesson);
    }

    @PostMapping("actions/generate_programs")
    public AutomateLesson generatePrograms(@RequestBody @Valid AutomateLesson automateLesson) {
        return automateLessonService.generatePrograms(automateLesson);
    }

    @AicAuth
    @PutMapping("{id}")
    public AutomateLesson update(@PathVariable("id") String id, @RequestBody AutomateLesson automateLesson) {
        automateLesson.setId(id);
        return automateLessonService.update(automateLesson);
    }

    @AicAuth
    @PatchMapping("{id}")
    public AutomateLesson patch(@PathVariable("id") String id, @RequestBody Map<String, Object> map) {
        return automateLessonService.update(id, map);
    }

    @PostMapping("{id}/actions/copy")
    public AutomateLesson copy(@PathVariable("id") String id) {
        AutomateLesson oldOne = automateLessonService.findOne(id);
        if (oldOne == null) {
            throw WafI18NException.of("NOT_FOUND", "指定课件不存在", HttpStatus.NOT_FOUND);
        }
        AutomateLesson newOne = new AutomateLesson();
        BeanUtils.copyProperties(oldOne, newOne);
        return automateLessonService.add(newOne);
    }

    @DeleteMapping("{id}")
    public AutomateLesson delete(@PathVariable("id") String id) {
        return automateLessonService.deleteOne(id);
    }

    @GetMapping("{id}")
    public AutomateLesson get(@PathVariable("id") String id) {
        return automateLessonService.findOne(id);
    }

    @AicAuth
    @PostMapping("{id}/actions/concat_video")
    public VideoConcat concatVideo(@PathVariable("id") String id) {
        return automateLessonService.concat(id);
    }

    @AicAuth
    @PostMapping("{id}/actions/concat_video_result")
    public VideoConcat getVideoConcat(@PathVariable("id") String id) {
        return automateLessonService.concatResult(id);
    }

    @PostMapping("/actions/submit_task")
    public AutomateCourseware submitParticleTask(@RequestBody @Valid AutomateTask automateTask) {
        return automateLessonService.addAndSubmitAiProductionTask(automateTask);
    }

    @PostMapping("/actions/submit_ai_courseware_task")
    public JSONObject submitLessonParticleTask(@RequestBody @Valid JSONObject body) {
        log.info("submitLessonParticleTask body: {}", body.toJSONString());
        return body;
    }

    @GetMapping("/actions/get_lesson_by_aippt_task")
    public AutomateLesson getLessonByAiPptTaskId(@RequestParam("task_id") Long taskId) {
        return automateLessonService.getLessonByAiPptTaskId(taskId);
    }
}
