package com.nd.aic.controller;


import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.nd.aic.base.query.Condition;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.query.Operator;
import com.nd.aic.entity.basic.ConfigDict;
import com.nd.aic.entity.basic.PerformancePlan;
import com.nd.aic.entity.basic.Pronunciation;
import com.nd.aic.entity.basic.TeachingEvent;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.basic.ConfigDictService;
import com.nd.aic.service.basic.PerformancePlanService;
import com.nd.aic.service.basic.PronunciationService;
import com.nd.aic.service.basic.TeachingEventService;
import com.nd.gaea.client.http.WafSecurityHttpClient;
import com.nd.gaea.rest.support.WafContext;
import com.nd.gaea.util.WafJsonMapper;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/basic_data")
public class BasicController {
    // private final TeachingActivityService teachingActivityService;
    private final TeachingEventService teachingEventService;
    private final PerformancePlanService performancePlanService;
    private final ConfigDictService configDictService;
    private final PronunciationService pronunciationService;
    private final FastGptRetryService fastGptRetryService;
    private final WafSecurityHttpClient wafHttpClient;
    @Value("${bp.url:https://aic-service.sdp.ndaeweb.com}")
    private String ProductionUrl;



    @PostMapping("import_data")
    public void importData(@RequestBody Map<String, List<JSONObject>> data) {
        for (String s : data.keySet()) {
            List<JSONObject> jsonObjects = data.get(s);
            PerformancePlan performancePlan = performancePlanService.findByName(s);
            if (null == performancePlan) {
                log.error("PerformancePlan not found: {}", s);
                continue;
            }
            if (null != performancePlan.getParamInfo()) {
                continue;
            }
            performancePlan.setParamInfo(jsonObjects);
            performancePlanService.save(performancePlan);
        }
    }

    @PostMapping("patch_production_line")
    public void patchProductionLine() {
        teachingEventService.patchProductionLine();
        performancePlanService.patchProductionLine();
        pronunciationService.patchProductionLine();
    }

    @GetMapping("/teaching_events")
    public List<TeachingEvent> teachingEvents() {
        return teachingEventService.findAll();
    }

    @GetMapping("/teaching_events/list")
    public Items<TeachingEvent> listTeachingEvents(@RequestParam(value = "unlimited", required = false) Boolean unlimited, ListParam<TeachingEvent> listParam) {
        if (BooleanUtils.isTrue(unlimited)) {
            listParam.setLimit(0);
            listParam.setSupportUnlimit(true);
        }
        return teachingEventService.list(listParam);
    }

    @PostMapping("/teaching_events/batch")
    public List<TeachingEvent> batchTeachingEvents(@RequestBody List<TeachingEvent> teachingEvents) {
        if (!teachingEventService.findAll().isEmpty()) {
            return null;
        }
        List<TeachingEvent> result = Lists.newArrayList();
        for (TeachingEvent teachingEvent : teachingEvents) {
            teachingEvent = teachingEventService.save(teachingEvent);
            result.add(teachingEvent);
        }
        return result;
    }

    @PostMapping("/teaching_events")
    public TeachingEvent addTeachingEvent(@Valid @RequestBody TeachingEvent teachingEvent) {
        return teachingEventService.save(teachingEvent);
    }

    @DeleteMapping("/teaching_events/{id}")
    public TeachingEvent deleteTeachingEvent(@PathVariable(value = "id") String id) {
        TeachingEvent teachingEvent = teachingEventService.delete(id);
        performancePlanService.deleteByTeachingEvent(teachingEvent.getName());
        return teachingEvent;
    }

    @PutMapping("/teaching_events/{id}")
    public TeachingEvent updateTeachingEvent(@PathVariable(value = "id") String id, @Valid @RequestBody TeachingEvent teachingEvent) {
        teachingEvent.setId(id);
        return teachingEventService.save(teachingEvent);
    }

    @GetMapping("/teaching_events/{id}")
    public TeachingEvent getTeachingEvent(@PathVariable(value = "id") String id) {
        // TeachingEvent teachingEvent = teachingEventService.findStrictOne(id);
        // performancePlanService.findByTeachingEvent(teachingEvent.getName());
        return teachingEventService.findStrictOne(id);
    }

    @GetMapping("/performance_plans")
    public List<PerformancePlan> performancePlans(@RequestParam(value = "teachingEvent", required = false) String teachingEvent) {
        List<PerformancePlan> performancePlans = performancePlanService.findAll();
        performancePlans.sort(Comparator.comparing(PerformancePlan::getOrderNo));
        performancePlans = performancePlans.stream().filter(item -> BooleanUtils.isTrue(item.getEnabled())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(teachingEvent)) {
            final List<String> teachingEvents = Lists.newArrayList(teachingEvent);
            performancePlans = performancePlans.stream().filter(e -> CollectionUtils.containsAll(e.getTeachingEvents(), teachingEvents)).collect(Collectors.toList());
        }
        return performancePlans;
    }

    @GetMapping("/performance_plans/list")
    public Items<PerformancePlan> listPerformancePlans(@RequestParam(value = "unlimited", required = false) Boolean unlimited, String applicableCharacterId, String applicableSceneId, ListParam<PerformancePlan> listParam) {
        if (BooleanUtils.isTrue(unlimited)) {
            listParam.setLimit(0);
            listParam.setSupportUnlimit(true);
        }
        if (StringUtils.isNotBlank(applicableCharacterId)) {
            listParam.addCondition(Condition.of("applicableCharacterIds", Operator.EQ, applicableCharacterId, String.class));
        }
        if (StringUtils.isNotBlank(applicableSceneId)) {
            listParam.addCondition(Condition.of("applicableSceneIds", Operator.EQ, applicableSceneId, String.class));
        }
        return performancePlanService.list(listParam);
    }

    @PostMapping("/performance_plans/batch")
    public List<PerformancePlan> batchPerformancePlans(@RequestBody List<PerformancePlan> performancePlans) {
        List<PerformancePlan> result = Lists.newArrayList();
        for (PerformancePlan performancePlan : performancePlans) {
            performancePlan = performancePlanService.save(performancePlan);
            result.add(performancePlan);
        }
        return result;
    }

    @PostMapping("/performance_plans/migrate")
    public List<PerformancePlan>  migratePerformancePlans() {
        String currentAccountId = WafContext.getCurrentAccountId();
        List<String> allowUsers = Lists.newArrayList("972819","147507","212102","268828","983509","103600","854619","871220","622588","********");
        if (!allowUsers.contains(currentAccountId)){
            return null;
        }
        List<PerformancePlan> performancePlans = performancePlanService.findAll();
        performancePlans.sort(Comparator.comparing(PerformancePlan::getOrderNo));
        CollectionType type = WafJsonMapper.getMapper().getTypeFactory().constructCollectionType(List.class, PerformancePlan.class);
        String productionApiUrl = ProductionUrl + "/v1.0/c/basic_data/performance_plans/batch";
        List<PerformancePlan> responsePlans = wafHttpClient.postForObject(productionApiUrl, performancePlans, type);
        if (responsePlans != null){
            System.out.println("y");
        }else {
            System.out.println("n");
        }
        return responsePlans;
    }

    @PostMapping("/performance_plans")
    public PerformancePlan addPerformancePlan(@Valid @RequestBody PerformancePlan performancePlan) {
        return performancePlanService.save(performancePlan);
    }

    @DeleteMapping("/performance_plans/{id}")
    public PerformancePlan deletePerformancePlan(@PathVariable(value = "id") String id) {
        return performancePlanService.delete(id);
    }

    @PutMapping("/performance_plans/{id}")
    public PerformancePlan updatePerformancePlan(@PathVariable(value = "id") String id, @Valid @RequestBody PerformancePlan performancePlan) {
        performancePlan.setId(id);
        return performancePlanService.save(performancePlan);
    }

    @GetMapping("/performance_plans/{id}")
    public PerformancePlan getPerformancePlan(@PathVariable(value = "id") String id) {
        return performancePlanService.findStrictOne(id);
    }

    @GetMapping("/performance_configs")
    public JSONObject performanceConfigs() {
        return fastGptRetryService.askForPerformanceConfig();
    }

    @GetMapping("/config_dicts")
    public List<ConfigDict> configDicts() {
        return configDictService.findAll();
    }

    @GetMapping("/config_dicts/list")
    public Items<ConfigDict> listConfigDicts(ListParam<ConfigDict> listParam) {
        return configDictService.list(listParam);
    }

    @PostMapping("/config_dicts")
    public ConfigDict addConfigDict(@Valid @RequestBody ConfigDict configDict) {
        return configDictService.save(configDict);
    }

    @DeleteMapping("/config_dicts/{id}")
    public ConfigDict deleteConfigDict(@PathVariable(value = "id") String id) {
        return configDictService.delete(id);
    }

    @PutMapping("/config_dicts/{id}")
    public ConfigDict updateConfigDictn(@PathVariable(value = "id") String id, @Valid @RequestBody ConfigDict configDict) {
        configDict.setId(id);
        return configDictService.save(configDict);
    }

    @GetMapping("/config_dicts/{id}")
    public ConfigDict getConfigDict(@PathVariable(value = "id") String id) {
        return configDictService.findStrictOne(id);
    }

    @GetMapping("/pronunciations")
    public List<Pronunciation> pronunciations() {
        return pronunciationService.list();
    }

    @GetMapping("/pronunciations/list")
    public Items<Pronunciation> listPronunciations(ListParam<Pronunciation> listParam) {
        return pronunciationService.list(listParam);
    }

    @PostMapping("/pronunciations")
    public Pronunciation addPronunciation(@Valid @RequestBody Pronunciation pronunciation) {
        return pronunciationService.save(pronunciation);
    }

    @DeleteMapping("/pronunciations/{id}")
    public Pronunciation deletePronunciation(@PathVariable(value = "id") String id) {
        return pronunciationService.delete(id);
    }

    @PutMapping("/pronunciations/{id}")
    public Pronunciation updatePronunciation(@PathVariable(value = "id") String id, @Valid @RequestBody Pronunciation pronunciation) {
        pronunciation.setId(id);
        return pronunciationService.save(pronunciation);
    }

    @GetMapping("/pronunciations/{id}")
    public Pronunciation getPronunciation(@PathVariable(value = "id") String id) {
        return pronunciationService.findStrictOne(id);
    }

}
