package com.nd.aic.controller;


import com.google.common.collect.Lists;

import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.entity.basic.PerformancePlan;
import com.nd.aic.entity.basic.TeachingEvent;
import com.nd.aic.service.basic.PerformancePlanService;
import com.nd.aic.service.basic.TeachingEventService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/guest/basic_data")
public class BasicControllerReader {
    private final TeachingEventService teachingEventService;
    private final PerformancePlanService performancePlanService;

    @GetMapping("/teaching_events")
    public List<TeachingEvent> teachingEvents() {
        return teachingEventService.findAll();
    }

    @GetMapping("/teaching_events/list")
    public Items<TeachingEvent> listTeachingEvents(@RequestParam(value = "unlimited", required = false) Boolean unlimited, ListParam<TeachingEvent> listParam) {
        if (BooleanUtils.isTrue(unlimited)) {
            listParam.setLimit(0);
            listParam.setSupportUnlimit(true);
        }
        return teachingEventService.list(listParam);
    }

    @GetMapping("/teaching_events/{id}")
    public TeachingEvent getTeachingEvent(@PathVariable(value = "id") String id) {
        return teachingEventService.findStrictOne(id);
    }

    @GetMapping("/performance_plans")
    public List<PerformancePlan> performancePlans(@RequestParam(value = "teachingEvent", required = false) String teachingEvent) {
        List<PerformancePlan> performancePlans = performancePlanService.findAll();
        performancePlans.sort(Comparator.comparing(PerformancePlan::getOrderNo));
        performancePlans = performancePlans.stream().filter(item -> BooleanUtils.isTrue(item.getEnabled())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(teachingEvent)) {
            final List<String> teachingEvents = Lists.newArrayList(teachingEvent);
            performancePlans = performancePlans.stream().filter(e -> CollectionUtils.containsAll(e.getTeachingEvents(), teachingEvents)).collect(Collectors.toList());
        }
        return performancePlans;
    }

    @GetMapping("/performance_plans/list")
    public Items<PerformancePlan> listPerformancePlans(@RequestParam(value = "unlimited", required = false) Boolean unlimited, ListParam<PerformancePlan> listParam) {
        if (BooleanUtils.isTrue(unlimited)) {
            listParam.setLimit(0);
            listParam.setSupportUnlimit(true);
        }
        return performancePlanService.list(listParam);
    }

    @GetMapping("/performance_plans/{id}")
    public PerformancePlan getPerformancePlan(@PathVariable(value = "id") String id) {
        return performancePlanService.findStrictOne(id);
    }

    @GetMapping("/performance_plans/detail")
    public PerformancePlan getPerformancePlanByName(@RequestParam String name) {
        return performancePlanService.findByName(name);
    }

}
