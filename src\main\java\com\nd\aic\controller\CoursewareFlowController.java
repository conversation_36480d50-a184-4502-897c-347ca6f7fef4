package com.nd.aic.controller;

import com.nd.aic.pojo.flow.FlowContentOptimizeDTO;
import com.nd.aic.pojo.flow.FlowDisassembleTeachActivityDTO;
import com.nd.aic.pojo.flow.FlowExtractTextDTO;
import com.nd.aic.pojo.flow.FlowGenerateVideoScriptDTO;
import com.nd.aic.pojo.flow.FlowOptimizeTextDTO;
import com.nd.aic.pojo.flow.FlowRelateTeachObjectDTO;
import com.nd.aic.service.CoursewareFlowService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.Map;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
// @RestController
// @RequestMapping(value = {"/v1.0/c/coursewares", "/v1.0/c/courseware_flows"})
public class CoursewareFlowController {

    private final CoursewareFlowService coursewareFlowService;

    private Map<String, String> ok() {
        Map<String, String> map = new HashMap<>(1);
        map.put("result", "ok");
        return map;
    }

    /**
     * 视频文字稿内容提取
     */
    @PostMapping("/actions/extract_text")
    public Object extractText(@Valid @RequestBody FlowExtractTextDTO flowExtractTextDTO) {
        coursewareFlowService.extractText(flowExtractTextDTO);
        return ok();
    }

    /**
     * 文字稿优化
     */
    @PostMapping("/actions/optimize_text")
    public Object optimizeText(@Valid @RequestBody FlowOptimizeTextDTO flowOptimizeTextDTO) {
        return coursewareFlowService.optimizeText(flowOptimizeTextDTO);
    }

    /**
     * 文字稿优化
     */
    @PostMapping("/actions/disassemble_teach_activity")
    public Object disassembleTeachActivity(@Valid @RequestBody FlowDisassembleTeachActivityDTO flowDisassembleTeachActivityDTO) {
        coursewareFlowService.disassembleTeachActivity(flowDisassembleTeachActivityDTO);
        return ok();
    }

    /**
     * 关联学习目标
     */
    @PostMapping("/actions/relate_teach_object")
    public Object relateTeachObject(@Valid @RequestBody FlowRelateTeachObjectDTO relateTeachObjectDTO) {
        coursewareFlowService.relateTeachObject(relateTeachObjectDTO);
        return ok();
    }

    /**
     * 生成表演类型
     */
    @PostMapping("/actions/generate_performance_type")
    public Object generatePerformanceType(@Valid @RequestBody FlowGenerateVideoScriptDTO flowGenerateVideoScriptDTO) {
        coursewareFlowService.generatePerformanceType(flowGenerateVideoScriptDTO);
        return ok();
    }

    @PostMapping("/actions/fastgpt")
    public Object fastGpt(@Valid @RequestBody FlowContentOptimizeDTO flowContentOptimizeDTO) {
        coursewareFlowService.forwardFastGPT(flowContentOptimizeDTO);
        return ok();
    }

    @PostMapping("/actions/fastgpt_callback")
    public Object fastGptCallback(@RequestParam("task_id") String taskId, @Valid @RequestBody String reply) {
        coursewareFlowService.fastGptCallback(taskId, reply);
        return ok();
    }

}
