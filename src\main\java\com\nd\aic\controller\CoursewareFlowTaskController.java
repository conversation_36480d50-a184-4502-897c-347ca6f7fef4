package com.nd.aic.controller;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.entity.IntegratedFlowTask;
import com.nd.aic.pojo.flow.IntegratedBCSFlowTaskDTO;
import com.nd.aic.pojo.flow.IntegratedFlowTaskDTO;
import com.nd.aic.service.CoursewareFlowService;
import com.nd.aic.service.IntegratedFlowTaskParticleService;
import com.nd.aic.service.IntegratedFlowTaskService;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
// @RestController
// @RequestMapping(value = {"/v1.0/c/courseware_flow_tasks"})
public class CoursewareFlowTaskController {

    private final CoursewareFlowService coursewareFlowService;
    private final IntegratedFlowTaskService integratedFlowTaskService;;
    private final IntegratedFlowTaskParticleService integratedFlowTaskParticleService;

    @GetMapping("")
    public Object findAll() {
        return integratedFlowTaskService.findAll();
    }

    @GetMapping("list")
    public Object list(ListParam<IntegratedFlowTask> listParam) {
        return integratedFlowTaskService.list(listParam);
    }

    @DeleteMapping("{id}")
    public Object delete(@PathVariable("id") String id) {
        return integratedFlowTaskService.deleteOne(id);
    }

    @GetMapping("{id}")
    public Object get(@PathVariable("id") String id) {
        return integratedFlowTaskService.findOne(id);
    }

    @GetMapping("{id}/particles")
    public Object particle(@PathVariable("id") String id) {
        return integratedFlowTaskParticleService.findByTaskId(id);
    }

    @PostMapping("{id}/actions/refresh")
    public Object refresh(@PathVariable("id") String id) {
        IntegratedFlowTask integratedFlowTask = integratedFlowTaskService.findOne(id);
        IntegratedFlowTaskDTO integratedFlowTaskDTO  = new IntegratedFlowTaskDTO();
        BeanUtils.copyProperties(integratedFlowTask, integratedFlowTaskDTO);
        integratedFlowTaskDTO.setPerformanceContents(null);
        integratedFlowTaskParticleService.deleteByTaskId(id);
        coursewareFlowService.runFlow(integratedFlowTaskDTO, integratedFlowTask);
        return integratedFlowTask;
    }

    @PostMapping("/actions/generate_performance_async")
    public Object generatePerformanceAsync(@Valid @RequestBody IntegratedFlowTaskDTO integratedFlowTaskDTO) {
        IntegratedFlowTask integratedFlowTask = createTask(integratedFlowTaskDTO);
        coursewareFlowService.runFlow(integratedFlowTaskDTO, integratedFlowTask);
        return integratedFlowTaskDTO;
    }

    @GetMapping("/actions/query_performance_task")
    public Object generatePerformanceAsync(@RequestParam("task_id") String taskId) {
        return coursewareFlowService.queryFlow(taskId);
    }

    @PostMapping("/actions/generate_performance_step")
    public Object generatePerformanceStep(@Valid @RequestBody IntegratedBCSFlowTaskDTO integratedFlowTaskDTO) {
        IntegratedFlowTask integratedFlowTask = createTask(integratedFlowTaskDTO);
        coursewareFlowService.runBcsFlow(integratedFlowTaskDTO, integratedFlowTask);
        return integratedFlowTaskDTO;
    }

    private IntegratedFlowTask createTask(IntegratedFlowTaskDTO integratedFlowTaskDTO) {
        if (StringUtils.isBlank(integratedFlowTaskDTO.getManuscript()) && CollectionUtils.isEmpty(integratedFlowTaskDTO.getVideoDentryInfos()) && CollectionUtils.isEmpty(integratedFlowTaskDTO.getTeachActivityDentryInfos())) {
            throw WafI18NException.of("INVALID_ARGUMENT", "文字稿不能为空",HttpStatus.BAD_REQUEST);
        }
        integratedFlowTaskDTO.setCreator(WafContext.getCurrentAccountId());

        IntegratedFlowTask integratedFlowTask = new IntegratedFlowTask();
        BeanUtils.copyProperties(integratedFlowTaskDTO, integratedFlowTask);
        integratedFlowTask.setStatus("running");
        integratedFlowTask = integratedFlowTaskService.add(integratedFlowTask);
        return integratedFlowTask;
    }

}
