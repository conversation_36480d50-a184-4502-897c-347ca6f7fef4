package com.nd.aic.controller;

import com.nd.aic.base.query.Items;
import com.nd.aic.enums.MaterialTypeEnum;
import com.nd.aic.pojo.dto.MaterialAddDTO;
import com.nd.aic.pojo.dto.MaterialQueryDTO;
import com.nd.aic.pojo.dto.MaterialResponseDTO;
import com.nd.aic.pojo.dto.MaterialUpdateDTO;
import com.nd.aic.service.CoursewareMaterialService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 课件素材管理控制器
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/materials")
public class CoursewareMaterialController {

    private final CoursewareMaterialService coursewareMaterialService;

    /**
     * 添加课件素材
     */
    @PostMapping
    public MaterialResponseDTO addMaterial(@Valid @RequestBody MaterialAddDTO addDTO) {
        return coursewareMaterialService.addMaterial(addDTO);
    }

    /**
     * 更新课件素材
     */
    @PutMapping("/{id}")
    public MaterialResponseDTO updateMaterial(@PathVariable String id, @Valid @RequestBody MaterialUpdateDTO updateDTO) {
        return coursewareMaterialService.updateMaterial(id, updateDTO);
    }

    /**
     * 删除课件素材
     */
    @DeleteMapping("/{id}")
    public void deleteMaterial(@PathVariable String id) {
        coursewareMaterialService.deleteMaterial(id);
    }

    /**
     * 获取课件素材详情
     */
    @GetMapping("/{id}")
    public MaterialResponseDTO getMaterialDetail(@PathVariable String id) {
        return coursewareMaterialService.getMaterialDetail(id);
    }

    /**
     * 查询课件素材列表
     */
    @PostMapping("/query")
    public Items<MaterialResponseDTO> queryMaterials(@RequestBody(required = false) MaterialQueryDTO queryDTO) {
        return coursewareMaterialService.queryMaterials(queryDTO);
    }

    /**
     * 根据类型获取课件素材
     */
    @GetMapping("/type/{type}")
    public List<MaterialResponseDTO> getMaterialsByType(@PathVariable MaterialTypeEnum type) {
        return coursewareMaterialService.getMaterialsByType(type);
    }

    /**
     * 批量删除课件素材
     */
    @DeleteMapping("/batch")
    public void batchDeleteMaterials(@RequestBody List<String> ids) {
        for (String id : ids) {
            coursewareMaterialService.deleteMaterial(id);
        }
    }

    /**
     * 获取素材类型列表
     */
    @GetMapping("/types")
    public List<MaterialTypeInfo> getMaterialTypes() {
        List<MaterialTypeInfo> types = new java.util.ArrayList<>();
        for (MaterialTypeEnum type : MaterialTypeEnum.values()) {
            MaterialTypeInfo info = new MaterialTypeInfo();
            info.setCode(type.getCode());
            info.setDescription(type.getDescription());
            types.add(info);
        }
        return types;
    }

    /**
     * 素材类型信息
     */
    @Data
    public static class MaterialTypeInfo {
        private String code;
        private String description;
    }
}
