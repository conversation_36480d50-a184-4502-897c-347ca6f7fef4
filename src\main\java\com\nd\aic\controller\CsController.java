package com.nd.aic.controller;

import com.nd.aic.config.AicAuth;
import com.nd.aic.config.AicTokenAuthenticationProvider;
import com.nd.aic.exception.AicException;
import com.nd.aic.pojo.cs.TokenParams;
import com.nd.aic.service.CsService;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/v1.0/cs")
@Slf4j
public class CsController {

    private final CsService csService;

    public CsController(CsService csService) {
        this.csService = csService;
    }

    @AicAuth
    @SneakyThrows
    @RequestMapping(value = "/token", method = {RequestMethod.GET, RequestMethod.POST})
    public Object getToken(@RequestBody TokenParams tokenParams) {
        String currentAccountId = WafContext.getCurrentAccountId();
        if (StringUtils.isBlank(currentAccountId)) {
            throw AicException.ofIllegalArgument("currentAccountId is blank");
        }
        if (WafContext.getCurrentToken() != null && StringUtils.equalsIgnoreCase(WafContext.getCurrentToken()
                .getAuthType(), AicTokenAuthenticationProvider.AUTH_TYPE)) {
            String path = "/anon" + StringUtils.defaultIfBlank(tokenParams.getPath(), "");
            tokenParams.setPath(path);
        }
        return csService.getCsToken(Long.parseLong(currentAccountId), tokenParams);
    }

}
