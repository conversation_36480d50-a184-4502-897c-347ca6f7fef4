package com.nd.aic.controller;

import com.nd.aic.client.FastGptFeignClient;
import com.nd.aic.client.pojo.chat.ChatCompletion;
import com.nd.aic.client.pojo.chat.ChatCompletionResponse;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/chat/completions")
public class FastGptController {
    private final FastGptFeignClient fastGptFeignClient;

    @PostMapping("")
    public ChatCompletionResponse fastGptForward(@RequestHeader("fastGptKey") String authorization, @RequestBody ChatCompletion chatCompletion) {
        return fastGptFeignClient.ask(authorization, chatCompletion);
    }
}
