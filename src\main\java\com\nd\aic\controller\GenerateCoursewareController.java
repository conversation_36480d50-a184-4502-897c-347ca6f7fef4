package com.nd.aic.controller;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationReq;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.service.CoursewareGenerationService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/courseware")
public class GenerateCoursewareController {

    private final CoursewareGenerationService coursewareGenerationService;

    /**
     * 异步生成课件脚本
     */
    @PostMapping("/generate")
    public JSONObject generateCoursewareTask(@Valid @RequestBody CoursewareGenerationReq coursewareGenerationReq) {
        JSONObject result = new JSONObject();
        String taskId = coursewareGenerationService.generateCoursewareTask(coursewareGenerationReq);
        result.put("task_id", taskId);
        return result;
    }

    /**
     * 同步生成课件脚本
     */
    @PostMapping("/generateSync")
    public CoursewareModel generateCoursewareSync(@Valid @RequestBody CoursewareGenerationReq coursewareGenerationReq) {
        return coursewareGenerationService.generateCourseware(coursewareGenerationReq, null);
    }

    /**
     * 查询任务详情
     */
    @GetMapping("/info/{task_id}")
    public JSONObject getCoursewareInfo(@PathVariable("task_id") String taskId) {
        return coursewareGenerationService.getCoursewareInfoByTaskId(taskId);
    }
}
