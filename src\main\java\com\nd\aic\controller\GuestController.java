package com.nd.aic.controller;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.client.pojo.bcs.LackResourceReq;
import com.nd.aic.client.pojo.tts.TtsSegmentVo;
import com.nd.aic.entity.AutomateCourseware;
import com.nd.aic.entity.Danmu;
import com.nd.aic.entity.basic.DataCache;
import com.nd.aic.exception.AicException;
import com.nd.aic.pojo.ndr.NdrSearchVO;
import com.nd.aic.service.AutomateCoursewareProductService;
import com.nd.aic.service.DanmuService;
import com.nd.aic.service.GuestService;
import com.nd.aic.service.basic.DataCacheService;
import com.nd.gaea.WafException;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/guest")
public class GuestController {

    private final AutomateCoursewareProductService automateCoursewareProductService;
    private final DataCacheService dataCacheService;
    private final GuestService guestService;
    private final DanmuService danmuService;


    @PostMapping("/automate_coursewares/actions/generate_courseware")
    public AutomateCourseware generateCourseware(@RequestParam(value = "biz_flag", required = false, defaultValue = "aig") String bizFlag,
                                                 @Valid @RequestBody AutomateCourseware automateCourseware) {
        if (StringUtils.isNotBlank(bizFlag)) {
            automateCourseware.setBizFlag(bizFlag);
        }
        try {
            return automateCoursewareProductService.genCoursewarePerformance(automateCourseware, true);
        } catch (Exception e) {
            log.error("runFlow", e);
            if (e instanceof WafI18NException || e instanceof AicException) {
                if (((WafException) e).getResponseEntity().getStatusCodeValue() >= 500) {
                    throw WafI18NException.of("INTERNAL_SERVER_ERROR", e.getMessage(), HttpStatus.BAD_REQUEST, e);
                }
                throw (WafException) e;
            }
            throw WafI18NException.of("INTERNAL_SERVER_ERROR", "生成课件脚本失败: " + e.getMessage(), HttpStatus.BAD_REQUEST, e);
        }
    }

    @PostMapping("/data_cache/actions/save")
    public DataCache saveCache(@RequestParam(value = "group") String group, @RequestParam("key") String key, @RequestParam(value = "expire_time", defaultValue = "168") Long expireTime, @Valid @RequestBody Object data) {
        DataCache dataCache = new DataCache();
        dataCache.setCategory(group);
        dataCache.setKey(key);
        dataCache.setCreateTime(new Date());
        dataCache.setExpireTime(new Date(System.currentTimeMillis() + 1000L * 60 * 60 * expireTime));
        dataCache.setTarget(data);
        return dataCacheService.save(dataCache);
    }

    @GetMapping("/data_cache/actions/query")
    public Object queryCache(@RequestParam(value = "group") String group, @RequestParam("key") String key) {
        DataCache dataCache = dataCacheService.findByKeyAndCategory(key, group);
        if (null != dataCache) {
            return dataCache.getTarget();
        }
        return null;
    }

    @PostMapping("/ndr/actions/search_resources")
    public Object search(@Valid @RequestBody NdrSearchVO searchVO) {
        return guestService.searchNdrResource(searchVO);
    }

    @PostMapping("/commons/actions/encrypt_token")
    public Object encryptToken(@Valid @RequestBody String token) throws Exception {
        return GuestService.encryptToken(token);
    }

    @PostMapping("/commons/actions/upload_files")
    public Object uploadFiles(@Valid @RequestParam List<MultipartFile> files) throws Exception {
        return guestService.uploadFile(files);
    }

    @GetMapping("/commons/actions/query_101ppt_material_url")
    public Object query101PptMaterialUrl(@RequestParam("resource_id") String resourceId) {
        return guestService.query101PptMaterialUrl(resourceId);
    }

    @GetMapping("/commons/actions/sleep")
    public Object sleepUntil(@RequestParam("seconds") Integer seconds) throws InterruptedException {
        Thread.sleep(seconds * 1000);
        return "OK";
    }

    @PostMapping("/commons/actions/submit_lack_requirement")
    public Object submitLackRequirement(@RequestBody LackResourceReq lackResourceReq) {
        String suid = StringUtils.defaultIfBlank(WafContext.getCurrentAccountId(), "********");
        return guestService.submitLackRequirement(suid, lackResourceReq);
    }

    @PostMapping("/commons/actions/synthesis_audio")
    public Object synthesisAudio(@Valid @RequestBody TtsSegmentVo ttsRequest) {
        return guestService.synthesisAudio(ttsRequest);
    }

    // ===============================================弹幕========================================================
    @GetMapping("/danmus")
    public Items<Danmu> listDanmu(ListParam<Danmu> listParam) {
        return danmuService.list(listParam);
    }

    @PostMapping("/danmus")
    public Danmu addDanmu(@Valid @RequestBody Danmu danmu) {
        return danmuService.save(danmu);
    }

    @DeleteMapping("/danmus/{id}")
    public Danmu deleteDanmu(@PathVariable(value = "id") String id) {
        return danmuService.delete(id);
    }

    @PutMapping("/danmus/{id}")
    public Danmu updateDanmu(@PathVariable(value = "id") String id, @Valid @RequestBody Danmu danmu) {
        danmu.setId(id);
        return danmuService.save(danmu);
    }

    @GetMapping("/danmus/{id}")
    public Danmu getDanmu(@PathVariable(value = "id") String id) {
        return danmuService.findStrictOne(id);
    }
    // ===============================================弹幕========================================================
}
