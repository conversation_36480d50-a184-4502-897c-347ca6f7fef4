package com.nd.aic.controller;

import com.nd.aic.pojo.interactive.InteractiveReq;
import com.nd.aic.service.InteractiveService;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/interactions")
@Slf4j
public class InteractiveController {

    private final InteractiveService interactiveService;

    @RequestMapping(value = "/actions/generate_interaction", method = RequestMethod.POST)
    public Object generateInteractive(@Valid @RequestBody() InteractiveReq interactiveReq) {
        return interactiveService.generateVideoInteraction(interactiveReq);
    }
}
