package com.nd.aic.controller;

import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.entity.LessonFlow;
import com.nd.aic.service.LessonFlowService;
import com.nd.aic.service.ToolsService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = {"/v1.0/c/lesson_flows", "/v1.0/guest/lesson_flows"})
public class LessonFlowController {

    private final LessonFlowService lessonFlowService;
    private final ToolsService toolsService;

    @GetMapping("")
    public Items<LessonFlow> list(ListParam<LessonFlow> listParam) {
        return lessonFlowService.list(listParam);
    }

    @PostMapping("")
    public LessonFlow add(@RequestBody @Valid LessonFlow lessonFlow) {
        lessonFlow.setId(null);
        return lessonFlowService.add(lessonFlow);
    }

    @PostMapping("actions/generate_teaching_activities")
    public LessonFlow generateTeachingActivities(@RequestBody @Valid LessonFlow lessonFlow) {
        return lessonFlowService.generateTeachingActivities(lessonFlow);
    }

    @PostMapping("actions/finish_step")
    public LessonFlow finishStep(@RequestParam("step") String step, @RequestBody @Valid LessonFlow lessonFlow) {
        return lessonFlowService.finishStep(lessonFlow, step);
    }

    @PostMapping("actions/submit_lesson_task")
    public LessonFlow submitLessonTask(@RequestBody @Valid LessonFlow lessonFlow, @RequestParam(value = "without_form", required = false) Boolean withoutForm) {
        return lessonFlowService.submitLessonTask(lessonFlow, withoutForm);
    }

    @PostMapping("{lesson_flow_id}/teaching_activities/{teaching_activity_id}/actions/retry")
    public LessonFlow retryTeachingActivity(@PathVariable("lesson_flow_id") String lessonFlowId, @PathVariable(value = "teaching_activity_id") String teachingActivityId) {
        return lessonFlowService.automateRetry(lessonFlowId, teachingActivityId);
    }

    @PutMapping("{id}")
    public LessonFlow update(@PathVariable("id") String id, @RequestBody LessonFlow lessonFlow) {
        lessonFlow.setId(id);
        return lessonFlowService.update(lessonFlow);
    }

    @PatchMapping("{id}")
    public LessonFlow patch(@PathVariable("id") String id, @RequestBody Map<String, Object> map) {
        return lessonFlowService.update(id, map);
    }

    @DeleteMapping("{id}")
    public LessonFlow delete(@PathVariable("id") String id) {
        return lessonFlowService.deleteOne(id);
    }

    @GetMapping("{id}")
    public LessonFlow get(@PathVariable("id") String id) {
        return lessonFlowService.findOne(id);
    }

    @PostMapping("actions/parse_ppt_script")
    public Object extractPptScript(@RequestParam("resource_id") String resourceId) {
        return toolsService.parsePptScript(resourceId);
    }

    @PostMapping("actions/parse_word_script")
    public Object extractWordScript(@RequestParam("resource_id") String resourceId) {
        return toolsService.parseWordScript(resourceId);
    }

}
