package com.nd.aic.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nd.aic.config.AicAuth;
import com.nd.aic.monitor.ExceptionEvent;
import com.nd.gaea.client.ApplicationContextUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/monitors")
@Slf4j
public class MonitorController {

    @AicAuth
    @PostMapping("/actions/im/disk_alert")
    public void diskAlert(@RequestBody JSONObject body) {
        JSONArray alerts = body.getJSONArray("alerts");
        if (CollectionUtils.isNotEmpty(alerts)) {
            for (int i = 0; i < alerts.size(); i++) {
                JSONObject alert = alerts.getJSONObject(i);
                String alertStatus = alert.getString("status");
                JSONObject labels = alert.getJSONObject("labels");
                JSONObject annotations = alert.getJSONObject("annotations");
                if (!StringUtils.equals(alertStatus, "firing") || labels == null || annotations == null) {
                    continue;
                }
                String instance = labels.getString("instance");
                String volume = labels.getString("volume");
                String summary = annotations.getString("summary");
                String errorMsg = summary + "，实例：" + StringUtils.defaultString(instance) + "，磁盘：" + StringUtils.defaultString(volume);
                ApplicationContextUtil.getApplicationContext().publishEvent(ExceptionEvent.builder().type("6").errorMsg(errorMsg).build());
            }
        }

    }
}
