package com.nd.aic.controller;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.config.AicAuth;
import com.nd.aic.pojo.ndr.NdrTokenReq;
import com.nd.aic.service.NdrSdkService;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/ndr")
@Slf4j
public class NdrController {

    private final NdrSdkService ndrSdkService;

    @RequestMapping(value = "access_token", method = RequestMethod.GET)
    public Object getAccessToken(@RequestParam("tenant_id") String tenantId, @RequestParam("container_id") String containerId) {
        return ndrSdkService.getAccessToken(tenantId, containerId);
    }

    @AicAuth
    @RequestMapping(value = "/token", method = RequestMethod.POST)
    public Object getToken(@Valid @RequestBody NdrTokenReq ndrTokenReq) {
        String token = "token " + NdrSdkService.getNdrToken(ndrTokenReq.getAk(), ndrTokenReq.getSk(), ndrTokenReq.getTenantId(), ndrTokenReq.getMethod()
                .toUpperCase(), ndrTokenReq.getPath());
        return new JSONObject().fluentPut("token", token);
    }

    @RequestMapping(value = "config", method = RequestMethod.GET)
    public Object getNdrConfig(@RequestParam("tenant_id") String tenantId, @RequestParam("container_id") String containerId) {
        return ndrSdkService.getNdrConfig(tenantId, containerId);
    }


}
