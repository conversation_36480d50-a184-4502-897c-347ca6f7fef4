package com.nd.aic.controller;

import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.entity.basic.PerformanceAddition;
import com.nd.aic.service.basic.PerformanceAdditionService;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/basic_data/performance_additionals")
public class PerformanceAdditionController {

    private final PerformanceAdditionService performanceAdditionService;

    @GetMapping("/list")
    public Items<PerformanceAddition> listPerformanceAddition(@RequestParam(value = "unlimited", required = false) Boolean unlimited, ListParam<PerformanceAddition> listParam) {
        if (BooleanUtils.isTrue(unlimited)) {
            listParam.setLimit(0);
            listParam.setSupportUnlimit(true);
        }
        return performanceAdditionService.list(listParam);
    }

    @PostMapping
    public PerformanceAddition addPerformanceAddition(@Valid @RequestBody PerformanceAddition performanceAddition) {
        return performanceAdditionService.save(performanceAddition);
    }

    @DeleteMapping("/{id}")
    public PerformanceAddition deletePerformanceAddition(@PathVariable(value = "id") String id) {
        return performanceAdditionService.delete(id);
    }

    @PutMapping("/{id}")
    public PerformanceAddition updatePerformanceAddition(@PathVariable(value = "id") String id, @Valid @RequestBody PerformanceAddition performanceAddition) {
        performanceAddition.setId(id);
        return performanceAdditionService.save(performanceAddition);
    }

    @GetMapping("/{id}")
    public PerformanceAddition getPerformanceAddition(@PathVariable(value = "id") String id) {
        return performanceAdditionService.findStrictOne(id);
    }

    @PostMapping("/actions/batch")
    public List<PerformanceAddition> batch(@Valid @RequestBody List<PerformanceAddition> performanceAdditions) {
        for (PerformanceAddition performanceAddition : performanceAdditions) {
             performanceAdditionService.save(performanceAddition);
        }
        return performanceAdditions;
    }

}
