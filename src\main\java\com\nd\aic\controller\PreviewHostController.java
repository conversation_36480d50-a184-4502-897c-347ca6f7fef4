package com.nd.aic.controller;


import com.nd.aic.entity.PreviewHost;
import com.nd.aic.service.PreviewHostService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/preview/hosts")
public class PreviewHostController {

    private final PreviewHostService previewHostService;

    @PostMapping("/add")
    public PreviewHost addPreviewHost(@RequestBody PreviewHost previewHost) {
        return previewHostService.savePreviewHost(previewHost);
    }

    @PostMapping("/apply")
    public PreviewHost applyHost(@RequestParam("occupy_id") String occupyId) {
        return previewHostService.applyHost(occupyId);
    }

    @PostMapping("/release")
    public void releaseHost(@RequestParam(name = "host_id", required = false) String hostId, @RequestParam(name = "occupy_id", required = false) String occupyId, @RequestParam(name = "ip_address", required = false) String ipAddress) {
        previewHostService.releaseHost(hostId, occupyId, ipAddress);
    }

    @PutMapping("/toggle")
    public PreviewHost toggleHost(@RequestParam(name = "host_id", required = false) String hostId, @RequestParam(name = "ip_address", required = false) String ipAddress, @RequestParam("enabled") Boolean enabled) {
        return previewHostService.toggleHost(hostId, ipAddress, enabled);
    }

    @PostMapping("/heartbeat")
    public void heartbeat(String ipAddress) {
        // TODO
    }

    @GetMapping("/list")
    public List<PreviewHost> list() {
        return previewHostService.findAll();
    }

    @DeleteMapping("/remove/{id}")
    public PreviewHost delete(@PathVariable String id) {
        return previewHostService.delete(id);
    }

    @PostMapping("/drop_collection")
    public void dropCollection() {
        previewHostService.dropCollection();
    }
}
