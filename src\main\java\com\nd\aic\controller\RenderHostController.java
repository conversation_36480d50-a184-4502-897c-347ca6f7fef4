package com.nd.aic.controller;


import com.nd.aic.entity.RenderHost;
import com.nd.aic.service.RenderHostService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/s/render/hosts")
public class RenderHostController {

    private final RenderHostService renderHostService;

    @PostMapping("/save")
    public RenderHost save(@RequestBody RenderHost renderHost) {
        return renderHostService.saveRenderHost(renderHost, false);
    }

    @GetMapping("/list")
    public List<RenderHost> list() {
        return renderHostService.findAllRenderHost();
    }

    @DeleteMapping("/remove/{id}")
    public RenderHost delete(@PathVariable String id) {
        return renderHostService.delete(id);
    }

    @PutMapping("/{hostId}/toggle")
    public void toggle(@PathVariable("hostId") String id, @RequestParam Boolean enabled) {
        renderHostService.toggle(id, enabled);
    }

}
