package com.nd.aic.controller;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.config.AicAuth;
import com.nd.aic.entity.RenderTask;
import com.nd.aic.pojo.common.Ids;
import com.nd.aic.pojo.render.RenderTaskAddReq;
import com.nd.aic.pojo.render.RenderTaskFinishReq;
import com.nd.aic.pojo.render.RenderTaskStartReq;
import com.nd.aic.service.RenderTaskService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/render")
public class RenderTaskController {

    private final RenderTaskService renderTaskService;

    @AicAuth
    @PostMapping("/add_task")
    public RenderTask addTask(@Valid @RequestBody RenderTaskAddReq renderTaskAddReq) {
        return renderTaskService.addTask(renderTaskAddReq);
    }

    @PostMapping("/update_task")
    public RenderTask updateTask(@Valid @RequestBody RenderTask renderTask) {
        return renderTaskService.updateTask(renderTask);
    }

    @AicAuth
    @GetMapping("/task_detail/{id}")
    public RenderTask taskDetail(@PathVariable String id) {
        return renderTaskService.findStrictOne(id);
    }

    /**
     * 获取任务列表
     */
    @GetMapping("/task_list")
    public Items<RenderTask> list(ListParam<RenderTask> listParam) {
        return renderTaskService.list(listParam);
    }

    /**
     * 获取待执行的渲染任务
     */
    @AicAuth
    @GetMapping("/pending_tasks")
    public List<RenderTask> getPendingTask(@RequestParam String ip) {
        RenderTask task = renderTaskService.getPendingTask(ip);
        return task != null ? Lists.newArrayList(task) : new ArrayList<>(0);
    }

    /**
     * 开始执行任务
     */
    @AicAuth
    @PostMapping("/start_task/{id}")
    public RenderTask startTask(@PathVariable String id, @Valid @RequestBody(required = false) RenderTaskStartReq renderTaskStartReq) {
        return renderTaskService.startTask(id, renderTaskStartReq != null ? renderTaskStartReq.getHostIp() : null);
    }

    /**
     * 完成任务，回写视频地址
     */
    @AicAuth
    @PostMapping("/finish_task/{id}")
    public RenderTask finishTask(@PathVariable String id, @Valid @RequestBody RenderTaskFinishReq renderTaskFinishReq) {
        return renderTaskService.finishTask(id, renderTaskFinishReq.getVideoUrl(), renderTaskFinishReq.getErrMsg());
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/delete_task/{id}")
    public RenderTask deleteTask(@PathVariable String id) {
        return renderTaskService.deleteTask(id);
    }

    /**
     * 批量删除任务
     */
    @DeleteMapping("/delete_tasks")
    public List<RenderTask> deleteTasks(@Valid @RequestBody Ids ids) {
        return renderTaskService.deleteTasks(ids.getIds());
    }

    /**
     * 获取待执行任务的排名
     */
    @GetMapping("/pending_task_rank/{id}")
    public JSONObject getPendingTaskRank(@PathVariable String id) {
        long rank = renderTaskService.getPendingTaskRank(id);
        return new JSONObject().fluentPut("rank", rank + 1);
    }

    /**
     * 批量更新任务优先级
     */
    @PostMapping("/update_tasks_priority")
    public void updateTasksPriority(@RequestBody List<RenderTask> tasks) {
        renderTaskService.updateTasksPriority(tasks);
    }

    /**
     * 上报任务进度
     */
    @AicAuth
    @PostMapping("/report_task_progress/{id}")
    public RenderTask reportTaskProgress(@PathVariable String id, @RequestParam Double progress) {
        return renderTaskService.reportTaskProgress(id, progress);
    }

    /**
     * 查询弹性扩容信息
     */
    @GetMapping("/elastic_scaling_info")
    public JSONObject getElasticScalingInfo() {
        // 获取当前时间并按10分钟间隔分组，确保每10分钟数据相同
        long currentTime = System.currentTimeMillis();
        long tenMinuteInterval = 10 * 60 * 1000; // 10分钟的毫秒数
        long seed = currentTime / tenMinuteInterval; // 每10分钟变化一次的种子值

        // 使用种子值创建Random对象，确保在同一个10分钟间隔内生成相同的随机数
        java.util.Random random = new java.util.Random(seed);

        // 生成测试数据
        int totalServers = 5 + random.nextInt(16); // 5-20台服务器
        int idleServers = random.nextInt(totalServers + 1); // 0到总数的空闲服务器
        int pendingTasks = random.nextInt(101); // 0-100个待执行任务

        return new JSONObject().fluentPut("status", 1)
                .fluentPut("message", "")
                .fluentPut("instance_count", 5)
                .fluentPut("params", new JSONObject().fluentPut("idle_server_count", idleServers)
                        .fluentPut("total_server_count", totalServers)
                        .fluentPut("pending_task_count", pendingTasks));
    }

}
