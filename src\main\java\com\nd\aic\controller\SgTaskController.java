package com.nd.aic.controller;

import com.nd.aic.entity.SgTask;
import com.nd.aic.service.SgTaskService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/sg")
public class SgTaskController {

    private final SgTaskService sgTaskService;

    @PutMapping("/update_task")
    public int updateTask(@RequestBody SgTask sgTask) {
        return sgTaskService.updateTask(sgTask);
    }

    @GetMapping("/pending_task")
    public SgTask getPendingTaskWithLock() {
        return sgTaskService.getPendingTaskWithLock();
    }

    @GetMapping("/task/sgTaskId/{sgTaskId}")
    public SgTask findBySgTaskId(@PathVariable String sgTaskId) {
        return sgTaskService.findBySgTaskId(sgTaskId);
    }

    @GetMapping("/tasks/cooking")
    public List<SgTask> findAllCookingTask() {
        return sgTaskService.findAllCookingTask();
    }

    @DeleteMapping("/task/{id}")
    public SgTask deleteTask(@PathVariable String id) {
        return sgTaskService.delete(id);
    }
}
