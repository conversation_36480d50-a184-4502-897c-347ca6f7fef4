package com.nd.aic.controller;


import com.google.common.collect.Lists;

import com.mongodb.util.JSON;
import com.nd.aic.client.TemplateRefFeignClient;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.pojo.template.TextTemplateArgMappings;
import com.nd.aic.pojo.template.TextTemplateParameterRecommendVO;
import com.nd.aic.pojo.template.TextTemplateSearchVO;
import com.nd.aic.service.AutomateTransactionService;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.ResourcePackageService;
import com.nd.aic.util.JsonUtils;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1.0/c/template_refs")
public class TextTemplateRefController {
    private final TemplateRefFeignClient templateRefFeignClient;
    private final AutomateTransactionService automateTransactionService;
    private final ResourcePackageService resourcePackageService;
    private final NdrService ndrService;
    private final FastGptRetryService fastGptRetryService;

    @GetMapping(value = "/templates")
    public Object listTemplates() {
        return templateRefFeignClient.listTemplates();
    }

    @GetMapping(value = "/templates/{id}")
    public Object getTemplate(@PathVariable("id") String id) {
        return templateRefFeignClient.getTemplate(id);
    }

    @PostMapping(value = "/tasks")
    public Object createTask(@RequestBody Map<String, Object> task) {
        return templateRefFeignClient.createTask(task);
    }

    @GetMapping(value = "/tasks/{id}")
    public Object getTask(@PathVariable("id") String id) {
        return templateRefFeignClient.getTask(id);
    }

    @PostMapping(value = "/actions/upload_front_video")
    public Object uploadVideo(@RequestBody() AutomateTransaction automateTransaction) {
        automateTransaction.setId(null);
        automateTransaction.setCreateAt(new Date());
        automateTransaction.setExpireAt(LocalDateTime.now().plusMinutes(10).toDate());
        automateTransaction.setCreator(WafContext.getCurrentAccountId());
        automateTransaction.setProductMode("upload_video");
        automateTransaction = automateTransactionService.addAutomateTransaction(automateTransaction);
        resourcePackageService.submitFrontVideo(automateTransaction);
        return automateTransaction;
    }

    @GetMapping(value = "/actions/upload_front_video_result")
    public Object uploadVideoResult(@RequestParam("id") String id) {
        return resourcePackageService.queryAutomateTransaction(id);
    }

    @PostMapping(value = "/actions/search_text_template")
    public Object searchTextTemplate(@RequestBody TextTemplateSearchVO textTemplateSearchVO) {
        TextTemplateArgMappings mappings = fastGptRetryService.fastGptCallForJsonResult("text_template", "fastgpt-pIMzAyd8dgmB65O5pjK7e9s1R5vDSKE2E98rgWvynM2TU1uwP23UseHI", null, (data) -> JsonUtils.parseObject(data, TextTemplateArgMappings.class));
        List<String> labels = Lists.newArrayList();
        if (StringUtils.isNotBlank(textTemplateSearchVO.getPerformanceCode())) {
            labels.add(textTemplateSearchVO.getPerformanceCode());
        } else {
            labels.add(textTemplateSearchVO.getPerformancePlan());
        }
        labels.removeIf(StringUtils::isBlank);
        List<String> tags = Lists.newArrayList();
        tags.add(mappings.getCategoryTagId());
        tags.add(MapUtils.getString(mappings.getStage(), textTemplateSearchVO.getStage()));
        tags.add(MapUtils.getString(mappings.getOrientation(), textTemplateSearchVO.getOrientation()));
        tags.add(MapUtils.getString(mappings.getStyle(), textTemplateSearchVO.getStyle()));
        tags.removeIf(StringUtils::isBlank);
        return ndrService.searchResource(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_COURSEWARE_CONTAINER_ID, labels, tags);
    }

    @PostMapping(value = "/actions/recommend_text_template_parameters")
    public Object recommendTextTemplateParameters(@RequestBody TextTemplateParameterRecommendVO textTemplateParameterRecommendVO) {
        String input = JsonUtils.toJson(textTemplateParameterRecommendVO);
        String key = "fastgpt-iLfPdCaw87dFgYpPXOE0Jj5k7yvgRXpBeHinfhZg2LfzMbvbqDDZoY6Ro";
        return fastGptRetryService.fastGptCallForJsonResult(input, key, null, (data) -> {
            // log.info("recommendTextTemplateParameters: {}", data);
            // data = StringUtils.substringBetween("```json", "```");
            return JSON.parse(data);
        });
    }
}
