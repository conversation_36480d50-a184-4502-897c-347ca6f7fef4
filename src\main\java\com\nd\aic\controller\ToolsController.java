package com.nd.aic.controller;


import com.google.common.collect.Maps;

import com.nd.aic.config.AicAuth;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.aic.service.ToolsService;
import com.nd.aic.service.automate.AutomateHandler;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping({"/v1.0/c/tools", "/v1.0/guest/tools"})
public class ToolsController {

    private final AutomateHandler automateHandler;
    private final ToolsService toolsService;

    /**
     * tts并返回MP3文件
     */
    @PostMapping("/actions/img2video")
    public Object img2video(@Valid @RequestBody DentryInfo dentryInfo) throws IOException {
        String resourceId = automateHandler.image2Video(dentryInfo.getName(), dentryInfo.getDentryId());
        Map<String, Object> result = Maps.newHashMap();
        result.put("resource_id", resourceId);
        return result;
    }

    @AicAuth
    @PostMapping("/actions/imgUrl2video")
    public Object img2video(@Valid @RequestBody String url) throws IOException {
        String resourceId = automateHandler.image2Video(url);
        Map<String, Object> result = Maps.newHashMap();
        result.put("resource_id", resourceId);
        return result;
    }

    @PostMapping("/actions/patch_production_line")
    public Object patchProductionLine(@RequestParam("table_name") String collName) {
        int updateCount = toolsService.patchField(collName, "productionLine", "courseware");
        Map<String, Object> result = Maps.newHashMap();
        result.put("update_count", updateCount);
        return result;
    }

    @PostMapping("/actions/parse_pptx")
    public Object parsePPTX(@RequestParam(value = "language", defaultValue = "中文") String language,
                            @RequestParam(value = "ppt_file", required = false) MultipartFile multipartFile) {
        return toolsService.parsePptx(language, multipartFile);
    }

}
