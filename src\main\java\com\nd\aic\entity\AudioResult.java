package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.pojo.audio.AudioMetaResult;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import javax.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Document(collection = "audio_resource")
public class AudioResult extends BaseDomain<String> {

    private Date createTime;
    private String creator;
    private String title;
    @Indexed
    @NotNull
    private String key;
    private String containerType;
    private String resourceId;
    private AudioMetaResult metaResult;

}
