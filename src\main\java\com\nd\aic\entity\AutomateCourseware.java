package com.nd.aic.entity;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.common.BizConstant;
import com.nd.aic.entity.automate.GlobalAnnotation;
import com.nd.aic.entity.automate.VoiceArgs;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.entity.flow.OriginalRequirement;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.aic.pojo.dto.InteractionDTO;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.Pattern;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@CompoundIndexes({@CompoundIndex(name = "prodLine_createAt_idx", def = "{'productionLine': 1, 'createAt': -1}")})
@Document(collection = "automate_courseware")
public class AutomateCourseware extends BaseDomain<String> {

    @NotBlank
    private String productionLine;
    private String bizFlag;
    // 课程主键
    private String bizKey;
    // 节目单ID
    private String programId;
    private String programName;
    private String programContent;
    // 需要强调的核心教学内容
    private String keyContent;
    @CreatedDate
    private Date createAt;
    private Date updateAt;
    @CreatedBy
    private String creator;
    private String status;
    private String message;
    @NotBlank
    private String title;
    private String language;
    private String clothingResource;
    private String characterResource;
    private String sceneResource;
    private JSONObject birthPosition;

    // private List<DentryInfo> audioDentryInfos;
    private String bgmType;
    private List<DentryInfo> bgmDentryInfos;
    private String bgmResource;
    private String ambientSoundResource;
    @NotBlank
    @Pattern(regexp = "teaching_activity|video|ppt|manuscript")
    private String productMode;
    //课件视频
    private List<DentryInfo> videoDentryInfos;
    private List<DentryInfo> pptDentryInfos;
    private List<DentryInfo> teachingActivityDentryInfos;
    private String manuscript;
    // 风格化讲稿
    private String stylizationManuscript;

    private VoiceArgs voiceArgs;
    private GlobalAnnotation globalAnnotation;
    // 自动化任务
    private Map<String, Object> lastTask;

    private List<AutomatePerformanceDetail> detailList;

    private CoursewareModel coursewareModel;

    /**
     * 提取出来的教学互动（已计算好触发时间）
     */
    private List<InteractionDTO> interactions;

    /**
     * 服务端拼装好的互动数据
     */
    private VideoInteraction interactionResult;

    private Boolean isLatestScript;

    //***** AI Hub 相关字段 *****

    /**
     * 需求
     */
    @Transient
    private OriginalRequirement lessonRequirement;
    private Map<String, String> inputParams;
    private String requirement;
    private String requirementMode;
    private String materialFilesJson;
    private String botId;
    private String conversationId;
    private String workflowRunId;
    private String aiTaskId;
    @Indexed
    private String aiTaskStatus;
    private String aiTaskErrMsg;

    @Transient
    private Boolean newActionEnabled;
    @Transient
    private String characterHandleMode = BizConstant.CHARACTER_HANDLE_MODE_DESTROY;
    @Transient
    private String characterInstanceId;

    private Object stylizeContent;
    private String manuscriptStyle;
}
