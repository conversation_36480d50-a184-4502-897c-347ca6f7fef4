package com.nd.aic.entity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.entity.flow.LessonProgram;
import com.nd.aic.entity.flow.LessonTeachingActivity;
import com.nd.aic.entity.flow.MaterialFile;
import com.nd.aic.entity.flow.OriginalRequirement;
import com.nd.aic.util.JsonUtils;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@CompoundIndexes({@CompoundIndex(name = "createAt_idx", def = "{'createAt': -1}")})
@Document(collection = "automate_lesson")
// @JsonInclude(JsonInclude.Include.ALWAYS)
public class AutomateLesson extends BaseDomain<String> {

    public static final Set<String> NEST_FIELDS = JsonUtils.getJacksonKeys(OriginalRequirement.class);

    // 生产线实例ID，返工不改变
    private String workflowInstanceId;
    // 执行ID，返工改变
    private String executionId;
    // 教学活动ID
    private String teachingActivityId;
    // 教学活动名称
    private String teachingActivityName;
    // 学习目标
    private String learningObject;
    // 角色
    private String character;
    // 场景
    private String scene;

    // 来源
    private String source;
    // // 类型
    // @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    // private String type;

    @CreatedDate
    private Date createAt;
    private Date updateAt;
    @CreatedBy
    private String creator;
    private String status;
    // private String message;
    private String title;
    // ----------------------一、原始需求----------------------
    // 1、服务于哪一本书的课程——单行文本框
    private String serverBook;
    // 2、教学活动颗粒的学习目标信息——多行文本框
    private String learningGoal;
    // 3、教学活动对应的书籍原文——多行文本框// 书籍原文
    @NotBlank
    private String content;
    // 4、面向的目标用户——单行文本框
    private String targetUser;
    // 5、发布的平台——单行文本框
    private String platform;
    // ----------------------原始需求----------------------
    @JsonUnwrapped
    private OriginalRequirement originalRequirement = new OriginalRequirement();

    // ----------------------一、原始需求----------------------
    // 素材需求
    private List<MaterialFile> materialFiles;
    // ----------------------一、原始需求----------------------

    // 生产线URL
    private String workflowUrl;
    // 教学活动生产需求
    private String requirementUrl;
    // 确认教学活动方案
    private String designUrl;
    // 确认教学活动设计
    private String activityUrl;
    // 匹配节目实施方案
    private String implPlanUrl;

    private Object transactions;
    // 原始教学设计
    private String originalTeachingDesignType;
    // 原始节目单
    private List<LessonProgram> originalPrograms;
    // 初始讲稿+互动
    private List<LessonTeachingActivity> teachingActivities;
    // 节目单列表
    private List<LessonProgram> programs = Lists.newArrayList();

    private String videoUrl;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        Map<String, Object> map = Maps.newHashMap(extProperties);
        List<String> keys = Lists.newArrayList(map.keySet());
        keys.retainAll(NEST_FIELDS);
        keys.forEach(map::remove);
        return map;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        if (NEST_FIELDS.contains(key)) {
            return;
        }
        extProperties.put(key, value);
    }

    public String getCharacter() {
        String character = this.character;
        if (StringUtils.isBlank(character) && null != this.originalRequirement) {
            character = this.originalRequirement.getTeacherIpRequirement();
        }
        return character;
    }
}
