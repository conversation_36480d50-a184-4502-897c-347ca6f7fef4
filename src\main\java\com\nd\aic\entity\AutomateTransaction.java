package com.nd.aic.entity;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.entity.flow.AutomateTransactionOutcomes;
import com.nd.aic.entity.flow.AutomateTransactionTemporary;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.gaea.client.WafResourceAccessException;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.web.client.ResourceAccessException;

import java.net.SocketTimeoutException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Document(collection = "automate_transaction")
public class AutomateTransaction extends BaseDomain<String> {

    @CreatedDate
    private Date createAt;
    private Date updateAt;
    private Date expireAt;
    @CreatedBy
    private String creator;
    // process/fail/success
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String status = "process";
    private String errorCode;
    private String errorMessage;
    private String title;
    private String language;

    private String sceneResource;
    private String characterResource;

    private String productionLine;
    @NotBlank
    private String productMode;
    private String jobFlag;
    // 不同模式对应的输入
    private List<DentryInfo> videoDentryInfos;
    private List<DentryInfo> pptDentryInfos;
    private List<DentryInfo> teachingActivityDentryInfos;
    private String manuscript;
    // 风格化讲稿
    private String stylizationManuscript;
    private List<DentryInfo> audioDentryInfos;
    private String resourceUrl;

    private List<String> availablePerformancePlans;
    private List<AutomatePerformanceDetail> detailList;
    private AutomateTransactionTemporary temporary;
    // 输出
    private AutomateTransactionOutcomes outcomes;

    public String getFinalManuscript() {
        if (StringUtils.isNotBlank(stylizationManuscript)) {
            return stylizationManuscript;
        }
        return manuscript;
    }

    @JsonAnyGetter
    public Map<String, Object> getter() {
        Map<String, Object> map = Maps.newHashMap();
        if (StringUtils.isNotEmpty(errorMessage)) {
            map.put("message", errorMessage);
        }
        return map;
    }

    public void setError(Exception e) {
        setStatus("fail");
        if (e instanceof WafI18NException) {
            setErrorCode(((WafI18NException) e).getError().getCode());
        } else if (e instanceof WafResourceAccessException) {
            setErrorCode(((WafResourceAccessException) e).getRemoteResponseEntity().getBody().getCode());
        } else {
            setErrorCode("INTERNAL_SERVER_ERROR");
        }
        setErrorMessage(e.getMessage());
    }

    public void setErrorX(Exception e) {
        setStatus("fail");
        if (e instanceof WafI18NException) {
            setErrorCode(((WafI18NException) e).getError().getCode());
        } else {
            setErrorCode("INTERNAL_SERVER_ERROR");
        }

        String latestCauseMsg = e.getMessage();
        Exception cause = e;
        while (null != cause.getCause()) {
            if (cause instanceof ResourceAccessException && null != cause.getCause() && cause.getCause() instanceof SocketTimeoutException) {
                ResourceAccessException resourceException = (ResourceAccessException) cause;
                latestCauseMsg = String.format("请求超时: %s", StringUtils.substringBefore(resourceException.getMessage(), "nested exception is"));
                break;
            }
            cause = (Exception) cause.getCause();
            if (StringUtils.isNotBlank(cause.getMessage())) {
                latestCauseMsg = cause.getMessage();
            }
        }
        if (cause instanceof WafResourceAccessException) {
            WafResourceAccessException wafException = ((WafResourceAccessException) cause);
            String errorMessage = String.format("responseBody: %s\ndetail: %s", wafException.getResponseBody(), wafException.getDetail());
            setErrorMessage(errorMessage);
        } else {
            setErrorMessage(latestCauseMsg);
        }
    }
}
