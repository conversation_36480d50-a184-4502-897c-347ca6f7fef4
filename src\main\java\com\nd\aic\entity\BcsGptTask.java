package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;

import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "bcs_gpt_task")
public class BcsGptTask extends BaseDomain<String> {

    // @Id
    // private String id;

    private LocalDateTime createTime;

    private String fastgptKey;

    //当前操作人
    private Long userId;
    //业务配置应用id
    //@NotBlank(message = "appId不能为空")
    private String appId;
    //业务配置表单id
    //@NotBlank(message = "formId不能为空")
    private String formId;
    //数据ID
    //@NotBlank(message = "dataId不能为空")
    private String dataId;
    //流程实例id
    //@NotBlank(message = "flowInstId不能为空")
    private String flowInstId;
    //节点实例id
    //@NotBlank(message = "nodeInstId不能为空")
    private String nodeInstId;
    //gpt生成内容输出字段
    private String outputKey;
    //gpt生成内容输出字段
    private String statustKey;
    //待gpt优化内容
    private String content;
    //gpt返回内容
    private String result;
}
