package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.enums.MaterialTypeEnum;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课件素材实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Document(collection = "courseware_material")
public class CoursewareMaterial extends BaseDomain<String> {

    /**
     * 素材名称
     */
    @Field("name")
    @Indexed
    private String name;

    /**
     * 素材描述
     */
    @Field("description")
    private String description;

    /**
     * 素材类型：IMAGE(图片), VIDEO(视频), AUDIO(音频), MODEL_3D(3D模型), OTHER(其他)
     */
    @Field("type")
    @Indexed
    private MaterialTypeEnum type;

    /**
     * 素材文件路径/URL
     */
    @Field("filePath")
    private String filePath;

    /**
     * 素材文件大小（字节）
     */
    @Field("fileSize")
    private Long fileSize;

    /**
     * 文件格式/MIME类型
     */
    @Field("mimeType")
    private String mimeType;

    /**
     * 文件扩展名
     */
    @Field("extension")
    private String extension;

    /**
     * 缩略图路径
     */
    @Field("thumbnailPath")
    private String thumbnailPath;

    /**
     * 素材标签
     */
    @Field("tags")
    @Indexed
    private List<String> tags;

    /**
     * 素材分类
     */
    @Field("category")
    @Indexed
    private String category;

    /**
     * 素材元数据（如图片尺寸、视频时长等）
     */
    @Field("metadata")
    private Map<String, Object> metadata;

    /**
     * 上传者ID
     */
    @Field("uploaderId")
    @Indexed
    private String uploaderId;

    /**
     * 课件素材状态：ACTIVE(激活), INACTIVE(停用)
     */
    @Field("status")
    @Indexed
    private String status;

    /**
     * 创建时间
     */
    @Field("createTime")
    @Indexed
    private Date createTime;

    /**
     * 更新时间
     */
    @Field("updateTime")
    private Date updateTime;

    /**
     * NDR ID
     */
    @Field("ndrId")
    private String ndrId;

    /**
     * 外部ID
     */
    @Field("externalId")
    @Indexed
    private String externalId;

    
    
}
