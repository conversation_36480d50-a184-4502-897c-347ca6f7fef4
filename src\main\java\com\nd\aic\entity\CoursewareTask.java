package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.pojo.CoursewareGenerationReq;
import com.nd.aic.pojo.CoursewareModel;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "courseware_task")
public class CoursewareTask extends BaseDomain<String> {

    // @Id
    // private String id;

    private String userId;

    @Indexed
    @Field(value = "task_id")
    private String taskId;

    @NotNull
    private CoursewareGenerationReq coursewareGenerationReq;

    private Integer status;

    private Long createTime;

    private Long updateTime;

    private String error;

    private CoursewareModel result;
}
