package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Document(collection = "dan_mu")
public class Danmu extends BaseDomain<String> {

    private Date createAt;
    @Indexed
    private String cid;
    private String username;
    private String avatar;
    private String content;
    private String style;
    private Integer time;

}
