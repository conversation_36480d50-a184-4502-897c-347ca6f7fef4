package com.nd.aic.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.cs.DentryInfo;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Document(collection = "integrated_flow_task")
public class IntegratedFlowTask extends BaseDomain<String> {

    @CreatedDate
    private Date createAt;
    @CreatedBy
    private String creator;
    private String status;
    private String message;
    @NotBlank
    private String title;
    private String characterResource;
    private String sceneResource;
    // private String openingPerformance;
    private Boolean addOpeningPerformance;
    //课件视频
    private List<DentryInfo> videoDentryInfos;
    private List<DentryInfo> audioDentryInfos;
    private List<DentryInfo> teachActivityDentryInfos;
    private String manuscript;
    private String teachActivities;
    private String performanceTypes;
    private List<CoursewareModel> performanceContents;
    private String performanceContentResource;
}
