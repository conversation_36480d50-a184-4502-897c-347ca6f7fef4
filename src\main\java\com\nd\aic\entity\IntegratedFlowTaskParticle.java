package com.nd.aic.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.flow.TeachEventType;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 生成表演内容任务颗粒
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@Document(collection = "integrated_flow_task_particle")
public class IntegratedFlowTaskParticle extends BaseDomain<String> {

    @Indexed
    private String taskId;
    @CreatedDate
    private Date createAt;

    private String teachStep;
    private String teachActivity;
    private String teachGoal;
    private String teachTime;
    private String teachTools;
    private List<String> texts;

    private List<TeachEventType> teachEventTypes;

    private CoursewareModel coursewareModel;


}
