package com.nd.aic.entity;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.entity.lesson_flow.FlowCallbackArguments;
import com.nd.aic.entity.lesson_flow.MaterialNotes;
import com.nd.aic.entity.lesson_flow.LessonFlowTeachingActivity;
import com.nd.aic.entity.lesson_flow.CourseProperties;
import com.nd.aic.util.JsonUtils;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "lesson_flow")
public class LessonFlow extends BaseDomain<String> {

    public static final String STATUS_INIT = "init";
    public static final String STATUS_PREPARE = "prepare";
    public static final String STATUS_AUTOMATING = "automating";
    public static final String STATUS_CONCAT = "concat_running";
    public static final String STATUS_COMPLETED = "completed";
    public static final String STATUS_FAILED = "failed";

    public static final Set<String> NEST_FIELDS = JsonUtils.getJacksonKeys(LessonFlow.class);

    // 拆解课时id
    private String bizId;
    @CreatedDate
    private Date createAt;
    private Date updateAt;
    @CreatedBy
    private String creator;
    private String status;
    private String errMsg;
    private Integer retryCount;

    private String title;

    private List<String> videoMaterials;

    private List<String> pptMaterials;

    private List<String> docMaterials;

    private List<String> otherMaterials;

    private String content;

    private List<LessonFlowTeachingActivity> teachingActivities;

    private List<MaterialNotes> materialNotes;

    // 教学素材批注(PPT)
    @Deprecated
    private List<MaterialNotes> pptMaterialNotes;

    // 教学素材批注(图片)
    @Deprecated
    private List<MaterialNotes> imageMaterialNotes;

    // 教学素材批注(视频)
    @Deprecated
    private List<MaterialNotes> videoMaterialNotes;

    // 教学素材批注(板书关键文字)
    @Deprecated
    private List<String> keywords;

    private CourseProperties courseProperties;

    private FlowCallbackArguments callbackArguments;

    private String videoConcatId;

    private String videoUrl;

    private String scriptUrl;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        if (NEST_FIELDS.contains(key)) {
            return;
        }
        extProperties.put(key, value);
    }
}
