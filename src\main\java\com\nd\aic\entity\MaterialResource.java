package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;

import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Document(collection = "material_resource3")
public class MaterialResource extends BaseDomain<String> {

    private String resourceId;
    private String title;
    private String description;
    private String period;
    private String grade;
    private String subject;
    private String version;
    private String subVersion;

    private String teachingMaterial;
    private List<String> chapters;
    private List<String> tags;

    private Object width;
    private Object height;
}
