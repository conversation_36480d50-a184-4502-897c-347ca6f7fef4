package com.nd.aic.entity;


import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.enums.PreviewHostStatusEnum;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import javax.validation.constraints.Pattern;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "preview_host")
public class PreviewHost extends BaseDomain<String> {

    /**
     * 主机IP
     */
    @Pattern(regexp = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", message = "Invalid IPv4 address")
    @NotBlank
    @Indexed(unique = true)
    private String ipAddress;

    /**
     * 推流地址
     */
    private String pushStreamUrl;

    /**
     * 拉流地址
     */
    @NotBlank
    private String pullStreamUrl;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 占用ID
     */
    private String occupyId;

    /**
     * 当前状态
     */
    private PreviewHostStatusEnum currentStatus;

    /**
     * 占用时间
     */
    private Date occupyTime;

    /**
     * 创建时间
     */
    private Date createTime;


}
