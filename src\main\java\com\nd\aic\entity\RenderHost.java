package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.enums.RenderHostStatusEnum;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import javax.validation.constraints.Pattern;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "render_host")
public class RenderHost extends BaseDomain<String> {

    /**
     * 主机IP
     */
    @Pattern(regexp = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", message = "Invalid IPv4 address")
    @NotBlank
    @Indexed(unique = true)
    private String ipAddress;

    /**
     * 主机配置
     */
    private String configuration;

    /**
     * 主机等级
     */
    private Integer level = 1;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 当前渲染的任务
     */
    private String currentTaskId;

    /**
     * 是否可用
     */
    private Boolean enabled;

    /**
     * 当前状态
     */
    private RenderHostStatusEnum currentStatus;

    /**
     * 是否自动添加
     */
    private Boolean autoAdd;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 开始工作时间
     */
    private Date startWorkTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
