package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.enums.RenderTaskStatusEnum;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.render.MrqSetting;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "render_task")
public class RenderTask extends BaseDomain<String> {

    private String taskName;
    private String bizType;
    @Indexed
    private String bizId;
    private String userId;
    private Long priority;
    /**
     * 渲染模式，obs、mrq
     */
    private String renderMode;
    private String videoUrl;
    private String videoResourceId;
    /**
     * 用户指定的任务执行的机器ip
     */
    private String ip;
    private MrqSetting mrqSetting;

    /**
     * 任务进度
     */
    private Double progress;

    /**
     * 任务执行的机器ip
     */
    private String hostIp;
    @Indexed
    private Date createTime;
    private Date startTime;
    private Date finishTime;
    @Indexed
    private RenderTaskStatusEnum status;
    private String statusMsg;
    private String errorMsg;
    private CoursewareModel coursewareModel;

    private int retryTime;
    /**
     * 互动内容
     */
    private VideoInteraction interaction;

    private Boolean enableArkitFace;
    private Boolean enableSubtitle;

    /**
     * 是否使用自研口型
     */
    private Boolean arkitNdTech;

    /**
     * AI生产线 botId
     */
    private String botId;
    private String conversationId;
    private String workflowRunId;
}
