package com.nd.aic.entity;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Document(collection = "render_task_history")
public class RenderTaskHistory extends RenderTask {

    @Indexed
    private Date deletedTime;
    private String deletedUserId;

}
