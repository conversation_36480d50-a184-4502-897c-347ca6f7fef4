package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.enums.SgTaskStatusEnum;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "sg_task")
public class SgTask extends BaseDomain<String> {

    private String name;

    @Indexed
    private String resourceId;
    private String resourceContainerId;
    private String sgModel;
    private List<String> toolTypes;
    private int emotionFlag;
    /**
     * 干声音频地址
     */
    private String audioUrl;
    /**
     * 表情批注文件地址
     */
    private String expressionUrl;
    @Indexed
    private String sgTaskId;
    private String sgTaskOrder;
    private String sgNdrId;

    @Indexed
    private SgTaskStatusEnum status;
    private String errorMsg;

    /**
     * 是否使用自研
     */
    private Boolean ndTech;

    @Indexed
    private Date createTime;
    private Date lockTime;
    private Date startTime;
    private Date cookTime;
    private Date finishTime;
}
