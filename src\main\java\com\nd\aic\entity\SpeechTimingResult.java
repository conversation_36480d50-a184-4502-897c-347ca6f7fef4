package com.nd.aic.entity;

import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.client.pojo.asr.AtaResult;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "speech_text_timing_result")
public class SpeechTimingResult extends BaseDomain<String> {

    private String audioResourceId;
    private String audioUrl;
    private String audioText;

    @Indexed
    private String audioKey;
    private String audioTextKey;

    private AtaResult ataResult;
    private Date createTime;
}
