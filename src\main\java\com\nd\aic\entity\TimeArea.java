package com.nd.aic.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * time area
 */
@AllArgsConstructor
@Data
public class TimeArea {

    /**
     * start time, unit: ms, if null, means from the beginning
     */
    private Long start;


    /**
     * end time, unit: ms, if null, means to the end
     */
    private Long end;

    public boolean overlaps(TimeArea other) {
        if (null == this.start || null == this.end) {
            return false;
        }
        return !(this.end <= other.start || this.start >= other.end);
    }

    public boolean overlapsWithGapThreshold(TimeArea other, long gapThreshold) {
        if (null == this.start || null == this.end || null == other.start || null == other.end) {
            return false;
        }

        // 计算重叠区域的开始和结束时间
        long overlapStart = Math.max(this.start, other.start);
        long overlapEnd = Math.min(this.end, other.end);

        // 计算重叠区域的长度
        long overlapLength = Math.max(0, overlapEnd - overlapStart);

        // 计算 this 的非重叠部分的长度
        long thisNonOverlapLength = this.end - this.start - overlapLength;

        // 有重叠   且  this的非重叠部分小于指定阈值，则返回 true
        return overlapLength > 0 && thisNonOverlapLength < gapThreshold;
    }

    public static void main(String[] args) {
        TimeArea self = new TimeArea(10000L, 15000L);
        TimeArea left = new TimeArea(9000L, 10000L);
        TimeArea leftOverlap = new TimeArea(9000L, 14400L);
        TimeArea overlap = new TimeArea(10500L, 14500L);
        TimeArea rightOverlap = new TimeArea(11000L, 18000L);
        TimeArea right = new TimeArea(16000L, 18000L);
        System.out.println("test overlapsWithThreshold ");
        System.out.println("left :" + self.overlapsWithGapThreshold(left, 1000));
        System.out.println("leftOverlap threshold not match:" + self.overlapsWithGapThreshold(leftOverlap, 1000));
        System.out.println("overlap :" + self.overlapsWithGapThreshold(overlap, 1000));
        System.out.println("rightOverlap threshold match:" + self.overlapsWithGapThreshold(rightOverlap, 1000));
        System.out.println("right :" + self.overlapsWithGapThreshold(right, 1000));

    }
}
