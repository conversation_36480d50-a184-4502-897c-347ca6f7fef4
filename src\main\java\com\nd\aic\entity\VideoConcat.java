package com.nd.aic.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nd.aic.base.domain.BaseDomain;
import com.nd.aic.entity.automate.CallbackArguments;
import com.nd.aic.pojo.CoursewareModel;

import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Document(collection = "video_concat")
public class VideoConcat extends BaseDomain<String> {

    public static final String STATUS_RUNNING = "running";
    public static final String STATUS_SUCCESS = "succeeded";
    public static final String STATUS_FAILED = "failed";

    private Boolean withTransition;
    private String title;
    private Date createAt;
    private Date expireAt;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String key;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<String> videoUrls;
    private List<String> renderScriptIds;

    private String finalVideo;
    private String status;
    private String errorMsg;

    private CallbackArguments callbackArguments;

}
