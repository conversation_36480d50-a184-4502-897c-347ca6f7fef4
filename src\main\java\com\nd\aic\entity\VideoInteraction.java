package com.nd.aic.entity;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class VideoInteraction {
    private String id;
    // 需要视频入库后设置
    private String refResourceId;
    // 需要视频入库后设置
    private String defaultMedia;
    // 需要视频入库后设置
    private List<MediaSource> mediaSources;
    // 需要视频入库后更新部分字段
    private List<ActiveEvent> activeEvents;

    private List<JSONObject> resources;

    @JsonIgnore
    private Map<String, Object> properties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return properties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        properties.put(key, value);
    }

    @Data
    public static class MediaSource {
        // 需要视频入库后设置
        private String id;
        // 需要视频入库后设置
        private String src;
        // 需要视频入库后设置 固定上传到课件库 1
        private String ndrTenantId;
        // 需要视频入库后设置 固定上传到课件库 "4f7a4a2c-9dac-411f-9a15-1851f8326247"
        private String ndrContainerId;

        @JsonIgnore
        private Map<String, Object> properties = Maps.newHashMap();

        @JsonAnyGetter
        public Map<String, Object> getter() {
            return properties;
        }

        @JsonAnySetter
        public void setter(String key, Object value) {
            properties.put(key, value);
        }
    }

    @Data
    public static class ActiveEvent {
        // 需要视频入库后设置
        private String mediaTarget;
        private Double timeAnchor;
        private String id;
        private String type;
        private String interactiveType;
        private int timeLimit;
        private Map<String, Object> customArgs;

        @JsonIgnore
        private Map<String, Object> properties = Maps.newHashMap();

        @JsonAnyGetter
        public Map<String, Object> getter() {
            return properties;
        }

        @JsonAnySetter
        public void setter(String key, Object value) {
            properties.put(key, value);
        }
    }
}
