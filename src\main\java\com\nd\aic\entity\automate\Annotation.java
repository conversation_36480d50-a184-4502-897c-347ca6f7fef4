package com.nd.aic.entity.automate;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;

import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Annotation {

    /**
     * 节目重点内容
     */
    private String programKeyContent;
    /**
     * 节目内容类型
     */
    private String programContentType;
    /**
     * 观众状态
     */
    private String audienceStatus;
    /**
     * 观众感受到的情绪
     */
    private String audienceEmotion;

    private Map<String, Object> properties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return properties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        properties.put(key, value);
    }
}
