package com.nd.aic.entity.automate;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;

import org.hibernate.validator.constraints.NotBlank;

import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GlobalAnnotation {

    /**
     * 教学类型
     */
    private String teachingType;


    /**
     * 教学环节
     */
    private String teachingSegment;

    /**
     * 表演风格
     */
    private String performanceStyle;

    /**
     * 国家和地区
     */
    private String countryRegion;

    /**
     * 观众年龄
     */
    private String audienceAge;

    /**
     * 宗教
     */
    private String religion;

    /**
     * 学段学科
     */
    private String stageSubject;

    private Map<String, Object> properties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return properties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        properties.put(key, value);
    }
}
