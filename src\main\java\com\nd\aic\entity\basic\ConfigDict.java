package com.nd.aic.entity.basic;

import com.nd.aic.base.domain.BaseDomain;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "basic_config_dict")
@CompoundIndexes({
        @CompoundIndex(name = "idx_cn", def = "{'category': 1, 'name': 1}")
})
public class ConfigDict extends BaseDomain<String> {

    private String creator;
    @NotBlank
    private String category;
    @NotBlank
    private String name;
    private String world;
    private String description;
    private Date createTime;
}
