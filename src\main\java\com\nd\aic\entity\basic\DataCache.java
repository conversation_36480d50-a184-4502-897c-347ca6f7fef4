package com.nd.aic.entity.basic;

import com.nd.aic.base.domain.BaseDomain;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "basic_data_cache")
@CompoundIndexes({
        @CompoundIndex(name = "idx_key", def = "{'key': 1, 'category': 1}")
})
public class DataCache extends BaseDomain<String> {

    private Date createTime;
    private Date expireTime;
    private String creator;
    private String category;
    @NotBlank
    @Indexed
    private String key;
    private Object target;
}
