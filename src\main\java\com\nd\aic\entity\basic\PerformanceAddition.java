package com.nd.aic.entity.basic;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.domain.BaseDomain;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "basic_performance_additional")
public class PerformanceAddition extends BaseDomain<String> {

    @NotBlank
    @Indexed(unique = true)
    private String name;
    private Boolean trigger;
    private Boolean enabled;
    private Date createTime;
    private List<String> baseOn;
    private String productionLine;
    /**
     * 参数信息
     */
    private List<JSONObject> paramInfo;
}
