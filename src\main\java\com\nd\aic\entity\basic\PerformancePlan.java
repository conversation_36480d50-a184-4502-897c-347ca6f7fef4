package com.nd.aic.entity.basic;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.domain.BaseDomain;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "basic_performance_plan2")
public class PerformancePlan extends BaseDomain<String> {

    private List<String> teachingEvents = new ArrayList<>();
    private String code;
    @NotBlank
    @Indexed(unique = true)
    private String name;
    private String alias;
    private String description;
    private String argumentInstructions;
    private String exampleUrl;
    private Date createTime;
    @NotNull
    private Boolean enabled;
    private Boolean aiLineEnabled;

    private String scene;
    private List<String> tags;
    private List<Map<String, Object>> fixedParams;
    private List<Map<String, Object>> variableParams;
    private String thumbUrl;
    private String scriptUrl;

    /**
     * 适用的角色ID列表
     */
    private List<String> applicableCharacterIds;

    /**
     * 适用的场景ID列表
     */
    private List<String> applicableSceneIds;

    /**
     * 参数信息
     */
    private List<JSONObject> paramInfo;
    private List<JSONObject> paramGroup;

    /**
     * 排序号
     */
    @Indexed
    private Long orderNo;

    private String productionLine;

    private Long minimum;

    private Boolean excludeDialogue;

}
