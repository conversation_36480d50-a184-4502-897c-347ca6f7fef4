package com.nd.aic.entity.basic;

import com.nd.aic.base.domain.BaseDomain;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "basic_pronunciation")
@CompoundIndexes({})
public class Pronunciation extends BaseDomain<String> {

    private String creator;
    private Date createTime;
    @NotBlank
    private String text;
    @NotBlank
    private String anno;
    private String productionLine;
}
