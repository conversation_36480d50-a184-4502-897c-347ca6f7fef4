package com.nd.aic.entity.basic;

import com.nd.aic.base.domain.BaseDomain;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Document(collection = "basic_teaching_activity2")
public class TeachingActivity extends BaseDomain<String> {

    private String code;
    @NotBlank
    private String name;
    private String description;
    private Date createTime;

    private List<String> tags;
    private List<String> stages;
    private List<String> subjects;
}
