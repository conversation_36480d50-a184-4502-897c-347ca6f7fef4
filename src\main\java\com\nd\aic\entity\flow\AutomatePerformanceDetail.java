package com.nd.aic.entity.flow;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.client.pojo.tts.ChildTtsSegment;
import com.nd.aic.entity.automate.Annotation;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.dto.PerformanceEnhanceDTO;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AutomatePerformanceDetail {

    private String uuid = UUID.randomUUID().toString();
    private String type = "performance";
    private String teachingActivityCode;
    private String teachingActivity;
    private String teachingEventCode;
    private String teachingEvent;
    private String originalScript;
    private String dialogue;
    /**
     * 音频资源ID(CS)
     */
    private String audioId;
    private String audioGenType;
    private List<ChildTtsSegment> ttsSegments;

    private String performancePlanCode;
    private String performancePlan;
    private Map<String, Object> material;
    private String materialInstructions;
    private String status;
    private Map<String, List<String>> pronunciationDict;

    private Annotation annotation;
    private Boolean transition;
    private String transitionName;
    private String transitionResource;
    @Deprecated
    private List<PerformanceEnhanceDTO> performanceEnhances;
    private List<PerformanceAdditionDTO> performanceAdditional;

    private InteractionDTO interaction;

    private Object materials;
}
