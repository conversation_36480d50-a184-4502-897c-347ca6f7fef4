package com.nd.aic.entity.flow;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.aic.pojo.dto.PlotInteractionDTO;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AutomateTransactionOutcomes {

    private String manuscript;
    private List<AutomatePerformanceDetail> detailList;

    private CoursewareModel coursewareModel;
    private List<InteractionDTO> interactions;
    private List<PlotInteractionDTO> plotInteractions;

    private Object outputResource;
}
