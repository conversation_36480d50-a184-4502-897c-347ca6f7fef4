package com.nd.aic.entity.flow;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nd.aic.pojo.AutomateTask;
import com.nd.aic.pojo.cs.DentryInfo;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
// @JsonInclude(JsonInclude.Include.ALWAYS)
public class LessonProgram {
    private Integer programIndex;
    private String type;
    private String programId;
    private String programName;
    private String programContent;
    private AutomateTask originalTask;
    // 需要强调的核心教学内容
    private String keyContent;
    private String originalScript;
    private String implementPlanName;
    private String implementPlanType;
    private String automateCoursewareId;
    private String taskId;

    private List<DentryInfo> materials;
    private List<MaterialFile> materialFiles;
    private String interaction;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        extProperties.put(key, value);
    }
}
