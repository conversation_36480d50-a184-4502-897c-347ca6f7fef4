package com.nd.aic.entity.flow;

import com.google.common.collect.Maps;

import com.nd.aic.pojo.cs.DentryInfo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
// @JsonInclude(JsonInclude.Include.ALWAYS)
public class OriginalRequirement {
    //-------------------------生产线信息-------------------------
    // 生产计划ID
    private String planId;
    // 生产任务ID
    private String taskId;
    // 归属成本项目
    private String costProject;

    // ----------------------课程信息----------------------
    // 教育领域
    private String educationField;
    // 学科
    private String subject;
    // 学段
    private String educationStage;
    // 年级
    private String grade;
    // 课程名称
    private String courseName;
    // 教材名称
    private String textbookName;

    // ----------------------丨 教学活动信息----------------------
    // 教学活动名称
    private String teachingActivity;
    // 完整的教学活动颗粒清单(文件上传)
    private String lessonGranulesFile;
    // 教学活动目的
    private String teachingPurpose;

    // ----------------------目标用户信息----------------------
    // 年龄段
    private List<String> ageRange;
    // 认知水平
    private List<String> cognitiveLevel;
    // 国家/地区
    private String countryRegion;
    // 语种
    private String language;
    // 业务情景
    private String businessScenario;
    // 发布平台
    private String publishPlatform;

    // ----------------------AI表演要求----------------------
    // AI老师IP要求
    private String teacherIpRequirement;
    // 美术风格要求
    private String artStyleRequirement;
    // 视频比例
    private String videoRatio;
    // 节目建议时长(s)
    private String suggestedDuration;
    // 播放设备
    private String playbackDevice;
    // 设备是否支持交互
    private String deviceSupportInteraction;

    // 关联的学习目标
    private List<Object> learningObjectives;
    // 涵养的知识点与知识内容
    private List<Object> knowledgeContents;
    // PPT
    private List<DentryInfo> pptFiles;

    // ----------------------* 对提供的教学内容的应用要求----------------------
    // 初始讲稿已经过需求方审核，无需进行风格化处理
    private Boolean stylized = false;
    // 存在必须使用提供的教学素材
    private Boolean mustUse = false;
    // 直接使用提供的教学步骤节目单
    private Boolean useProgram = false;
    // 无特殊应用要求
    private Boolean noSpecialApply = false;

    public Map<String, String> toParamDict() {
        Map<String, String> dict = Maps.newHashMap();

        // 教育领域
        if (educationField != null) {
            dict.put("education_field", educationField);
        }
        // 学科
        if (subject != null) {
            dict.put("subject", subject);
        }
        // 学段
        if (educationStage != null) {
            dict.put("education_level", educationStage);
        }
        // 课程名称
        if (courseName != null) {
            dict.put("course_name", courseName);
        }
        // 教材名称
        if (textbookName != null) {
            dict.put("textbook_name", textbookName);
        }
        // 年龄段
        if (CollectionUtils.isNotEmpty(ageRange)) {
            dict.put("age", String.join(",", ageRange));
        }
        // 认知水平
        if (CollectionUtils.isNotEmpty(cognitiveLevel)) {
            dict.put("cognitive_level", String.join(",", cognitiveLevel));
        }
        // 国家
        if (countryRegion != null) {
            dict.put("country", countryRegion);
        }
        // 语种
        if (language != null) {
            dict.put("language", language);
        }
        // 业务情景
        if (businessScenario != null) {
            dict.put("application_scenario", businessScenario);
        }
        // 发布平台
        if (publishPlatform != null) {
            dict.put("publish_platform", publishPlatform);
        }
        // 视频比例
        if (videoRatio != null) {
            dict.put("video_scale", videoRatio);
        }
        // 建议时长
        if (suggestedDuration != null) {
            dict.put("time", suggestedDuration);
        }
        // 播放设备
        if (playbackDevice != null) {
            dict.put("equipment", playbackDevice);
        }
        // 美术风格
        if (artStyleRequirement != null) {
            dict.put("material_style", artStyleRequirement);
        }
        // 年级
        if (grade != null) {
            dict.put("grade", grade);
        }
        // 风格化
        if (BooleanUtils.isTrue(stylized)) {
            dict.put("script_style", "已风格化");
        }

        return dict;
    }

}
