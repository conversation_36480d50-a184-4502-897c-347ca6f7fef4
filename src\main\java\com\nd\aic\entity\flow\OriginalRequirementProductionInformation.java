package com.nd.aic.entity.flow;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OriginalRequirementProductionInformation {
    // 生产计划ID
    private String planId;
    // 生产任务ID
    private String taskId;
    // 归属成本项目
    private String costProject;
}
