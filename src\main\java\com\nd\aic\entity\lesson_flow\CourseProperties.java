package com.nd.aic.entity.lesson_flow;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CourseProperties {

    private String operationsUrl;

    private String operationsId;

    // AI老师IP要求
    private String teacherIpRequirement;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        if (extProperties == null) {
            extProperties = Maps.newHashMap();
        }
        extProperties.put(key, value);
    }
}
