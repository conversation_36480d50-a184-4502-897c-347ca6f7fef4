package com.nd.aic.entity.lesson_flow;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Map;

import lombok.Data;

@Data
public class FlowCallbackArguments {
    private String sdpAppId;
    private String sdpBizType;
    private String suid;
    private String dataId;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        extProperties.put(key, value);
    }
}
