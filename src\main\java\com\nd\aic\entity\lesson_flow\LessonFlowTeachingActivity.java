package com.nd.aic.entity.lesson_flow;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.entity.flow.MaterialFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LessonFlowTeachingActivity {

    public static final String STATUS_INIT = "init";
    public static final String STATUS_PREPARE = "prepare";
    public static final String STATUS_AUTOMATING = "automating";
    public static final String STATUS_COMPLETED = "completed";

    private String id = UUID.randomUUID().toString();
    // 教学环节
    private String teachingStage;
    // 一级教学活动
    private String teachingActivity1;
    // 二级教学活动
    private String teachingActivity2;
    // 活动目的
    private String teachingPurpose;
    // 教学内容
    private String teachingContent;
    // 原始讲稿
    private String originalScriptContent;
    // 关联学习目标
    private String learningObjective;
    //素材需求
    private String materialRequirement;
    //活动参考时长
    private String activityDuration;
    // 建议资源形态
    private String suggestedResourceType;

    // 生产任务地址(BCS工作流表单地址)
    private String taskUrl;
    // 可视化生产过程ID参数
    private String automateLessonId;

    private List<MaterialNotes> materialNotes;
    private List<MaterialFile> materialFiles;

    private List<String> renderScriptIds;
    private String videoUrl;
    private String status = STATUS_INIT;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        extProperties.put(key, value);
    }
}
