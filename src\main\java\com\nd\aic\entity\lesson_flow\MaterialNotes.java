package com.nd.aic.entity.lesson_flow;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

import lombok.Data;

@Data
public class MaterialNotes {
    private String id;
    private String name;
    private String description;
    private String url;
    private String type;
    private Boolean required;
    private String text;
    @JsonProperty("associatedMaterial")
    private String associatedMaterial;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        extProperties.put(key, value);
    }
}
