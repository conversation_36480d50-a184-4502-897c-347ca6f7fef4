package com.nd.aic.enums;


import com.google.common.collect.Lists;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

import lombok.Getter;

/**
 * 角色
 */
@Getter
public enum CharacterEnum {

    COMMON("5fd878ba-1040-480a-bc21-b1d6a5086ed9", "美队", "common", "metahuman_renyun", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    PIXAR("47b23a4a-f491-42f6-ab20-edeed2cc6a26", "皮克斯", "pixar", "pixarNew", "pix_renyun", Lists.newArrayList("faceAniMaker", "faceAni2UE")),
    CHIBI("77975790-cbc0-4bf3-a62a-59b6b3227c45", "Q版-喵星人霓", "chibi", "neon", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    BA_BA_LA("0bb07793-810d-422a-aac6-043cec393a31", "芭芭拉", "chibi"),
    CC4Female("4E2E3BB342799D657F77ADB53B842284", "CC4女性老师", "npc", true, "japgirl", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    CC4Female21("57D3F9A04B754EB70CC743804EE75212", "CC4女性老师2-本地", "npc", true, "japgirl", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    CC4Female22("b88a03c8-b34f-4858-81c8-3d002f3fa5b8", "CC4女性老师2", "npc2", true, "japgirl", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    CC4Female3("03C3B7B24AF0A1AE45FDF799C4B96D69", "D老师提供角色测试男", "npc2", true, "japgirl", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    CC4Female4("5E328A0A45DFDDE59F1D8FA81E4A022B", "D老师提供角色测试女", "npc2", true, "japgirl", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    CC4Female5("FAFE97F24389F2C3157018BF38008149", "crf测试角色非洲男", "npc2", true, "japgirl", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    CC4Female6("9635F67C49D94042D985D89D8108EF4C", "crf测试角色非洲女", "npc2", true, "japgirl", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    AO_KE_MAN("c6c54148-1ddd-49cd-8d2a-6e5c15d75c65", "澳科大男老师", "common", "metahuman"),
    WHITE_CAT("ae0213cd-6e53-4188-9ddb-a41f8fda8674", "白猫", "whiteCat", true, "miao_wuyutong", Lists.newArrayList("faceAniMaker", "faceAni2UE"), 50),
    NEON("63d46702-d3ab-4bd0-8c70-40af230c0b39", "喵星人霓(新)", "neon", true,"neon_v2", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    NEON2("5800c372-a29e-4578-a7a1-5ce3120864ec", "喵星人霓(新)", "neon", true,"neon_v2", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    TANG_YU("CF52860D487F80A6C7667BA6DAD5A6D9", "唐钰", "common", false,"metahuman_tangyu", Lists.newArrayList("a2fMaker", "faceAni2UE")),
    ;

    private final String id;
    private final String name;
    private final String actionKey;
    private String newActionKey;
    private boolean isNpc;
    private String faceModel;
    private List<String> faceTools;
    private Integer cameraOffsetZ;

    CharacterEnum(String id, String name, String actionKey) {
        this.id = id;
        this.name = name;
        this.actionKey = actionKey;
    }

    CharacterEnum(String id, String name, String actionKey, String faceModel) {
        this.id = id;
        this.name = name;
        this.actionKey = actionKey;
        this.faceModel = faceModel;
    }

    CharacterEnum(String id, String name, String actionKey, String faceModel, List<String> faceTools) {
        this.id = id;
        this.name = name;
        this.actionKey = actionKey;
        this.faceModel = faceModel;
        this.faceTools = faceTools;
    }

    CharacterEnum(String id, String name, String actionKey, String newActionKey, String faceModel, List<String> faceTools) {
        this.id = id;
        this.name = name;
        this.actionKey = actionKey;
        this.newActionKey = newActionKey;
        this.faceModel = faceModel;
        this.faceTools = faceTools;
    }

    CharacterEnum(String id, String name, String actionKey, boolean isNpc, String faceModel, List<String> faceTools) {
        this.id = id;
        this.name = name;
        this.actionKey = actionKey;
        this.isNpc = isNpc;
        this.faceModel = faceModel;
        this.faceTools = faceTools;
    }

    CharacterEnum(String id, String name, String actionKey, boolean isNpc, String faceModel, List<String> faceTools, Integer cameraOffsetZ) {
        this.id = id;
        this.name = name;
        this.actionKey = actionKey;
        this.isNpc = isNpc;
        this.faceModel = faceModel;
        this.faceTools = faceTools;
        this.cameraOffsetZ = cameraOffsetZ;
    }

    public static CharacterEnum getCharacterById(String id) {
        for (CharacterEnum c : CharacterEnum.values()) {
            if (StringUtils.equals(c.getId(), id)) {
                return c;
            }
        }
        return null;
    }

    public static String getActionKeyById(String id, boolean isNewActionKey) {
        for (CharacterEnum c : CharacterEnum.values()) {
            if (StringUtils.equals(c.getId(), id)) {
                return isNewActionKey ? c.getNewActionKey() : c.getActionKey();
            }
        }
        return isNewActionKey ? CharacterEnum.COMMON.getNewActionKey() : CharacterEnum.COMMON.getActionKey();
    }
}
