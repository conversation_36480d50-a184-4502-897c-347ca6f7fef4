package com.nd.aic.enums;

import com.google.common.collect.Lists;

import com.nd.aic.service.performance.enhance.AbstractEnhancePerformanceHandler;
import com.nd.aic.service.performance.enhance.EmphasizeKeyPointsEnhanceHandler;
import com.nd.aic.service.performance.enhance.SetOffAtmosphereEnhanceHandler;

import java.util.List;

import lombok.Getter;

/**
 * 表演方案类型枚举
 */
@Getter
public enum EnhancePerformanceTypeEnum {

    EMPHASIZE_KEY_POINTS("强调重点", "强调重点", EmphasizeKeyPointsEnhanceHandler.class),
    SET_OFF_ATMOSPHERE("烘托氛围", "烘托氛围", SetOffAtmosphereEnhanceHandler.class),
    ;

    private final String category;
    private final String name;
    private final Class<? extends AbstractEnhancePerformanceHandler> handlerCls;

    EnhancePerformanceTypeEnum(String category, String name, Class<? extends AbstractEnhancePerformanceHandler> handlerCls) {
        this.category = category;
        this.name = name;
        this.handlerCls = handlerCls;
    }

    /**
     * 获取增强表演方案类型
     *
     * @param category 增强表演方案名称
     */
    public static List<EnhancePerformanceTypeEnum> getByCategory(String category) {
        List<EnhancePerformanceTypeEnum> list = Lists.newArrayList();
        for (EnhancePerformanceTypeEnum type : EnhancePerformanceTypeEnum.values()) {
            if (type.getCategory().equals(category)) {
                list.add(type);
            }
        }
        return list;
    }

    public static EnhancePerformanceTypeEnum getByName(String category, String name) {
        List<EnhancePerformanceTypeEnum> list = getByCategory(category);
        if (list.isEmpty()) {
            return null;
        }
        for (EnhancePerformanceTypeEnum type : list) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return list.get(0);
    }
}
