package com.nd.aic.enums;

import lombok.Getter;

/**
 * 素材类型枚举
 */
@Getter
public enum MaterialTypeEnum {
    
    /**
     * 图片素材
     */
    IMAGE("IMAGE", "图片"),
    
    /**
     * 视频素材
     */
    VIDEO("VIDEO", "视频"),
    
    /**
     * 音频素材
     */
    AUDIO("AUDIO", "音频"),
    
    /**
     * 文档素材
     */
    DOCUMENT("DOCUMENT", "文档"),
    
    /**
     * 其他类型
     */
    OTHER("OTHER", "其他");

    private final String code;
    private final String description;

    MaterialTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static MaterialTypeEnum fromCode(String code) {
        for (MaterialTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER;
    }
}
