package com.nd.aic.enums;


import com.nd.aic.service.performance.addition.AdditionAeImgTxtHandler;
import com.nd.aic.service.performance.addition.AdditionAeTextHandler;
import com.nd.aic.service.performance.addition.AdditionLottieImageHandler;
import com.nd.aic.service.performance.addition.AdditionLottieTextHandler;
import com.nd.aic.service.performance.addition.AdditionSoundEffectHandler;
import com.nd.aic.service.performance.addition.SeasonChangeAdditionHandler;
import com.nd.aic.service.performance.addition.TextBubbleAdditionHandler;
import com.nd.aic.service.performance.addition.TextDisplayAdditionHandler;
import com.nd.aic.service.performance.addition.TextFallAdditionHandler;
import com.nd.aic.service.performance.addition.WeatherChangeAdditionHandler;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * 增强表演方案类型枚举
 */
@Getter
public enum PerformanceAdditionTypeEnum {

    SEASONS_CHANGE("季节变化", SeasonChangeAdditionHandler.class),
    WEATHER_CHANGE("天气变化", WeatherChangeAdditionHandler.class),
    TEXT_DISPLAY("文字展示", TextDisplayAdditionHandler.class),
    TEXT_FALL("文字下落", TextFallAdditionHandler.class),
    TEXT_BUBBLE("文字气泡", TextBubbleAdditionHandler.class),
    TEXT_PERFORMANCE_LOTTIE("LOTTIE文字表演", AdditionLottieTextHandler.class),
    IMAGE_PERFORMANCE_LOTTIE("LOTTIE图片表演", AdditionLottieImageHandler.class),
    TEXT_PERFORMANCE_AE("VIDEO_TEXT文字表演", AdditionAeTextHandler.class),
    IMAGE_PERFORMANCE_AE("VIDEO_IMAGE图片表演", AdditionAeImgTxtHandler.class),
    SOUND_EFFECTS("SOUND_EFFECT", AdditionSoundEffectHandler.class),
    ;

    private final String name;
    private final Class<?> handlerCls;

    PerformanceAdditionTypeEnum(String name, Class<?> handlerCls) {
        this.name = name;
        this.handlerCls = handlerCls;
    }

    public static PerformanceAdditionTypeEnum getByName(String name) {
        name = StringUtils.trim(name);
        if (StringUtils.isNotBlank(name)) {
            for (PerformanceAdditionTypeEnum type : PerformanceAdditionTypeEnum.values()) {
                if (type.getName().equals(name)) {
                    return type;
                }
            }
        }
        return null;
    }
}
