package com.nd.aic.enums;

import com.nd.aic.common.BizConstant;
import com.nd.aic.service.performance.*;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * 表演方案类型枚举
 */
@Getter
public enum PerformanceTypeEnum {

    COMMON("基础讲解", CommonPerformanceHandler.class, "原地讲解"),
    BOARD_WRITING_EXPLANATION("板书讲解", BoardWritingExplanationHandler.class),
    PLAY_VIDEO("播放视频", PlayVideoHandler.class),
    DISPLAY_IMAGE("展示图片", DisplayImageHandler.class),
    APPEAR_BOOK("抛出书本", AppearBookHandler.class, true, 9800),
    THROW_OUT_BOOK("变出书本", ThrowOutBookHandler.class, true, 6000),
    GUIDE_TO_WATCH_VIDEO("引导观看视频", GuideToWatchVideoHandler.class, true, 11000),
    SHOW_DOCUMENT_COVER("展示文档封面", ShowDocumentCoverHandler.class, "二次元：展示文档封面"),
    SHOW_DOCUMENT_CONTENT("展示文档内容", ShowDocumentContentHandler.class, true, 5000),
    DISPLAY_KEYWORDS("关键字辅助讲解", DisplayKeywordsHandler.class),

    THROW_OUT_TOPIC("抛出课题艺术字", ThrowOutTopicHandler.class, true, 7000, "抛出课题"),
    ARTISTIC_TITLE_SHOW_KEY_INFO("艺术字展示问题关键信息", ShowArtisticTextQuestionKeyInfoHandler.class),
    SHOW_KEY_CONTENT_LEFT_RIGHT("左右小黑板展示关键字", ShowKeyContentBothSideHandler.class),
    STICKER_SHOW_MULTI_QUESTION("贴图展示多问题", ShowMultiQuestionBySticker.class),
    SHOW_MULTI_QUESTION("角色展示多个问题", ShowMultiQuestionHandler.class),
    ENCOURAGING_VISUAL_CANDY("给出鼓励视觉糖果", EncouragingVisualCandyHandler.class, true),
    GATHER_INFO_INTO_QUESTION_MARK("关键信息聚拢成问号", GatherInfoIntoQuestionMarkHandler.class, true, 9500),
    OPENING_CEREMONY("背向指屏幕开课仪式", OpeningCeremonyHandler.class, true, 3500),
    HANDLED_SIGN_TERMINATION_TASK("手持提示牌终止任务", HandHeldSignTerminationTaskHandler.class),
    MULTI_GROUP_BOARD_SHOW_CONTENT("多板书叠加辅助讲解", MultiGroupBoardShowContentHandler.class, true, 3000, "二次元：多板书叠加辅助讲解"),

    SHOW_PHRASES("多词条展示", ShowPhrasesHandler.class),
    SLANTED_BOARD_WRITING_WALKING("斜角度板书上前行", SlantedBoardWritingWalkingHandler.class, true, "二次元：斜角度板书上前行"),
    OVERHEAD_BOARD_WRITING_WALKING("俯视镜头下板书上前行", OverheadBoardWritingWalkingHandler.class, true, "二次元：俯视镜头下板书上前行"),
    FLOATING_BOARD_LECTURE("悬浮使用黑板讲解", FloatingBoardLectureHandler.class, true),
    BOOK_FLIPPING_BROWSING("书本翻页浏览", BookFlippingBrowsingHandler.class, "书本翻页"),
    WALK_FORWARD_MULTI_BOARD_SHOW("向前行走多版面展示内容", WalkForwardMultiBoardShowHandler.class, true, 8000),
    DISPLAY_CASE_NAME("展示案例名称", DisplayCaseNameHandler.class, "二次元：漫步微型场景放出案例名称"),
    STICKER_SHOW("贴图表演", StickerShowHandler.class, "通用贴图表演"),
    WALK_AND_FLIP_CARDS("横向行走翻牌讲解", WalkAndFlipCardsHandler.class, "二次元：横向行走翻牌讲解"),
    TEACHING_INTERACTION("教学互动", TeachingInteractionHandler.class, false),

    WALK_ACROSS_BOOK("视频前横向走过", WalkAcrossBookHandler.class),
    DISPLAY_ARTISTIC_TEXT_COVER("展示艺术字封面", DisplayArtisticTextCoverHandler.class, false, 6700, "二次元：展示艺术字封面"),
    POINT_TO_BLACKBOARD("手搓小黑板", PointToBlackboardHandler.class),
    FLY_TO_BLACKBOARD("飞向小黑板", FlyToBlackboardHandler.class, "飞向小黑板_全屏版"),
    MULTI_PANEL_DISPLAY_CONTENT("多版面展示内容", MultiPanelDisplayContentHandler.class, "二次元：多版面展示内容"),
    END_CREDITS_REVIEW("展示人物传记", EndCreditsReviewHandler.class, "展示人物传记"),
    SHOW_COUNTDOWN("展示倒计时", ShowCountdownHandler.class, true, "展示倒计时"),
    DISPLAY_QUESTIONS("展示思考题", DisplayQuestionsHandler.class, true, "二次元：展示题目"),
    FLOATING_FRAME("悬浮画框", FloatingFrameHandler.class, true, "悬浮画框"),
    LEFT_RIGHT_KEYWORD("角色驱动左右手关键词表演", LeftRightKeywordHandler.class, true, "角色驱动左右手关键词表演"),

    LEFT_RIGHT_BOOM_SHOW_CONTENT("角色驱动左右气泡呈现关键字", LeftRightBoomShowContentHandler.class),
    WALK_AND_KEY_GRAPHICS("角色横向行走关键图文辅助讲解", WalkAndKeyGraphicsHandler.class, true, "二次元：横向行走关键图文辅助"),
    SHOW_3D_CHART("展示3D图表", Show3dChartHandler.class, false),
    LEFT_RIGHT_SHOW_ICON("角色驱动左右手图标表演", LeftRightShowIconHandler.class, true, "二次元：左右手图标表演"),
    SWORD_FLYING("御剑飞行", SwordFlyingHandler.class, true, 5400),
    ROTATION_EFFECTS_ASSISTED_DEBUT("角色旋转特效辅助亮相", RotationEffectsAssistedDebutHandler.class, true, 4000, "角色旋转特效辅助亮相"),
    DISPLAY_IDENTITY_INFO("展示身份信息", DisplayIdentityInfoHandler.class, false, 2367, "二次元：展示身份信息"),
    FRONT_OF_SHOW_ISSUE("角色身前展示课题",FrontOfShowIssueHandler.class,false,"角色身前展示课题"),
    DISPLAY_THINKING_SYMBOL("角色抛出思考符号", DisplayThinkingSymbolHandler.class, false),
    COSTUME_CHANGE("撒花换装亮相", CostumeChangeHandler.class, false, 7100),
    CLOUD_MIST_TRANSITION("云雾转场", CloudMistTransitionHandler.class),

    HORIZONTAL_3D_PRESENTATION("横图立体呈现配合角色讲解", Horizontal3dPresentationHandler.class),
    SHOW_PAPER_COVER("展示纸质材料封面", ShowPaperCoverHandler.class),
    AdmissionHandler("入场", AdmissionHandler.class, true, 9700),
    ExitHandler("退场", ExitHandler.class, true, 8400),
    VOICE_OVER_HANDLER("画外音", VoiceOverHandler.class),
    FULL_BOARD("全屏板书", FullBoardHandler.class, "二次元：全屏版小黑板展示"),
    END_FADEOUT("结束淡出", FadeoutHandler.class, false, BizConstant.TIME_STRETCH),

    IDLE("角色待机", IdleHandler.class, false,  "待机表演"),

    // 霓NEON表演方案
    NEON_ADMISSION("开场", AdmissionHandler.class, true, 11700),
    NEON_EXPLAIN_WITH_VIDEO("配合视频讲解", NeonExplainWithVideoHandler.class, true),
    NEON_EXPLAIN_TO_VIDEO("对着视频讲解", NeonExplainToVideoHandler.class, true),
    NEON_SHOW_TITLE_VIDEO("展示标题", NeonShowTitleHandler.class, true),
    NEON_SHOW_IMAGES_VIDEO("展示三个图片讲解", NeonShowThreeImagesHandler.class, true),
    NEON_SHOW_IMAGE_VIDEO_MATERIAL("展示图片视频素材", NeonShowImageVideoHandler.class, true),
    NEON_SHOW_IMAGE_VIDEO_LR("左右展示两个素材", NeonShowImageVideoLRHandler.class, true),
    NEON_SHOW_IMAGE_VIDEO_LB("左下方召唤小黑板", NeonShowImageVideoLBHandler.class, true),

    GUIDE_AND_WATCH_VIDEO("引导并观看视频", GuideAndWatchVideoHandler.class, false),
    IMAGE_STACK_SHOW("多图片叠加展示", ImageStackShowHandler.class, false),
    // ------------------------------------- 分割线 -------------------------------------------------


    KEYWORD_SHOW("单个关键字展示", DisplaySingleKeywordHandler.class, "二次元：单个关键字展示"),
    ROLE_SHOW_STICK("角色驱动符号表演", RoleShowSymbolHandler.class, "二次元：角色驱动符号表演"),
    SHOW_QR_CODE("抛出二维码", ShowQRCodeHandler.class, true, 120000),
    ICON_IN_HAND("左右手图标表演", IconInHandHandler.class),
    SHOW_KEY_CONTENT_LEFT_RIGHT_IMAGE("左右小黑板展示内容", ShowKeyContentLeftRightHandler.class, "二次元：左右小黑板展示内容"),
    BLACKBOARD_SHOW("二次元：小黑板展示定义", BlackboardShowHandler.class, "小黑板展示定义"),
    OPENING_DRIVE("开车入场", OpeningDriverHandler.class, true, 5000),
    OPENING_RIDE("骑马入场", OpeningRideHandler.class, true, 5000),
    PLAY_ALPHA_VIDEO("播放透明视频", PlayAlphaHandler.class),
    TOUR_MAP("3D模型分屏表演", SplitScreenHandler.class),
    EMOJI_SHOW("贴图动作分屏表演", StickerMotionSplitHandler.class, "二次元：贴图动画分屏表演"),
    SHOW_CASE_NAME("显示案例名称", ShowCaseNameHandler.class, "二次元：漫步微型场景放出案例名称"),
    MULTI_IMAGE_DISPLAY("多图立体呈现", MultiImageDisplayHandler.class, "二次元：多图立体呈现"),
    SHOW_COUNT_DOWN_NUMBER("展示倒计时数字", ShowCountdownNumberHandler.class, false),
    RPG_STORY("RPG故事演义", RPGStoryHandler.class, "二次元：RPG故事演绎"),
    MINI_BOARD_SHOW_CONTENT("小黑板展示内容", MiniBoardShowContentHandler.class, "二次元：小黑板展示内容"),
    MINI_BOARD_SHOW_CONTENT_LOTTIE("小黑板展示内容（Lottie）", MiniBoardShowContentByLottieHandler.class, "二次元：小黑板展示内容"),
    // MULTI_BOARD_SHOW_CONTENT("多版面展示内容", MultiBoardShowContentHandler.class, "二次元：多版面展示内容"),
    // HAND_RUB_BLACKBOARD("手搓小黑板", HandRubBlackboardHandler.class, "手刷小黑板表演"),
    DISPLAY_TEXTBOOK_CONTENT("展示教材内容", DisplayTextbookContentHandler.class, "二次元：展示教材内容"),
    STUDENT_STICKER_PERFORMANCE("学生贴图表演", StudentStickerPerformanceHandler.class, "二次元：学生贴图表演"),
    WORKFLOW_SHOW("工作流程展示", WorkflowShowHandler.class, "二次元：流程图表演"),
    WALK_AND_TALK("边走边讲解", WalkAndTalkHandler.class),
    LEFT_RIGHT_BUBBLES("左右气泡表演", SidesBubblesHandler.class),

    // 通用WEB表演方案
    UNIVERSAL_WEB("通用WEB表演", UniversalWebPerformanceHandler.class, "web表演方案接入");
    private final String name;
    private final Class<?> handlerCls;
    private boolean isBigRoutine = false;

    /**
     * 不等于0表示表演方案是固定时长，单位毫秒
     */
    private long duration;
    private String alias;

    PerformanceTypeEnum(String name, Class<?> handlerCls) {
        this.name = name;
        this.handlerCls = handlerCls;
    }

    PerformanceTypeEnum(String name, Class<?> handlerCls, boolean isBigRoutine) {
        this.name = name;
        this.handlerCls = handlerCls;
        this.isBigRoutine = isBigRoutine;
    }


    PerformanceTypeEnum(String name, Class<?> handlerCls, boolean isBigRoutine, long duration) {
        this.name = name;
        this.handlerCls = handlerCls;
        this.isBigRoutine = isBigRoutine;
        this.duration = duration;
    }


    PerformanceTypeEnum(String name, Class<?> handlerCls, String alias) {
        this.name = name;
        this.handlerCls = handlerCls;
        this.alias = alias;
    }


    PerformanceTypeEnum(String name, Class<?> handlerCls, boolean isBigRoutine, String alias) {
        this.name = name;
        this.handlerCls = handlerCls;
        this.isBigRoutine = isBigRoutine;
        this.alias = alias;
    }

    PerformanceTypeEnum(String name, Class<?> handlerCls, boolean isBigRoutine, long duration, String alias) {
        this.name = name;
        this.handlerCls = handlerCls;
        this.isBigRoutine = isBigRoutine;
        this.duration = duration;
        this.alias = alias;
    }

    /**
     * 获取表演方案类型
     *
     * @param name 表演方案名称
     */
    public static PerformanceTypeEnum getByName(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (PerformanceTypeEnum type : PerformanceTypeEnum.values()) {
                if (type.getName().equals(name) || StringUtils.equals(type.getAlias(), name)) {
                    return type;
                }
            }
        }
        return PerformanceTypeEnum.COMMON;
    }

    /**
     * 获取表演方案类型
     *
     * @param name 表演方案名称
     */
    public static PerformanceTypeEnum getByNameAndAlias(String name, String alias) {
        if (StringUtils.isNotBlank(name)) {
            for (PerformanceTypeEnum type : PerformanceTypeEnum.values()) {
                if (type.getName().equals(name) || StringUtils.equals(type.getAlias(), name)) {
                    return type;
                }
            }
        }

        if (StringUtils.isNotBlank(alias)) {
            for (PerformanceTypeEnum type : PerformanceTypeEnum.values()) {
                if (type.getName().equals(alias) || StringUtils.equals(type.getAlias(), alias)) {
                    return type;
                }
            }
        }

        return PerformanceTypeEnum.COMMON;
    }
}
