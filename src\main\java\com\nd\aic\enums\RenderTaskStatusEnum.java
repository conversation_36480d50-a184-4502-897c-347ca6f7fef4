package com.nd.aic.enums;

import lombok.Getter;

@Getter
public enum RenderTaskStatusEnum {

    PREPARING(-1, "准备中"),
    PENDING(0, "待执行"),
    IN_PROGRESS(1, "进行中"),
    FAILED(2, "失败"),
    COMPLETED(3, "已完成");

    private final int code;
    private final String description;

    RenderTaskStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据 code 查找枚举
     */
    public static RenderTaskStatusEnum fromCode(int code) {
        for (RenderTaskStatusEnum status : RenderTaskStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

}
