package com.nd.aic.enums;

import lombok.Getter;

/**
 * 套路类型
 */
@Getter
public enum RoutineTypeEnum {

    /**
     * 大套路
     */
    BigRoutine("BigRoutine"),

    /**
     * 屏幕特效
     */
    ScreenEffect("ScreenEffect"),

    /**
     * 角色套路
     */
    CharacterAction("CharacterAction"),

    /**
     * 场景套路
     */
    SceneAction("SceneAction"),

    /**
     * 动作套路
     */
    Animation("Animation"),

    /**
     * 设置套路参数
     */
    Setting("Setting")
    ;

    private final String name;

    RoutineTypeEnum(String name) {
        this.name = name;
    }
}
