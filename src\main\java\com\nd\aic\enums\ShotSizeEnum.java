package com.nd.aic.enums;

import lombok.Getter;

/**
 * 景别
 */
@Getter
public enum ShotSizeEnum {

    EXTREME_CLOSE_UP("extremeCloseUp", "特写"),
    CLOSE_UP("closeUp", "近景"),
    MEDIUM_CLOSE_UP("mediumCloseUp", "中近景"),
    MEDIUM_SHOT("mediumShot", "中景"),
    FULL_SHOT("fullShot", "全景"),
    LONG_SHOT("longShot", "远景"),
    EXTREME_LONG_SHOT("extremeLongShot", "大远景"),
    ;

    private final String value;
    private final String name;

    ShotSizeEnum(String value, String content) {
        this.value = value;
        this.name = content;
    }
}
