package com.nd.aic.enums;

import lombok.Getter;

@Getter
public enum StandPositionEnum {

    /**
     * 任意位置
     */
    WHATEVER("whatever"),
    /**
     * 左侧构图
     */
    LEFT("left"),
    /**
     * 中间构图
     */
    MIDDLE("middle"),
    /**
     * 向左移动
     */
    MOVE_LEFT("move_left"),
    /**
     * 向右移动
     */
    MOVE_RIGHT("move_right"),
    /**
     * 右侧构图
     */
    RIGHT("right");

    private final String standPosition;

    StandPositionEnum(String standPosition) {
        this.standPosition = standPosition;
    }

    public static StandPositionEnum getByName(String standPosition) {
        for (StandPositionEnum value : StandPositionEnum.values()) {
            if (value.getStandPosition().equals(standPosition)) {
                return value;
            }
        }
        return null;
    }

}
