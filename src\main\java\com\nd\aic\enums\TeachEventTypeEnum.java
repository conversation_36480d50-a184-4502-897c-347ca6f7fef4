package com.nd.aic.enums;

import com.google.common.collect.Lists;

import java.util.List;

import lombok.Getter;

@Getter
public enum TeachEventTypeEnum {
    TEACHER_ENTRY("教师入场", Lists.newArrayList()),
    COMMON("通用", Lists.newArrayList());

    private final String name;
    private final List<PerformanceTypeEnum> performanceTypes;

    TeachEventTypeEnum(String name, List<PerformanceTypeEnum> performanceTypes) {
        this.name = name;
        this.performanceTypes = performanceTypes;
    }

    public static TeachEventTypeEnum getByName(String name) {
        for (TeachEventTypeEnum type : TeachEventTypeEnum.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return TeachEventTypeEnum.COMMON;

    }
}
