package com.nd.aic.exception;

import com.nd.gaea.WafException;

import org.springframework.http.HttpStatus;

public class AicException extends WafException {

    public AicException(String code, String message, HttpStatus status) {
        super(code, message, status);
    }

    public AicException(String message, HttpStatus status) {
        super(message, message, status);
    }

    public static AicException ofIllegalArgument(String message) {
        return new AicException("AIC/INVALID_ARGUMENT", message, HttpStatus.BAD_REQUEST);
    }

    public static AicException of(String message) {
        return new AicException("AIC/ERROR", message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public static AicException of(String message, HttpStatus status) {
        return new AicException(message, status);
    }

}
