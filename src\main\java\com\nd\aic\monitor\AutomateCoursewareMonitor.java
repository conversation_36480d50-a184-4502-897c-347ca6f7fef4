package com.nd.aic.monitor;

import com.google.common.collect.Lists;

import com.nd.aic.client.AIHubFeignClient;
import com.nd.aic.client.pojo.aihub.AIHubTaskResult;
import com.nd.aic.entity.AutomateCourseware;
import com.nd.aic.service.AutomateCoursewareService;
import com.nd.gaea.rest.support.WafContext;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class AutomateCoursewareMonitor {

    private final AutomateCoursewareService automateCoursewareService;
    private final AIHubFeignClient aiHubFeignClient;

    @SchedulerLock(name = "AutomateCoursewareMonitor", lockAtLeastFor = "5s")
    @Scheduled(cron = "0/10 * * * * ?")
    public void execute() {
        if (!WafContext.isProductStage()) {
            return;
        }
        List<AutomateCourseware> coursewares = automateCoursewareService.findByAiTaskStatusIn(Lists.newArrayList(AIHubTaskResult.STATUS_INIT, AIHubTaskResult.STATUS_RUNNING));
        for (AutomateCourseware courseware : coursewares) {
            String botId = courseware.getBotId();
            String aiTaskId = courseware.getAiTaskId();
            String aiTaskStatus = courseware.getAiTaskStatus();
            if (StringUtils.isNotBlank(botId) && StringUtils.isNotBlank(aiTaskId)) {
                AIHubTaskResult result = aiHubFeignClient.queryTask(botId, aiTaskId);
                if (!StringUtils.equals(aiTaskStatus, result.getStatus())) {
                    AutomateCourseware newCourseware = new AutomateCourseware();
                    newCourseware.setId(courseware.getId());
                    newCourseware.setAiTaskStatus(result.getStatus());
                    if (result.getAnswer() != null && StringUtils.isNotBlank(result.getAnswer().getErrorMessage())) {
                        newCourseware.setAiTaskErrMsg(result.getAnswer().getErrorMessage());
                    }
                    automateCoursewareService.update(newCourseware, true);
                    continue;
                }
                if (courseware.getCreateAt() != null) {
                    // 1天
                    long expectedInMillis = DateUtils.MILLIS_PER_DAY;
                    long diffInMillis = System.currentTimeMillis() - courseware.getCreateAt().getTime();
                    if (diffInMillis > expectedInMillis) {
                        AutomateCourseware newCourseware = new AutomateCourseware();
                        newCourseware.setId(courseware.getId());
                        newCourseware.setAiTaskStatus(AIHubTaskResult.STATUS_FAILED);
                        newCourseware.setAiTaskErrMsg("超时");
                        automateCoursewareService.update(newCourseware, true);
                    }
                }
            }
        }
    }
}
