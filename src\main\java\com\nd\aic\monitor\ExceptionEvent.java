package com.nd.aic.monitor;

import org.springframework.context.ApplicationEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Setter
@Getter
public class ExceptionEvent extends ApplicationEvent {

    private String type;
    private String errorMsg;
    private String extraMsg;
    private String title;
    private String userId;
    private String renderTaskId;
    private Throwable exception;

    public ExceptionEvent(String type, String errorMsg, String extraMsg, String title, String userId, String renderTaskId, Throwable exception) {
        super("ExceptionEvent");
        this.type = type;
        this.errorMsg = errorMsg;
        this.extraMsg = extraMsg;
        this.title = title;
        this.userId = userId;
        this.renderTaskId = renderTaskId;
        this.exception = exception;
    }
}
