package com.nd.aic.monitor;

import com.google.common.collect.Lists;

import com.nd.aic.client.FastGptFeignClient;
import com.nd.aic.client.pojo.chat.ChatCompletion;
import com.nd.aic.client.pojo.chat.ChatCompletionResponse;
import com.nd.aic.client.pojo.chat.ChatMessage;
import com.nd.aic.service.ImAgentService;
import com.nd.gaea.rest.support.WafContext;
import com.nd.gaea.util.WafJsonMapper;
import com.nd.uc.sdk.org.entity.UserInfo;
import com.nd.uc.sdk.org.service.OrgAccountService;

import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class ExceptionEventListener implements ApplicationListener<ExceptionEvent> {

    private final ImAgentService imAgentService;
    private final FastGptFeignClient fastGptFeignClient;
    private final OrgAccountService orgAccountService;

    @Async
    @Override
    public void onApplicationEvent(ExceptionEvent event) {
        String message = "";
        try {
            ChatCompletion chatCompletion = new ChatCompletion();
            chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(event.getType())));
            ChatCompletionResponse response = fastGptFeignClient.askForImTemplate(chatCompletion);
            String content = StringUtils.trim(response.getChoices().get(0).getMessage().getContent());
            if (StringUtils.isBlank(content)) {
                return;
            }
            Map<String, String> valuesMap = new HashMap<>(8);
            valuesMap.put("env", WafContext.getEnvironment());
            valuesMap.put("errorMsg", escape(StringUtils.abbreviate(StringUtils.defaultString(event.getErrorMsg()), 350)));
            valuesMap.put("extraMsg", StringUtils.defaultIfBlank(event.getExtraMsg(), StringUtils.EMPTY));
            valuesMap.put("renderTaskId", StringUtils.defaultIfBlank(event.getRenderTaskId(), StringUtils.EMPTY));
            valuesMap.put("title", escape(StringUtils.defaultIfBlank(event.getTitle(), StringUtils.EMPTY)));
            String userId = event.getUserId();
            if (StringUtils.isNotBlank(userId)) {
                try {
                    UserInfo userInfo = orgAccountService.getUserInfo(Long.parseLong(userId));
                    valuesMap.put("userName", escape(StringUtils.defaultIfBlank(userInfo.getNickName(), userInfo.getRealName())));
                } catch (Exception ignore) {
                    // ignore
                }
            } else {
                valuesMap.put("userName", "");
            }
            Throwable e = event.getException();
            if (e != null) {
                List<String> result = new ArrayList<>();
                String[] frames = ExceptionUtils.getStackFrames(e);
                for (String frame : frames) {
                    if (StringUtils.contains(frame, "com.nd.aic")) {
                        result.add(StringUtils.trim(escape(frame)));
                        if (result.size() == 5) {
                            break;
                        }
                    }
                }
                valuesMap.put("stacks", StringUtils.join(result, "\\r\\n"));
            }
            StringSubstitutor sub = new StringSubstitutor(valuesMap);
            message = sub.replace(content);
            imAgentService.send(message);
        } catch (Exception e) {
            log.error(message, e);
        }
    }

    @SneakyThrows
    private String escape(String str) {
        return StringUtils.replace(StringEscapeUtils.escapeXml10(StringUtils.strip(WafJsonMapper.toJson(str), "\"")), "\\", "\\\\");
    }

}
