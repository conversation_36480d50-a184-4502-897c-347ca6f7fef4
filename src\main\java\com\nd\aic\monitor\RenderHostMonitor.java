package com.nd.aic.monitor;

import com.nd.aic.entity.RenderHost;
import com.nd.aic.entity.RenderTask;
import com.nd.aic.enums.RenderHostStatusEnum;
import com.nd.aic.service.RenderHostService;
import com.nd.aic.service.RenderTaskService;
import com.nd.aic.util.TimeUtils;
import com.nd.gaea.client.ApplicationContextUtil;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class RenderHostMonitor {

    private final RenderHostService renderHostService;
    private final RenderTaskService renderTaskService;

    @SchedulerLock(name = "RenderHostMonitor", lockAtLeastFor = "1m")
    @Scheduled(cron = "0 0 10-20 ? * MON-FRI")
    public void execute() {
        List<RenderHost> hosts = renderHostService.findAllRenderHost();
        for (RenderHost host : hosts) {
            if (BooleanUtils.isNotFalse(host.getEnabled()) && BooleanUtils.isNotTrue(host.getAutoAdd()) && RenderHostStatusEnum.RENDERING.equals(host.getCurrentStatus())) {
                if (host.getStartWorkTime() != null) {

                    long diffInMillis = System.currentTimeMillis() - host.getStartWorkTime().getTime();
                    if (isRenderTaskTimeout(host, diffInMillis)) {
                        long diffInMinutes = diffInMillis / DateUtils.MILLIS_PER_MINUTE;
                        ApplicationContextUtil.getApplicationContext()
                                .publishEvent(ExceptionEvent.builder()
                                        .type("3")
                                        .errorMsg("主机【" + host.getIpAddress() + "】已渲染超过 " + diffInMinutes + " 分钟")
                                        .build());
                    }
                }
            }
        }

    }

    private boolean isRenderTaskTimeout(RenderHost host, long diffInMillis) {
        long expectedInMillis = 3 * DateUtils.MILLIS_PER_HOUR;
        RenderTask renderTask = renderTaskService.findOne(host.getCurrentTaskId());
        if (renderTask != null && renderTask.getCoursewareModel() != null && StringUtils.isNotBlank(renderTask.getCoursewareModel().getLength())) {
            String totalLength = TimeUtils.timeConvert(renderTask.getCoursewareModel().getLength(), null);
            if (StringUtils.isNotBlank(totalLength)) {
                long totalInMillis = TimeUtils.convertToMilliseconds(totalLength);
                // 预期时长大约脚本时长的10倍
                expectedInMillis = Math.min(Math.max(totalInMillis * 10, 20 * DateUtils.MILLIS_PER_MINUTE), expectedInMillis);
            }
        }
        return diffInMillis > expectedInMillis;
    }
}
