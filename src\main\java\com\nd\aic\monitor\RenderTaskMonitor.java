package com.nd.aic.monitor;

import com.nd.aic.entity.RenderHost;
import com.nd.aic.entity.RenderTask;
import com.nd.aic.enums.RenderTaskStatusEnum;
import com.nd.aic.service.RenderHostService;
import com.nd.aic.service.RenderTaskService;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class RenderTaskMonitor {

    private final RenderHostService renderHostService;
    private final RenderTaskService renderTaskService;

    @SchedulerLock(name = "RenderTaskMonitor", lockAtLeastFor = "1m")
    @Scheduled(cron = "0 0/10 * * * ?")
    public void execute() {
        List<RenderTask> renderTasks = renderTaskService.findByStatus(RenderTaskStatusEnum.IN_PROGRESS, 100);
        for (RenderTask renderTask : renderTasks) {
            if (renderTask.getStartTime() != null) {
                // 3小时
                long expectedInMillis = 3 * DateUtils.MILLIS_PER_HOUR;
                // 2天
                long needDeletedInMillis = 2 * DateUtils.MILLIS_PER_DAY;
                long diffInMillis = System.currentTimeMillis() - renderTask.getStartTime().getTime();
                // 超时过久，需删除任务
                if (diffInMillis > needDeletedInMillis) {
                    renderTaskService.deleteTask(renderTask.getId());
                }
                // 渲染超时
                else if (diffInMillis > expectedInMillis) {
                    RenderHost renderHost = renderHostService.findRenderHostByTaskId(renderTask.getId());
                    // 未找到对应任务的渲染主机
                    if (renderHost == null) {
                        renderTaskService.finishTask(renderTask.getId(), null, "渲染中断");
                    }
                }
            }
        }
    }
}
