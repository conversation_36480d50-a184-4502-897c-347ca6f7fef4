package com.nd.aic.monitor;

import com.google.common.collect.Lists;

import com.nd.aic.entity.SgTask;
import com.nd.aic.enums.SgTaskStatusEnum;
import com.nd.aic.service.SgTaskService;
import com.nd.gaea.client.ApplicationContextUtil;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class SgTaskMonitor {

    private final SgTaskService sgTaskService;

    @SchedulerLock(name = "SgTaskMonitor", lockAtLeastFor = "1m")
    @Scheduled(cron = "0 30 10-20 ? * MON-FRI")
    public void execute() {
        List<SgTask> sgTasks = sgTaskService.findAllCookingTask();
        List<String> cookNdrIds = Lists.newArrayList();
        List<String> pendingIds = Lists.newArrayList();
        long cookHours = 0;
        for (SgTask sgTask : sgTasks) {
            if (SgTaskStatusEnum.PENDING.equals(sgTask.getStatus()) || SgTaskStatusEnum.LOCKING.equals(sgTask.getStatus())) {
                long diffInMillis = System.currentTimeMillis() - sgTask.getCreateTime().getTime();
                long diffInMinutes = diffInMillis / DateUtils.MILLIS_PER_MINUTE;
                long timeoutMinutes = 15;
                if (diffInMinutes >= timeoutMinutes) {
                    pendingIds.add(sgTask.getId());
                }
            } else if (SgTaskStatusEnum.COOKING.equals(sgTask.getStatus()) && sgTask.getCookTime() != null) {
                long diffInMillis = System.currentTimeMillis() - sgTask.getCookTime().getTime();
                long diffInHours = diffInMillis / DateUtils.MILLIS_PER_HOUR;
                if (diffInHours > 3 * 24) {
                    sgTask.setStatus(SgTaskStatusEnum.FAILED);
                    sgTask.setErrorMsg("COOK任务超时");
                    sgTask.setFinishTime(new Date());
                    sgTaskService.save(sgTask);
                    continue;
                }
                long timeoutHours = 1;
                if (diffInHours >= timeoutHours) {
                    cookNdrIds.add(sgTask.getSgNdrId());
                }
                cookHours = Math.max(diffInHours, cookHours);
            }
        }
        if (CollectionUtils.isNotEmpty(cookNdrIds)) {
            ApplicationContextUtil.getApplicationContext()
                    .publishEvent(ExceptionEvent.builder()
                            .type("5")
                            .errorMsg("口型资源【" + StringUtils.abbreviate(StringUtils.join(cookNdrIds, "，"), 300) + "】，已 COOK 超过 " + cookHours + " 小时")
                            .build());
        }

        if (CollectionUtils.isNotEmpty(pendingIds)) {
            ApplicationContextUtil.getApplicationContext().publishEvent(ExceptionEvent.builder().type("5").errorMsg("口型任务，被锁定超过 15 分钟").build());
        }
    }

}
