package com.nd.aic.pojo;

import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AutomateTask {
    @NotBlank(message = "ID不能为空")
    private String id;
    private Date createAt;
    private Date updateAt;
    private String creator;
    // 来源，教学活动设计业务配置表单
    @NotBlank(message = "来源不能为空")
    private String source;
    // 生产线实例ID，返工不改变
    @NotBlank(message = "生产线实例ID不能为空")
    private String workflowInstanceId;
    // 执行ID，返工改变
    @NotBlank(message = "执行ID不能为空")
    private String executionId;
    // 教学活动ID
    private String teachingActivityId;
    // 教学活动名称
    private String teachingActivityName;
    // 学习目标
    private String learningObject;
    // 角色
    private String character;
    // 场景
    private String scene;
    // 项目ID
    private String projectId;
    // 项目名称
    private String projectName;
    // 课程ID
    private String courseId;
    // 课程名称
    private String courseName;

    // 以下为输入
    private List<Program> programs;
    // 节目单索引
    private Integer programIndex;
    // 节目单ID
    @NotBlank(message = "节目单ID不能为空")
    private String programId;
    @NotBlank(message = "节目单名称不能为空")
    private String programName;
    @NotBlank(message = "节目单内容不能为空")
    private String programContent;
    @NotBlank(message = "原始讲稿不能为空")
    private String originalScript;
    // @NotBlank
    private String programKnowledge;
    private String programExpectedOutcome;
    private String suggestTime;

    // 生产线URL
    private String workflowUrl;
    // 教学活动生产需求
    private String requirementUrl;
    // 确认教学活动方案
    private String designUrl;
    // 确认教学活动设计
    private String activityUrl;
    // 匹配节目实施方案
    private String implPlanUrl;

    @Data
    public static class Program {
        private Integer programIndex;
        // 节目单ID
        @NotBlank(message = "节目单ID不能为空")
        private String programId;
        private String programName;
        private String programContent;
    }
}
