package com.nd.aic.pojo;

import com.nd.aic.entity.basic.TeachingActivity;
import com.nd.aic.entity.basic.TeachingEvent;

import java.util.List;

import javax.validation.Valid;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BasicTeachingActivity extends TeachingActivity {

    @Valid
    private List<TeachingEvent> includeEvents;
    private List<TeachingEvent> excludeEvents;
}
