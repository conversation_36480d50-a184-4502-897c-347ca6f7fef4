package com.nd.aic.pojo;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.entity.TimeArea;
import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.enums.PerformanceTypeEnum;
import com.nd.aic.pojo.dto.ActionResourceDTO;
import com.nd.aic.pojo.dto.CoursewareResourceConfigDTO;
import com.nd.aic.pojo.dto.CoursewareResourceDTO;
import com.nd.aic.pojo.dto.ScenePositionResourceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;

import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;

@Data
public class CoursewareGenerationContext {


    /**
     * 原始请求
     */
    private CoursewareGenerationReq coursewareGenerationReq;

    /**
     * 课件标题
     */
    private String title;

    // ------------------- 以下是NDR音频资源 -------------------

    /**
     * NDR库id
     */
    private String containerId;

    /**
     * NDR资源id
     */
    private String resourceId;

    /**
     * 音频资源时长，格式 03:14.00
     */
    private String totalLength;

    /**
     * 音频地址
     */
    private String audioUrl;

    private String audioText;

    /**
     * 行歌词
     */
    private String lineLyric;

    private List<String> lineLyricList;

    /**
     * NDR 混音了BGM的讲话音频
     */
    private String bgmResourceId;

    /**
     * NDR 背景音乐资源
     */
    private String backgroundMusicResourceId;

    /**
     * NDR 环境音
     */
    private String ambientSoundResource;

    /**
     * NDR 字幕资源
     */
    private String subtitleResourceId;

    // ------------------- 以下是批注参数相关 -------------------

    /**
     * 全局场景ID
     */
    private String globalScene;

    /**
     * 全局角色ID
     */
    private String globalCharacter;

    /**
     * 角色衣服ID
     */
    private String globalClothing;

    /**
     * 出生点
     */
    private String birthPointName;

    /**
     * 出生点
     */
    private JSONObject birthPosition;

    /**
     * 是否生成角色
     */
    private Boolean characterGenerationEnabled = Boolean.TRUE;

    /**
     * 是否生成服装
     */
    private Boolean clothingGenerationEnabled = Boolean.TRUE;

    /**
     * 是否使用新动作
     */
    private Boolean newActionEnabled = Boolean.FALSE;

    /**
     * 灯光
     */
    private String light;
    // ------------------- 以下是表演方案批注相关 -------------------

    /**
     * 教学事件批注列表
     */
    private List<TeachEventTypeDTO> teachEventTypes;


    List<AtaUtteranceWithPos> ataUtterances;

    private List<AtaWordWithPos> ataWords;

    /**
     * PPT对应的板书视频资源
     */
    private List<String> pptVideoResources;

    /**
     * 课件资源配置
     */
    private CoursewareResourceConfigDTO coursewareResourceConfig = new CoursewareResourceConfigDTO();

    //------------------- 以下是场景相关 -------------------

    /**
     * 当前场景ID
     */
    private String currentSceneId;

    /**
     * 当前角色资源ID
     */
    private String currentCharacterResId;

    /**
     * 当前角色点位
     */
    private BirthPointSegment currentBirthPoint;

    /**
     * 当前镜头ID
     */
    private String currentCameraId;

    /**
     * 当前CC角色
     */
    private SceneObjectSegment currentNpcCharacter;

    /**
     * 当前CC角色位置
     */
    private boolean currentNpcCharacterInRight = true;


    // ------------------- 以下是板书相关 -------------------

    /**
     * 板书 Segment
     */
    private SceneObjectSegment currentBoardSegment;

    /**
     * 板书实例ID
     */
    private String currentBoardInstanceId;

    // ------------------- 以下是字幕相关 -------------------

    /**
     * time areas to hide subtitle
     */
    private Collection<TimeArea> timeAreasOfHideSubtitle = new ArrayList<>();

    // 禁用AE增加表演
    private Boolean disableAdditionAes = Boolean.FALSE;
    // 业务来源，用于处理实验室的需求定制
    private String bizFlag;
    // 图片增加表演时间范围记录，用于排除重叠的图片增加表演
    private TimeArea latestAdditionImageTimeArea;

    /**
     * 最近互动触发时间(毫秒)
     */
    private Long lastInteractionTime;
    
    /**
     * 获取角色实例ID
     */
    public String getCharacterInstanceId() {
        return getCurrentNpcCharacter() != null ? getCurrentNpcCharacter().getInstanceId() : null;
    }

    public List<ActionResourceDTO> getCompatibleActions() {
        return getCompatibleActions(false);
    }

    public List<ActionResourceDTO> getCompatibleActions(boolean newActionEnabled) {
        String actionKey = CharacterEnum.getActionKeyById(currentCharacterResId, newActionEnabled);
        List<ActionResourceDTO> actions = getCoursewareResourceConfig().getActionGroups().get(actionKey);
        return actions != null ? actions : new ArrayList<>(0);
    }

    public List<CoursewareResourceDTO> getCompatibleClothes() {
        String actionKey = CharacterEnum.getActionKeyById(currentCharacterResId, false);
        List<CoursewareResourceDTO> clothes = getCoursewareResourceConfig().getClothesGroups().get(actionKey);
        return clothes != null ? clothes : new ArrayList<>(0);
    }

    /**
     * 是否第一个教学事件类型
     */
    public boolean isFirstTeachEventType(TeachEventTypeDTO teachEventType) {
        if (CollectionUtils.isEmpty(teachEventTypes)) {
            return false;
        }
        List<TeachEventTypeDTO> filteredEvents = teachEventTypes.stream()
                .filter(item -> !PerformanceTypeEnum.END_FADEOUT.getName().equals(item.getPerformanceType()))
                .collect(Collectors.toList());
        return !filteredEvents.isEmpty() && filteredEvents.get(0).equals(teachEventType);
    }

    /**
     * 是否最后一个教学事件类型
     */
    public boolean isLastTeachEventType(TeachEventTypeDTO teachEventType) {
        if (CollectionUtils.isEmpty(teachEventTypes)) {
            return false;
        }
        List<TeachEventTypeDTO> filteredEvents = teachEventTypes.stream()
                .filter(item -> !PerformanceTypeEnum.END_FADEOUT.getName().equals(item.getPerformanceType()))
                .collect(Collectors.toList());
        return !filteredEvents.isEmpty() && filteredEvents.get(filteredEvents.size() - 1).equals(teachEventType);
    }

    /**
     * 获取上一个教学事件类型
     * 
     * @param teachEventType 当前教学事件类型
     * @return 上一个教学事件类型，如果当前事件是第一个或列表为空则返回null
     */
    public TeachEventTypeDTO getPreviousTeachEventType(TeachEventTypeDTO teachEventType) {
        if (CollectionUtils.isEmpty(teachEventTypes)) {
            return null;
        }
        
        int currentIndex = teachEventTypes.indexOf(teachEventType);
        if (currentIndex <= 0) {
            return null;
        }
        
        return teachEventTypes.get(currentIndex - 1);
    }

    /**
     * 获取出生点灯光
     */
    public String getBirthPositionLight() {
        return birthPosition != null ? birthPosition.getString("light") : null;
    }

    /**
     * 查找第一个板书场景点位
     */
    public ScenePositionResourceDTO findFirstBoardScenePosition() {
        List<ScenePositionResourceDTO> birthPointRes = coursewareResourceConfig.getScenePositionsMap().get(currentSceneId);
        if (CollectionUtils.isNotEmpty(birthPointRes)) {
            return birthPointRes.stream().filter(scenePositionResource -> scenePositionResource.getBoardTransform() != null).findFirst().orElse(null);
        }
        return null;
    }

    /**
     * 添加隐藏字幕时间区间
     *
     * @param timeArea `TimeArea`
     */
    public void addTimeAreaOfHideSubtitle(TimeArea timeArea) {
        timeAreasOfHideSubtitle.add(timeArea);
    }
}
