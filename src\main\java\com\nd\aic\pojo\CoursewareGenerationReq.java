package com.nd.aic.pojo;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.common.BizConstant;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;

import org.hibernate.validator.constraints.NotBlank;

import java.io.File;
import java.util.List;

import lombok.Data;

@Data
public class CoursewareGenerationReq {

    @NotBlank
    private String title;

    private String tenantId;

    private String containerId;

    private String resourceId;

    /**
     * 是否生成角色
     */
    private Boolean characterGenerationEnabled = Boolean.TRUE;

    /**
     * 是否生成服装
     */
    private Boolean clothingGenerationEnabled = Boolean.TRUE;

    /**
     * 是否使用新动作
     */
    private Boolean newActionEnabled = Boolean.FALSE;

    /**
     * 角色处理方式：hide-隐藏，destroy-销毁，remain-保留
     */
    private String characterHandleMode = BizConstant.CHARACTER_HANDLE_MODE_DESTROY;

    /**
     * 角色实例ID
     */
    private String characterInstanceId;

    /**
     * 全局场景
     */
    private String globalScene;

    /**
     * 全局角色
     */
    private String globalCharacter;

    /**
     * 角色衣服ID
     */
    private String globalClothing;

    /**
     * 出生点
     */
    private String birthPointName;

    /**
     * 出生点
     */
    private JSONObject birthPosition;

    /**
     * 发音人及语速
     */
    private String voiceId;
    private Float voiceSpeed;

    /**
     * 教学事件批注列表
     */
    private List<TeachEventTypeDTO> teachEventTypes;

    private List<String> pptVideoResources;

    private String bgmType;
    private File bgmFile;
    private String backgroundMusicResourceId;
    private String ambientSoundResource;

    // 业务来源，用于处理实验室的需求定制
    private String bizFlag;
}
