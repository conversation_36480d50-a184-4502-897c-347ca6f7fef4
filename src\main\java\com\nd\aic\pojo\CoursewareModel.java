package com.nd.aic.pojo;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.entity.VideoInteraction;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.ArkitFaceSegment;
import com.nd.aic.pojo.segment.AtmosphereSegment;
import com.nd.aic.pojo.segment.AudioSegment;
import com.nd.aic.pojo.segment.BgmSegment;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.BuffSegment;
import com.nd.aic.pojo.segment.CameraFeatureSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.CharacterSegment;
import com.nd.aic.pojo.segment.ClothingSegment;
import com.nd.aic.pojo.segment.GifSegment;
import com.nd.aic.pojo.segment.LightSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.ObjectSegment;
import com.nd.aic.pojo.segment.PathFindingSegment;
import com.nd.aic.pojo.segment.PreloadSegment;
import com.nd.aic.pojo.segment.PrepareSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneChangeSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.pojo.segment.SceneSegment;
import com.nd.aic.pojo.segment.SettingSegment;
import com.nd.aic.pojo.segment.UISegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.pojo.segment.WeatherSegment;
import com.nd.aic.pojo.segment.WordSegment;

import org.springframework.data.annotation.Transient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class CoursewareModel {

    private String taskId;

    /**
     * NDR容器ID
     */
    private String containerId;

    /**
     * NDR资源ID
     */
    private String resourceId;

    /**
     * 口型生成任务ID
     */
    @Deprecated
    private String arkitTaskId;

    /**
     * 标题
     */
    private String title;

    /**
     * 时长 00:00:00
     */
    private String length;

    private String type;

    private String bizType;

    private String version;

    private String scene;

    private String clothing;

    private String updateTime;

    private List<CameraSegment> cameraSegment = new ArrayList<>();

    private List<CameraFeatureSegment> cameraFeatureSegment = new ArrayList<>();

    private List<ActionSegment> actionSegment = new ArrayList<>();

    private List<ClothingSegment> clothingSegment = new ArrayList<>();

    private List<SceneSegment> sceneSegment = new ArrayList<>();

    private List<VideoSegment> videoSegment = new ArrayList<>();

    private List<BirthPointSegment> birthPointSegment = new ArrayList<>();

    private List<WeatherSegment> weatherSegment = new ArrayList<>();

    private List<CharacterSegment> characterSegment = new ArrayList<>();

    private List<PathFindingSegment> pathFindingSegment = new ArrayList<>();

    private List<LightSegment> lightSegment = new ArrayList<>();

    private List<BuffSegment> buffSegment = new ArrayList<>();

    private List<LottieSegment> lottieSegment = new ArrayList<>();

    private List<RoutineSegment> routineSegment = new ArrayList<>();

    private List<SceneChangeSegment> sceneChangeSegment = new ArrayList<>();

    private List<SceneObjectSegment> sceneObjectSegment = new ArrayList<>();

    private List<GifSegment> gifSegment = new ArrayList<>();

    private List<ArkitFaceSegment> arkitFaceSegment = new ArrayList<>();

    private List<BgmSegment> backgroundMusicSegment = new ArrayList<>();

    private List<AudioSegment> audioSegment = new ArrayList<>();

    private List<AtmosphereSegment> atmosphereSegment = new ArrayList<>();

    private List<WordSegment> wordSegment = new ArrayList<>();

    private List<SettingSegment> settingSegment = new ArrayList<>();

    private List<PreloadSegment> preloadSegment = new ArrayList<>();

    private List<PrepareSegment> prepareSegment = new ArrayList<>();

    private List<ObjectSegment> objects = new ArrayList<>();

    private List<JSONObject> propSegment  = new ArrayList<>();

    private List<UISegment> uiSegment  = new ArrayList<>();

    private List<UISegment> interactiveUiSegment  = new ArrayList<>();

    private JSONObject mrqSetting;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private JSONObject elapsed = new JSONObject(true);

    @JsonIgnore
    private Map<String, Object> properties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return properties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        properties.put(key, value);
    }

    /**
     * 互动结果
     */
    @Transient
    @JsonIgnore
    private VideoInteraction interactionResult;
}
