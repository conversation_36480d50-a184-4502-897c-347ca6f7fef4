package com.nd.aic.pojo;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

@ToString
@Builder
@Data
public class SentenceTiming {

    /**
     * 句子文本
     */
    private String text;

    /**
     * 开始时间（毫秒）
     */
    private Integer startTime;

    /**
     * 结束时间（毫秒）
     */
    private Integer endTime;

    /**
     * 文本开始位置
     */
    private int startPos;

    /**
     * 文本结束位置
     */
    private int endPos;


    public Integer getDuration() {
        return endTime - startTime;
    }

    public Double getDurationSec() {
        return (endTime - startTime) / 1000.0;
    }
}
