package com.nd.aic.pojo.ai;

import com.nd.aic.entity.automate.Annotation;
import com.nd.aic.entity.automate.GlobalAnnotation;

import org.hibernate.validator.constraints.NotBlank;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class AigcRequest {
    // 业务分类
    @NotBlank
    private String topic;
    // 批注内容
    @NotBlank
    private String content;
    // 视频的提示词
    private String prompt;
    private String provideType = "Response";
    private Option option;
    private Production production;

    private GlobalAnnotation globalAnnotation;
    private List<String> contents;
    private String performancePlan;
    private Annotation annotation;

    @Data
    public static class Option {
        // 期望宽，部分模型无法指定具体的分辨率，那么会当做宽的比
        private Integer width;
        // 期望高，部分模型无法指定具体的分辨率，那么会当做高的比
        private Integer height;
    }

    @Data
    public static class Production {
        // 生产线代码
        private String productionCategoryCode;
    }

}
