package com.nd.aic.pojo.ai;

import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

import lombok.Data;

@Data
public class StylizationScript {

    @NotBlank
    private String style;
    @NotBlank
    private String requirement;
    // @NotEmpty
    // private List<Item> detailList;

    @Data
    public static class Item {
        private String originalScript;
        private String dialogue;
    }
}
