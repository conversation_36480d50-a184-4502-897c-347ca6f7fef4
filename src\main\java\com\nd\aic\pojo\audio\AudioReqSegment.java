package com.nd.aic.pojo.audio;

import com.nd.aic.client.pojo.tts.ChildTtsSegment;
import com.nd.aic.enums.PerformanceTypeEnum;
import com.nd.aic.util.TextUtils;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AudioReqSegment {

    /**
     * 对话稿-带TTS标注
     */
    private String dialogue;

    /**
     * 字幕稿
     */
    private String subtitle;

    /**
     * 音频资源ID(CS)
     */
    private String audioId;

    /**
     * 发音标注
     */
    private Map<String, List<String>> pronunciationDict;

    /**
     * TTS标注子片段
     */
    private List<ChildTtsSegment> ttsSegments;

    /**
     * 时长，单位毫秒
     */
    private Long duration;

    /**
     * 句尾静音时长，单位毫秒
     */
    private Long silenceDuration;

    /**
     * 教学活动
     */
    private String teachingActivity;

    /**
     * 表演类型
     */
    private PerformanceTypeEnum performanceType;

    private Long start;
    private Long end;

    public String getSubtitle() {
        if (StringUtils.isEmpty(subtitle)) {
            subtitle = TextUtils.clearBreak(dialogue);
        }
        return subtitle;
    }
}
