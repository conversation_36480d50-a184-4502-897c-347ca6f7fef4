package com.nd.aic.pojo.audio;

import com.google.common.collect.Lists;

import java.io.File;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AudioReqSegmentGroup {

    private List<String> teachingActivities;
    private String groupName;
    private String bgmType;
    private File bgmFile;

    private File dryVoice;
    private File mixingAudio;

    private boolean latest;

    private List<AudioReqSegment> reqSegments = Lists.newArrayList();
    // private List<TtsSegment> ttsSegments = Lists.newArrayList();
}
