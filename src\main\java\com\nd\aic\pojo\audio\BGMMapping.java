package com.nd.aic.pojo.audio;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Random;

public class BGMMapping {

    private static final Map<String, String> teachingActivityMap = Maps.newHashMap();
    private static final Map<String, List<String>> bgmMap = Maps.newHashMap();
    public static final boolean FIXED_BGM = true;

    static {
        // 开场
        teachingActivityMap.put("授课开场", "开场A");
        teachingActivityMap.put("助教出场", "开场B");
        // 教学引导或前言
        teachingActivityMap.put("情景导入", "教学引导或前言A");
        teachingActivityMap.put("了解学习目标", "教学引导或前言B");
        teachingActivityMap.put("问卷引导", "教学引导或前言C");

        teachingActivityMap.put("目标导入", "教学引导或前言B");//TEST
        // 提问或互动
        teachingActivityMap.put("布置任务：展示小组学习成果", "提问与互动A");
        teachingActivityMap.put("结合案例思考", "提问与互动B");
        teachingActivityMap.put("布置任务：结合案例完成实验探究", "提问与互动C");
        teachingActivityMap.put("布置任务：结合案例自主学习", "提问与互动C");
        teachingActivityMap.put("布置任务：使用教材自主学习", "提问与互动D");
        // TODO
        teachingActivityMap.put("实验探究", "提问与互动C");//TEST
        // 讲解
        teachingActivityMap.put("通用", "讲解A");
        teachingActivityMap.put("结合案例学习新知", "讲解A");
        // 教学总结
        teachingActivityMap.put("课堂总结", "教学总结A");
        teachingActivityMap.put("学习内容总结回顾", "教学总结B");
        teachingActivityMap.put("展示学习结论", "教学总结C");

        teachingActivityMap.put("报告展示", "教学总结C");//TEST
        // 结尾
        teachingActivityMap.put("授课结束", "结尾A");
        // 讲故事
        teachingActivityMap.put("讲故事", "讲故事");
        // 音频
        // 讲故事
        bgmMap.put("讲故事", Lists.newArrayList("cb2022f5-aa3e-430a-8a8c-5b31716269c8"));
        // 开场
        bgmMap.put("开场A", Lists.newArrayList("16f33793-eaaf-416a-bf46-6ae63ba31434", "489beb06-3677-43a3-95e4-d695dd992096"));
        bgmMap.put("开场B", Lists.newArrayList("6870f3e3-21c6-48ad-85b6-38dafac04ca8", "11a4fec4-33f7-4c13-ac69-336284638032",
                "cd78143c-fe70-49bd-83a0-b1aeaed3b965", "e3bea9d2-1181-45b6-87db-87e05b1f8998", "5d18cc4d-e02e-4332-b60f-ff4fc39d8305"));
        // 教学引导或前言
        bgmMap.put("教学引导或前言A", Lists.newArrayList("45bdc1d5-59ec-4c26-9d4e-755bc2187d8c", "b08244ab-3d77-49ad-830a-8f45d6db337e"));
        bgmMap.put("教学引导或前言B", Lists.newArrayList("9c408c0a-4a20-4809-85e2-8035081df9fa", "1bebde5e-d5f5-4cae-ae04-7385cb74193c"));
        bgmMap.put("教学引导或前言C", Lists.newArrayList("c794f225-dc63-4f37-9e6c-b9f7e54493f8"));
        // 提问或互动
        bgmMap.put("提问与互动A", Lists.newArrayList("cafd0472-ac56-4be9-a170-9ed406957aec"));
        bgmMap.put("提问与互动B", Lists.newArrayList("45e9eeb3-af02-42d6-a2b7-baedacecc51a", "79a67928-4370-4d19-a389-83af00b8172b", "11d7c71c-893e-4684-9b99-ce782e085bfb"));
        bgmMap.put("提问与互动C", Lists.newArrayList("bf63905c-0f13-4dfd-a07d-c97f34cae3bb", "59db9063-ece9-4476-98e4-0679cd40ce49", "8dc7c3c6-5dea-42fa-b7a4-9da07dc9d6af", "3c4dbf9e-4977-4962-9ca6-f28609584892", "ef4618be-7460-4c6a-b4bd-dafeecfbb25d", "8a8d57aa-aa42-49e3-9a14-be804d63f14c"));
        bgmMap.put("提问与互动D", Lists.newArrayList("d216ff18-0450-4519-a47e-f70faaf61171"));
        // 讲解
        bgmMap.put("讲解A", Lists.newArrayList("5c04ba31-92f7-4835-8a2d-9685893e3a4a", "fac495d8-552b-4f1c-85da-d8f5dbc34b84"));
        bgmMap.put("讲解B", Lists.newArrayList("31173688-c157-48cf-9461-9ed8d789a8d0", "1829012e-d116-4ae7-821a-6c9639c4a68f", "67d23150-07a8-4db9-9d15-e8b5ae253acd", "7cdc7c05-6966-4ce7-a7ba-bc10251cea10", "6f4c8b4f-587d-4b65-bfc7-d5d88e960441", "07703df4-6f13-41f5-9731-80fc06c0b85a", "5499070c-5c3e-45bb-b6cb-baea1fc0eccf", "ea053110-d8dd-470b-b10e-874082a3eda2", "668486ec-8b26-4b3e-b14b-cdd4bf7346c0", "205d5a57-d912-4353-9077-13e369a0b13b"));
        // 教学总结
        bgmMap.put("教学总结A", Lists.newArrayList("3fe057d4-1b93-4fc9-8006-2320ccb52c34"));
        bgmMap.put("教学总结B", Lists.newArrayList("81158ef7-2dec-4e0d-8979-e7c086fa9edd", "6ee63a52-bb0b-4d7a-8633-15528dc375ea"));
        bgmMap.put("教学总结C", Lists.newArrayList("a481ddb2-4ecb-4d0f-ae89-7536c525e066"));
        // 结尾
        bgmMap.put("结尾A", Lists.newArrayList("cd1b640f-a1e8-4ae4-82e8-345e1e10980b", "be9fc3ad-3397-4e8d-8c4c-1de901559d8d"));
        // 通用==讲解A
        bgmMap.put("通用", Lists.newArrayList("5c04ba31-92f7-4835-8a2d-9685893e3a4a", "fac495d8-552b-4f1c-85da-d8f5dbc34b84"));
        // BGM 20241014
        // 木吉他 爵士 134 非版权
        // 木吉他 爵士 散拍 非版权
        // 木吉他 爵士 142 非版权
        // 口琴 流行 142 非版权
        // 吉他 爵士 84 非版权
        bgmMap.put("20241014", Lists.newArrayList("b9be7515-5b58-4434-870b-eb62e18393c6", "020ed6f6-c388-418d-a2a4-410a8b9c9108", "fb1305e5-5e61-4356-a456-bf9b25fb099a",
                "94d37a5e-424c-4fcf-9950-0be6da867fba", "f6829161-b77a-4730-a40f-c98e92080802"));
    }

    public static String mappingBgmByTeachingActivity(String teachingActivity) {
        String bgmKey = teachingActivityMap.put(teachingActivity, "通用");
        return mappingBgmByGroupName(bgmKey);
    }

    public static String mappingBgmByGroupName(String bgmKey) {
        if (FIXED_BGM) {
            List<String> bgmList = bgmMap.get("20241014");
            int idx = new Random().nextInt(bgmList.size());
            return bgmList.get(idx);
        }
        List<String> bgmList = bgmMap.get(bgmKey);
        int idx = new Random().nextInt(bgmList.size());
        return bgmList.get(idx);
    }

    public static String getGroup(String teachingActivity) {
        return teachingActivityMap.getOrDefault(teachingActivity, "通用");
    }
}
