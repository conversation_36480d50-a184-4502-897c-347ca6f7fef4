package com.nd.aic.pojo.automate;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GptPerformance {
    // @JsonProperty("teach_activity")
    // private String teachingActivity;
    @JsonProperty("teach_event")
    private String teachingEvent;
    @JsonProperty("perform_plan")
    private String performancePlan;
    @JsonProperty("dialogue")
    private String dialogue;

    // @Getter
    // @Setter
    // public static class PP {
    //     // @JsonProperty("teach_event")
    //     // private String teachingEvent;
    //     @JsonProperty("perform_plan")
    //     private String performancePlan;
    //     @JsonProperty("dialogue")
    //     private String dialogue;
    // }
}
