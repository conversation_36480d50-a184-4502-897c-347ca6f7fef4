package com.nd.aic.pojo.automate;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GptTeachingEvent {
    // @JsonProperty("teach_activity")
    // private String teachingActivity;
    @JsonProperty("teach_event")
    private String teachingEvent;
    @JsonProperty("dialogue")
    private String dialogue;
    // @JsonProperty("teach_events")
    // private List<TE> teachingEvents;

    // @Getter
    // @Setter
    // public static class TE {
    //     @JsonProperty("teach_event")
    //     private String teachingEvent;
    //     @JsonProperty("dialogue")
    //     private String dialogue;
    // }
}
