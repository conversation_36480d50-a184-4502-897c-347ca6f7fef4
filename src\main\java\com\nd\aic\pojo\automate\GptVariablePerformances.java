package com.nd.aic.pojo.automate;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GptVariablePerformances {
    // @JsonProperty("teach_activity")
    // private String teachingActivity;
    @JsonProperty("teach_event")
    private String teachingEvent;
    @JsonProperty("perform_plans")
    private List<PP> performancePlans;

    @Getter
    @Setter
    public static class PP {
        @JsonProperty("name")
        private String performancePlan;
        @JsonProperty("description")
        private String description;
    }
}
