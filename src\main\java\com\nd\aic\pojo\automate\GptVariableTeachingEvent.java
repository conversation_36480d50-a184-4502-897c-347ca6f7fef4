package com.nd.aic.pojo.automate;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GptVariableTeachingEvent {
    @JsonProperty("name")
    private String teachingEvent;
    @JsonProperty("description")
    private String description;
    @JsonProperty("manuscript")
    private String manuscript;

    // @Getter
    // @Setter
    // public static class TE {
    //     @JsonProperty("teach_event")
    //     private String teachingEvent;
    //     @JsonProperty("description")
    //     private String description;
    // }
}
