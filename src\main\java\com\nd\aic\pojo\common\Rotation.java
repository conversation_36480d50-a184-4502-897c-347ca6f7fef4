package com.nd.aic.pojo.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 转向
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Rotation {
    private double pitch;
    private double yaw;
    private double roll;

    public Rotation pitch(double pitch) {
        this.pitch = pitch;
        return this;
    }

    public Rotation yaw(double yaw) {
        this.yaw = yaw;
        return this;
    }

    public Rotation roll(double roll) {
        this.roll = roll;
        return this;
    }


}
