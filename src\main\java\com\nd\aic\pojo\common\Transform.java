package com.nd.aic.pojo.common;

import com.alibaba.fastjson.JSONObject;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 3D向量
 */
@AllArgsConstructor
@Data
public class Transform {
    private Vector3D position;
    private Rotation rotation;
    private Vector3D scale;
    private String state = "stand";

    /**
     * 构造函数
     */
    public Transform() {
        this.position = new Vector3D(0,0,0);
        this.rotation = new Rotation(0,0,0);
        this.scale = new Vector3D(1.0,1.0,1.0);
    }

    public Transform position(Vector3D position) {
        this.position = position;
        return this;
    }

    public Transform rotation(Rotation rotation) {
        this.rotation = rotation;
        return this;
    }

    public Transform scale(Vector3D scale) {
        this.scale = scale;
        return this;
    }

    public JSONObject toJSONObject() {
        return new JSONObject().fluentPut("location", position)
                .fluentPut("rotation", rotation)
                .fluentPut("scale", scale)
                .fluentPut("state", state);
    }
}
