package com.nd.aic.pojo.common;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Vector3D {

    private double x;
    private double y;
    private double z;

    public Vector3D x(double x) {
        this.x = x;
        return this;
    }

    public Vector3D y(double y) {
        this.y = y;
        return this;
    }

    public Vector3D z(double z) {
        this.z = z;
        return this;
    }


    @Override
    public String toString() {
        return "{" +
                "x=" + x +
                ", y=" + y +
                ", z=" + z +
                '}';
    }
}