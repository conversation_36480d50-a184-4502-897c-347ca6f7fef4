package com.nd.aic.pojo.cs;

import com.fasterxml.jackson.annotation.JsonInclude;

import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TokenParams {

    /**
     * -- GETTER --
     *
     *
     * -- SETTER --
     *
     * @return the tokenType
     * @param tokenType the tokenType to set
     */
    private String tokenType;

    /**
     * -- GETTER --
     *
     *
     * -- SETTER --
     *
     * @return the path
     * @param path the path to set
     */
    private String path;

    /**
     * -- GETTER --
     *
     *
     * -- SETTER --
     *
     * @return the dentryId
     * @param dentryId the dentryId to set
     */
    @Pattern(regexp = "^[A-Fa-f0-9]{8}-([A-Fa-f0-9]{4}-){3}[A-Fa-f0-9]{12}$", message = "dentry_id illegal")
    private String dentryId;

    /**
     * -- GETTER --
     *
     *
     * -- SETTER --
     *
     * @return the scope
     * @param scope the scope to set
     */
    @Range(min = 0, max = 1, message = "scope value illegal")
    private int scope;

    /**
     * -- GETTER --
     *
     *
     * -- SETTER --
     *
     * @return the expireAt
     * @param expireAt the expireAt to set
     */
    @Min(value = 0, message = "expire_at value illegal")
    private Long expireAt;

    /**
     * -- GETTER --
     *
     *
     * -- SETTER --
     *
     * @return the params
     * @param params the params to set
     */
    private String params;

}

