package com.nd.aic.pojo.dto;

import lombok.Data;
import java.util.List;

/**
 * AI搜索响应DTO
 */
@Data
public class AISearchResponseDTO {
    
    /**
     * 查询信息
     */
    private QueryInfo query;
    
    /**
     * 搜索记录列表
     */
    private List<SearchRecord> records;
    
    /**
     * 查询信息
     */
    @Data
    public static class QueryInfo {
        /**
         * 查询内容
         */
        private String content;
    }
    
    /**
     * 搜索记录
     */
    @Data
    public static class SearchRecord {
        /**
         * 文档片段
         */
        private Segment segment;
        
        /**
         * 子块信息
         */
        private Object childChunks;
        
        /**
         * 相似度分数
         */
        private Double score;
        
        /**
         * t-SNE位置
         */
        private Object tsnePosition;
    }
    
    /**
     * 文档片段
     */
    @Data
    public static class Segment {
        /**
         * 片段ID
         */
        private String id;
        
        /**
         * 位置
         */
        private Integer position;
        
        /**
         * 文档ID
         */
        private String documentId;
        
        /**
         * 内容
         */
        private String content;
        
        /**
         * 签名内容
         */
        private String signContent;
        
        /**
         * 答案
         */
        private String answer;
        
        /**
         * 字数
         */
        private Integer wordCount;
        
        /**
         * 令牌数
         */
        private Integer tokens;
        
        /**
         * 关键词列表
         */
        private List<String> keywords;
        
        /**
         * 索引节点ID
         */
        private String indexNodeId;
        
        /**
         * 索引节点哈希
         */
        private String indexNodeHash;
        
        /**
         * 命中次数
         */
        private Integer hitCount;
        
        /**
         * 是否启用
         */
        private Boolean enabled;
        
        /**
         * 禁用时间
         */
        private Long disabledAt;
        
        /**
         * 禁用者
         */
        private String disabledBy;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * 创建者
         */
        private String createdBy;
        
        /**
         * 创建时间
         */
        private Long createdAt;
        
        /**
         * 索引时间
         */
        private Long indexingAt;
        
        /**
         * 完成时间
         */
        private Long completedAt;
        
        /**
         * 错误信息
         */
        private String error;
        
        /**
         * 停止时间
         */
        private Long stoppedAt;
        
        /**
         * 关联文档
         */
        private Document document;
    }
    
    /**
     * 文档信息
     */
    @Data
    public static class Document {
        /**
         * 文档ID
         */
        private String id;
        
        /**
         * 数据源类型
         */
        private String dataSourceType;
        
        /**
         * 文档名称
         */
        private String name;
        
        /**
         * 文档类型
         */
        private String docType;
        
        /**
         * 文档元数据
         */
        private Object docMetadata;
    }
}
