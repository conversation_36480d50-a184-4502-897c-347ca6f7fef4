package com.nd.aic.pojo.dto;

import com.nd.aic.enums.StandPositionEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString(callSuper = true)
@Getter
@Setter
public class CameraResourceDTO extends CoursewareResourceDTO {

    /**
     * 景别，如：全景、中景、近景、特写
     */
    private String shotSize;

    /**
     * 运动方式，如：推、拉、固定、环移
     */
    private String moveType;

    /**
     * 运动方向，如：前推、后拉、右摇左、左摇右、左绕右
     */
    private String moveDir;

    /**
     * 水平角度
     */
    private String pan;

    /**
     * 时长
     */
    private Double duration;

    private Boolean lookAt;

    /**
     * 开场镜头
     */
    private Boolean opening;

    /**
     * 结尾镜头
     */
    private Boolean ending;

    /**
     * 构图
     */
    @Deprecated
    private String composition;

    /**
     * 角色站立位置
     */
    @Deprecated
    private StandPositionEnum position;

}
