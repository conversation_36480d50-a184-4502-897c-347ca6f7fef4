package com.nd.aic.pojo.dto;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class CoursewareResourceConfigDTO {
    private List<CoursewareResourceDTO> scenes;
    private Map<String, List<ScenePositionResourceDTO>> scenePositionsMap;
    private List<CameraResourceDTO> characterCameras;
    /**
     * 板书镜头
     */
    private List<CameraResourceDTO> boardCharacterCameras;
    private Map<String, List<CoursewareResourceDTO>> sceneCamerasMap;
    /**
     * 动作组
     */
    private Map<String, List<ActionResourceDTO>> actionGroups;
    /**
     * 服装组
     */
    private Map<String, List<CoursewareResourceDTO>> clothesGroups;
    /**
     * 转场
     */
    private List<CoursewareResourceDTO> transitionAnimations;
}
