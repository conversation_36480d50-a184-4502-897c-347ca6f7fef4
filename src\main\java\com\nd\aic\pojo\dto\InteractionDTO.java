package com.nd.aic.pojo.dto;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class InteractionDTO {
    // 互动类型（选择题、判断题）
    private String type;
    // 互动问题
    private String question;
    // 互动选项
    private List<Option> options;
    // 触发时间
    private Long triggerTime;

    @JsonProperty(value = "Data")
    private Object data;

    private String url;

    @JsonProperty(value = "needSpeak")
    private Boolean needSpeak;
    @JsonProperty(value = "speakDelay")
    private Integer speakDelay;
    @JsonProperty(value = "speakUrl")
    private String speakUrl;

    @JsonIgnore
    private Map<String, Object> extProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        extProperties.put(key, value);
    }

    @Data
    public static class Option {
        private Boolean correct;
        private String text;
    }
}
