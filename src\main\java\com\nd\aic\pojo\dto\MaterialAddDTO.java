package com.nd.aic.pojo.dto;

import com.nd.aic.enums.MaterialTypeEnum;

import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 添加素材请求DTO
 */
@Data
public class MaterialAddDTO {

    /**
     * 素材名称
     */
    @NotBlank(message = "素材名称不能为空")
    private String name;

    /**
     * 素材描述
     */
    private String description;

    /**
     * 素材类型
     */
    @NotNull(message = "素材类型不能为空")
    private MaterialTypeEnum type;

    /**
     * 素材文件路径/URL
     */
    @NotBlank(message = "素材URL不能为空")
    private String filePath;

    /**
     * 素材文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件格式/MIME类型
     */
    private String mimeType;

    /**
     * 文件扩展名
     */
    private String extension;

    /**
     * 缩略图路径
     */
    private String thumbnailPath;

    /**
     * 素材标签
     */
    private List<String> tags;

    /**
     * 素材分类
     */
    private String category;

    /**
     * 素材元数据
     */
    private Map<String, Object> metadata;
}
