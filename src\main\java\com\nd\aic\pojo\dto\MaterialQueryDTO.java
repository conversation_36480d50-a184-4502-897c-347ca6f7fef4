package com.nd.aic.pojo.dto;

import com.nd.aic.enums.MaterialTypeEnum;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 素材查询请求DTO
 */
@Data
public class MaterialQueryDTO {

    /**
     * 素材ID列表（用于批量查询）
     */
    private List<String> ids;

    /**
     * 素材名称（模糊查询）
     */
    private String name;

    /**
     * 素材类型
     */
    private MaterialTypeEnum type;

    /**
     * 素材标签
     */
    private List<String> tags;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;

    /**
     * 文件大小最小值
     */
    private Long fileSizeMin;

    /**
     * 文件大小最大值
     */
    private Long fileSizeMax;

    /**
     * 关键词搜索（在名称、描述、标签中搜索）
     */
    private String keyword;

    /**
     * 排序字段
     */
    private String sortBy = "createTime";

    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection = "desc";

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 20;
}
