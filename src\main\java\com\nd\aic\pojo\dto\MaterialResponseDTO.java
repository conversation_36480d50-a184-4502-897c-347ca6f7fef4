package com.nd.aic.pojo.dto;

import com.nd.aic.enums.MaterialTypeEnum;

import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 素材响应DTO
 */
@Data
public class MaterialResponseDTO {

    /**
     * 素材ID
     */
    private String id;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材描述
     */
    private String description;

    /**
     * 素材类型
     */
    private MaterialTypeEnum type;

    /**
     * 素材文件路径/URL
     */
    private String filePath;

    /**
     * 素材文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件格式/MIME类型
     */
    private String mimeType;

    /**
     * 文件扩展名
     */
    private String extension;

    /**
     * 缩略图路径
     */
    private String thumbnailPath;

    /**
     * 素材标签
     */
    private List<String> tags;

    /**
     * 素材分类
     */
    private String category;

    /**
     * 素材元数据
     */
    private Map<String, Object> metadata;

    /**
     * 上传者ID
     */
    private String uploaderId;

    /**
     * 素材状态
     */
    private String status;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 向量搜索返回的分值
     */
    private Double score;

    
}
