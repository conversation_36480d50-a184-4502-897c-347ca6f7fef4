package com.nd.aic.pojo.dto;

import com.nd.aic.service.performance.param.AbstractPerformanceAdditionParam;
import com.nd.aic.service.performance.param.AbstractPerformanceParam;
import com.nd.gaea.util.WafJsonMapper;

import java.util.HashMap;
import java.util.Map;

import lombok.Data;
import lombok.SneakyThrows;

/**
 * 增强表演（附加表演）
 */
@Data
public class PerformanceAdditionDTO {

    /**
     * 表演名称
     */
    private String name;

    /**
     * 批注的文本
     */
    private String text;

    /**
     * 批注原由
     */
    private String reason;

    /**
     * 指定资源
     */
    private String resourceName;

    /**
     * 指定资源
     */
    private String resourceId;

    /**
     * 开始位置
     */
    private Integer startPos;

    /**
     * 结束位置
     */
    private Integer endPos;

    /**
     * 最小时长
     */
    private Integer minDuration;

    /**
     * 镜头景别
     */
    private String shotType;
    /**
     * 镜头构图
     */
    private String shotComposition;

    /**
     * 表演参数
     */
    private Map<String, Object> param;

    /**
     * 获取参数
     */
    public Map<String, Object> getParam() {
        return param == null ? new HashMap<>(0) : param;
    }

    /**
     * 获取参数
     */
    @SneakyThrows
    public <T extends AbstractPerformanceAdditionParam> T getParamObject(Class<T> clazz) {
        return WafJsonMapper.parse(WafJsonMapper.toJson(getParam()), clazz);
    }

}
