package com.nd.aic.pojo.dto;

import com.fasterxml.jackson.annotation.JsonUnwrapped;

import org.apache.commons.lang3.StringUtils;

import lombok.Data;

@Data
public class PlotInteractionDTO {
    private Integer index;
    private String dialogue;

    @JsonUnwrapped
    private InteractionDTO interaction;

    public PlotInteractionDTO() {
    }

    public PlotInteractionDTO(String dialogue) {
        this.dialogue = StringUtils.defaultString(dialogue, "");
    }

}
