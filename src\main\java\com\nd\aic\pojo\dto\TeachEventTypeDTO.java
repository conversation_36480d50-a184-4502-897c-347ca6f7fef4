package com.nd.aic.pojo.dto;

import com.nd.aic.client.pojo.tts.ChildTtsSegment;
import com.nd.aic.service.performance.param.AbstractPerformanceParam;
import com.nd.gaea.util.WafJsonMapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;

@Data
public class TeachEventTypeDTO {

    private String time;
    private String endTime;

    /**
     * 资源时长
     */
    private Long resourceDuration;

    /**
     * 句尾静音时长，单位毫秒
     */
    private Long silenceDuration;

    /**
     * 教学活动
     */
    private String teachActivity;

    /**
     * 教学步骤
     */
    private String teachStepType;

    /**
     * 教学事件
     */
    private String teachEventType;

    /**
     * 对话稿
     */
    @NotNull
    private String dialogue;

    /**
     * 配音稿（包含TTS标记）
     */
    private String dubbing;

    /**
     * 音频资源ID(CS)
     */
    private String audioId;

    /**
     * 发音标注
     */
    private Map<String, List<String>> pronunciationDict;

    /**
     * TTS标注子片段
     */
    private List<ChildTtsSegment> ttsSegments;

    /**
     * 对话稿开始位置
     */
    private Integer dialogueStartPos;

    /**
     * 表演类型
     */
    private String performanceType;

    /**
     * 表演方案别名
     */
    private String performanceTypeAlias;

    /**
     * 表演增强
     */
    @Deprecated
    private List<PerformanceEnhanceDTO> performanceEnhances;

    /**
     * 增强表演（附加表演）
     */
    private List<PerformanceAdditionDTO> performanceAdditional;

    /**
     * 教学互动
     */
    private InteractionDTO interaction;

    /**
     * 表演参数
     */
    @Getter(AccessLevel.NONE)
    private Map<String, Object> performanceParam;

    /**
     * 表演配置
     */
    @Deprecated
    private PerformanceConfigDTO performanceConfig;

    /**
     * 是否是有效的表演方案
     */
    private Boolean valid = true;

    /**
     * 获取表演方案的参数
     */
    @SneakyThrows
    public <T extends AbstractPerformanceParam> T getPerformanceParam(Class<T> clazz) {
        return WafJsonMapper.parse(WafJsonMapper.toJson(getRawPerformanceParam()), clazz);
    }

    /**
     * 获取表演方案的参数
     */
    public Map<String, Object> getRawPerformanceParam() {
        return performanceParam == null ? new HashMap<>(0) : performanceParam;
    }
}
