package com.nd.aic.pojo.flow;

import org.hibernate.validator.constraints.NotBlank;

import lombok.Data;

@Data
public class FlowBaseDTO {

    @NotBlank(message = "fastgptKey不能为空")
    private String fastgptKey;
    //当前操作人
    private Long userId;
    //业务配置应用id
    @NotBlank(message = "appId不能为空")
    private String appId;
    //业务配置表单id
    @NotBlank(message = "formId不能为空")
    private String formId;
    //数据ID
    @NotBlank(message = "dataId不能为空")
    private String dataId;
    //流程实例id
    @NotBlank(message = "flowInstId不能为空")
    private String flowInstId;
    //节点实例id
    @NotBlank(message = "nodeInstId不能为空")
    private String nodeInstId;
    //gpt生成内容输出字段
    private String outputKey;
    //gpt生成状态输出字段
    private String statusKey;
}
