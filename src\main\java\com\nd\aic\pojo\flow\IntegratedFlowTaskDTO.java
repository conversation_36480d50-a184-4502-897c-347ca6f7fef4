package com.nd.aic.pojo.flow;

import com.nd.aic.entity.IntegratedFlowTask;
import com.nd.aic.entity.IntegratedFlowTaskParticle;

import org.bson.types.ObjectId;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class IntegratedFlowTaskDTO extends IntegratedFlowTask {

    private String extraTextGptKey = "fastgpt-sUcsfH5OsFAhYDdX4pLUSpIU8GUUOQBVb6Ags24UbtkBr7ILRENbJcm";
    private String disassembleGptKey = "fastgpt-uRTMTHFa1mVogV5QC0H2XkbzGp4qjNXBKLJ49Wi43zrmm2thb7fv5ueoOW";
    // 简单
    // private String performanceTypeGptKey = "fastgpt-sLQBCxMMkNJ3gNZdKFb7xjzL0iGQwPn8JUUxMrN6xuXmDYHQktgoQe4";
    // 流程
    private String performanceTypeGptKey = "fastgpt-bs9c9pPO55KYBPh5ME6NuD5uyN4Bb3Ah76Urz5bRZUodniDDd1rRSQ";

    private Boolean mergePerformances = true;
    private List<IntegratedFlowTaskParticle> particles;

    public IntegratedFlowTaskDTO() {
        setId(new ObjectId().toString());
        // setOpeningPerformance("御剑飞行");
    }
}
