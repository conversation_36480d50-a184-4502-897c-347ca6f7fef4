package com.nd.aic.pojo.interactive;

import com.google.common.collect.Lists;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class InteractiveData {

    @JsonIgnore
    private static final Map<String, String> BIND_AUDIO_MEDIA_TARGET = new HashMap<String, String>() {{
        put("answerno", "//gcdncs.101.com/v0.1/static/eb_aic/Assets/%E7%AD%94%E9%94%99%E4%BA%86%EF%BC%8C%E7%BB%A7%E7%BB%AD%E5%8A%A0%E6%B2%B9%E5%93%A6.mp3");
        put("answeryes", "//gcdncs.101.com/v0.1/static/eb_aic/Assets/%E7%AD%94%E5%AF%B9%E4%BA%86%EF%BC%8C%E7%9C%9F%E6%A3%92.mp3");
    }};

    private String id;
    private String refResourceId;
    private String defaultMedia;
    private List<MediaSource> mediaSources;
    private String interactiveType;
    private List<ActiveEvent> activeEvents;
    private List<Resource> resources;

    @Data
    public static class MediaSource {
        private String id;
        private String src;
        private String ndrTenantId;
        private String ndrContainerId;
    }

    // Getters and Setters
    @Data
    public static class ActiveEvent {
        private String id;
        private String mediaTarget;
        private double timeAnchor = 0;
        private Map<String, List<ActiveEventAction>> backActions;
        private String type;
        private ActiveEventCustomArgs customArgs;
        private String interactiveType;
        private int timeLimit = 30;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActiveEventAction {
        private String action = "play";
        private String mediaTarget = "";
        private Object customArgs = null;
    }

    @Data
    public static class ActiveEventCustomArgs {
        // 选择题/判断题
        private String id; // uuid,前端生成一个, 要和另外标志的一样
        @JsonProperty("needTimeout")
        private Boolean needTimeout;
        private String prompt = ""; // "xxxxx", 题干、题目 前端插入
        private Boolean isRecycle;
        private String qtiType;
        private String qtiCode;
        private List<Answer> answeryes;
        private List<Answer> answerno;

        // 填空题
        private String coreType;//NONE
        private String voiceType;//qa
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Answer {
        private String id; // uuid,前端生成一个, 要和另外标志的一样
        private String bindAudioMediaTarget;
    }

    @Data
    public static class Resource {
        private String identifier = "@id1";
        private String type = "question";
        private String questionCode = "$RE0203";
        private ResourceData data;
        private ResourceCustomArgs customProperties;
    }

    @Data
    public static class ResourceData {
        private String prompt = null;
        private List<ResourceDataChoice> choices = Lists.newArrayList();
    }

    @Data
    public static class ResourceDataChoice {
        private String identifier;
        private String label;
        private String text;
        private String image;
        private boolean correct;
    }

    @Data
    public static class ResourceCustomArgs {
        private ChoicesLabel choicesLabel = new ChoicesLabel();
    }

    @Data
    public static class ChoicesLabel {
        private String id = "";
        private String patternText = "";
    }
}
