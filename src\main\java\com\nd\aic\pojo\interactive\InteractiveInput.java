package com.nd.aic.pojo.interactive;

import java.util.List;

import lombok.Data;

@Data
public class InteractiveInput {
    private String type;
    private String question;
    private long triggerTime;
    private List<Option> options;

    // Get<PERSON> and Setters
    @Data
    public static class Option {
        private boolean correct;
        private String text;

        // Get<PERSON> and Setters
    }
}