package com.nd.aic.pojo.ndr;

import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

import lombok.Data;

@Data
public class NdrSearchVO {
    private List<String> tags;
    private List<String> labels;
    private String keyword;

    @NotBlank
    private String accessToken;

    @Data
    public static class Headers {
        @NotBlank(message = "tenantId is blank")
        private String tenantId;
        @NotBlank(message = "containerId is blank")
        private String containerId;
        @NotBlank(message = "ak is blank")
        private String ak;
        @NotBlank(message = "sk is blank")
        private String sk;
    }
}
