package com.nd.aic.pojo.render;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RenderPrepareResult {
    private Boolean ready;
    private Boolean needUpdate;

    public RenderPrepareResult(Boolean ready) {
        this.ready = ready;
        this.needUpdate = false;
    }

    public RenderPrepareResult(Boolean ready, Boolean needUpdate) {
        this.ready = ready;
        this.needUpdate = needUpdate;
    }
}
