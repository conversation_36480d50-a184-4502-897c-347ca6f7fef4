package com.nd.aic.pojo.render;

import com.nd.aic.entity.VideoInteraction;
import com.nd.aic.pojo.CoursewareModel;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class RenderTaskAddReq {

    private String taskName;

    @NotBlank
    private String bizId;
    private String bizType;
    private String ip;
    private Long priority = 5L;
    @NotNull
    private CoursewareModel coursewareModel;
    private VideoInteraction interaction;
    private MrqSetting mrqSetting;
    private Boolean enableArkitFace;
    private Boolean enableSubtitle;
    private String renderMode;
    private Boolean arkitNdTech;

    private String botId;
    private String conversationId;
    private String workflowRunId;
}
