package com.nd.aic.pojo.resource;

import com.google.common.collect.Lists;

import com.fasterxml.jackson.annotation.JsonProperty;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResDescript {
    @JsonProperty("resId")
    private String resId;

    @JsonProperty("resVersion")
    private int resVersion;

    @JsonProperty("resType")
    private String resType;

    @JsonProperty("resName")
    private String resName;

    @JsonProperty("resLogoPath")
    private String resLogoPath;

    @JsonProperty("resPakPath")
    private String resPakPath;

    @JsonProperty("resInfoPath")
    private String resInfoPath;

    @JsonProperty("resDescriptPath")
    private String resDescriptPath;

    @JsonProperty("resTagInfos")
    private List<String> resTagInfos;

    public static ResDescript createFrontVideo(String resId, String resName) {
        ResDescript resDescript = new ResDescript();
        resDescript.setResId(resId);
        resDescript.setResVersion(1000);
        resDescript.setResType("FrontVideo");
        resDescript.setResName(resName);
        resDescript.setResLogoPath(String.format("/Game/OutputCollected/FrontMedia/FrontVideo/%s/Logo.png", resId));
        resDescript.setResPakPath(String.format("/Game/OutputCollected/FrontMedia/FrontVideo/%s/Res.pak", resId));
        resDescript.setResInfoPath(String.format("/Game/OutputCollected/FrontMedia/FrontVideo/%s/ResInfo.cfg", resId));
        resDescript.setResDescriptPath(String.format("/Game/OutputCollected/FrontMedia/FrontVideo/%s/ResDescript.cfg", resId));
        resDescript.setResTagInfos(Lists.newArrayList());
        return resDescript;
    }

    public static ResDescript createImgSeq(String resId, String resName) {
        if (StringUtils.isEmpty(resId)) {
            resName = "Image Sequence";
        }
        ResDescript resDescript = new ResDescript();
        resDescript.setResId(resId);
        resDescript.setResVersion(1000);
        resDescript.setResType("FrontImgSeq");
        resDescript.setResName(resName);
        resDescript.setResLogoPath(String.format("/Game/OutputCollected/FrontMedia/FrontImgSeq/%s/Logo.png", resId));
        resDescript.setResPakPath(String.format("/Game/OutputCollected/FrontMedia/FrontImgSeq/%s/Res.pak", resId));
        resDescript.setResInfoPath(String.format("/Game/OutputCollected/FrontMedia/FrontImgSeq/%s/ResInfo.cfg", resId));
        resDescript.setResDescriptPath(String.format("/Game/OutputCollected/FrontMedia/FrontImgSeq/%s/ResDescript.cfg", resId));
        resDescript.setResTagInfos(Lists.newArrayList());
        return resDescript;
    }
}
