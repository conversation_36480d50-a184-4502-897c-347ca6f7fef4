package com.nd.aic.pojo.resource;

import com.fasterxml.jackson.annotation.JsonProperty;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;

import lombok.Data;

@Data
public class ResInfo {
    @JsonProperty(value = "filePath")
    private String filePath;
    @JsonProperty(value = "headInfo")
    private HeadInfo headInfo;

    public static ResInfo createFrontVideo(String resId, String resName) {
        ResInfo resInfo = new ResInfo();
        resInfo.setFilePath(String.format("%s/video.mp4", resId));
        HeadInfo headInfo = new HeadInfo();
        headInfo.setCompVersion("1.0");
        headInfo.setResVersion(0);
        headInfo.setDateTime(LocalDateTime.now().toString("yyyy.MM.dd-HH.mm.ss"));
        headInfo.setResId(resId);
        headInfo.setResName(resName);
        headInfo.setResType("FrontVideo");

        resInfo.setHeadInfo(headInfo);
        return resInfo;
    }

    public static ResInfo createImgSeq(String resId, String resName) {
        if (StringUtils.isEmpty(resId)) {
            resName = "Image Sequence";
        }
        ResInfo resInfo = new ResInfo();
        resInfo.setFilePath(String.format("%s/images", resId));
        HeadInfo headInfo = new HeadInfo();
        headInfo.setCompVersion("1.0");
        headInfo.setResVersion(0);
        // "2024.09.11-16.22.14"
        headInfo.setDateTime(LocalDateTime.now().toString("yyyy.MM.dd-HH.mm.ss"));
        headInfo.setResId(resId);
        headInfo.setResName(resName);
        headInfo.setResType("FrontImgSeq");

        resInfo.setHeadInfo(headInfo);
        return resInfo;
    }

    @Data
    public static class HeadInfo {
        @JsonProperty(value = "compVersion")
        private String compVersion;
        @JsonProperty(value = "resVersion")
        private Integer resVersion;
        @JsonProperty(value = "dateTime")
        private String dateTime;
        @JsonProperty(value = "resId")
        private String resId;
        @JsonProperty(value = "resName")
        private String resName;
        @JsonProperty(value = "resType")
        private String resType;
    }
}
