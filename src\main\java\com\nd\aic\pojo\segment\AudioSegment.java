package com.nd.aic.pojo.segment;

import com.alibaba.fastjson.JSONObject;

import java.util.UUID;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AudioSegment extends BaseSegment<AudioSegment> {

    // private String id = UUID.randomUUID().toString();

    public AudioSegment() {
        super();
    }

    /**
     * soundType
     */
    public AudioSegment soundType(String soundType) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        json.fluentPut("SoundType", soundType);
        this.setCustom(json);
        return this;
    }

    /**
     * soundType
     */
    public AudioSegment targetType(String targetType) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        json.fluentPut("TargetType", targetType);
        this.setCustom(json);
        return this;
    }

    /**
     * distance
     */
    public AudioSegment distance(Integer distance) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        json.fluentPut("Distance", distance);
        this.setCustom(json);
        return this;
    }

    /**
     * Location
     */
    public AudioSegment location(Float x, Float y, Float z) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        json.fluentPut("Location", new JSONObject().fluentPut("x", x).fluentPut("y", y).fluentPut("z", z));
        this.setCustom(json);
        return this;
    }

    /**
     * Location
     */
    public AudioSegment offset(int offset) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        json.fluentPut("Offset", offset);
        this.setCustom(json);
        return this;
    }

    /**
     * Volume
     */
    public AudioSegment volume(Float volume) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        if (null == volume) {
            json.remove("Volume");
        } else {
            json.fluentPut("Volume", volume);
        }
        this.setCustom(json);
        return this;
    }

    // ”Music“,"Sfx","UI"，“Voice”
    // (背景音乐，音效，UI，语音)
    public AudioSegment soundGroup(String soundGroup) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        json.fluentPut("SoundGroup", soundGroup);
        this.setCustom(json);
        return this;
    }

}
