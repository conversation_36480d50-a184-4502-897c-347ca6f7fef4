package com.nd.aic.pojo.segment;


import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.common.Rotation;
import com.nd.aic.pojo.common.Vector3D;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@SuppressWarnings("unchecked")
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Data
public abstract class BaseSegment<T extends BaseSegment<T>> {

    private String time;
    private String endTime;
    private String resourceId;
    /**
     * 控制端的处理器
     */
    private Object perform;
    private String type;
    private String reason;
    private JSONObject custom = new JSONObject();
    private Collection<String> dependencies;

    @JsonIgnore
    private Map<String, Object> extraProperties = Maps.newHashMap();

    @JsonAnyGetter
    public Map<String, Object> getter() {
        return extraProperties;
    }

    @JsonAnySetter
    public void setter(String key, Object value) {
        extraProperties.put(key, value);
    }

    @JsonIgnore
    public Long getDuration() {
        return TimeUtils.getDuration(this.time, this.endTime);
    }

    @JsonIgnore
    public Double getDurationSec() {
        return TimeUtils.getDurationSec(this.time, this.endTime);
    }

    public void setTimeInMilliseconds(long milliseconds) {
        this.time = TimeUtils.formatMilliseconds(milliseconds);
    }

    public void setEndTimeInMilliseconds(long milliseconds) {
        this.endTime = TimeUtils.formatMilliseconds(milliseconds);
    }

    public T time(String time) {
        this.time = time;
        return (T) this;
    }

    public T time(Long time) {
        if (time != null) {
            this.time = TimeUtils.formatMilliseconds(time);
        }
        return (T) this;
    }

    public T time(Integer time) {
        if (time != null) {
            this.time = TimeUtils.formatMilliseconds(time);
        }
        return (T) this;
    }

    public T endTime(String endTime) {
        this.endTime = endTime;
        return (T) this;
    }

    public T endTime(Long endTime) {
        if (endTime != null) {
            this.endTime = TimeUtils.formatMilliseconds(endTime);
        }
        return (T) this;
    }

    public T endTime(Integer endTime) {
        if (endTime != null) {
            this.endTime = TimeUtils.formatMilliseconds(endTime);
        }
        return (T) this;
    }

    public T resourceId(String resourceId) {
        this.resourceId = resourceId;
        return (T) this;
    }

    public T perform(JSONObject perform) {
        this.perform = perform;
        return (T) this;
    }

    @JsonIgnore
    public String getPerformName() {
        if (this.perform == null) {
            return null;
        }
        return this.perform instanceof JSONObject ? ((JSONObject) this.perform).getString("name") : this.perform.toString();
    }

    public T perform(String perform) {
        if (this.perform == null) {
            this.perform = new JSONObject();
        }
        if (this.perform instanceof JSONObject) {
            ((JSONObject) this.perform).fluentPut("name", perform);
        } else {
            this.perform = perform;
        }
        return (T) this;
    }

    public T performOption(String key, Object value) {
        if (this.perform == null) {
            this.perform = new JSONObject();
        }
        if (this.perform instanceof JSONObject) {
            String optionsKey = "options";
            JSONObject options = ((JSONObject) this.perform).getJSONObject(optionsKey);
            if (options == null) {
                options = new JSONObject();
            }
            options.put(key, value);
            ((JSONObject) this.perform).put(optionsKey, options);
        }
        return (T) this;
    }

    public T type(String type) {
        this.type = type;
        return (T) this;
    }

    public T reason(String reason) {
        this.reason = reason;
        return (T) this;
    }

    public T custom(JSONObject custom) {
        if (this.custom != null && custom != null) {
            this.custom.putAll(custom);
        } else {
            this.custom = custom;
        }
        return (T) this;
    }

    public T dependencies(Collection<String> dependencies) {
        this.dependencies = dependencies;
        return (T) this;
    }


    public T dependency(String dependency) {
        if (StringUtils.isBlank(dependency)) {
            return (T) this;
        }
        if (this.dependencies == null) {
            this.dependencies = new HashSet<>();
        }
        this.dependencies.add(dependency);
        return (T) this;
    }

    public T cTime(Double time) {
        if (time == null) {
            return (T) this;
        }
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        double roundedTime = BigDecimal.valueOf(time).setScale(3, RoundingMode.HALF_UP).doubleValue();
        this.custom.put("Time", roundedTime);
        return (T) this;
    }

    /**
     * 设置播放类型
     *
     * @param playType 1，0，-1
     * @return this
     */
    public T playType(Integer playType) {
        if (playType == null) {
            return (T) this;
        }
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("PlayType", playType);
        return (T) this;
    }

    public T targetId(String targetId) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("TargetID", targetId);
        return (T) this;
    }

    @JsonIgnore
    public String getTargetId() {
        if (this.custom == null) {
            return null;
        }
        return StringUtils.defaultIfBlank(this.custom.getString("targetID"), this.custom.getString("TargetID"));
    }

    public T instanceId(String instanceId) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("instanceId", instanceId);
        return (T) this;
    }

    @JsonIgnore
    public String getInstanceId() {
        if (this.custom == null) {
            return null;
        }
        return StringUtils.defaultIfBlank(this.custom.getString("instanceId"), this.custom.getString("InstanceId"));
    }

    public T skillType(String skillType) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("SkillType", skillType);
        return (T) this;
    }

    @JsonIgnore
    public String getSkillType() {
        if (this.custom == null) {
            return null;
        }
        return StringUtils.defaultIfBlank(this.custom.getString("SkillType"), this.custom.getString("skillType"));
    }

    public T metaRoleComponentName(String metaRoleComponentName) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("MetaRoleComponentName", metaRoleComponentName);
        return (T) this;
    }

    public T metaRoleType(String metaRoleType) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("MetaRoleType", metaRoleType);
        return (T) this;
    }

    /**
     * 获取参数
     */
    @JsonIgnore
    public JSONObject getParam() {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        String key = "Param";
        JSONObject param = custom.getJSONObject(key);
        if (param == null) {
            param = new JSONObject();
            this.custom.put(key, param);
        }
        return param;
    }


    /**
     * 设置参数
     */
    @JsonIgnore
    public T putParam(String key, Object value) {
        getParam().put(key, value);
        return (T) this;
    }

    /**
     * 移除参数
     */
    @JsonIgnore
    public T removeParam(String key) {
        getParam().remove(key);
        return (T) this;
    }

    /**
     * 获取属性
     */
    @JsonIgnore
    public JSONArray getProperties() {
        String key = "Properties";
        JSONArray properties = getParam().getJSONArray(key);
        if (properties == null) {
            properties = new JSONArray();
            putParam(key, properties);
        }
        return properties;
    }

    /**
     * 设置属性
     */
    public T addProperty(String name, String type, Object value) {
        return addProperty(name, type, value, null);
    }

    /**
     * 设置属性
     */
    public T addProperty(String name, ParamPropertyTypeEnum type, Object value) {
        return addProperty(name, type.getName(), value);
    }

    /**
     * 设置属性
     */
    public T addProperty(String name, String type, Object value, Map<String, Object> other) {
        JSONArray properties = getProperties();
        // 遍历属性数组，检查是否有相同的 Name
        for (int i = 0; i < properties.size(); i++) {
            JSONObject property = properties.getJSONObject(i);
            if (property.getString("Name").equals(name)) {
                property.fluentPut("Type", type).fluentPut("Value", value);
                return (T) this;
            }
        }
        other = MapUtils.emptyIfNull(other);
        properties.add(new JSONObject().fluentPut("Name", name).fluentPut("Type", type).fluentPut("Value", value).fluentPutAll(other));
        return (T) this;
    }

    /**
     * 设置属性
     */
    public T addProperty(JSONObject property) {
        getProperties().add(property);
        return (T) this;
    }

    /**
     * 设置属性
     */
    public T addProperty(List<JSONObject> properties) {
        getProperties().addAll(properties);
        return (T) this;
    }

    /**
     * 设置ShotTrack
     */
    public T addShotTrack(int index, Double durationSec) {
        String key = "ShotTrack";
        JSONArray shotTracks = getParam().getJSONArray(key);
        if (shotTracks == null) {
            shotTracks = new JSONArray();
            putParam(key, shotTracks);
        }
        shotTracks.add(new JSONObject().fluentPut("index", index).fluentPut("duration", durationSec));
        return (T) this;
    }

    public T relativeCharacter() {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("relativeObjId", "MajorCharacter");
        return (T) this;
    }

    public T relativeCharacterInParam() {
        this.putParam("relativeObjId", "MajorCharacter");
        return (T) this;
    }

    public T location(Vector3D vector3D) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("Location", vector3D);
        return (T) this;
    }

    public T rotation(double pitch, double yaw, double roll) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("Rotation", new Rotation(pitch, yaw, roll));
        return (T) this;
    }

    public T location(double x, double y, double z) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("Location", new Vector3D(x, y, z));
        return (T) this;
    }

    public T rotation(Rotation rotation) {
        if (this.custom == null) {
            this.custom = new JSONObject();
        }
        this.custom.put("Rotation", rotation);
        return (T) this;
    }

    public T locationParam(double x, double y, double z) {
        this.putParam("Location", new Vector3D(x, y, z));
        return (T) this;
    }

    public T locationParam(Vector3D vector3D) {
        this.putParam("Location", vector3D);
        return (T) this;
    }

    public T rotationParam(double pitch, double yaw, double roll) {
        this.putParam("Rotation", new Rotation(pitch, yaw, roll));
        return (T) this;
    }

    public T rotationParam(Rotation rotation) {
        this.putParam("Rotation", rotation);
        return (T) this;
    }

    public T scaleParam(double x, double y, double z) {
        this.putParam("Scale", new Vector3D(x, y, z));
        return (T) this;
    }

    public T scaleParam(Vector3D vector3D) {
        this.putParam("Scale", vector3D);
        return (T) this;
    }
}
