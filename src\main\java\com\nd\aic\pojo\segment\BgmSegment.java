package com.nd.aic.pojo.segment;

import com.alibaba.fastjson.JSONObject;

import java.util.UUID;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BgmSegment extends BaseSegment<BgmSegment> {

    private String id = UUID.randomUUID().toString();
    private String tiFlag = "source";

    public BgmSegment() {
        super();
        setReason("背景音乐");
        volume(7);
    }

    /**
     * 打开视频音量
     */
    public BgmSegment volume(Integer volume) {
        JSONObject json = this.getCustom();
        if (json == null) {
            json = new JSONObject();
        }

        json.fluentPut("volume", volume);
        this.setCustom(json);
        return this;
    }
}
