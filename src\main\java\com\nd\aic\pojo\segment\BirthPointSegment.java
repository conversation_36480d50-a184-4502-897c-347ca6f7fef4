package com.nd.aic.pojo.segment;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nd.aic.pojo.common.Vector3D;

import org.apache.commons.collections4.MapUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BirthPointSegment extends BaseSegment<BirthPointSegment> {

    /**
     * 对应的板书世界坐标
     */
    @JsonIgnore
    private JSONObject boardTransform;

    @JsonIgnore
    public Vector3D getLocation() {
        if (getCustom() != null) {
            Vector3D location = getCustom().getObject("location", Vector3D.class);
            if (location == null) {
                location = getCustom().getObject("Location", Vector3D.class);
            }
            return location;
        }
        return null;
    }

    /**
     * 克隆出生地坐标
     */
    public JSONObject cloneTransform() {
        if (MapUtils.isEmpty(getCustom())) {
            return null;
        }
        return new JSONObject(Maps.newHashMap(getCustom()));
    }

    /**
     * 克隆板书坐标
     */
    public JSONObject cloneBoardTransform() {
        if (MapUtils.isEmpty(boardTransform)) {
            return null;
        }
        return new JSONObject(Maps.newHashMap(boardTransform));
    }

    @JsonIgnore
    public boolean isBoardPoint() {
        return MapUtils.isNotEmpty(boardTransform);
    }
}
