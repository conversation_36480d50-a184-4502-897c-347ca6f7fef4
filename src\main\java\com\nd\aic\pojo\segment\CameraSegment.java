package com.nd.aic.pojo.segment;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CameraSegment extends BaseSegment<CameraSegment> {

    private String ndrResourceId;
    @JsonIgnore
    private String pan;
    @JsonIgnore
    private String shotSize;
    @JsonIgnore
    private Boolean lookAt;
}
