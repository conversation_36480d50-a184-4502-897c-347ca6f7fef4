package com.nd.aic.pojo.segment;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nd.aic.enums.LottieParamTypeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LottieSegment extends BaseSegment<LottieSegment> {

    public LottieSegment windowId(int windowId) {
        getCustom().put("windowId", windowId);
        return this;
    }

    /**
     * 设置音量
     * @param volume 音量
     * @return LottieSegment
     */
    public LottieSegment volume(int volume) {
        JSONObject jsonObject = this.getCustom();
        if(jsonObject == null) {
            jsonObject = new JSONObject();
            this.setCustom(jsonObject);
        }
        jsonObject.fluentPut("audio", new JSONObject().fluentPut("volume", volume));
        return this;
    }

    /**
     * 设置index
     * @param zIndex index
     * @return LottieSegment
     */
    public  LottieSegment zIndex(int zIndex) {
        JSONObject jsonObject = this.getCustom();
        if (jsonObject == null) {
            jsonObject = new JSONObject();
            this.setCustom(jsonObject);
        }
        jsonObject.fluentPut("zIndex", zIndex);
        return this;
    }

    public LottieSegment addWidthHeightTopLeft(int width, int height, int top, int left) {
        JSONObject jsonObject = this.getCustom();
        if (jsonObject == null) {
            jsonObject = new JSONObject();
            this.setCustom(jsonObject);
        }
        jsonObject.fluentPut("width", width).fluentPut("height", height).fluentPut("top", top).fluentPut("left", left);
        return this;
    }

    public LottieSegment addLottieParam(String key, String type, String value) {
        getLottieParams().fluentPut(key, new JSONObject().fluentPut("type", type).fluentPut("value", value));
        return this;
    }

    public LottieSegment addLottieParam(String key, LottieParamTypeEnum type, String value) {
        getLottieParams().fluentPut(key, new JSONObject().fluentPut("type", type.getText()).fluentPut("value", value));
        return this;
    }

    /**
     * 获取参数
     */
    @JsonIgnore
    public JSONObject getLottieParams() {
        JSONObject custom = this.getCustom();
        if (custom == null) {
            custom = new JSONObject();
            this.setCustom(custom);
        }
        String key = "params";
        JSONObject param = custom.getJSONObject(key);
        if (param == null) {
            param = new JSONObject();
            custom.put(key, param);
        }
        return param;
    }
}
