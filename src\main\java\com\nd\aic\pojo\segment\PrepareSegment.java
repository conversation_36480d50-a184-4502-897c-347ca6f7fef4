package com.nd.aic.pojo.segment;

import com.google.common.collect.Maps;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class PrepareSegment {

    private List<SceneSegment> sceneSegment;

    private List<SceneObjectSegment> sceneObjectSegment;

    // 兼容旧数据
    @JsonIgnore
    private List<SceneSegment> scene_segment;
    @JsonIgnore
    private List<SceneObjectSegment> scene_object_segment;
    @JsonAnyGetter
    public Map<String, Object> getter() {
        Map<String, Object> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(scene_segment)) {
            map.put("scene_segment", scene_segment);
        }
        if (CollectionUtils.isNotEmpty(scene_object_segment)) {
            map.put("scene_segment", scene_object_segment);
        }
        return map;
    }


}
