package com.nd.aic.pojo.segment;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RoutineSegment extends BaseSegment<RoutineSegment> {

    public RoutineSegment() {
        setParamVersion(1);
    }

    /**
     * 设置参数的版本号
     *
     * @param version 版本号
     */
    public RoutineSegment setParamVersion(int version) {
        putParam("ParamVersion", version);
        return this;
    }

    /**
     * 设置表演方案的资源ID
     * @param tag 表演方案的标签
     * @param objectId 表演方案的资源ID
     * @return this
     */
    public RoutineSegment bindObject(String tag, String objectId) {

        // add bind object
        JSONObject param = getParam();

        JSONArray objectArray = param.getJSONArray("ObjectBindings");
        if (objectArray == null) {
            objectArray = new JSONArray();
            putParam("ObjectBindings", objectArray);
        }

        objectArray.add(new JSONObject() {{
            put("tag", tag);
            put("id", objectId);
        }});

        return this;
    }

}
