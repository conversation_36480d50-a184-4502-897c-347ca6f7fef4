package com.nd.aic.pojo.segment;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SceneObjectSegment extends BaseSegment<SceneObjectSegment> {

    /**
     * 板书视频
     */
    @JsonIgnore
    public JSONArray getVideos() {
        String key = "videos";
        JSONArray videos = getCustom().getJSONArray(key);
        if (videos == null) {
            videos = new JSONArray();
            getCustom().put(key, videos);
        }
        return videos;
    }

    public SceneObjectSegment addVideo(String time, String endTime, String resourceId, Double volume) {
        JSONArray videos = getVideos();
        videos.add(new JSONObject(true).fluentPut("time", time)
                .fluentPut("end_time", endTime)
                .fluentPut("resource_id", resourceId)
                .fluentPut("volume", volume));
        return this;
    }

    public boolean hasLocation() {
        return getCustom().containsKey("location") || getCustom().containsKey("Location");
    }
}
