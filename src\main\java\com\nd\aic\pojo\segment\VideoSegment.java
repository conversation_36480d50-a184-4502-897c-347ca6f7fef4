package com.nd.aic.pojo.segment;

import com.alibaba.fastjson.JSONObject;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class VideoSegment extends BaseSegment<VideoSegment> {

    /**
     * 打开视频音量
     */
    public VideoSegment turnOnAudio(){
        JSONObject json = this.getCustom();
        if(json == null){
            json = new JSONObject();
        }

        json.fluentPut("Volumn", 1);
        this.setCustom(json);
        return this;
    }

    /**
     * 设置视频播放的速率
     * @param rt 播放速率
     * @return VideoSegment
     */
    public VideoSegment rate(double rt){
        JSONObject json = this.getCustom();
        if(json == null){
            json = new JSONObject();
        }

        json.fluentPut("MediaRate", rt);
        this.setCustom(json);
        return this;
    }
}
