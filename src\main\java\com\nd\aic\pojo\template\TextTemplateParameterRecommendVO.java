package com.nd.aic.pojo.template;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

import lombok.Data;

@Data
public class TextTemplateParameterRecommendVO {
    private String performanceCode;
    private String performancePlan;
    private String textTemplateName;
    private String dialogue;
    private List<Param> params;

    @Data
    public static class Param {
        private String key;
        private String description;
        private Constraint constraint;

        @Data
        public static class Constraint {
            @JsonProperty("lineRange")
            private Range lineRange;
            @JsonProperty("columnRange")
            private Range columnRange;

            @Data
            public static class Range {
                private int min;
                private int max;
            }
        }
    }
}
