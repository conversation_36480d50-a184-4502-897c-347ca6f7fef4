package com.nd.aic.pojo.tts;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.File;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AudioSynthesisResult {
    private String audioId;
    private String audioUrl;
    @JsonIgnore
    private File audioFile;
    private int audioChannel = 1;
    private int sampleRate = 24000;

    public AudioSynthesisResult(String audioId, String audioUrl, File audioFile) {
        this.audioId = audioId;
        this.audioUrl =audioUrl;
        this.audioFile = audioFile;
    }
}
