package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.AudioResult;

import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AudioResultRepository extends BaseRepository<AudioResult, String> {

    AudioResult findFirstByKeyAndContainerTypeOrderByIdDesc(String key, String containerType);

    List<AudioResult> findByKeyAndContainerTypeOrderByIdDesc(String key, String containerType);

    int deleteByContainerTypeAndCreateTimeBefore(String containerType, Date date);
}
