package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.AutomateCourseware;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AutomateCoursewareRepository extends BaseRepository<AutomateCourseware, String> {

    @Override
    Page<AutomateCourseware> findAll(Pageable pageable);

    List<AutomateCourseware> findByAiTaskStatusIn(List<String> aiTaskStatusList);
}
