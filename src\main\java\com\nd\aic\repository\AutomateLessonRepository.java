package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.AutomateLesson;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public interface AutomateLessonRepository extends BaseRepository<AutomateLesson, String> {

    @Override
    Page<AutomateLesson> findAll(Pageable pageable);

    AutomateLesson findByWorkflowInstanceId(String workflowInstanceId);

    AutomateLesson findByWorkflowInstanceIdAndExecutionId(String workflowInstanceId, String executionId);

    AutomateLesson findBySourceAndExecutionId(String source, String executionId);
}
