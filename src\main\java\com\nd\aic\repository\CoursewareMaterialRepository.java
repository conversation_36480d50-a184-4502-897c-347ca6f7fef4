package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.CoursewareMaterial;
import com.nd.aic.enums.MaterialTypeEnum;

import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 课件素材Repository
 */
@Repository
public interface CoursewareMaterialRepository extends BaseRepository<CoursewareMaterial, String> {

    /**
     * 根据类型查询
     */
    List<CoursewareMaterial> findByType(MaterialTypeEnum type);

}
