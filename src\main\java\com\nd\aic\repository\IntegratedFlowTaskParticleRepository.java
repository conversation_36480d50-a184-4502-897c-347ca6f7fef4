package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.IntegratedFlowTaskParticle;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IntegratedFlowTaskParticleRepository extends BaseRepository<IntegratedFlowTaskParticle, String> {

    @Override
    Page<IntegratedFlowTaskParticle> findAll(Pageable pageable);

    List<IntegratedFlowTaskParticle> findAllByTaskId(String taskId);

    List<IntegratedFlowTaskParticle> deleteAllByTaskId(String taskId);
}
