package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.IntegratedFlowTask;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public interface IntegratedFlowTaskRepository extends BaseRepository<IntegratedFlowTask, String> {

    @Override
    Page<IntegratedFlowTask> findAll(Pageable pageable);
}
