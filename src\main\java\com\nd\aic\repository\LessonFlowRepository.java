package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.LessonFlow;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LessonFlowRepository extends BaseRepository<LessonFlow, String> {

    @Override
    Page<LessonFlow> findAll(Pageable pageable);

    List<LessonFlow> findByStatus(String statusPrepare);
}
