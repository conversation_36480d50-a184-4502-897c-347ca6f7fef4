package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.SgTask;

import org.springframework.stereotype.Repository;

@Repository
public interface SgTaskRepository extends BaseRepository<SgTask, String> {

    SgTask findFirstByResourceIdAndSgModelAndNdTech(String resourceId, String sgModel, Boolean ndTech);

    SgTask findFirstBySgTaskId(String sgTaskId);
}
