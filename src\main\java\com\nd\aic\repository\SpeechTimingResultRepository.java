package com.nd.aic.repository;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.SpeechTimingResult;

import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpeechTimingResultRepository extends BaseRepository<SpeechTimingResult, String> {

    SpeechTimingResult findByAudioKeyAndAudioTextKey(String audioKey, String audioTextKey);

    List<SpeechTimingResult> findByAudioKey(String audioKey);
}
