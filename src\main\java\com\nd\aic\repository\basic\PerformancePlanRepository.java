package com.nd.aic.repository.basic;

import com.nd.aic.base.repository.BaseRepository;
import com.nd.aic.entity.basic.PerformancePlan;

import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PerformancePlanRepository extends BaseRepository<PerformancePlan, String> {
    List<PerformancePlan> findByTeachingEvents(List<String> teachingEvents);
    List<PerformancePlan> findByTeachingEventsIn(List<String> teachingEvents);
    PerformancePlan findFirstByName(String name);
    PerformancePlan findFirstByNameAndProductionLine(String name, String productionLine);
}
