package com.nd.aic.service;

import com.nd.aic.client.AigcFeignClient;
import com.nd.aic.client.pojo.aigc.ResReq;
import com.nd.aic.client.pojo.aigc.ResResp;
import com.nd.aic.client.pojo.aigc.ResTask;
import com.nd.aic.entity.automate.GlobalAnnotation;
import com.nd.aic.pojo.ai.AigcRequest;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class AigcService {

    private final AigcFeignClient aigcFeignClient;

    private void patchDefaultParams(ResReq resReq, String productionCategoryCode) {
        if (null == resReq.getOption()) {
            resReq.setOption(new ResReq.Option());
            resReq.getOption().setWidth(1920);
            resReq.getOption().setHeight(1080);
        }
        if (null == resReq.getProduction()) {
            resReq.setProduction(new ResReq.Production());
        }
        if (null == resReq.getProduction().getProductionCategoryCode()) {
            resReq.getProduction().setProductionCategoryCode(productionCategoryCode);
        }
    }

    private ResReq buildResReq(AigcRequest aigcRequest) {
        ResReq resReq = new ResReq();
        resReq.setTopic(aigcRequest.getTopic());
        resReq.setContent(aigcRequest.getContent());
        // resReq.setPrompt(aigcRequest.getPrompt());
        resReq.setProvideType(aigcRequest.getProvideType());
        if (null != aigcRequest.getOption()) {
            resReq.setOption(new ResReq.Option());
            resReq.getOption().setWidth(aigcRequest.getOption().getWidth());
            resReq.getOption().setHeight(aigcRequest.getOption().getHeight());
        }
        if (null != aigcRequest.getProduction()) {
            resReq.setProduction(new ResReq.Production());
            resReq.getProduction().setProductionCategoryCode(aigcRequest.getProduction().getProductionCategoryCode());
        }
        StringBuilder builder = new StringBuilder();
        builder.append("标题：\n").append(aigcRequest.getTopic()).append("\n\n");
        builder.append("内容：\n").append(aigcRequest.getContent()).append("\n\n");
        if (CollectionUtils.isNotEmpty(aigcRequest.getContents())) {
            String contents = String.join("\n", aigcRequest.getContents());
            contents = Arrays.stream(contents.split("\n")).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.joining("\n"));
            builder.append("上下文(课件完整讲稿)：\n").append(contents).append("\n\n");
        }
        if (null != aigcRequest.getGlobalAnnotation()) {
            StringBuilder requireBuilder = new StringBuilder();
            GlobalAnnotation globalAnnotation = aigcRequest.getGlobalAnnotation();
            if (StringUtils.isNotBlank(globalAnnotation.getPerformanceStyle())) {
                requireBuilder.append("表演风格：").append(globalAnnotation.getPerformanceStyle()).append("\n");
            }
            if (StringUtils.isNotBlank(globalAnnotation.getCountryRegion())) {
                requireBuilder.append("国家和地区：").append(globalAnnotation.getCountryRegion()).append("\n");
            }
            if (StringUtils.isNotBlank(globalAnnotation.getAudienceAge())) {
                requireBuilder.append("观众年龄：").append(globalAnnotation.getAudienceAge()).append("\n");
            }
            if (StringUtils.isNotBlank(globalAnnotation.getReligion())) {
                requireBuilder.append("宗教：").append(globalAnnotation.getReligion()).append("\n");
            }
            if (requireBuilder.length() > 0) {
                builder.append("要求：\n").append(requireBuilder).append("\n");
            }
        }
        resReq.setContent(builder.toString());
        // resReq.setPrompt(builder.toString());

        return resReq;
    }

    /**
     * 文生图
     */
    public ResTask txt2img(AigcRequest aigcReq) {
        ResReq resReq = buildResReq(aigcReq);
        patchDefaultParams(resReq, "ai_courseware_image");
        return aigcFeignClient.txt2img(resReq);
    }

    /**
     * 文生视频
     */
    public ResTask txt2video(AigcRequest aigcReq) {
        ResReq resReq = buildResReq(aigcReq);
        patchDefaultParams(resReq, "ai_courseware_video");
        return aigcFeignClient.txt2video(resReq);
    }

    /**
     * AIGC任务查询
     */
    public ResResp taskResult(String taskId) {
        return aigcFeignClient.result(taskId);
    }
}
