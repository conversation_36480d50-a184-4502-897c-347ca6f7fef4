package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.client.CombinationTtsClient;
import com.nd.aic.client.SpeechTextTimingClient;
import com.nd.aic.client.pojo.asr.AsrTask;
import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.client.pojo.asr.LatexContent;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.common.BizConstant;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.audio.AudioMetaResult;
import com.nd.aic.pojo.audio.AudioReq;
import com.nd.aic.pojo.audio.AudioReqSegment;
import com.nd.aic.pojo.audio.AudioReqSegmentGroup;
import com.nd.aic.pojo.audio.BGMMapping;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.aic.pojo.tts.AudioSynthesisResult;
import com.nd.aic.util.CacheKeyUtils;
import com.nd.aic.util.LatexUtils;
import com.nd.aic.util.SubtitleUtils;
import com.nd.ndr.model.DownloadUrlViewModel;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class AudioResourceService {

    private static final Map<String, String> voiceMap = Maps.newHashMap();
    // private static final Map<String, File> bgmMap = Maps.newHashMap();

    private final CombinationTtsClient combinationTtsClient;
    // private final MmTtsClient mmTtsClient;
    // private final BdTtsClient bdTtsClient;
    private final SpeechTextTimingClient speechTextTimingClient;
    private final SpeechTimingService speechTimingService;
    private final NdrService ndrService;
    private final MediaService mediaService;
    private final AudioResultService audioResultService;
    private final FastGptRetryService fastGptRetryService;

    static {
        voiceMap.put("default", "20@nd");
        // voiceMap.put("default", "male-qn-jingying");
        // 写实任运
        voiceMap.put("5fd878ba-1040-480a-bc21-b1d6a5086ed9", "20@nd");
        // 皮克斯任运
        voiceMap.put("47b23a4a-f491-42f6-ab20-edeed2cc6a26", "20@nd");
        // 霓
        voiceMap.put("77975790-cbc0-4bf3-a62a-59b6b3227c45", "22@nd");
        voiceMap.put("63d46702-d3ab-4bd0-8c70-40af230c0b39", "43:122@nd");
        voiceMap.put("5800c372-a29e-4578-a7a1-5ce3120864ec", "43:122@nd");
        // 芭芭拉
        voiceMap.put("0bb07793-810d-422a-aac6-043cec393a31", "42@nd");
        // CC4女性老师  女性主持人
        voiceMap.put("4E2E3BB342799D657F77ADB53B842284", "21@nd");
        // 萌萌女童
        voiceMap.put("ae0213cd-6e53-4188-9ddb-a41f8fda8674", "22@nd");
        // 唐钰
        voiceMap.put("CF52860D487F80A6C7667BA6DAD5A6D9", "21@nd");
    }

    private final CsService csService;

    public String mappingVoiceId(String character) {
        return voiceMap.get(character);
    }

    /**
     * 生成音频
     *
     * @return 返回NDR资源id
     */
    @SneakyThrows
    public AudioMetaResult createCourseWareAudioMeta(AudioReq audioReq) {
        if (CollectionUtils.isEmpty(audioReq.getSegments())) {
            throw WafI18NException.of("INVALID_SEGMENTS", "TTS合成内容不能为空", HttpStatus.BAD_REQUEST);
        }
        String voiceType = audioReq.getVoiceType();

        List<AudioReqSegmentGroup> groups = groupByTeachingActivity(audioReq);
        for (AudioReqSegmentGroup group : groups) {
            // 按分组合成语音
            group.setBgmType(audioReq.getBgmType());
            group.setBgmFile(audioReq.getBgmFile());
            generateGroupTtsSegment(group, audioReq.getVoiceSpeed(), voiceType);
        }
        long timeOffset = 0;
        for (AudioReqSegmentGroup group : groups) {
            for (AudioReqSegment reqSegment : group.getReqSegments()) {
                reqSegment.setStart(timeOffset);
                reqSegment.setEnd(timeOffset + reqSegment.getDuration());
                timeOffset += reqSegment.getDuration();
            }
        }

        groups = groups.stream().filter(e -> Objects.nonNull(e.getDryVoice())).collect(Collectors.toList());
        List<File> dryAudios = groups.stream().map(AudioReqSegmentGroup::getDryVoice).collect(Collectors.toList());
        List<File> mixingAudios = groups.stream().map(AudioReqSegmentGroup::getMixingAudio).collect(Collectors.toList());
        File dryAudio;
        File mixingAudio;
        if (groups.size() == 1) {
            dryAudio = dryAudios.get(0);
            mixingAudio = mixingAudios.get(0);
        } else {
            dryAudio = mediaService.concatMp3(dryAudios);
            mixingAudio = mediaService.concatMp3(mixingAudios);
            dryAudios.forEach(FileUtils::deleteQuietly);
            mixingAudios.forEach(FileUtils::deleteQuietly);
        }
        File wavFile = mediaService.mp32wav(mixingAudio);

        LatexContent processResult = LatexUtils.parseLatexContent(audioReq.getAudioText());
        AsrTask task = speechTextTimingClient.ataSubmit(FileUtils.readFileToByteArray(dryAudio), processResult.getOutput());
        AtaResult ataResult = speechTextTimingClient.ataQuery(task.getId());
        ataResult.setLatexMappings(processResult.getLatexMappings());
        // String klrcContent = SubtitleUtils.genKlrcContent(ataResult);
        String lrcContent = SubtitleUtils.genLrcContent(ataResult);
        String assContent = SubtitleUtils.genAssContent(ataResult);
        ResourceMetaTiListViewModel meta = ndrService.createAudioMeta(audioReq.getTitle(), (long) (ataResult.getDuration() + 1), mixingAudio, dryAudio, lrcContent, null);
        // boolean useInAudioSegment = StringUtils.equals(audioReq.getCharacterResource(), CharacterEnum.WHITE_CAT.getId());
        ResourceMetaTiListViewModel bgmMeta = ndrService.createBGMMeta(audioReq.getTitle(), (long) (ataResult.getDuration() + 1), wavFile, true);
        ResourceMetaTiListViewModel subtitleMeta = ndrService.createSubtitleMeta(audioReq.getTitle(), assContent);
        // 缓存
        speechTimingService.saveAtaResult(meta.getId(), null, audioReq.getAudioText(), ataResult);
        return new AudioMetaResult(meta.getId(), bgmMeta.getId(), subtitleMeta.getId());
    }

    @SneakyThrows
    private File mappingBgm(String groupName) {
        String dentryId = BGMMapping.mappingBgmByGroupName(groupName);
        String filename = String.format("bgm-%s.mp3", dentryId);
        File mediaFile = FileUtils.getFile(mediaService.getBgmDirectory(), filename);
        if (mediaFile.length() > 0) {
            return mediaFile;
        }
        String mediaUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryId;
        mediaFile = mediaService.download(mediaUrl, filename);
        mediaFile = mediaService.convertBgm(mediaFile);
        return mediaFile;
    }

    @SneakyThrows
    private void generateGroupTtsSegment(AudioReqSegmentGroup group, Float voiceSpeed, String voiceType) {
        // group.setBgm(bgmMap.getOrDefault(group.getTeachingActivity(), DEFAULT_BGM));
        if (StringUtils.isEmpty(group.getBgmType()) || StringUtils.equalsIgnoreCase(group.getBgmType(), BizConstant.BGM_TYPE_RECOMMEND)) {
            group.setBgmFile(mappingBgm(group.getGroupName()));
        }
        List<AudioReqSegment> ttsSegments = group.getReqSegments();

        List<File> mp3fileList = ttsSegments.parallelStream().map(ttsSegment -> {
            File mp3File = null;
            long mills = ttsSegment.getDuration() == null ? 0L : ttsSegment.getDuration();
            if (StringUtils.isNotBlank(ttsSegment.getDialogue())) {
                TtsSegment ttsArgument = new TtsSegment()
                        .setVoiceId(voiceType)
                        .setVoiceSpeed(voiceSpeed)
                        .setText(ttsSegment.getDialogue())
                        .setPronunciationDict(ttsSegment.getPronunciationDict())
                        .setTtsSegments(ttsSegment.getTtsSegments());
                mp3File = dialogueAudio(ttsSegment.getAudioId(), ttsArgument);
                // 句尾静音时长
                if (ttsSegment.getSilenceDuration() != null && ttsSegment.getSilenceDuration() > 0) {
                    mp3File = mediaService.joinSilent(mp3File, ttsSegment.getSilenceDuration() / 1000f, true);
                }
                if (mills > 0) {
                    long mp3DurationMills = mediaService.durationMills(mp3File);
                    if (mp3DurationMills < mills) {
                        float seconds = (mills - mp3DurationMills) / 1000f;
                        mp3File = mediaService.joinSilent(mp3File, seconds, true);
                    }
                }
            } else if (mills > 0L) {
                mills = mills < 100 ? 100 : mills;
                mp3File = mediaService.createSilent(mills / 1000f);
            }

            if (null != mp3File) {
                long duration = mediaService.durationMills(mp3File);
                ttsSegment.setDuration(duration);
            }
            return mp3File;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (mp3fileList.isEmpty()) {
            return;
        }

        File dryAudio;
        File audioWithBgm;
        if (mp3fileList.size() == 1) {
            dryAudio = mp3fileList.get(0);
        } else {
            dryAudio = mediaService.concatMp3(mp3fileList);
        }
        if (null != group.getBgmFile()) {
            dryAudio = mediaService.addBgm(dryAudio, group.getBgmFile(), true);
            audioWithBgm = mediaService.addBgm(dryAudio, group.getBgmFile(), false);
        } else {
            audioWithBgm = dryAudio;
        }
        group.setDryVoice(dryAudio);
        group.setMixingAudio(audioWithBgm);
    }

    private List<AudioReqSegmentGroup> groupByTeachingActivity(AudioReq audioReq) {
        List<AudioReqSegmentGroup> groups = Lists.newArrayList();

        AudioReqSegmentGroup group = null;
        for (AudioReqSegment segment : audioReq.getSegments()) {
            String teachingActivity = "default";//StringUtils.defaultString(segment.getTeachingActivity(), "default");
            String groupName = BGMMapping.getGroup(teachingActivity);
            if (null == group || !StringUtils.equals(group.getGroupName(), groupName)) {
                group = new AudioReqSegmentGroup();
                group.setTeachingActivities(Lists.newArrayList(teachingActivity));
                group.setGroupName(groupName);
                groups.add(group);
            }
            if (!group.getTeachingActivities().contains(teachingActivity)) {
                group.getTeachingActivities().add(teachingActivity);
            }
            group.getReqSegments().add(segment);
        }
        return groups;
    }

    public AudioMetaResult courseWareAudioMeta(AudioReq audioReq) {
        if (StringUtils.isEmpty(audioReq.getVoiceType())) {
            String defaultVoiceType = voiceMap.get("default");
            String voiceType = voiceMap.getOrDefault(audioReq.getCharacterResource(), defaultVoiceType);
            audioReq.setVoiceType(voiceType);
        }
        String cacheKey = cacheKey(audioReq);
        AudioMetaResult cachedAudioMetaResult = audioResultService.queryAudioResult(cacheKey, "NDR");

        if (cachedAudioMetaResult != null) {
            return cachedAudioMetaResult;
        }
        AudioMetaResult audioMetaResult = createCourseWareAudioMeta(audioReq);
        audioResultService.saveAudio(cacheKey, audioReq.getTitle(), null, audioMetaResult, "NDR");
        return audioMetaResult;
    }

    private String cacheKey(AudioReq audioReq) {
        return CacheKeyUtils.audioCacheKey(audioReq);
    }

    @SneakyThrows
    private File dialogueAudio(String audioId, TtsSegment ttsSegment) {
        File audioFile;
        if (StringUtils.isNotEmpty(audioId)) {
            String mp3Url = "https://cdncs.101.com/v0.1/download?dentryId=" + audioId;
            audioFile = mediaService.download(mp3Url, String.format("%s.mp3", audioId), true);
        } else {
            AudioSynthesisResult audioSynthesisResult = mergeTts(ttsSegment);
            audioFile = audioSynthesisResult.getAudioFile();
        }
        return audioFile;
    }

    @SneakyThrows
    public AudioSynthesisResult mergeTts(TtsSegment ttsSegment) {
        String cacheKey = CacheKeyUtils.audioCacheKey(ttsSegment);

        AudioSynthesisResult audioSynthesisResult;
        String resourceId = audioResultService.queryAudio(cacheKey, "CS");
        if (resourceId != null) {
            String mp3Url = "https://cdncs.101.com/v0.1/download?dentryId=" + resourceId;
            Dentry dentry = Dentry.get("aic_scontent", resourceId, "");
            String audioUrl = "https://gcdncs.101.com/v0.1/static/" + dentry.getPath();
            File audioFile = mediaService.download(mp3Url, String.format("%s.mp3", resourceId), true);
            audioSynthesisResult = new AudioSynthesisResult(resourceId, audioUrl, audioFile);
        } else {
            List<File> mp3List = Lists.newArrayList();
            for (TtsSegment segment : CacheKeyUtils.splitSegment(ttsSegment)) {
                File mp3 = combinationTtsClient.mergeTts(segment);
                mp3.deleteOnExit();
                mp3List.add(mp3);
            }
            if (mp3List.isEmpty()) {
                throw WafI18NException.of("INVALID_TTS", "TTS合成内容分段错误", HttpStatus.BAD_REQUEST);
            }

            String title = ttsSegment.getTitle();
            File mp3File;
            if (mp3List.size() > 1) {
                mp3File = mediaService.concatMp3(mp3List);
            } else {
                mp3File = mp3List.get(0);
                title = StringUtils.defaultIfBlank(title, mp3File.getName());
            }
            title = StringUtils.defaultIfBlank(title, String.format("%s.mp3", cacheKey));

            Dentry dentry = csService.uploadFile(147507L, "merge_tts/", title, mp3File);
            String audioUrl = "https://gcdncs.101.com/v0.1/static/" + dentry.getPath();
            audioResultService.saveAudio(cacheKey, dentry.getName(), dentry.getDentryId(), null, "CS");
            audioSynthesisResult = new AudioSynthesisResult(dentry.getDentryId(), audioUrl, mp3File);
        }
        return audioSynthesisResult;
    }

    public String queryMergeTtsCache(String characterId, TtsSegment ttsSegment) {
        if (StringUtils.isEmpty(ttsSegment.getVoiceId())) {
            String defaultVoiceType = voiceMap.get("default");
            String voiceType = voiceMap.getOrDefault(characterId, defaultVoiceType);
            ttsSegment.setVoiceId(voiceType);
        }
        String cacheKey = CacheKeyUtils.audioCacheKey(ttsSegment);
        return audioResultService.queryAudio(cacheKey, "CS");
    }

    public ResourceMetaTiListViewModel createCourseWareAudioMeta(String title, String dialogues, DentryInfo dentryInfo) throws IOException {
        File mp3File = null;
        try {
            String mediaUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryInfo.getDentryId();
            mp3File = mediaService.download(mediaUrl, String.format("%s.mp3", dentryInfo.getName()));
            AsrTask task = speechTextTimingClient.ataSubmit(FileUtils.readFileToByteArray(mp3File), dialogues);
            AtaResult ataResult = speechTextTimingClient.ataQuery(task.getId());
            String lrcContent = SubtitleUtils.genLrcContent(ataResult);
            // String klrcContent = SubtitleUtils.genKlrcContent(ataResult);
            return ndrService.createAudioMeta(title, (long) (ataResult.getDuration() + 1), mp3File, lrcContent, null);
        } finally {
            FileUtils.deleteQuietly(mp3File);
        }
    }

    public long bgmResourceDuration(String resourceId) throws IOException {
        return audioResourceDuration(resourceId, NdrConstant.AIMV_TENANT_ID,NdrConstant.AIMV_COURSEWARE_CONTAINER_ID, "source", "wav");
    }

    public long audioResourceDuration(String resourceId, String tenantId, String containerId) throws IOException {
        return audioResourceDuration(resourceId, tenantId, containerId, "href", "mp3");
    }

    public long audioResourceDuration(String resourceId, String tenantId, String containerId, String tiFileFlag, String extName) throws IOException {
        tiFileFlag = StringUtils.defaultString(tiFileFlag, "href");
        extName = StringUtils.defaultString(extName, "mp3");
        Map<String, DownloadUrlViewModel> urlViewModelMap = ndrService.getResourceUrlModel(tenantId,containerId, resourceId, tiFileFlag);
        DownloadUrlViewModel downloadUrlViewModel = urlViewModelMap.get(tiFileFlag);
        String url = "https://" + downloadUrlViewModel.getHost() + downloadUrlViewModel.getUrl();
        url = StringUtils.replace(url, "gcdncs.101.com", "cdncs.101.com");
        url = StringUtils.substringBefore(url, "?");
        String filename = String.format("bgm_res_%s.%s", resourceId, extName);
        File file = mediaService.download(url, filename, true);
        return mediaService.durationMills(file);
    }
}
