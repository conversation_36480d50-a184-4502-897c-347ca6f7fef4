package com.nd.aic.service;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.common.UserHandler;
import com.nd.aic.entity.AudioResult;
import com.nd.aic.pojo.audio.AudioMetaResult;
import com.nd.aic.repository.AudioResultRepository;

import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class AudioResultService extends BaseService<AudioResult, String> implements InitializingBean {

    private final AudioResultRepository audioResultRepository;

    @Override
    public void afterPropertiesSet() throws Exception {
        audioResultRepository.deleteByContainerTypeAndCreateTimeBefore("NDR", LocalDateTime.parse("2025-05-07").toDate());
    }

    @Override
    protected Module module() {
        return new Module("AudioResultService");
    }

    @Override
    public AudioResult add(AudioResult audioResult) {
        audioResult.setCreateTime(new Date());
        audioResult.setCreator(UserHandler.getUser("0"));
        return super.add(audioResult);
    }

    public List<AudioResult> listByKeyAndContainerType(String key, String containerType) {
        return audioResultRepository.findByKeyAndContainerTypeOrderByIdDesc(key, containerType);
    }

    public AudioResult findByKeyAndContainerType(String key, String containerType) {
        return audioResultRepository.findFirstByKeyAndContainerTypeOrderByIdDesc(key, containerType);
    }

    public String queryAudio(String cacheKey, String containerType) {
        AudioResult audioResult = findByKeyAndContainerType(cacheKey, containerType);
        if (audioResult == null) {
            return null;
        }
        return audioResult.getResourceId();
    }

    public AudioMetaResult queryAudioResult(String cacheKey, String containerType) {
        AudioResult audioResult = findByKeyAndContainerType(cacheKey, containerType);
        if (audioResult == null) {
            return null;
        }
        return audioResult.getMetaResult();
    }

    public AudioResult saveAudio(String cacheKey, String title, String resourceId, AudioMetaResult result, String containerType) {
        //String cacheKey = cacheKey(audioReq);
        AudioResult audioResult = new AudioResult();
        audioResult.setTitle(title);
        audioResult.setKey(cacheKey);
        audioResult.setContainerType(containerType);
        audioResult.setResourceId(resourceId);
        audioResult.setMetaResult(result);
        return add(audioResult);
    }
}
