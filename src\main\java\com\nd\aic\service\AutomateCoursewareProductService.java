package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.client.AIHubFeignClient;
import com.nd.aic.client.pojo.aihub.AIHubTaskId;
import com.nd.aic.client.pojo.aihub.AIHubTaskReq;
import com.nd.aic.client.pojo.aihub.AIHubTaskResult;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.common.BizConstant;
import com.nd.aic.common.BizHandler;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.common.UserHandler;
import com.nd.aic.entity.AutomateCourseware;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.automate.VoiceArgs;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.entity.flow.AutomateTransactionOutcomes;
import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.enums.PerformanceTypeEnum;
import com.nd.aic.exception.AicException;
import com.nd.aic.monitor.ExceptionEvent;
import com.nd.aic.pojo.CoursewareGenerationReq;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.automate.GptTeachingEvent;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.aic.pojo.dto.PlotInteractionDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.automate.AudioHandler;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.automate.IAutomateProcessor;
import com.nd.aic.service.automate.ManuscriptAutomateProcessor;
import com.nd.aic.service.automate.ObjectConverter;
import com.nd.aic.service.automate.VideoAutomateProcessor;
import com.nd.aic.util.JsonUtils;
import com.nd.aic.util.TextUtils;
import com.nd.gaea.WafException;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.rest.support.WafContext;
import com.nd.ndr.model.DownloadUrlViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class AutomateCoursewareProductService implements InitializingBean {

    private final Map<String, IAutomateProcessor> automateProcessorMap = Maps.newHashMap();
    private final List<IAutomateProcessor> automateProcessors;
    private final VideoAutomateProcessor videoAutomateProcessor;
    private final AutomateTransactionService automateTransactionService;

    private final AudioResourceService audioResourceService;
    private final CoursewareGenerationService coursewareGenerationService;
    private final AutomateHandler automateHandler;
    private final AudioHandler audioHandler;
    private final ManuscriptAutomateProcessor manuscriptAutomateProcessor;

    private static final IPerformancePlanPreprocessor DEFAULT_PERFORMANCE_PLAN_PREPROCESSOR = (automateCourseware, detail, coursewareGenerationReq, teachEventTypeDTO) -> {
    };
    private final Map<String, IPerformancePlanPreprocessor> performancePlanPreprocessorMap = Maps.newHashMap();
    private final MediaService mediaService;
    private final NdrService ndrService;
    private final AIHubFeignClient aiHubFeignClient;

    @Override
    public void afterPropertiesSet() {
        for (IAutomateProcessor automateProcessor : automateProcessors) {
            automateProcessorMap.put(automateProcessor.productMode(), automateProcessor);
        }
        // 表演方案预处理
        // performancePlanPreprocessorMap.put("教学互动", (automateCourseware, detail, coursewareGenerationReq, teachEventTypeDTO) -> {
        //    teachEventTypeDTO.setDialogue("");
        //    teachEventTypeDTO.setDubbing("<#0.5#>");
        // });
        performancePlanPreprocessorMap.put("板书讲解", (automateCourseware, detail, coursewareGenerationReq, teachEventTypeDTO) -> {
            Integer pptIndex = MapUtils.getInteger(detail.getMaterial(), "ppt_index");
            if (null != pptIndex) {
                List<String> pptVideoResources = coursewareGenerationReq.getPptVideoResources();
                if (CollectionUtils.isEmpty(pptVideoResources) && CollectionUtils.isNotEmpty(automateCourseware.getPptDentryInfos())) {
                    pptVideoResources = automateHandler.ppt2VideoResources(automateCourseware.getPptDentryInfos().get(0).getDentryId());
                    coursewareGenerationReq.setPptVideoResources(pptVideoResources);
                }
                if (CollectionUtils.size(pptVideoResources) <= pptIndex) {
                    throw WafI18NException.of("BAD_REQUEST", "pptIndex超出范围", HttpStatus.BAD_REQUEST);
                }
            }
        });
        performancePlanPreprocessorMap.put("default", DEFAULT_PERFORMANCE_PLAN_PREPROCESSOR);
    }

    @Async
    public void splitAudio(AutomateTransaction automateTransaction) {
        cleanAutomateDetails(automateTransaction);
        try {
            UserHandler.getUser(automateTransaction.getCreator());
            if (CollectionUtils.isEmpty(automateTransaction.getAudioDentryInfos())) {
                throw WafI18NException.of("BAD_REQUEST", "音频资源不能为空", HttpStatus.BAD_REQUEST);
            }
            if (CollectionUtils.isEmpty(automateTransaction.getDetailList()) || automateTransaction.getDetailList().stream().noneMatch(e -> StringUtils.isNotBlank(e.getDialogue()))) {
                throw WafI18NException.of("BAD_REQUEST", "节目单/对白不能为空", HttpStatus.BAD_REQUEST);
            }
            String audioDentryId = automateTransaction.getAudioDentryInfos().get(0).getDentryId();
            Dentry dentry = Dentry.get("null", audioDentryId, "");
            String audioUrl = "https://gcdncs.101.com/v0.1/static/" + dentry.getPath();
            List<AutomatePerformanceDetail> detailList = audioHandler.splitAudio(audioUrl, automateTransaction);
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setDetailList(detailList);
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            automateTransaction.setError(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
            UserHandler.clear();
        }
    }

    @Async
    public void extractText(AutomateTransaction automateTransaction) {
        cleanAutomateDetails(automateTransaction);
        videoAutomateProcessor.validate(automateTransaction);
        try {
            String manuscript = videoAutomateProcessor.extractText(automateTransaction);
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setManuscript(manuscript);
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            automateTransaction.setError(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
    }

    @Async
    public void annotateTeachingEvents(AutomateTransaction automateTransaction) {
        // fastgpt-tKTifh9ui1OR9SkzYGRqBpamWiTrLBEuKwdZ4BX0KVCQhCwCUg3iioID2nK
        try {
            cleanAutomateDetails(automateTransaction);
            if (CollectionUtils.isEmpty(automateTransaction.getDetailList())) {
                throw WafI18NException.of("BAD_REQUEST", "教学活动清单不能为空", HttpStatus.BAD_REQUEST);
            }
            List<AutomatePerformanceDetail> detailList = automateHandler.annotateTeachingEvents(automateTransaction);
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setDetailList(detailList);
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            automateTransaction.setError(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
    }

    @Async
    public void disassembleTeachingActivity(AutomateTransaction automateTransaction) {
        // fastgpt-p8rMGPMcFDXCwnhsamIpkW4tVpSsZuD5iuoK91V9rEDP1KLEPizTb
        try {
            cleanAutomateDetails(automateTransaction);
            String manuscript = automateTransaction.getFinalManuscript();
            if (StringUtils.isEmpty(automateTransaction.getFinalManuscript()) && StringUtils.equals("video", automateTransaction.getProductMode())) {
                videoAutomateProcessor.validate(automateTransaction);
                manuscript = videoAutomateProcessor.extractText(automateTransaction);
                videoAutomateProcessor.ensureOutcomes(automateTransaction);
                automateTransaction.getOutcomes().setManuscript(manuscript);
            }
            manuscriptAutomateProcessor.validate(automateTransaction);
            List<GptTeachingEvent> gptTeachings = automateHandler.callDisassembleTeachingActivity(manuscript, automateTransaction.getProductionLine());
            List<AutomatePerformanceDetail> detailList = ObjectConverter.eventParticle2DetailList(gptTeachings);
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setDetailList(detailList);
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            automateTransaction.setError(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
    }

    @Async
    public void generatePerformancePlan(AutomateTransaction automateTransaction) {
        try {
            if (CollectionUtils.isEmpty(automateTransaction.getDetailList())) {
                throw WafI18NException.of("BAD_REQUEST", "教学活动清单不能为空", HttpStatus.BAD_REQUEST);
            }
            cleanAutomateDetails(automateTransaction);
            List<AutomatePerformanceDetail> detailList = automateHandler.relatedPerformancePlan(automateTransaction, automateTransaction.getDetailList(), automateTransaction.getCharacterResource());
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setDetailList(detailList);
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            automateTransaction.setError(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
    }

    public void validate(AutomateTransaction automateTransaction) {
        IAutomateProcessor automateProcessor = automateProcessorMap.get(automateTransaction.getProductMode());
        if (automateProcessor == null) {
            throw WafI18NException.of("INVALID_PRODUCT_MODE", "不支持的生产模式", HttpStatus.BAD_REQUEST);
        }
        automateProcessor.validate(automateTransaction);
    }

    public AutomateTransaction automateTransactionRetry(String transactionId) {
        AutomateTransaction automateTransaction = automateTransactionService.findOne(transactionId);
        automateTransaction.setCreateAt(new Date());
        automateTransaction.setStatus("process");
        automateTransaction.setErrorCode(null);
        automateTransaction.setErrorMessage(null);
        ApplicationContextUtil.getApplicationContext().getBean(AutomateCoursewareProductService.class).automateTransactionAsync(automateTransaction);
        return automateTransaction;
    }

    @Async
    public void automateTransactionAsync(AutomateTransaction automateTransaction) {
        try {
            UserHandler.getUser(automateTransaction.getCreator());
            cleanAutomateDetails(automateTransaction);
            IAutomateProcessor automateProcessor = automateProcessorMap.get(automateTransaction.getProductMode());
            if (automateProcessor == null) {
                throw new RuntimeException("不支持的生产模式");
            }
            automateProcessor.validate(automateTransaction);
            automateProcessor.process(automateTransaction);
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            automateTransaction.setErrorX(e);
            log.error("automate process fail", e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
            UserHandler.clear();
        }
    }

    public AutomateTransaction queryAutomateTransaction(String transactionId) {
        AutomateTransaction automateTransaction = automateTransactionService.findOne(transactionId);
        if (automateTransaction.getCreateAt() == null) {
            automateTransaction.setCreateAt(new Date());
            automateTransaction = automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
        if (StringUtils.equals(automateTransaction.getStatus(), "process")) {
            boolean timeout;
            if (null != automateTransaction.getExpireAt()) {
                timeout = LocalDateTime.now().isAfter(LocalDateTime.fromDateFields(automateTransaction.getExpireAt()));
            } else {
                timeout = LocalDateTime.now().minusMinutes(15).isAfter(LocalDateTime.fromDateFields(automateTransaction.getCreateAt()));
            }
            if (timeout) {
                automateTransaction.setStatus("fail");
                automateTransaction.setErrorCode("TIMEOUT");
                automateTransaction.setErrorMessage("事务处理超时");
                automateTransaction = automateTransactionService.updateAutomateTransaction(automateTransaction);
            }
        }
        return automateTransaction;
    }

    @Async
    public void genCoursewarePerformanceAsync(AutomateCourseware automateCourseware, AutomateTransaction automateTransaction) {
        try {
            automateCourseware = genCoursewarePerformance(automateCourseware);
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setCoursewareModel(automateCourseware.getCoursewareModel());
            automateTransaction.getOutcomes().setDetailList(automateCourseware.getDetailList());
            automateTransaction.getOutcomes().setInteractions(automateCourseware.getInteractions());
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error("generateCoursewareAsync fail", e);
            automateTransaction.setErrorX(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
    }

    public AutomateCourseware genCoursewarePerformance(AutomateCourseware automateCourseware) {
        if (StringUtils.equals(CharacterEnum.NEON.getId(), automateCourseware.getCharacterResource())) {
            if (null == automateCourseware.getVoiceArgs() || null == automateCourseware.getVoiceArgs().getVoiceId()) {
                if (null == automateCourseware.getVoiceArgs()) {
                    automateCourseware.setVoiceArgs(new VoiceArgs());
                }
                automateCourseware.getVoiceArgs().setVoiceId(audioResourceService.mappingVoiceId(automateCourseware.getCharacterResource()));
                automateCourseware.getVoiceArgs().setVoiceSpeed(1.0f);
            }
        }
        return genCoursewarePerformance(automateCourseware, false);
    }

    @SneakyThrows
    public AutomateCourseware genCoursewarePerformance(AutomateCourseware automateCourseware, boolean raiseOriginError) {
        long start = System.currentTimeMillis();
        try {
            UserHandler.getUser(StringUtils.defaultIfBlank(automateCourseware.getCreator(), "10017913"));
            BizHandler.setBizFlag(automateCourseware.getBizFlag());
            CoursewareGenerationReq coursewareGenerationReq = new CoursewareGenerationReq();
            coursewareGenerationReq.setTeachEventTypes(Lists.newArrayList());

            TeachEventTypeDTO latestTeachEventTypeDTO = null;
            for (AutomatePerformanceDetail detail : automateCourseware.getDetailList()) {
                if (StringUtils.equals("transition", detail.getType())) {
                    if (null != latestTeachEventTypeDTO) {
                        latestTeachEventTypeDTO.getRawPerformanceParam().put("transition", true);
                        latestTeachEventTypeDTO.getRawPerformanceParam().put("transition_name", detail.getTransitionName());
                        latestTeachEventTypeDTO.getRawPerformanceParam().put("transition_resource", detail.getTransitionResource());
                    }
                    continue;
                }

                TeachEventTypeDTO teachEventTypeDTO = new TeachEventTypeDTO();
                teachEventTypeDTO.setTeachActivity(detail.getTeachingActivity());
                teachEventTypeDTO.setTeachEventType(detail.getTeachingEvent());
                teachEventTypeDTO.setPerformanceType(detail.getPerformancePlan());
                teachEventTypeDTO.setDialogue(TextUtils.clearBreak(detail.getDialogue()));
                teachEventTypeDTO.setDubbing(detail.getDialogue());
                performancePlanPreprocessorMap.getOrDefault(detail.getPerformancePlan(), DEFAULT_PERFORMANCE_PLAN_PREPROCESSOR).preprocess(automateCourseware, detail, coursewareGenerationReq, teachEventTypeDTO);
                teachEventTypeDTO.setAudioId(detail.getAudioId());
                teachEventTypeDTO.setPronunciationDict(detail.getPronunciationDict());
                teachEventTypeDTO.setTtsSegments(detail.getTtsSegments());
                teachEventTypeDTO.setPerformanceEnhances(detail.getPerformanceEnhances());
                teachEventTypeDTO.setPerformanceAdditional(detail.getPerformanceAdditional());
                teachEventTypeDTO.setInteraction(detail.getInteraction());
                teachEventTypeDTO.setPerformanceTypeAlias(detail.getPerformancePlanAlias());
                if (null != detail.getMaterial()) {
                    teachEventTypeDTO.setPerformanceParam(detail.getMaterial());
                }
                if (Objects.nonNull(detail.getTransition())) {
                    teachEventTypeDTO.getRawPerformanceParam().put("transition", detail.getTransition());
                }
                coursewareGenerationReq.getTeachEventTypes().add(teachEventTypeDTO);
                latestTeachEventTypeDTO = teachEventTypeDTO;
            }
            if (!StringUtils.equals(automateCourseware.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)) {
                addFadeout(coursewareGenerationReq);
            }
            // BGM处理
            coursewareGenerationReq.setAmbientSoundResource(automateCourseware.getAmbientSoundResource());
            processBgm(automateCourseware, coursewareGenerationReq);
            final List<TeachEventTypeDTO> rawTeachEventTypes = Lists.newArrayList(coursewareGenerationReq.getTeachEventTypes());
            coursewareGenerationReq.setTitle(automateCourseware.getTitle());
            coursewareGenerationReq.setGlobalScene(automateCourseware.getSceneResource());
            coursewareGenerationReq.setGlobalCharacter(automateCourseware.getCharacterResource());
            coursewareGenerationReq.setGlobalClothing(automateCourseware.getClothingResource());
            if (null != automateCourseware.getBirthPosition() && !automateCourseware.getBirthPosition().isEmpty()) {
                coursewareGenerationReq.setBirthPosition(automateCourseware.getBirthPosition());
            }
            coursewareGenerationReq.setBizFlag(automateCourseware.getBizFlag());
            coursewareGenerationReq.setNewActionEnabled(automateCourseware.getNewActionEnabled());
            coursewareGenerationReq.setCharacterHandleMode(automateCourseware.getCharacterHandleMode());
            coursewareGenerationReq.setCharacterInstanceId(automateCourseware.getCharacterInstanceId());

            // coursewareGenerationReq.setSubtitleGenerationEnabled(true);
            if (null != automateCourseware.getVoiceArgs()) {
                coursewareGenerationReq.setVoiceId(automateCourseware.getVoiceArgs().getVoiceId());
                coursewareGenerationReq.setVoiceSpeed(automateCourseware.getVoiceArgs().getVoiceSpeed());
            }
            CoursewareModel coursewareModel = coursewareGenerationService.generateCourseware(coursewareGenerationReq, null);
            List<InteractionDTO> interactions = rawTeachEventTypes.stream().map(TeachEventTypeDTO::getInteraction).filter(Objects::nonNull).collect(Collectors.toList());
            automateCourseware.setCoursewareModel(coursewareModel);
            automateCourseware.setInteractions(interactions);
            automateCourseware.setInteractionResult(coursewareModel.getInteractionResult());
            patchAudioId(automateCourseware);
            long end = System.currentTimeMillis();
            long totalTime = end - start;
            coursewareModel.getElapsed().put("total", totalTime);
            return automateCourseware;
        } catch (Exception e) {
            log.error("runFlow", e);
            // 避免本地开发发送消息
            if (!StringUtils.startsWith(InetAddress.getLocalHost().getHostAddress(), "192")) {
                String errorMsg = e.getCause() != null ? e.getCause().toString() : e.toString();
                ApplicationContextUtil.getApplicationContext()
                        .publishEvent(ExceptionEvent.builder()
                                .type("2")
                                .userId(StringUtils.defaultIfBlank(WafContext.getCurrentAccountId(), automateCourseware.getCreator()))
                                .exception(e)
                                .title(automateCourseware.getTitle())
                                .errorMsg(errorMsg)
                                .build());
            }
            if (raiseOriginError) {
                throw e;
            }
            if (e instanceof WafI18NException || e instanceof AicException) {
                throw (WafException) e;
            }
            throw WafI18NException.of("INTERNAL_SERVER_ERROR", "生成课件脚本失败", HttpStatus.INTERNAL_SERVER_ERROR, e);
        } finally {
            log.info("completed task");
            BizHandler.removeBizFlag();
            UserHandler.clear();
        }
    }

    private void addFadeout(CoursewareGenerationReq coursewareGenerationReq) {
        TeachEventTypeDTO teachEventTypeDTO = new TeachEventTypeDTO();
        teachEventTypeDTO.setTeachEventType("讲授新知");
        teachEventTypeDTO.setPerformanceType(PerformanceTypeEnum.END_FADEOUT.getName());
        teachEventTypeDTO.setPerformanceParam(Maps.newHashMap());
        coursewareGenerationReq.getTeachEventTypes().add(teachEventTypeDTO);
    }

    private void processBgm(AutomateCourseware automateCourseware, CoursewareGenerationReq coursewareGenerationReq) throws IOException, InterruptedException {
        if (!StringUtils.equals(automateCourseware.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)
                && StringUtils.isEmpty(automateCourseware.getBgmResource())
                && StringUtils.equals(StringUtils.defaultString(automateCourseware.getBgmType(), BizConstant.BGM_TYPE_RECOMMEND), BizConstant.BGM_TYPE_RECOMMEND)) {
            try {
                String bgmResource = recommendBgm(automateCourseware.getDetailList().stream().map(AutomatePerformanceDetail::getDialogue).collect(Collectors.joining("\n")));
                automateCourseware.setBgmResource(bgmResource);
            } catch (Exception e) {
                log.error("recommend bgm failed", e);
            }
        }
        if (StringUtils.isNotEmpty(automateCourseware.getBgmResource())) {
            automateCourseware.setBgmType(BizConstant.BGM_TYPE_NONE);
        }
        coursewareGenerationReq.setBgmType(StringUtils.defaultString(automateCourseware.getBgmType(), BizConstant.BGM_TYPE_RECOMMEND));
        coursewareGenerationReq.setBackgroundMusicResourceId(automateCourseware.getBgmResource());
        if (StringUtils.equalsIgnoreCase("upload", automateCourseware.getBgmType()) && CollectionUtils.isNotEmpty(automateCourseware.getBgmDentryInfos())) {
            String audioId = automateCourseware.getBgmDentryInfos().get(0).getDentryId();
            String mp3Url = "https://cdncs.101.com/v0.1/download?dentryId=" + audioId;
            File bgmFile = mediaService.download(mp3Url, String.format("%s.mp3", audioId), true);
            coursewareGenerationReq.setBgmType(audioId);
            coursewareGenerationReq.setBgmFile(bgmFile);
        } else if (StringUtils.equals(automateCourseware.getBizFlag(), BizConstant.BIZ_FLAG_VLAB) && StringUtils.isNotEmpty(automateCourseware.getBgmResource())) {
            // 实验室需要将BGM下载进行混音
            Map<String, DownloadUrlViewModel> urlViewModelMap = ndrService.getResourceUrlModel(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_COURSEWARE_CONTAINER_ID, automateCourseware.getBgmResource(), "href");
            DownloadUrlViewModel downloadUrlViewModel = urlViewModelMap.get("href");
            String url = "https://" + downloadUrlViewModel.getHost() + downloadUrlViewModel.getUrl();
            url = StringUtils.replace(url, "gcdncs.101.com", "cdncs.101.com");
            url = StringUtils.substringBefore(url, "?");
            String filename = String.format("%s.mp3", automateCourseware.getBgmResource());
            File file = mediaService.download(url, filename, true);
            coursewareGenerationReq.setBgmType(String.format("ndr:%s", automateCourseware.getBgmResource()));
            coursewareGenerationReq.setBgmFile(file);
        }
        // if (StringUtils.equals(automateCourseware.getBizFlag(), BizConstant.BIZ_FLAG_VLAB) && null != coursewareGenerationReq.getBgmFile()) {
        //     coursewareGenerationReq.setBgmFile(mediaService.processBgm(coursewareGenerationReq.getBgmFile()));
        //     log.info("process bgm local file: {}", coursewareGenerationReq.getBgmFile());
        // }
    }

    private String recommendBgm(String text) throws ExecutionException, InterruptedException, TimeoutException {
        String userId = StringUtils.defaultString(UserHandler.getUser(null), AIHubFeignClient.DEFAULT_USER_ID);
        final String bgmRecommendBotId = "3cfaccdf-5b11-47bb-bce1-f0dbcb8e4a58";
        AIHubTaskReq aiHubTaskReq = new AIHubTaskReq();
        aiHubTaskReq.setQuery("null");
        aiHubTaskReq.setInputs(Maps.newHashMap());
        aiHubTaskReq.getInputs().put("text", text);
        aiHubTaskReq.setUserId(userId);
        AIHubTaskId taskId = aiHubFeignClient.createTask(bgmRecommendBotId, aiHubTaskReq);
        log.info("submit task with text {}, task: {}", text, taskId);
        final AtomicBoolean isTimeout = new AtomicBoolean(false);
        try {
            return CompletableFuture.supplyAsync(() -> {
                String result = null;
                while (result == null) {
                    if (isTimeout.get()) {
                        break;
                    }
                    AIHubTaskResult aiHubTaskResult = aiHubFeignClient.queryTask(bgmRecommendBotId, taskId.getAsyncTaskId());
                    // log.info("query task {} result: {}", text, JsonUtils.toJson(aiHubTaskResult));
                    if (StringUtils.equals(aiHubTaskResult.getStatus(), "running") || StringUtils.equals(aiHubTaskResult.getStatus(), "init")) {
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException ignore) {
                            break;
                        }
                        continue;
                    }

                    AIHubTaskResult.Answer answer = AIHubTaskResult.getAnswerOrRaise(aiHubTaskResult);
                    log.info("AIHub task result: {}", JsonUtils.toJson(answer));
                    result = (String) answer.getData().getOutputs().get("text");
                    if (StringUtils.isEmpty(result)) {
                        throw WafI18NException.of("AIHUB_TASK_FAILED", "BGM推荐失败: " + answer.getAnswer(), HttpStatus.BAD_REQUEST);
                    }
                }
                return result;
            }).get(30, TimeUnit.SECONDS);
        } finally {
            isTimeout.set(true);
        }
    }

    private void patchAudioId(AutomateCourseware automateCourseware) {
        String voiceId = null == automateCourseware.getVoiceArgs() ? null : automateCourseware.getVoiceArgs().getVoiceId();
        Float voiceSpeed = null == automateCourseware.getVoiceArgs() ? null : automateCourseware.getVoiceArgs().getVoiceSpeed();
        for (AutomatePerformanceDetail detail : automateCourseware.getDetailList()) {
            if (StringUtils.equals("transition", detail.getType())) {
                // 过渡动画不需要生成音频
                continue;
            }
            if (StringUtils.isBlank(detail.getDialogue()) || StringUtils.isNotBlank(detail.getAudioId())) {
                continue;
            }
            if (StringUtils.isBlank(detail.getAudioId())) {
                TtsSegment ttsSegment = new TtsSegment()
                        .setVoiceId(voiceId)
                        .setVoiceSpeed(voiceSpeed)
                        .setText(detail.getDialogue())
                        .setPronunciationDict(detail.getPronunciationDict());
                String audioId = audioResourceService.queryMergeTtsCache(automateCourseware.getCharacterResource(), ttsSegment);
                if (StringUtils.isNotBlank(audioId)) {
                    detail.setAudioId(audioId);
                    detail.setAudioGenType("tts");
                } else {
                    log.error("audioId mapping failed: {}: {}", automateCourseware.getId(), ttsSegment);
                }
            }
        }
    }

    private void cleanAutomateDetails(AutomateTransaction automateTransaction) {
        if (null != automateTransaction.getDetailList()) {
            automateTransaction.getDetailList().removeIf(e -> StringUtils.equals("transition", e.getType()));
        }
    }

    @Async
    public void generateInteractionsAsync(AutomateTransaction automateTransaction) {
        try {
            List<PlotInteractionDTO> dialogues = automateTransaction.getDetailList().stream()
                    .map(AutomatePerformanceDetail::getDialogue)
                    .map(PlotInteractionDTO::new)
                    .collect(Collectors.toList());
            List<PlotInteractionDTO> result = generateInteractions(dialogues);
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setPlotInteractions(result);
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            automateTransaction.setError(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
    }

    public List<PlotInteractionDTO> generateInteractions(List<PlotInteractionDTO> plotInteractionDTOS) {
        for (PlotInteractionDTO plotInteractionDTO : plotInteractionDTOS) {
            plotInteractionDTO.setDialogue(StringUtils.defaultString(plotInteractionDTO.getDialogue(), ""));
            plotInteractionDTO.setInteraction(null);
        }
        return automateHandler.generateInteractions(plotInteractionDTOS);
    }

    public interface IPerformancePlanPreprocessor {
        void preprocess(AutomateCourseware automateCourseware, AutomatePerformanceDetail detail, CoursewareGenerationReq coursewareGenerationReq, TeachEventTypeDTO teachEventTypeDTO);
    }
}
