package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.client.AIHubFeignClient;
import com.nd.aic.client.pojo.aihub.AIHubTaskId;
import com.nd.aic.client.pojo.aihub.AIHubTaskReq;
import com.nd.aic.client.pojo.aihub.AIHubTaskResult;
import com.nd.aic.common.BizConstant;
import com.nd.aic.entity.AutomateCourseware;
import com.nd.aic.exception.AicException;
import com.nd.aic.repository.AutomateCoursewareRepository;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class AutomateCoursewareService extends BaseService<AutomateCourseware, String> implements InitializingBean {

    private final AIHubFeignClient aiHubFeignClient;
    private final AutomateCoursewareRepository automateCoursewareRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
        mongoTemplate.updateMulti(
                new Query(Criteria.where("bizKey").exists(false)),
                new Update().set("bizKey", "0"),
                AutomateCourseware.class);
    }

    @Override
    protected Module module() {
        return new Module("AUTOMATE_COURSEWARE");
    }

    @Override
    public Items<AutomateCourseware> list(ListParam<AutomateCourseware> listParam) {
        listParam.setExcludeFields(Lists.newArrayList("detailList"));
        return super.list(listParam);
    }

    @Override
    public List<AutomateCourseware> findAll() {
        List<AutomateCourseware> automateCoursewares = automateCoursewareRepository.findAll();
        Collections.reverse(automateCoursewares);
        return automateCoursewares;
    }

    // @Override
    // public AutomateCourseware findOne(String id) {
    //     AutomateCourseware automateCourseware = super.findOne(id);
    //     if (automateCourseware == null) {
    //         throw module().notFound();
    //     }
    //     return automateCourseware;
    // }

    public List<AutomateCourseware> findByAiTaskStatusIn(List<String> aiTaskStatusList) {
        return automateCoursewareRepository.findByAiTaskStatusIn(aiTaskStatusList);
    }

    public AutomateCourseware deleteOne(String id) {
        AutomateCourseware automateCourseware = automateCoursewareRepository.findOne(id);
        if (automateCourseware != null) {
            automateCoursewareRepository.delete(automateCourseware);
        }
        return automateCourseware;
    }

    @Override
    public AutomateCourseware add(AutomateCourseware automateCourseware) {
        if (StringUtils.isBlank(automateCourseware.getProgramId())) {
            automateCourseware.setId(null);
        }
        automateCourseware.setCreateAt(new Date());
        automateCourseware.setUpdateAt(new Date());
        if (StringUtils.isNotBlank(WafContext.getCurrentAccountId())) {
            automateCourseware.setCreator(WafContext.getCurrentAccountId());
        }
        if (StringUtils.isBlank(automateCourseware.getBgmType())) {
            automateCourseware.setBgmType(BizConstant.BGM_TYPE_RECOMMEND);
        }
        return super.add(automateCourseware);
    }

    public AutomateCourseware update(AutomateCourseware automateCourseware, boolean ignoreNotFound) {
        if (StringUtils.isEmpty(automateCourseware.getId())) {
            throw module().nullId();
        }
        AutomateCourseware dbCourseware;
        if (ignoreNotFound) {
            dbCourseware = findOne(automateCourseware.getId());
            if (dbCourseware == null) {
                return null;
            }
        } else {
            dbCourseware = findStrictOne(automateCourseware.getId());
        }
        automateCourseware.setId(null);
        automateCourseware.setCreateAt(null);
        automateCourseware.setCreator(null);
        BeanUtil.copyProperties(automateCourseware, dbCourseware, CopyOptions.create().ignoreNullValue());
        dbCourseware.setUpdateAt(new Date());
        return automateCoursewareRepository.save(dbCourseware);
    }

    public AutomateCourseware addAndSubmitAiProductionTask(AutomateCourseware automateCourseware) {
        if (StringUtils.isBlank(automateCourseware.getRequirement())) {
            throw AicException.ofIllegalArgument("需求不能为空");
        }
        if (StringUtils.equals(automateCourseware.getRequirementMode(), "录课讲稿")) {
            if (null != automateCourseware.getLessonRequirement() && BooleanUtils.isTrue(automateCourseware.getLessonRequirement().getStylized())) {
                automateCourseware.setManuscriptStyle("已风格化");
            } else if (StringUtils.isEmpty(automateCourseware.getManuscriptStyle())) {
                Map<String, String> styleMap = Maps.newHashMap();
                styleMap.put("47b23a4a-f491-42f6-ab20-edeed2cc6a26", "轻松闲谈");
                styleMap.put("ae0213cd-6e53-4188-9ddb-a41f8fda8674", "娓娓道来");
                automateCourseware.setManuscriptStyle(styleMap.getOrDefault(automateCourseware.getCharacterResource(), "轻松闲谈"));
            }
        }
        automateCourseware = add(automateCourseware);
        // 提交任务
        String botId = "352d1dc1-45d6-4eb2-a43b-6f00c04f2087";
        AIHubTaskReq aiHubTaskReq = new AIHubTaskReq();
        aiHubTaskReq.setQuery(automateCourseware.getRequirement());
        Map<String, Object> inputs = Maps.newHashMap();
        if (null == automateCourseware.getInputParams() && null != automateCourseware.getLessonRequirement()) {
            inputs.putAll(automateCourseware.getLessonRequirement().toParamDict());
        }
        inputs.put("input_mode", automateCourseware.getRequirementMode());
        inputs.put("id", automateCourseware.getId());
        inputs.put("name", automateCourseware.getTitle());
        inputs.put("character", automateCourseware.getCharacterResource());
        inputs.put("scene", automateCourseware.getSceneResource());
        inputs.put("material_files_json", automateCourseware.getMaterialFilesJson());
        inputs.put("is_sync_get_video", "否");
        inputs.put("script_style", automateCourseware.getManuscriptStyle());
        inputs.put("program_content", automateCourseware.getProgramContent());
        inputs.put("key_content", automateCourseware.getKeyContent());
        aiHubTaskReq.setInputs(inputs);

        if (CollectionUtils.isNotEmpty(automateCourseware.getPptDentryInfos())) {
            Map<String, Object> file = Maps.newHashMap();
            file.put("type", "document");
            file.put("transfer_method", "remote_url");
            file.put("url", "https://cdncs.101.com/v0.1/download?dentryId=" + automateCourseware.getPptDentryInfos().get(0).getDentryId());
            List<Map<String, Object>> files = new ArrayList<>(1);
            files.add(file);
            aiHubTaskReq.setFiles(files);
        }

        AIHubTaskId createTaskResp = aiHubFeignClient.createTask(botId, aiHubTaskReq);
        automateCourseware.setBotId(botId);
        automateCourseware.setAiTaskId(createTaskResp.getAsyncTaskId());
        automateCourseware.setAiTaskStatus(AIHubTaskResult.STATUS_INIT);
        return update(automateCourseware);
    }
}
