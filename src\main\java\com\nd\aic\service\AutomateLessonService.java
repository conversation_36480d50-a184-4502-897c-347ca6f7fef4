package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.client.AiPptFeignClient;
import com.nd.aic.client.AicFeignClient;
import com.nd.aic.client.pojo.aippt.TaskDetailDto;
import com.nd.aic.common.BizConstant;
import com.nd.aic.entity.AutomateCourseware;
import com.nd.aic.entity.AutomateLesson;
import com.nd.aic.entity.RenderTask;
import com.nd.aic.entity.VideoConcat;
import com.nd.aic.entity.automate.CallbackArguments;
import com.nd.aic.entity.flow.LessonProgram;
import com.nd.aic.entity.flow.LessonTeachingActivity;
import com.nd.aic.entity.flow.MaterialFile;
import com.nd.aic.enums.RenderTaskStatusEnum;
import com.nd.aic.pojo.AutomateTask;
import com.nd.aic.pojo.automate.GptSceneCharacter;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.aic.repository.AutomateLessonRepository;
import com.nd.aic.util.JsonUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class AutomateLessonService extends BaseService<AutomateLesson, String> implements InitializingBean {

    @Value("${ENABLE_VIDEO_CONCAT:true}")
    private Boolean enableVideoConcat;
    private final AutomateLessonRepository automateLessonRepository;
    private final FastGptRetryService fastGptRetryService;
    private final AutomateCoursewareService automateCoursewareService;
    private final MongoTemplate mongoTemplate;
    private final RenderTaskService renderTaskService;
    private final VideoConcatService videoConcatService;
    private final AiPptFeignClient aiPptFeignClient;
    private final AicFeignClient aicFeignClient;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化时可以进行一些必要的设置或检查
        log.info("AutomateLessonService initialized with repository: {}", automateLessonRepository.getClass().getName());
        mongoTemplate.updateMulti(new Query(Criteria.where("source").exists(false)), new Update().set("source", "lesson"), AutomateLesson.class);
    }

    @Override
    protected Module module() {
        return new Module("AUTOMATE_LESSON");
    }

    @Override
    public Items<AutomateLesson> list(ListParam<AutomateLesson> listParam) {
        // listParam.setExcludeFields(Lists.newArrayList("programs"));
        return super.list(listParam);
    }

    @Override
    public List<AutomateLesson> findAll() {
        List<AutomateLesson> automateLessons = automateLessonRepository.findAll();
        Collections.reverse(automateLessons);
        return automateLessons;
    }

    @Override
    public AutomateLesson findOne(String id) {
        return super.findOne(id);
    }

    public AutomateLesson getLessonByAiPptTaskId(Long taskId) {
        String source = BizConstant.SOURCE_AIPPT_PREFIX + BizConstant.AIPPT_BIZ_TYPE_CRAFT;
        AutomateLesson automateLesson = automateLessonRepository.findBySourceAndExecutionId(source, taskId.toString());
        if (automateLesson == null) {
            TaskDetailDto taskDetailDto = aiPptFeignClient.taskDetail(taskId, WafContext.getCurrentAccountId()).get(0);
            automateLesson = new AutomateLesson();
            automateLesson.setSource(source);
            automateLesson.setWorkflowInstanceId(taskId.toString());
            automateLesson.setExecutionId(taskId.toString());
            automateLesson.setCreator(taskDetailDto.getTask().getCreateUid());
            automateLesson.setTitle(taskDetailDto.getTask().getCourseName());
            automateLesson.setTeachingActivityName(taskDetailDto.getTask().getCourseName());
            automateLesson.setTeachingActivityId(taskDetailDto.getTask().getCourseCode());
            automateLesson.setOriginalTeachingDesignType("original_manuscript");
            automateLesson.setContent(MapUtils.getString(taskDetailDto.getFormList().get(0).getDataBus(), "teach_dialogue"));
            automateLesson = add(automateLesson);
        }
        return automateLesson;
    }

    public AutomateLesson deleteOne(String id) {
        AutomateLesson automateLesson = automateLessonRepository.findOne(id);
        if (automateLesson != null) {
            automateLessonRepository.delete(automateLesson);
        }
        return automateLesson;
    }

    @Override
    public AutomateLesson add(AutomateLesson automateLesson) {
        automateLesson.setId(null);
        automateLesson.setCreateAt(new Date());
        automateLesson.setUpdateAt(new Date());
        if (StringUtils.isNotEmpty(WafContext.getCurrentAccountId())) {
            automateLesson.setCreator(WafContext.getCurrentAccountId());
        }
        return automateLessonRepository.save(automateLesson);
    }

    public AutomateLesson generatePrograms(AutomateLesson automateLesson) {
        String FAST_GPT_KEY = "fastgpt-vdfowthAY75qmhMHRbXy0c2r9jdjQi9AGRd38IZSNnMD2pFLV4S3WiYXpmQcp";
        Map<String, Object> variables = Maps.newHashMap();

        String content = automateLesson.getContent();
        if (CollectionUtils.isNotEmpty(automateLesson.getTeachingActivities())) {
            content = automateLesson.getTeachingActivities().stream()
                    // 排队互动数据
                    .filter(e -> !StringUtils.equals(e.getType(), "互动"))
                    .map(LessonTeachingActivity::getContent)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining("\n"));
            String interactions = automateLesson.getTeachingActivities().stream()
                    // 仅保留互动数据
                    .filter(e -> StringUtils.equals(e.getType(), "互动"))
                    .map(LessonTeachingActivity::getContent)
                    .filter(StringUtils::isNotEmpty).map(e -> "- " + e)
                    .collect(Collectors.joining("\n"));
            if (StringUtils.isNotEmpty(interactions)) {
                variables.put("interactions", interactions);
            }
        }

        if (!StringUtils.equals(automateLesson.getSource(), BizConstant.SOURCE_BCS_WORKFLOW)) {
            variables.put("requirement", "仅拆解出一个节目单");
        }

        try {
            List<LessonProgram> items = fastGptRetryService.fastGptCallForJsonResult(content, FAST_GPT_KEY, variables, (data) -> JsonUtils.parseList(data, LessonProgram.class));
            for (LessonProgram item : items) {
                item.setKeyContent(null);

                if (CollectionUtils.isNotEmpty(automateLesson.getMaterialFiles())) {
                    List<MaterialFile> materialFiles = automateLesson.getMaterialFiles().stream()
                            .filter(e -> StringUtils.contains(item.getOriginalScript(), e.getDescription()))
                            .collect(Collectors.toList());
                    item.setMaterialFiles(materialFiles);
                    item.setMaterials(materialFiles.stream().map(e -> {
                        DentryInfo dentryInfo = new DentryInfo();
                        dentryInfo.setDentryId(e.getId());
                        dentryInfo.setName(e.getName());
                        return dentryInfo;
                    }).collect(Collectors.toList()));
                }
            }
            automateLesson.setPrograms(items);
        } catch (Exception e) {
            log.error("generatePrograms error: {}", e.getMessage());
            throw WafI18NException.of("FAST_GPT_ERROR", "录课逐字稿无法拆解为教学步骤节目单，请确认！", HttpStatus.BAD_REQUEST, e);
        }
        return automateLesson;
    }

    public AutomateLesson update(AutomateLesson automateLesson) {
        if (StringUtils.isEmpty(automateLesson.getId())) {
            throw module().nullId();
        }
        AutomateLesson one = findStrictOne(automateLesson.getId());
        automateLesson.setVideoUrl(one.getVideoUrl());
        return super.update(automateLesson);
    }

    public AutomateLesson submitLessonTask(AutomateLesson automateLesson) {
        AutomateLesson finalAutomateLesson = automateLesson;
        automateLesson.getPrograms().forEach(program -> {
            AutomateCourseware automateCourseware = new AutomateCourseware();
            automateCourseware.setCreateAt(new Date());
            automateCourseware.setUpdateAt(new Date());
            automateCourseware.setCreator(StringUtils.defaultString(WafContext.getCurrentAccountId(), finalAutomateLesson.getCreator()));
            automateCourseware.setRequirementMode("录课讲稿");
            automateCourseware.setProductMode("manuscript");
            automateCourseware.setProductionLine("courseware");
            automateCourseware.setBizFlag("lesson");
            automateCourseware.setBizKey(finalAutomateLesson.getId());
            automateCourseware.setTitle(program.getProgramName());
            automateCourseware.setCharacterResource(finalAutomateLesson.getCharacter());
            automateCourseware.setSceneResource("a1c04877-67b8-4530-8c90-c93399f710be");
            automateCourseware.setRequirement(program.getOriginalScript());
            automateCourseware.setManuscript(program.getOriginalScript());
            automateCourseware.setProgramId(program.getProgramId());
            automateCourseware.setProgramName(program.getProgramName());
            automateCourseware.setProgramContent(program.getProgramContent());
            automateCourseware.setKeyContent(program.getKeyContent());
            if (CollectionUtils.isNotEmpty(program.getMaterialFiles())) {
                List<MaterialFile> materialFiles = program.getMaterialFiles().stream()
                        .filter(e -> StringUtils.containsAny(StringUtils.lowerCase(e.getType()), "video", "image"))
                        .collect(Collectors.toList());
                automateCourseware.setMaterialFilesJson(JsonUtils.toJson(materialFiles));
            }
            if (null != finalAutomateLesson.getOriginalRequirement()) {
                automateCourseware.setInputParams(finalAutomateLesson.getOriginalRequirement().toParamDict());
            }
            automateCourseware = automateCoursewareService.addAndSubmitAiProductionTask(automateCourseware);
            program.setAutomateCoursewareId(automateCourseware.getId());
            program.setTaskId(automateCourseware.getAiTaskId());
        });
        automateLesson = save(automateLesson);
        return automateLesson;
    }

    public AutomateCourseware addAndSubmitAiProductionTask(AutomateTask automateTask) {
        if (StringUtils.isBlank(automateTask.getOriginalScript())) {
            throw WafI18NException.of("NOT_FOUND", "课件需求不能为空", HttpStatus.BAD_REQUEST);
        }
        AutomateLesson automateLesson = getOrCreateAssociatedLesson(automateTask);

        AutomateCourseware automateCourseware = new AutomateCourseware();
        automateCourseware.setId(automateTask.getId());
        automateCourseware.setCreateAt(new Date());
        automateCourseware.setUpdateAt(new Date());
        automateCourseware.setCreator(automateTask.getCreator());
        if (StringUtils.isNotEmpty(WafContext.getCurrentAccountId())) {
            automateCourseware.setCreator(WafContext.getCurrentAccountId());
        }
        automateCourseware.setTitle(automateTask.getProgramName());
        automateCourseware.setCharacterResource(automateLesson.getCharacter());
        automateCourseware.setSceneResource(automateLesson.getScene());
        automateCourseware.setRequirementMode("录课讲稿");
        automateCourseware.setRequirement(automateTask.getOriginalScript());
        automateCourseware.setManuscript(automateTask.getOriginalScript());
        automateCourseware.setProductMode("manuscript");
        automateCourseware.setProductionLine("courseware");
        automateCourseware.setBizFlag("lesson");
        automateCourseware.setBizKey(automateLesson.getId());
        automateCourseware.setProgramId(automateTask.getProgramId());
        automateCourseware.setProgramName(automateTask.getProgramName());
        automateCourseware.setProgramContent(automateTask.getProgramContent());
        automateCourseware.setKeyContent(automateTask.getProgramKnowledge());
        AutomateCourseware oldOne = automateCoursewareService.findOne(automateTask.getId());
        if (oldOne != null) {
            return oldOne;
        } else {
            automateCourseware = automateCoursewareService.addAndSubmitAiProductionTask(automateCourseware);
        }

        LessonProgram lessonProgram = getLessonProgram(automateTask, automateCourseware);
        automateLesson.setContent(Lists.newArrayList(automateLesson.getContent(), automateTask.getOriginalScript()).stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("\n")));
        checkProgramIndex(automateTask, automateLesson.getPrograms(), lessonProgram);
        automateLesson.getPrograms().sort(Comparator.comparingInt(LessonProgram::getProgramIndex));
        LessonProgram existsOne = automateLesson.getPrograms().stream().filter(e -> StringUtils.equals(e.getProgramId(), lessonProgram.getProgramId())).findFirst().orElse(null);
        if (null != existsOne) {
            BeanUtils.copyProperties(lessonProgram, existsOne);
        } else {
            automateLesson.getPrograms().add(lessonProgram);
        }
        save(automateLesson);
        return automateCourseware;
    }

    private static LessonProgram getLessonProgram(AutomateTask automateTask, AutomateCourseware automateCourseware) {
        LessonProgram lessonProgram = new LessonProgram();
        lessonProgram.setOriginalTask(automateTask);
        lessonProgram.setProgramId(automateTask.getProgramId());
        lessonProgram.setProgramName(automateTask.getProgramName());
        lessonProgram.setProgramContent(automateTask.getProgramContent());
        lessonProgram.setOriginalScript(automateTask.getOriginalScript());
        lessonProgram.setImplementPlanName("AI老师讲解");
        lessonProgram.setImplementPlanType("表演型");
        lessonProgram.setAutomateCoursewareId(automateCourseware.getId());
        lessonProgram.setTaskId(automateCourseware.getAiTaskId());
        return lessonProgram;
    }

    private void checkProgramIndex(AutomateTask automateTask, List<LessonProgram> lessonPrograms, LessonProgram currentLessonProgram) {
        int programIndex = calcProgramIndex(automateTask, lessonPrograms, currentLessonProgram);
        currentLessonProgram.setProgramIndex(programIndex);
        for (LessonProgram lessonProgram : lessonPrograms) {
            if (null == lessonProgram.getProgramIndex()) {
                programIndex = calcProgramIndex(automateTask, lessonPrograms, lessonProgram);
                lessonProgram.setProgramIndex(programIndex);
            }
        }
    }

    private int calcProgramIndex(AutomateTask automateTask, List<LessonProgram> lessonPrograms, LessonProgram lessonProgram) {
        boolean allProgramExistsId = automateTask.getPrograms().stream().allMatch(e -> StringUtils.isNotEmpty(e.getProgramId()));
        int programIndex = -1;
        if (allProgramExistsId) {
            log.info("所有节目单已存在ID，开始根据节目单ID匹配索引");
            programIndex = automateTask.getPrograms().stream().map(AutomateTask.Program::getProgramId).collect(Collectors.toList()).indexOf(lessonProgram.getProgramId());
            if (-1 == programIndex) {
                String programs = automateTask.getPrograms().stream().map(e -> String.format("%s(%s)", e.getProgramName(), e.getProgramId())).collect(Collectors.joining(", "));
                log.warn("未找到对应ID的节目单，请检查输入参数: {} -> {}", automateTask.getProgramId(), programs);
            }
        }
        if (-1 == programIndex) {
            log.info("所有节目单未存在ID，根据现存节目计算索引");
            LessonProgram existsOne = lessonPrograms.stream().filter(e -> StringUtils.equals(e.getProgramId(), automateTask.getProgramId())).findFirst().orElse(null);
            if (null != existsOne) {
                programIndex = lessonPrograms.indexOf(existsOne);
            } else {
                programIndex = lessonPrograms.size();
            }
        }
        return programIndex;
    }

    private AutomateLesson getOrCreateAssociatedLesson(AutomateTask automateTask) {
        AutomateLesson automateLesson = automateLessonRepository.findByWorkflowInstanceIdAndExecutionId(automateTask.getWorkflowInstanceId(), automateTask.getExecutionId());
        if (automateLesson == null) {
            automateLesson = new AutomateLesson();
            automateLesson.setWorkflowInstanceId(automateTask.getWorkflowInstanceId());
            automateLesson.setExecutionId(automateTask.getExecutionId());
            automateLesson.setCreateAt(new Date());
            automateLesson.setUpdateAt(new Date());
            automateLesson.setCreator(automateLesson.getCreator());
            if (StringUtils.isNotEmpty(WafContext.getCurrentAccountId())) {
                automateLesson.setCreator(WafContext.getCurrentAccountId());
            }
            automateLesson.setContent("");
            automateLesson.setPrograms(Lists.newArrayList());
            automateLesson.setSource(StringUtils.defaultString(automateTask.getSource(), "bcs_workflow"));
            // 关联表单地址
            automateLesson.setWorkflowUrl(automateTask.getWorkflowUrl());
            automateLesson.setRequirementUrl(automateTask.getRequirementUrl());
            automateLesson.setDesignUrl(automateTask.getDesignUrl());
            automateLesson.setActivityUrl(automateTask.getActivityUrl());
            automateLesson.setImplPlanUrl(automateTask.getImplPlanUrl());

            automateLesson.setTeachingActivityId(automateTask.getTeachingActivityId());
            automateLesson.setTeachingActivityName(automateTask.getTeachingActivityName());
            automateLesson.setLearningObject(automateTask.getLearningObject());
            automateLesson.setCharacter(automateTask.getCharacter());
            automateLesson.setScene(automateTask.getScene());
            recommendSceneAndCharacter(automateTask, automateLesson, false);

            automateLesson = automateLessonRepository.save(automateLesson);
        }
        if (StringUtils.isEmpty(automateLesson.getScene()) || StringUtils.isEmpty(automateLesson.getCharacter())) {
            // 处理旧数据
            recommendSceneAndCharacter(automateTask, automateLesson, true);
            automateLesson = automateLessonRepository.save(automateLesson);
        }
        if (StringUtils.isBlank(automateLesson.getScene()) || StringUtils.isBlank(automateLesson.getCharacter())) {
            throw new IllegalArgumentException("场景和角色不能为空");
        }
        return automateLesson;
    }

    private void recommendSceneAndCharacter(AutomateTask automateTask, AutomateLesson automateLesson, boolean old) {
        GptSceneCharacter gptSceneCharacter;
        gptSceneCharacter = new GptSceneCharacter("a1c04877-67b8-4530-8c90-c93399f710be", "47b23a4a-f491-42f6-ab20-edeed2cc6a26");
        if (StringUtils.isEmpty(automateLesson.getScene()) || StringUtils.isEmpty(automateLesson.getCharacter())) {
            if (StringUtils.isEmpty(automateLesson.getCharacter())) {
                automateLesson.setCharacter(StringUtils.defaultString(automateTask.getCharacter(), gptSceneCharacter.getCharacter()));
            }
            if (StringUtils.isEmpty(automateLesson.getScene())) {
                automateLesson.setScene(StringUtils.defaultString(automateTask.getScene(), gptSceneCharacter.getScene()));
            }
        }
    }

    public VideoConcat concat(String id) {
        AutomateLesson automateLesson = automateLessonRepository.findOne(id);
        if (automateLesson == null) {
            throw module().notFound();
        }
        return concat(automateLesson, false);
    }

    public VideoConcat concat(AutomateLesson automateLesson, boolean retry) {
        if (BooleanUtils.isNotTrue(enableVideoConcat)) {
            throw WafI18NException.of("NOT_IMPLEMENTED", "不支持此功能", HttpStatus.NOT_IMPLEMENTED);
        }
        if (CollectionUtils.isEmpty(automateLesson.getPrograms())) {
            throw WafI18NException.of("INVALID_ARGUMENT", "节目单为空", HttpStatus.BAD_REQUEST);
        }
        String env = ApplicationContextUtil.getApplicationContext().getEnvironment().getProperty("sdp.env");

        List<String> renderScriptIds = Lists.newArrayList();
        List<String> videoUrls = Lists.newArrayList();
        for (int i = 0; i < automateLesson.getPrograms().size(); i++) {
            LessonProgram e = automateLesson.getPrograms().get(i);
            int seq = i + 1;
            if (StringUtils.isBlank(e.getAutomateCoursewareId())) {
                throw WafI18NException.of("NOT_COMPLETED", String.format("节目单(%d)未完成", seq), HttpStatus.BAD_REQUEST);
            }
            AutomateCourseware automateCourseware = automateCoursewareService.findOne(e.getAutomateCoursewareId());
            if (StringUtils.equals(automateCourseware.getAiTaskStatus(), "failed") && retry) {
                throw WafI18NException.of("RETRYABLE_FAILED", String.format("节目单(%d)生产线执行失败", seq), HttpStatus.BAD_REQUEST);
            }

            int tryCount = 0;
            RenderTask renderTask = null;
            if (StringUtils.equals(env, "test")) {
                Items<RenderTask> items = aicFeignClient.listRenderTask("biz_id eq " + e.getAutomateCoursewareId(), true);
                tryCount = items.getItems().size();
                if (CollectionUtils.isNotEmpty(items.getItems())) {
                    renderTask = items.getItems().get(0);
                }
            } else {
                List<RenderTask> renderTasks = renderTaskService.findByBizId(e.getAutomateCoursewareId());
                tryCount = renderTasks.size();
                if (CollectionUtils.isNotEmpty(renderTasks)) {
                    renderTask = renderTasks.get(0);
                }
            }
            if (renderTask == null) {
                throw WafI18NException.of("NOT_COMPLETED", String.format("节目单(%d)渲染任务未生成", seq), HttpStatus.BAD_REQUEST);
            }
            if (retry && renderTask.getStatus() == RenderTaskStatusEnum.FAILED && tryCount < 3) {
                // 重试渲染
                log.info("节目单({})渲染任务失败，准备重试", seq);
                RenderTask retryTask = new RenderTask();
                BeanUtils.copyProperties(renderTask, retryTask);
                retryTask.setId(null);
                retryTask.setStatus(RenderTaskStatusEnum.PENDING);
                renderTask = renderTaskService.add(retryTask);
            }
            if (StringUtils.isEmpty(renderTask.getVideoUrl())) {
                throw WafI18NException.of("NOT_COMPLETED", String.format("节目单(%d)视频未渲染生成", seq), HttpStatus.BAD_REQUEST);
            }
            renderScriptIds.add(renderTask.getId());
            videoUrls.add(renderTask.getVideoUrl());
        }

        VideoConcat videoConcat = new VideoConcat();
        videoConcat.setTitle(StringUtils.defaultString(automateLesson.getTitle(), automateLesson.getTeachingActivityName()));
        videoConcat.setCreateAt(new Date());
        videoConcat.setKey("lesson_concat:" + automateLesson.getId());
        // 设置过期时间为1小时后
        videoConcat.setExpireAt(LocalDateTime.now().plusMinutes(60).toDate());
        videoConcat.setVideoUrls(videoUrls);
        videoConcat.setRenderScriptIds(renderScriptIds);
        videoConcat.setStatus(VideoConcat.STATUS_RUNNING);
        if (StringUtils.startsWith(automateLesson.getSource(), BizConstant.SOURCE_AIPPT_PREFIX)) {
            CallbackArguments callbackArguments = new CallbackArguments();
            callbackArguments.setSdpAppId("8331e61b-f0b0-45fe-8a91-************");
            callbackArguments.setSdpBizType(StringUtils.substringAfter(automateLesson.getSource(), BizConstant.SOURCE_AIPPT_PREFIX));
            callbackArguments.setTaskId(Long.parseLong(automateLesson.getExecutionId()));
            callbackArguments.setSuid(StringUtils.defaultString(WafContext.getCurrentAccountId(), automateLesson.getCreator()));
            videoConcat.setCallbackArguments(callbackArguments);
            log.info("concat aippt lesson_concat with callback:{}", videoConcat.getCallbackArguments());
        }
        log.info("source {}, concat videoConcat:{}", automateLesson.getSource(), videoConcat);
        return videoConcatService.addVideoConcat(videoConcat);
    }

    public VideoConcat concatResult(String id) {
        return videoConcatService.findByKey("lesson_concat:" + id);
    }
}
