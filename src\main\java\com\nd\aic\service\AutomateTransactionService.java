package com.nd.aic.service;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.repository.AutomateTransactionRepository;
import com.nd.gaea.rest.support.WafContext;

import org.joda.time.LocalDateTime;
import org.springframework.stereotype.Service;

import java.util.Date;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class AutomateTransactionService extends BaseService<AutomateTransaction, String> {

    private final AutomateTransactionRepository automateTransactionRepository;

    @Override
    protected Module module() {
        return new Module("AUTOMATE_TRANSACTION");
    }

    @Override
    public AutomateTransaction findOne(String id) {
        AutomateTransaction automateTransaction = super.findOne(id);
        if (automateTransaction == null) {
            throw module().notFound();
        }
        return automateTransaction;
    }

    @Deprecated
    @Override
    public AutomateTransaction add(AutomateTransaction automateTransaction) {
        return addAutomateTransaction(automateTransaction);
    }

    public AutomateTransaction addAutomateTransaction(AutomateTransaction automateTransaction) {
        automateTransaction.setId(null);
        automateTransaction.setCreator(WafContext.getCurrentAccountId());
        automateTransaction.setUpdateAt(new Date());
        automateTransaction.setCreateAt(new Date());
        if (null == automateTransaction.getExpireAt()) {
            automateTransaction.setExpireAt(LocalDateTime.now().plusMinutes(15).toDate());
        }
        automateTransaction.setStatus("process");
        return automateTransactionRepository.save(automateTransaction);
    }

    public AutomateTransaction updateAutomateTransaction(AutomateTransaction automateTransaction) {
        automateTransaction.setUpdateAt(new Date());
        return update(automateTransaction);
    }
}
