package com.nd.aic.service;

import com.nd.aic.entity.BcsGptTask;
import com.nd.aic.repository.BcsGptTaskRepository;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class BcsGptTaskService {

    private final BcsGptTaskRepository bcsGptTaskRepository;

    public BcsGptTask findOne(String id) {
        return bcsGptTaskRepository.findOne(id);
    }

    public BcsGptTask add(BcsGptTask bcsGptTask) {
        // bcsGptTask.setId(null);
        bcsGptTask.setCreateTime(LocalDateTime.now());
        return bcsGptTaskRepository.save(bcsGptTask);
    }

    public BcsGptTask update(BcsGptTask bcsGptTask) {
        return bcsGptTaskRepository.save(bcsGptTask);
    }
}
