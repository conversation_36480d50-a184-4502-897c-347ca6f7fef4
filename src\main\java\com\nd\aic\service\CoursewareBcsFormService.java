package com.nd.aic.service;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.bcs.AuditAffairDTO;
import com.nd.aic.pojo.bcs.UpdateAffairDTO;
import com.nd.aic.pojo.flow.FlowBaseDTO;
import com.nd.gaea.client.http.WafSecurityHttpClient;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CoursewareBcsFormService {
    private static final String API_UPDATE_AFFAIR_DATA = "/v0.1/events/operation/system_operation_update_model_data";
    private static final String API_AUDIT_AFFAIR_NODE = "/v0.1/events/operation/system_assistant_audit_gateway";

    @Value("${app.bcs.host:https://bcs-app-service-sandbox.sdp.101.com}")
    private String bcsHost;

    private final WafSecurityHttpClient wafSecurityHttpClient;

    public CoursewareBcsFormService() {
        this.wafSecurityHttpClient = new WafSecurityHttpClient() {
            @Override
            protected HttpHeaders mergerHeaders(URI uri, HttpMethod method, HttpHeaders httpHeaders) {
                return super.mergerHeaders(uri, method, httpHeaders);
            }
        };
    }

    public void updateAffairData(FlowBaseDTO flowBaseDTO, String text) {
        updateAffairData(flowBaseDTO, text, null);
    }

    public void updateAffairData(FlowBaseDTO flowBaseDTO, String text, String status) {
        String url = bcsHost + API_UPDATE_AFFAIR_DATA;

        Map<String, Object> modelData = Maps.newHashMap();
        modelData.put("data_id", flowBaseDTO.getDataId());
        if (StringUtils.isNotBlank(text)) {
            modelData.put(flowBaseDTO.getOutputKey(), text);
        }
        if (StringUtils.isNotBlank(status) && StringUtils.isNotBlank(flowBaseDTO.getStatusKey())) {
            modelData.put(flowBaseDTO.getStatusKey(), status);
        }

        UpdateAffairDTO updateAffairDTO = new UpdateAffairDTO();
        updateAffairDTO.setAppId(flowBaseDTO.getAppId());
        updateAffairDTO.setFormId(flowBaseDTO.getFormId());
        updateAffairDTO.setFlowInstId(flowBaseDTO.getFlowInstId());
        updateAffairDTO.setNodeInstId(flowBaseDTO.getNodeInstId());
        updateAffairDTO.setModelData(modelData);
        log.info("updateAffairData data: {}", updateAffairDTO);
        HttpEntity<UpdateAffairDTO> httpEntity = new HttpEntity<>(updateAffairDTO, httpHeaders(flowBaseDTO));
        ResponseEntity<JSONObject> response = wafSecurityHttpClient.executeForEntity(url, HttpMethod.POST, httpEntity, JSONObject.class);
        JSONObject result = response.getBody();
        log.info("update affair data: {}", result);
    }

    public void auditAffairNode(FlowBaseDTO flowBaseDTO) {
        String url = bcsHost + API_AUDIT_AFFAIR_NODE;

        AuditAffairDTO auditAffairDTO = new AuditAffairDTO();
        auditAffairDTO.setAppId(flowBaseDTO.getAppId());
        auditAffairDTO.setFormId(flowBaseDTO.getFormId());
        auditAffairDTO.setFlowInstId(flowBaseDTO.getFlowInstId());
        auditAffairDTO.setNodeInstId(flowBaseDTO.getNodeInstId());
        auditAffairDTO.setDataId(flowBaseDTO.getDataId());
        auditAffairDTO.setAuditStatus(1);
        auditAffairDTO.setOpinion("审核通过");
        log.info("auditAffairNode data: {}", auditAffairDTO);
        HttpEntity<AuditAffairDTO> httpEntity = new HttpEntity<>(auditAffairDTO, httpHeaders(flowBaseDTO));
        ResponseEntity<JSONObject> response = wafSecurityHttpClient.executeForEntity(url, HttpMethod.POST, httpEntity, JSONObject.class);
        JSONObject result = response.getBody();
        log.info("audit affair node: {}", result);
    }

    private HttpHeaders httpHeaders(FlowBaseDTO flowBaseDTO) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("sdp-app-id", "b4fb92a0-af7f-49c2-b270-8f62afac1133");
        httpHeaders.add("bcs-app-id", flowBaseDTO.getAppId());
        httpHeaders.add("lock-object", flowBaseDTO.getFlowInstId());
        httpHeaders.add("Userid", String.valueOf(flowBaseDTO.getUserId()));
        return httpHeaders;
    }

}
