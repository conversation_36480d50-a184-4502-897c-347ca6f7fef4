package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.client.FastGptFeignClient;
import com.nd.aic.client.SpeechTextTimingClient;
import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.client.pojo.asr.AsrTask;
import com.nd.aic.client.pojo.asr.AtaUtterance;
import com.nd.aic.client.pojo.chat.ChatCompletion;
import com.nd.aic.client.pojo.chat.ChatCompletionResponse;
import com.nd.aic.client.pojo.chat.ChatMessage;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.common.UserHandler;
import com.nd.aic.entity.BcsGptTask;
import com.nd.aic.entity.IntegratedFlowTask;
import com.nd.aic.entity.IntegratedFlowTaskParticle;
import com.nd.aic.enums.PerformanceTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationReq;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.flow.FlowContentOptimizeDTO;
import com.nd.aic.pojo.flow.FlowDisassembleTeachActivityDTO;
import com.nd.aic.pojo.flow.FlowExtractTextDTO;
import com.nd.aic.pojo.flow.FlowGenerateVideoScriptDTO;
import com.nd.aic.pojo.flow.FlowOptimizeTextDTO;
import com.nd.aic.pojo.flow.FlowRelateTeachObjectDTO;
import com.nd.aic.pojo.flow.IntegratedBCSFlowTaskDTO;
import com.nd.aic.pojo.flow.IntegratedFlowTaskDTO;
import com.nd.aic.pojo.flow.TeachEventType;
import com.nd.aic.pojo.flow.XlsxTeachingActivity;
import com.nd.aic.util.JsonUtils;
import com.nd.aic.util.XlsxUtils;
import com.nd.gaea.WafException;
import com.nd.gaea.client.http.WafHttpClient;
import com.nd.gaea.rest.support.WafContext;
import com.nd.gaea.util.WafJsonMapper;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class CoursewareFlowService {

    private static final File TEMP_DIRECTORY = new File(CoursewareFlowService.class.getResource("/").getPath());

    private final SpeechTextTimingClient speechTextTimingClient;
    private final FastGptFeignClient fastGptFeignClient;
    private final WafHttpClient wafHttpClient;
    private final CsService csService;
    private final CoursewareBcsFormService coursewareBcsFormService;
    private final MediaService mediaService;
    private final BcsGptTaskService bcsGptTaskService;
    private final CoursewareGenerationService coursewareGenerationService;
    private final AudioResourceService audioResourceService;
    private final IntegratedFlowTaskService integratedFlowTaskService;
    private final IntegratedFlowTaskParticleService integratedFlowTaskParticleService;
    private final NdrService ndrService;

    @SneakyThrows
    private String mp3Url(String videoDentryId) {
        return mediaService.mp4ToMp3(videoDentryId);
    }

    @SneakyThrows
    @Async
    public void extractText(FlowExtractTextDTO flowExtractTextDTO) {
        log.info("extractText data: {}", WafJsonMapper.toJson(flowExtractTextDTO));
        String texts = doExtractText(flowExtractTextDTO.getVideoDentryId(), flowExtractTextDTO.getFastgptKey());
        log.info("extractText callback");
        coursewareBcsFormService.updateAffairData(flowExtractTextDTO, texts);
        coursewareBcsFormService.auditAffairNode(flowExtractTextDTO);
    }

    private String doExtractText(String videoDentryId, String fastgptKey) {
        String mediaUrl = mp3Url(videoDentryId);
        AsrTask task = speechTextTimingClient.vcSubmit(mediaUrl);
        AtaResult result = speechTextTimingClient.vcQuery(task.getId());
        String texts = result.getUtterances().stream().map(AtaUtterance::getText).collect(Collectors.joining("\n"));
        log.info("extractText optimize by fastgpt");
        //GPT文字纠错
        texts = fastGptCall(texts, fastgptKey);
        return texts;
    }

    @Deprecated
    public Object optimizeText(FlowOptimizeTextDTO flowOptimizeTextDTO) {
        String textUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + flowOptimizeTextDTO.getTextDentryId();
        String texts = wafHttpClient.getForObject(textUrl, String.class);
        texts = fastGptCall(texts, flowOptimizeTextDTO.getFastgptKey());
        return uploadToCs(texts, "txt").getDentryId();
    }

    @SneakyThrows
    @Async
    public void disassembleTeachActivity(FlowDisassembleTeachActivityDTO disassembleTeachActivityDTO) {
        log.info("disassembleTeachActivity data: {}", WafJsonMapper.toJson(disassembleTeachActivityDTO));
        String texts = disassembleTeachActivityDTO.getInputValue();
        String jsonText = doDisassembleTeachActivity(texts, disassembleTeachActivityDTO.getFastgptKey());
        Dentry dentry = uploadToCs(jsonText, "json");
        String resultUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId();
        log.info("disassembleTeachActivity callback begin");
        coursewareBcsFormService.updateAffairData(disassembleTeachActivityDTO, resultUrl);
        coursewareBcsFormService.auditAffairNode(disassembleTeachActivityDTO);
    }

    private String doDisassembleTeachActivity(String texts, String fastgptKey) {
        String authorization = "Bearer " + fastgptKey;
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(texts)));
        ChatCompletionResponse response = fastGptFeignClient.ask(authorization, chatCompletion);
        texts = response.getChoices().get(0).getMessage().getContent();
        return trimJsonData(texts);
    }

    @Async
    public void relateTeachObject(FlowRelateTeachObjectDTO relateTeachObjectDTO) {
        //String textUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + relateTeachObjectDTO.getTextDentryId();
        String textUrl = relateTeachObjectDTO.getContentUrl();
        String texts = wafHttpClient.getForObject(textUrl, String.class);
        texts = fastGptCall(texts, relateTeachObjectDTO.getFastgptKey());
        Dentry dentry = uploadToCs(texts, "json");
        String resultUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId();
        log.info("relateTeachObjectDTO callback begin");
        coursewareBcsFormService.updateAffairData(relateTeachObjectDTO, resultUrl);
        coursewareBcsFormService.auditAffairNode(relateTeachObjectDTO);
    }

    @SneakyThrows
    @Async
    public void generatePerformanceType(FlowGenerateVideoScriptDTO flowGenerateVideoScriptDTO) {
        //String textUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + relateTeachObjectDTO.getTextDentryId();
        String textUrl = flowGenerateVideoScriptDTO.getContentUrl();
        String texts = wafHttpClient.getForObject(textUrl, String.class);
        String jsonText = doGeneratePerformanceTypes(texts, flowGenerateVideoScriptDTO.getFastgptKey(), false);
        List<CoursewareModel> coursewareModels = doGenerateCoursewareReqs(flowGenerateVideoScriptDTO.getTitle(), jsonText, null, null, null);

        Dentry dentry = uploadToCs(WafJsonMapper.toJson(coursewareModels), "json");
        String resultUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId();
        log.info("generateVideoScript callback");
        coursewareBcsFormService.updateAffairData(flowGenerateVideoScriptDTO, resultUrl);
        coursewareBcsFormService.auditAffairNode(flowGenerateVideoScriptDTO);
    }

    private String doGeneratePerformanceTypes(String texts, String fastgptKey, Boolean addOpeningPerformance) {
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("add_opening", BooleanUtils.isTrue(addOpeningPerformance) ? "true" : "false");
        texts = fastGptCall(texts, fastgptKey, variables);
        return trimJsonData(texts);
    }

    private Float calcOpeningTiming(CoursewareGenerationReq coursewareGenerationReq) {
        List<String> performanceTypes = coursewareGenerationReq.getTeachEventTypes().stream().map(TeachEventTypeDTO::getPerformanceType).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        long mills = 0;
        for (String performanceType : performanceTypes) {
            long duration = PerformanceTypeEnum.getByName(performanceType).getDuration();
            if (0 == duration) {
                break;
            }
            mills += duration;
        }
        if (0 != mills) {
            return mills / 1000f;
        }
        return null;
    }

    private List<CoursewareModel> doGenerateCoursewareReqs(String title, String jsonText, String sceneResourceId, String characterResourceId, String audioResourceId) {
        List<CoursewareGenerationReq> coursewareGenerationReqs = JSON.parseArray(jsonText, CoursewareGenerationReq.class);
        List<CoursewareModel> coursewareModels = Lists.newArrayList();
        int sequence = 1;
        for (CoursewareGenerationReq coursewareGenerationReq : coursewareGenerationReqs) {
            // String dialogues = coursewareGenerationReq.getTeachEventTypes().stream().map(TeachEventTypeDTO::getDialogue).filter(StringUtils::isNotBlank).collect(Collectors.joining("\n"));
            // if (StringUtils.isEmpty(audioResourceId) && StringUtils.isNotBlank(dialogues)) {
            //     Float silentSeconds = calcOpeningTiming(coursewareGenerationReq);
            //     //...dialogues->mp3 生成表演环节再合成音频，不需要提前合成
            //     // ResourceMetaTiListViewModel resourceMeta = createCourseWareMeta(String.format("%s-%d", title, sequence++), dialogues, silentSeconds);
            //     // log.info("tts resource id: {}", resourceMeta.getId());
            //     // audioResourceId = resourceMeta.getId();
            //     audioResourceId = createCourseWareMeta(title, coursewareGenerationReq.getTeachEventTypes());
            // }
            coursewareGenerationReq.setTitle(title);
            if (StringUtils.isNotBlank(audioResourceId)) {
                coursewareGenerationReq.setTenantId(NdrConstant.KARAOKE_TENANT_ID);
                coursewareGenerationReq.setContainerId(NdrConstant.KARAOKE_COURSEWARE_CONTAINER_ID);
                coursewareGenerationReq.setResourceId(audioResourceId);
            }
            coursewareGenerationReq.setGlobalScene(sceneResourceId);
            coursewareGenerationReq.setGlobalCharacter(characterResourceId);
            try {
                System.out.println(WafJsonMapper.toJson(coursewareGenerationReq));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            CoursewareModel coursewareModel = coursewareGenerationService.generateCourseware(coursewareGenerationReq, null);
            coursewareModels.add(coursewareModel);
        }
        return coursewareModels;
    }

    // private String createCourseWareMeta(String title, List<TeachEventTypeDTO> teachEventTypes) {
    //     List<AudioReqSegment> segments = teachEventTypes.stream().map(e -> {
    //         AudioReqSegment segment = new AudioReqSegment();
    //         segment.setDialogue(e.getDialogue());
    //         if (StringUtils.isNotEmpty(e.getPerformanceType())) {
    //             PerformanceTypeEnum performanceTypeEnum = PerformanceTypeEnum.getByName(e.getPerformanceType());
    //             segment.setPerformanceType(performanceTypeEnum);
    //             segment.setDuration(performanceTypeEnum.getDuration());
    //         }
    //         segment.setTeachingActivity(e.getTeachActivity());
    //         return segment;
    //     }).collect(Collectors.toList());
    //
    //     AudioReq audioReq = new AudioReq();
    //     audioReq.setTitle(title);
    //     audioReq.setSegments(segments);
    //     return audioResourceService.courseWareAudioMeta(audioReq);
    // }

    private String fastGptCall(String texts, String fastGptKey) {
        return fastGptCall(texts, fastGptKey, null);
    }

    private String fastGptCall(String texts, String fastGptKey, Map<String, Object> variables) {
        String authorization = "Bearer " + fastGptKey;
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(texts)));
        chatCompletion.setVariables(variables);
        ChatCompletionResponse response = fastGptFeignClient.ask(authorization, chatCompletion);
        texts = response.getChoices().get(0).getMessage().getContent();
        return texts;
    }

    private static String trimJsonData(String data) {
        data = StringUtils.trim(data);
        data = StringUtils.stripStart(data, "```json");
        data = StringUtils.stripEnd(data, "```");
        return data;
    }

    @SneakyThrows
    private Dentry uploadToCs(String content, String extName) {
        long accountId = Long.parseLong(StringUtils.defaultIfBlank(WafContext.getCurrentAccountId(), "0"));
        String fileName = String.format("%d.%s", accountId, extName);
        File file = writeToTempFile(content, fileName);
        file.deleteOnExit();
        try {
            return csService.uploadFile(accountId, fileName, file);
        } finally {
            FileUtils.deleteQuietly(file);
        }
    }

    @SneakyThrows
    private static File writeToTempFile(String content, String fileName) {
        File file = FileUtils.getFile(TEMP_DIRECTORY, fileName);
        FileUtils.deleteQuietly(file);
        FileUtils.write(file, content);
        return file;
    }

    @Async
    public void forwardFastGPT(FlowContentOptimizeDTO flowContentOptimizeDTO) {
        BcsGptTask bcsGptTask = new BcsGptTask();
        BeanUtils.copyProperties(flowContentOptimizeDTO, bcsGptTask);
        bcsGptTask = bcsGptTaskService.add(bcsGptTask);

        //GPT文字纠错
        String authorization = "Bearer " + flowContentOptimizeDTO.getFastgptKey();
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("task_id", bcsGptTask.getId());
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setVariables(variables);
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(flowContentOptimizeDTO.getContent())));
        ChatCompletionResponse response = fastGptFeignClient.ask(authorization, chatCompletion);
        String texts = response.getChoices().get(0).getMessage().getContent();
        bcsGptTask.setResult(texts);
        bcsGptTaskService.update(bcsGptTask);
    }

    @Async
    public void fastGptCallback(String taskId, String reply) {
        try {
            BcsGptTask bcsGptTask = bcsGptTaskService.findOne(taskId);
            FlowExtractTextDTO flowExtractTextDTO = new FlowExtractTextDTO();
            BeanUtils.copyProperties(bcsGptTask, flowExtractTextDTO);

            coursewareBcsFormService.updateAffairData(flowExtractTextDTO, reply);
            coursewareBcsFormService.auditAffairNode(flowExtractTextDTO);
        } catch (Exception e) {
            log.error("fastgpt回调失败", e);
        }
    }

    public Object queryFlow(String taskId) {
        return integratedFlowTaskService.findOne(taskId);
    }

    @Async
    public void runFlow(IntegratedFlowTaskDTO integratedFlowTaskDTO, IntegratedFlowTask integratedFlowTask) {
        try {
            UserHandler.getUser(integratedFlowTask.getCreator());
            integratedFlowTaskDTO.setParticles(integratedFlowTaskParticleService.findByTaskId(integratedFlowTask.getId()));

            List<CoursewareModel> coursewareModels = genPerformanceContents(integratedFlowTaskDTO);
            integratedFlowTaskDTO.setPerformanceContents(coursewareModels);

            if (CollectionUtils.isNotEmpty(integratedFlowTaskDTO.getParticles())) {
                for (IntegratedFlowTaskParticle particle : integratedFlowTaskDTO.getParticles()) {
                    particle.setTaskId(integratedFlowTaskDTO.getId());
                    integratedFlowTaskParticleService.save(particle);
                }
            }

            integratedFlowTask.setStatus("success");
            integratedFlowTaskService.update(integratedFlowTask);
        } catch (Exception e) {
            log.error("runFlow", e);
            integratedFlowTask.setStatus("error");
            integratedFlowTask.setMessage(e.getMessage());
        } finally {
            integratedFlowTask.setManuscript(integratedFlowTaskDTO.getManuscript());
            integratedFlowTask.setTeachActivities(integratedFlowTaskDTO.getTeachActivities());
            integratedFlowTask.setPerformanceTypes(integratedFlowTaskDTO.getPerformanceTypes());
            integratedFlowTask.setPerformanceContents(integratedFlowTaskDTO.getPerformanceContents());
            integratedFlowTaskService.update(integratedFlowTask);
            log.info("completed task");
            UserHandler.clear();
        }
    }

    @SneakyThrows
    private List<CoursewareModel> genPerformanceContents(IntegratedFlowTaskDTO integratedFlowTaskDTO) {
        List<CoursewareModel> performanceContents;
        if (null != integratedFlowTaskDTO.getPerformanceContents()) {
            performanceContents = integratedFlowTaskDTO.getPerformanceContents();
        } else {
            String performanceTypes;
            if (CollectionUtils.isNotEmpty(integratedFlowTaskDTO.getTeachActivityDentryInfos())) {
                performanceTypes = parsePerformanceTypes(integratedFlowTaskDTO);
            } else {
                performanceTypes = genPerformanceTypes(integratedFlowTaskDTO);
            }
            String audioResourceId = null;
            if (CollectionUtils.isNotEmpty(integratedFlowTaskDTO.getAudioDentryInfos())) {
                String dialogues = integratedFlowTaskDTO.getParticles().stream()
                        .flatMap(e -> e.getTeachEventTypes().stream())
                        .map(TeachEventType::getDialogue)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining("\n"));
                audioResourceId = audioResourceService.createCourseWareAudioMeta(integratedFlowTaskDTO.getTitle(), dialogues, integratedFlowTaskDTO.getAudioDentryInfos().get(0)).getId();
            }
            performanceContents = doGenerateCoursewareReqs(integratedFlowTaskDTO.getTitle(),
                    performanceTypes,
                    integratedFlowTaskDTO.getSceneResource(),
                    integratedFlowTaskDTO.getCharacterResource(),
                    audioResourceId);
            integratedFlowTaskDTO.setPerformanceContents(performanceContents);
        }
        //构造表演内容颗粒列表
        if (CollectionUtils.isNotEmpty(integratedFlowTaskDTO.getParticles())) {
            int size = CollectionUtils.size(integratedFlowTaskDTO.getParticles());
            if (size != performanceContents.size()) {
                throw WafI18NException.of("表演颗粒与教学活动数量不匹配");
            }
            for (int i = 0; i < size; i++) {
                //教学活动颗粒
                IntegratedFlowTaskParticle teachActivityParticle = integratedFlowTaskDTO.getParticles().get(i);
                //教学活动分析出的表演事件类型
                CoursewareModel coursewareModel = performanceContents.get(i);
                teachActivityParticle.setCoursewareModel(coursewareModel);
            }
        } else {
            List<IntegratedFlowTaskParticle> particles = Lists.newArrayList();
            for (CoursewareModel performanceContent : performanceContents) {
                IntegratedFlowTaskParticle particle = new IntegratedFlowTaskParticle();
                particle.setCoursewareModel(performanceContent);
                particles.add(particle);
            }
            integratedFlowTaskDTO.setParticles(particles);
        }
        return performanceContents;
    }

    @SneakyThrows
    private String genPerformanceTypes(IntegratedFlowTaskDTO integratedFlowTaskDTO) {
        log.info("genPerformanceTypes");
        String performanceTypes;
        if (null != integratedFlowTaskDTO.getPerformanceTypes()) {
            performanceTypes = integratedFlowTaskDTO.getPerformanceTypes();
        } else {
            performanceTypes = doGeneratePerformanceTypes(genTeachActivity(integratedFlowTaskDTO), integratedFlowTaskDTO.getPerformanceTypeGptKey(), integratedFlowTaskDTO.getAddOpeningPerformance());
        }
        List<IntegratedFlowTaskParticle> particles = JsonUtils.parseList(performanceTypes, IntegratedFlowTaskParticle.class);

        // 合并教学活动颗粒和表演事件类型颗粒数据
        mergeParticleData(integratedFlowTaskDTO, particles);

        for (IntegratedFlowTaskParticle particle : particles) {
            for (TeachEventType teachEventType : particle.getTeachEventTypes()) {
                teachEventType.setTeachActivity(particle.getTeachActivity());
            }
        }
        if (BooleanUtils.isTrue(integratedFlowTaskDTO.getMergePerformances()) && CollectionUtils.size(particles) > 1) {
            // 合并所有教学活动表演为一个颗粒
            IntegratedFlowTaskParticle teachEventParticle = new IntegratedFlowTaskParticle();
            BeanUtils.copyProperties(particles.get(0), teachEventParticle);
            teachEventParticle.setTeachActivity(integratedFlowTaskDTO.getTitle());
            teachEventParticle.setTeachEventTypes(Lists.newArrayList(teachEventParticle.getTeachEventTypes()));
            for (int i = 1; i < particles.size(); i++) {
                teachEventParticle.getTeachEventTypes().addAll(particles.get(i).getTeachEventTypes());
            }
            performanceTypes = WafJsonMapper.toJson(Lists.newArrayList(teachEventParticle));
        } else {
            particles.get(0).setTeachActivity(integratedFlowTaskDTO.getTitle());
        }

        integratedFlowTaskDTO.setPerformanceTypes(performanceTypes);
        return performanceTypes;
    }

    //合并教学活动颗粒和表演事件颗粒（数组元素一一对应）
    private void mergeParticleData(IntegratedFlowTaskDTO integratedFlowTaskDTO, List<IntegratedFlowTaskParticle> particles) {
        if (CollectionUtils.isNotEmpty(integratedFlowTaskDTO.getParticles())) {
            if (CollectionUtils.size(integratedFlowTaskDTO.getParticles()) != particles.size()) {
                throw WafI18NException.of("拆解的表演事件类型与教学活动数量不匹配");
            }
            for (int i = 0; i < particles.size(); i++) {
                //教学活动颗粒
                IntegratedFlowTaskParticle teachActivityParticle = integratedFlowTaskDTO.getParticles().get(i);
                //教学活动分析出的表演事件类型
                IntegratedFlowTaskParticle teachEventParticle = particles.get(i);
                teachActivityParticle.setTeachEventTypes(teachEventParticle.getTeachEventTypes());
            }
        } else {
            integratedFlowTaskDTO.setParticles(particles);
        }
    }

    @SneakyThrows
    private String genTeachActivity(IntegratedFlowTaskDTO integratedFlowTaskDTO) {
        log.info("genTeachActivity");
        String teachActivities;
        if (null != integratedFlowTaskDTO.getTeachActivities()) {
            teachActivities = integratedFlowTaskDTO.getTeachActivities();
        } else {
            teachActivities = doDisassembleTeachActivity(genManuscriptContent(integratedFlowTaskDTO), integratedFlowTaskDTO.getDisassembleGptKey());
        }
        List<IntegratedFlowTaskParticle> teachActivityParticles = JsonUtils.parseList(teachActivities, IntegratedFlowTaskParticle.class);
        //临时调整只需要一个教学活动
        IntegratedFlowTaskParticle integratedFlowTaskParticle = teachActivityParticles.get(0);
        for (IntegratedFlowTaskParticle teachActivityParticle : teachActivityParticles) {
            if (teachActivityParticle != integratedFlowTaskParticle) {
                integratedFlowTaskParticle.getTexts().addAll(teachActivityParticle.getTexts());
            }
        }
        teachActivityParticles = Lists.newArrayList(integratedFlowTaskParticle);
        teachActivities = WafJsonMapper.toJson(teachActivityParticles);
        integratedFlowTaskDTO.setParticles(teachActivityParticles);
        integratedFlowTaskDTO.setTeachActivities(teachActivities);
        return teachActivities;
    }

    @SneakyThrows
    private String genManuscriptContent(IntegratedFlowTaskDTO integratedFlowTaskDTO) {
        if (StringUtils.isNotBlank(integratedFlowTaskDTO.getManuscript())) {
            return integratedFlowTaskDTO.getManuscript();
        }
        if (CollectionUtils.isEmpty(integratedFlowTaskDTO.getVideoDentryInfos())) {
            throw new WafException("文字稿与视频不能同时为空！", HttpStatus.BAD_REQUEST);
        }
        String videoDentryId = integratedFlowTaskDTO.getVideoDentryInfos().get(0).getDentryId();
        String manuscriptContent = doExtractText(videoDentryId, integratedFlowTaskDTO.getExtraTextGptKey());
        integratedFlowTaskDTO.setManuscript(manuscriptContent);
        return manuscriptContent;
    }

    @SneakyThrows
    public String parsePerformanceTypes(IntegratedFlowTaskDTO integratedFlowTaskDTO) {
        DentryInfo dentryInfo = integratedFlowTaskDTO.getTeachActivityDentryInfos().get(0);
        String xlsxUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryInfo.getDentryId();
        File xlsxFile = mediaService.download(xlsxUrl, String.format("%d.xlsx", System.currentTimeMillis()));
        List<XlsxTeachingActivity> teachingActivities = XlsxUtils.parseTeachActivityList(xlsxFile);

        List<IntegratedFlowTaskParticle> teachActivityParticles = Lists.newArrayList();

        IntegratedFlowTaskParticle teachActivityParticle = null;
        for (XlsxTeachingActivity teachingActivity : teachingActivities) {
            if (null == teachActivityParticle || !StringUtils.equals(teachActivityParticle.getTeachActivity(), teachingActivity.getActivity())) {
                String teachingActivityName = teachingActivity.getActivity();
                teachActivityParticle = new IntegratedFlowTaskParticle();
                teachActivityParticle.setTeachActivity(teachingActivityName);
                teachActivityParticle.setTexts(Lists.newArrayList());
                teachActivityParticle.setTeachEventTypes(Lists.newArrayList());
                teachActivityParticles.add(teachActivityParticle);
            }
            String script = teachingActivity.getScript();
            if (StringUtils.isNotBlank(script)) {
                teachActivityParticle.getTexts().add(script);
            }

            TeachEventType teachEventType = new TeachEventType();
            teachEventType.setTeachActivity(teachingActivity.getActivity());
            teachEventType.setTeachEventType(teachingActivity.getEvent());
            teachEventType.setPerformanceType(teachingActivity.getPerformancePlan());
            teachEventType.setDialogue(StringUtils.isNotBlank(script) ? script : null);
            try {
                if (StringUtils.isNotBlank(teachingActivity.getMaterial())) {
                    teachEventType.setPerformanceConfig(JSON.parseObject(teachingActivity.getMaterial()));
                }
            } catch (Exception e) {
                //log.error("param format error", e);
                Map<String, Object> performanceConfig = Maps.newHashMap();
                performanceConfig.put("description", teachingActivity.getMaterial());
                teachEventType.setPerformanceConfig(performanceConfig);
            }
            teachActivityParticle.getTeachEventTypes().add(teachEventType);
        }

        if (BooleanUtils.isTrue(integratedFlowTaskDTO.getMergePerformances()) && CollectionUtils.size(teachActivityParticles) > 1) {
            // 合并所有教学活动表演为一个颗粒
            IntegratedFlowTaskParticle mergeTeachEventParticle = new IntegratedFlowTaskParticle();
            BeanUtils.copyProperties(teachActivityParticles.get(0), mergeTeachEventParticle);
            mergeTeachEventParticle.setTeachActivity(integratedFlowTaskDTO.getTitle());
            mergeTeachEventParticle.setTeachEventTypes(Lists.newArrayList(mergeTeachEventParticle.getTeachEventTypes()));
            for (int i = 1; i < teachActivityParticles.size(); i++) {
                mergeTeachEventParticle.getTeachEventTypes().addAll(teachActivityParticles.get(i).getTeachEventTypes());
                mergeTeachEventParticle.getTexts().addAll(teachActivityParticles.get(i).getTexts());
            }
            teachActivityParticles = Lists.newArrayList(mergeTeachEventParticle);
        } else {
            teachActivityParticles.get(0).setTeachActivity(integratedFlowTaskDTO.getTitle());
        }

        String content = WafJsonMapper.toJson(teachActivityParticles);
        String manuscript = teachActivityParticles.stream()
                .flatMap(e -> e.getTeachEventTypes().stream())
                .map(TeachEventType::getDialogue)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("\n"));

        integratedFlowTaskDTO.setManuscript(manuscript);
        integratedFlowTaskDTO.setParticles(teachActivityParticles);
        integratedFlowTaskDTO.setManuscript(integratedFlowTaskDTO.getManuscript());
        integratedFlowTaskDTO.setTeachActivities(integratedFlowTaskDTO.getTeachActivities());
        integratedFlowTaskDTO.setPerformanceTypes(integratedFlowTaskDTO.getPerformanceTypes());

        return content;
    }

    @SneakyThrows
    @Async
    public void runBcsFlow(IntegratedBCSFlowTaskDTO integratedFlowTaskDTO, IntegratedFlowTask integratedFlowTask) {
        BcsGptTask bcsGptTask = new BcsGptTask();
        BeanUtils.copyProperties(integratedFlowTaskDTO.getFlowBaseDTO(), bcsGptTask);
        bcsGptTaskService.add(bcsGptTask);

        integratedFlowTaskDTO.setMergePerformances(true);
        runFlow(integratedFlowTaskDTO, integratedFlowTask);

        if (StringUtils.equals("success", integratedFlowTask.getStatus()) && CollectionUtils.isNotEmpty(integratedFlowTaskDTO.getParticles())) {
            CoursewareModel coursewareModel = integratedFlowTaskDTO.getParticles().get(0).getCoursewareModel();
            Dentry dentry = uploadToCs(WafJsonMapper.toJson(coursewareModel), "json");
            String resultUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId();
            log.info("runBcsFlow callback begin");
            coursewareBcsFormService.updateAffairData(integratedFlowTaskDTO.getFlowBaseDTO(), resultUrl, "成功");
            coursewareBcsFormService.auditAffairNode(integratedFlowTaskDTO.getFlowBaseDTO());
        } else {
            log.info("runBcsFlow callback fail");
            coursewareBcsFormService.updateAffairData(integratedFlowTaskDTO.getFlowBaseDTO(), null, "失败");
        }
    }
}
