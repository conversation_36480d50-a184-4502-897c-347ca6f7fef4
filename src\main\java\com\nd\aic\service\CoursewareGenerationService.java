package com.nd.aic.service;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.InteractionFeignClient;
import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.common.BizConstant;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.common.SegmentPerformConstant;
import com.nd.aic.entity.CoursewareTask;
import com.nd.aic.entity.TimeArea;
import com.nd.aic.entity.VideoInteraction;
import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.enums.PerformanceTypeEnum;
import com.nd.aic.enums.RoutineTypeEnum;
import com.nd.aic.enums.TaskStatusEnum;
import com.nd.aic.enums.TeachEventTypeEnum;
import com.nd.aic.exception.AicException;
import com.nd.aic.pojo.AtaUtteranceWithPos;
import com.nd.aic.pojo.AtaWordWithPos;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareGenerationReq;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.audio.AudioMetaResult;
import com.nd.aic.pojo.audio.AudioReq;
import com.nd.aic.pojo.audio.AudioReqSegment;
import com.nd.aic.pojo.common.Vector3D;
import com.nd.aic.pojo.dto.CoursewareResourceConfigDTO;
import com.nd.aic.pojo.dto.CoursewareResourceDTO;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.aic.pojo.dto.ScenePositionResourceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BgmSegment;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.BuffSegment;
import com.nd.aic.pojo.segment.CameraFeatureSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.CharacterSegment;
import com.nd.aic.pojo.segment.LightSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.PreloadSegment;
import com.nd.aic.pojo.segment.PrepareSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.pojo.segment.SceneSegment;
import com.nd.aic.pojo.segment.SettingSegment;
import com.nd.aic.pojo.segment.UISegment;
import com.nd.aic.pojo.segment.WordSegment;
import com.nd.aic.service.event.TeachEventTypeProcessor;
import com.nd.aic.service.performance.PerformanceHandler;
import com.nd.aic.util.ActionUtil;
import com.nd.aic.util.AtaWordUtil;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.InteractiveQuestionUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.SubtitleUtils;
import com.nd.aic.util.TimeRangeUtils;
import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.UUIDUtil;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.client.http.WafHttpClient;
import com.nd.gaea.util.WafJsonMapper;
import com.nd.ndr.model.DownloadUrlViewModel;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class CoursewareGenerationService {

    private final CoursewareTaskService coursewareTaskService;
    private final NdrService ndrService;
    private final FastGptRetryService fastGptRetryService;
    private final SpeechTimingService speechTimingService;
    private final WafHttpClient wafHttpClient;
    private final List<TeachEventTypeProcessor> teachEventTypeProcessors;
    private final AudioResourceService audioResourceService;
    private final InteractionFeignClient interactionFeignClient;

    public String generateCoursewareTask(CoursewareGenerationReq coursewareGenerationReq) {
        String taskId = UUID.randomUUID().toString();
        final CoursewareTask task = createTask(coursewareGenerationReq);
        task.setTaskId(taskId);
        task.setStatus(TaskStatusEnum.DOING.getValue());
        coursewareTaskService.insert(task);
        log.info("开始生成课件, time:{}", System.currentTimeMillis());
        CompletableFuture.runAsync(() -> {
            try {
                CoursewareModel mvModel = generateCourseware(task.getCoursewareGenerationReq(), task);
                task.setResult(mvModel);
                task.setStatus(TaskStatusEnum.SUCCESS.getValue());
                log.info("generateCoursewareByTask taskId:{} success", task.getTaskId());
            } catch (Exception e) {
                task.setStatus(TaskStatusEnum.FAIL.getValue());
                task.setError(e.getMessage());
                log.error("generateCoursewareByTask taskId:{} fail", task.getTaskId());
            } finally {
                coursewareTaskService.update(task);
            }
        });
        return taskId;
    }

    public JSONObject getCoursewareInfoByTaskId(String taskId) {
        JSONObject result = new JSONObject();
        CoursewareTask coursewareTask = coursewareTaskService.queryByTaskId(taskId);
        if (coursewareTask == null) {
            result.put("status", TaskStatusEnum.FAIL.getValue());
            return result;
        }
        result.put("status", coursewareTask.getStatus());
        result.put("courseware_model", coursewareTask.getResult());
        result.put("error", coursewareTask.getError());
        return result;
    }

    private String generateAudioResource(final CoursewareGenerationContext context, String characterId, CoursewareGenerationReq coursewareGenerationReq) {
        String title = coursewareGenerationReq.getTitle();
        // Set<TeachEventTypeDTO> invalidData = Sets.newHashSet();
        List<TeachEventTypeDTO> teachEventTypes = coursewareGenerationReq.getTeachEventTypes();

        List<AudioReqSegment> segments = teachEventTypes.stream().map(e -> {
            AudioReqSegment segment = new AudioReqSegment();
            segment.setTeachingActivity(e.getTeachActivity());
            segment.setDialogue(StringUtils.defaultString(e.getDubbing(), e.getDialogue()));
            segment.setSubtitle(e.getDialogue());
            segment.setAudioId(e.getAudioId());
            segment.setPronunciationDict(e.getPronunciationDict());
            segment.setTtsSegments(e.getTtsSegments());
            if (StringUtils.isNotEmpty(e.getPerformanceType())) {
                PerformanceTypeEnum performanceTypeEnum = PerformanceTypeEnum.getByName(e.getPerformanceType());
                segment.setPerformanceType(performanceTypeEnum);
                segment.setDuration(performanceTypeEnum.getDuration());

                Object handler = ApplicationContextUtil.getApplicationContext().getBean(performanceTypeEnum.getHandlerCls());
                if (handler instanceof PerformanceHandler) {
                    // 先准备资源
                    ((PerformanceHandler) handler).beforePrepare(e, context);
                    if (null != e.getResourceDuration()) {
                        long duration = null == segment.getDuration() ? e.getResourceDuration() : Math.max(e.getResourceDuration(), segment.getDuration());
                        segment.setDuration(duration);
                    }
                    segment.setSilenceDuration(e.getSilenceDuration());
                }
            }
            if (StringUtils.isBlank(e.getDialogue()) && (segment.getDuration() == null || segment.getDuration() == 0L)) {
                e.setValid(false);
            }
            if (StringUtils.isBlank(segment.getDialogue()) && (segment.getDuration() == null || segment.getDuration() == 0L)) {
                // invalidData.add(e);
                return null;
            }
            return segment;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        AudioReq audioReq = new AudioReq();
        audioReq.setTitle(title);
        audioReq.setAudioText(context.getAudioText());
        audioReq.setCharacterResource(characterId);
        audioReq.setSegments(segments);
        audioReq.setVoiceType(coursewareGenerationReq.getVoiceId());
        audioReq.setVoiceSpeed(coursewareGenerationReq.getVoiceSpeed());
        audioReq.setBgmType(coursewareGenerationReq.getBgmType());
        audioReq.setBgmFile(coursewareGenerationReq.getBgmFile());
        AudioMetaResult audioMetaResult = audioResourceService.courseWareAudioMeta(audioReq);

        context.setResourceId(audioMetaResult.getAudioResourceId());
        context.setBgmResourceId(audioMetaResult.getBgmResourceId());
        context.setSubtitleResourceId(audioMetaResult.getSubtitleResourceId());
        return audioMetaResult.getAudioResourceId();
    }

    public CoursewareModel generateCourseware(CoursewareGenerationReq coursewareGenerationReq, CoursewareTask task) {
        if (task == null) {
            task = createTask(coursewareGenerationReq);
        }
        CoursewareModel coursewareModel = new CoursewareModel();
        coursewareModel.setTitle(coursewareGenerationReq.getTitle());
        coursewareModel.setType(BizConstant.DEFAULT_COURSEWARE_MODEL_TYPE);
        coursewareModel.setVersion(BizConstant.DEFAULT_COURSEWARE_MODEL_VERSION);
        coursewareModel.setBizType(BizConstant.DEFAULT_COURSEWARE_MODE_BIZ_TYPE);

        // 创建上下文
        CoursewareGenerationContext context = createCoursewareGenerationContext(coursewareGenerationReq, coursewareModel);
        if (StringUtils.isEmpty(coursewareGenerationReq.getResourceId())) {
            coursewareGenerationReq.setTenantId(NdrConstant.KARAOKE_TENANT_ID);
            coursewareGenerationReq.setContainerId(NdrConstant.KARAOKE_COURSEWARE_CONTAINER_ID);
            long start = System.currentTimeMillis();
            coursewareGenerationReq.setResourceId(generateAudioResource(context, context.getGlobalCharacter(), coursewareGenerationReq));
            coursewareModel.getElapsed().put("tts", System.currentTimeMillis() - start);
        }
        coursewareModel.setResourceId(coursewareGenerationReq.getResourceId());
        coursewareModel.setContainerId(coursewareGenerationReq.getContainerId());
        // 初始化
        initCoursewareGenerationContext(task, coursewareModel, context);
        updateTask(task);
        // 设置
        if (!StringUtils.equals(coursewareGenerationReq.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)) {
            generateSetting(coursewareModel);
        }
        // 场景
        generateScene(context, coursewareModel);
        // 角色和灯光
        generateCharacter(context, coursewareModel);
        // 服装
        generateClothing(context, coursewareModel);
        // 音频和字幕
        generateBgmAndSubtitle(context, coursewareModel);
        // 更新任务
        updateTask(task);
        // 处理教学事件
        resolveTeachEventType(context, coursewareModel);
        // 处理口型
        // generateArkitFace(coursewareModel, arkitFaceFuture, BooleanUtils.isTrue(coursewareGenerationReq.getEnableArkitFace()));
        // 预加载 需要在场景、角色确定及板书之后执行, 以便获取到正确的资源ID
        // generatePreload(context, coursewareModel);
        // 互动
        generateInteraction(context, coursewareModel);
        // 规范化coursewareModel
        normalizeCoursewareModel(context, coursewareModel);

        coursewareModel.setTaskId(task.getTaskId());
        coursewareModel.setUpdateTime(TimeUtils.formatIso8601DateString(task.getUpdateTime()));
        return coursewareModel;
    }

    /**
     * 生成设置
     */
    private void generateSetting(final CoursewareModel coursewareModel) {
        // 设置分辨率
        SettingSegment screenResolutionSegment = new SettingSegment();
        screenResolutionSegment.setTime("00:00.0");
        screenResolutionSegment.setType("SetScreenResolution");
        screenResolutionSegment.custom(new JSONObject().fluentPut("width", 1920).fluentPut("height", 1080).fluentPut("fullscreen", false));
        coursewareModel.getSettingSegment().add(screenResolutionSegment);
    }

    /**
     * 预加载资源
     */
    private void generatePreload(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        //判断是否是白猫角色
        boolean useWhiteCat = StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.WHITE_CAT.getId());
        // 预加载场景
        if (StringUtils.isNotEmpty(context.getCurrentSceneId()) && !useWhiteCat) {
            PreloadSegment scenePreload = new PreloadSegment();
            scenePreload.setTime("00:00.0");
            scenePreload.setType(null);
            scenePreload.setResourceId(context.getCurrentSceneId());
            scenePreload.setReason("场景预加载启动");
            scenePreload.custom(new JSONObject().fluentPut("preloadResType", 0));
            coursewareModel.getPreloadSegment().add(scenePreload);
        }

        // 预加载角色
        if (context.getCurrentNpcCharacter() == null && StringUtils.isNotEmpty(context.getCurrentCharacterResId())) {
            PreloadSegment characterPreload = new PreloadSegment();
            characterPreload.setTime("00:00.0");
            characterPreload.setType(null);
            characterPreload.setResourceId(context.getCurrentCharacterResId());
            characterPreload.setReason("角色预加载启动");
            characterPreload.custom(new JSONObject().fluentPut("preloadResType", 1));
            coursewareModel.getPreloadSegment().add(characterPreload);
        }

        if (context.getCurrentNpcCharacter() != null && !useWhiteCat) {
            // CC4 NPC预加载角色
            PreloadSegment npcPreload = new PreloadSegment();
            npcPreload.setTime("00:00.0");
            npcPreload.setType(null);
            npcPreload.setResourceId(context.getCurrentNpcCharacter().getResourceId());
            npcPreload.setReason("CC4 NPC预加载启动");
            npcPreload.custom(new JSONObject().fluentPut("preloadResType", 2));
            coursewareModel.getPreloadSegment().add(npcPreload);
        }
    }

    /**
     * 生成场景和点位
     */
    private void generateScene(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        String sceneId;
        if (StringUtils.isNotBlank(context.getGlobalScene())) {
            sceneId = context.getGlobalScene();
        } else {
            List<CoursewareResourceDTO> scenes = context.getCoursewareResourceConfig().getScenes();
            if (CollectionUtils.isEmpty(scenes)) {
                context.getCoursewareResourceConfig().setScenes(Lists.newArrayList());
                return;
            }
            CoursewareResourceDTO scene = scenes.get(RandomUtils.nextInt(0, scenes.size()));
            sceneId = scene.getId();
        }

        coursewareModel.getSceneSegment().add(SegmentUtil.createSceneSegment("00:00.0", context.getTotalLength(), sceneId));
        coursewareModel.setScene(sceneId);
        context.setCurrentSceneId(sceneId);
        // 场景点位
        if (null != context.getBirthPosition()) {
            BirthPointSegment birthPointSegment = SegmentUtil.createBirthPointSegment("00:00.00", "AI推荐点位", context.getBirthPosition());
            coursewareModel.getBirthPointSegment().add(birthPointSegment);
            context.setCurrentBirthPoint(birthPointSegment);
        } else {
            List<ScenePositionResourceDTO> scenePositions = context.getCoursewareResourceConfig().getScenePositionsMap().get(sceneId);
            if (CollectionUtils.isEmpty(scenePositions)) {
                return;
            }
            String birthPointName = context.getBirthPointName();
            CoursewareResourceDTO birthPointRes = scenePositions.stream()
                    .filter(item -> StringUtils.equals(birthPointName, item.getType()))
                    .findFirst()
                    .orElse(null);
            if (birthPointRes != null) {
                BirthPointSegment birthPointSegment = SegmentUtil.createBirthPointSegment("00:00.00", birthPointRes.getType(), birthPointRes.getCustom());
                coursewareModel.getBirthPointSegment().add(birthPointSegment);
                context.setCurrentBirthPoint(birthPointSegment);
            }

            // TODO 随机出生点
            // Collections.shuffle(scenePositions);
            // if (CollectionUtils.isNotEmpty(scenePositions)) {
            //     BirthPointSegment birthPointSegment = SegmentUtil.createBirthPointSegment("00:00.00", scenePositions.get(0).getType(), scenePositions.get(0).getCustom());
            //     coursewareModel.getBirthPointSegment().add(birthPointSegment);
            //     context.setCurrentBirthPoint(birthPointSegment);
            // }
        }
    }

    /**
     * 角色和灯光
     */
    private void generateCharacter(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        if (!context.getCharacterGenerationEnabled()) {
            return;
        }
        String resourceId = context.getGlobalCharacter();
        CharacterEnum characterEnum = CharacterEnum.getCharacterById(resourceId);
        if (characterEnum == null) {
            characterEnum = CharacterEnum.COMMON;
        }
        context.setCurrentCharacterResId(characterEnum.getId());
        coursewareModel.getCharacterSegment().add(SegmentUtil.createCharacterSegment("00:00.0", null, characterEnum.getId(), characterEnum.isNpc()));

        // CC角色
        if (characterEnum.isNpc()) {
            String characterInstanceId = StringUtils.defaultIfBlank(context.getCoursewareGenerationReq()
                    .getCharacterInstanceId(), UUIDUtil.upperUuid());
            SceneObjectSegment npcSegment = new SceneObjectSegment();
            npcSegment.time("00:00.0").resourceId(resourceId).reason("NPC").instanceId(characterInstanceId);
            if (StringUtils.equals(context.getBizFlag(), BizConstant.BIZ_FLAG_VLAB) && CharacterUtils.isNeonNewOrWhiteCat(context)) {
                npcSegment.location(2662.382392, -1141.325128, 2411.515837);
            }

            // 设置角色隐藏模式
            SceneObjectSegment hideNpcSegment = null;
            if (StringUtils.equals(BizConstant.CHARACTER_HANDLE_MODE_DESTROY, context.getCoursewareGenerationReq().getCharacterHandleMode())) {
                npcSegment.setEndTime(context.getTotalLength());
            } else if (StringUtils.equals(BizConstant.CHARACTER_HANDLE_MODE_HIDE, context.getCoursewareGenerationReq().getCharacterHandleMode())) {
                hideNpcSegment = new SceneObjectSegment().time(context.getTotalLength()).resourceId(npcSegment.getResourceId()).type("MetaRoleSetting").reason("HideNpc").instanceId(npcSegment.getInstanceId());
                hideNpcSegment.getCustom().put("Show", false);
            }

            npcSegment.getCustom().fluentPut("bAdjustLocationToFloor", true);
            BirthPointSegment birthPointSegment = context.getCurrentBirthPoint();
            if (birthPointSegment != null) {
                npcSegment.getCustom().putAll(birthPointSegment.cloneTransform());
            }
            coursewareModel.getSceneObjectSegment().add(npcSegment);
            if(hideNpcSegment != null) {
                coursewareModel.getSceneObjectSegment().add(hideNpcSegment);
            }
            context.setCurrentNpcCharacter(npcSegment);
            coursewareModel.getPrepareSegment()
                    .add(new PrepareSegment().setSceneObjectSegment(Lists.newArrayList(context.getCurrentNpcCharacter())));
        }

        // 灯光
        if (context.getCurrentNpcCharacter() != null && !StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.WHITE_CAT.getId())) {
            SceneObjectSegment lightObjectSegment = new SceneObjectSegment();
            lightObjectSegment.setTime("00:00.0");
            lightObjectSegment.setEndTime(context.getTotalLength());
            String resId = StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.CC4Female21.getId()) ? "0DF499D544A63D5838905B99BA892776" : "2F2338F647F74045B69F638737A54959";
            lightObjectSegment.setResourceId(resId);
            lightObjectSegment.setReason("CC角色灯光");
            JSONObject custom = new JSONObject();
            String instanceID = UUIDUtil.upperUuid();
            custom.fluentPut("instanceID", instanceID)
                    .fluentPut("location", new Vector3D(0, 0, -1000))
                    .fluentPut("rotation", new Vector3D(0, 0, 0))
                    .fluentPut("scale", new Vector3D(1, 1, 1));
            lightObjectSegment.setCustom(custom);
            coursewareModel.getSceneObjectSegment().add(lightObjectSegment);

            BuffSegment buffSegment = new BuffSegment();
            buffSegment.setTime("00:00.05");
            buffSegment.setReason("挂灯");
            buffSegment.setType("buff");
            buffSegment.cTime(3600d).targetId(context.getCharacterInstanceId());
            buffSegment.getCustom()
                    .fluentPut("Name", "Performing.Attatch")
                    .fluentPut("Param", new JSONObject().fluentPut("InsID", instanceID)
                            .fluentPut("BoneName", "root")
                            .fluentPut("Offset", new Vector3D(0, 0, 0))
                            .fluentPut("Rotation", new Vector3D(0, 0, 0)));
            coursewareModel.getBuffSegment().add(buffSegment);
        }
        if (!StringUtils.equals(context.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)) {
            if (context.getBirthPositionLight() != null) {
                coursewareModel.getLightSegment().add(SegmentUtil.createLightSegment("00:00.0", null, context.getBirthPositionLight(), "点位灯光"));
            } else {
                String lightResId = "51d4c54d-ccbb-4a9c-be5a-e352351ba217";
                coursewareModel.getLightSegment().add(SegmentUtil.createLightSegment("00:00.0", null, lightResId, "AI课件-基础灯光"));
            }
        }
    }

    /**
     * 生成服装
     */
    private void generateClothing(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        if (!context.getClothingGenerationEnabled()) {
            return;
        }
        List<CoursewareResourceDTO> clothes = context.getCompatibleClothes();
        if (StringUtils.isNotEmpty(context.getGlobalClothing())) {
            clothes = clothes.stream().filter(e -> StringUtils.equals(context.getGlobalClothing(), e.getId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(clothes)) {
            return;
        }
        Collections.shuffle(clothes);
        coursewareModel.getClothingSegment()
                .add(SegmentUtil.createClothingSegment("00:00.0", null, clothes.get(0).getId(), clothes.get(0).getName()));
        coursewareModel.setClothing(clothes.get(0).getId());
    }

    /**
     * 生成背景音和字幕
     */
    private void generateBgmAndSubtitle(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        if (StringUtils.isNotBlank(context.getBgmResourceId())) {
            String resourceId = context.getBgmResourceId();
            coursewareModel.getAudioSegment().add(SegmentUtil.createAudioSegment("00:00.0", context.getTotalLength(), resourceId, "Voice", 1f));
        }
        if (!StringUtils.equals(context.getBizFlag(), BizConstant.BIZ_FLAG_VLAB) && StringUtils.isNotEmpty(context.getBackgroundMusicResourceId())) {
            // 非实验室的脚本添加背景音乐到backgroundMusic轨道， 实验室版本不支持backgroundMusic轨道
            String resourceId = context.getBackgroundMusicResourceId();
            long bgmDuration = 60 * 60 * 1000;
            try {
                bgmDuration = audioResourceService.bgmResourceDuration(resourceId);
            } catch (IOException ignore) {
            }
            long endTime = TimeUtils.convertToMilliseconds(context.getTotalLength());
            long startTime = 0;
            while (startTime < endTime) {
                String st = TimeUtils.formatMilliseconds(startTime);
                String et = TimeUtils.formatMilliseconds(Math.min(endTime, startTime + bgmDuration));
                coursewareModel.getAudioSegment().add(SegmentUtil.createAudioSegment(st, et, resourceId, "Music", 0.5f));
                startTime += bgmDuration;
            }
        }
        if (!StringUtils.equals(context.getBizFlag(), BizConstant.BIZ_FLAG_VLAB) && StringUtils.isNotEmpty(context.getAmbientSoundResource())) {
            // 环境音效
            String resourceId = context.getAmbientSoundResource();
            long audioDuration = 60 * 60 * 1000;
            try {
                audioDuration = audioResourceService.audioResourceDuration(resourceId, NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_COURSEWARE_CONTAINER_ID);
            } catch (IOException ignore) {
            }
            long endTime = TimeUtils.convertToMilliseconds(context.getTotalLength());
            long startTime = 0;
            while (startTime < endTime) {
                String st = TimeUtils.formatMilliseconds(startTime);
                String et = TimeUtils.formatMilliseconds(Math.min(endTime, startTime + audioDuration));
                coursewareModel.getAudioSegment().add(SegmentUtil.createAudioSegment(st, et, resourceId, "Music", 0.4f));
                startTime += audioDuration + 3000;
            }
        }
        if (StringUtils.equals(context.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)) {
            AtaResult ataResult = speechTimingService.getAtaResult(context.getResourceId(), context.getAudioUrl(), context.getAudioText());
            String lineLyrics = SubtitleUtils.genLrcContent(ataResult, true);
            List<String> lineLyricList = Arrays.stream(StringUtils.split(lineLyrics, "\n"))/*.map(String::trim)*/.collect(Collectors.toList());

            String regex = "\\[(\\d{2}:\\d{2}\\.\\d{1,2})]\\[(\\d{2}:\\d{2}\\.\\d{1,2})](.+)";
            Pattern pattern = Pattern.compile(regex);
            List<String[]> timeAndLyrics = new ArrayList<>(lineLyricList.size());
            for (String line : lineLyricList) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    String st = matcher.group(1);
                    String et = matcher.group(2);
                    String text = matcher.group(3).trim();
                    timeAndLyrics.add(new String[]{st, et, text});
                }
            }
            for (String[] timeAndLyric : timeAndLyrics) {
                String lyric = timeAndLyric[2];
                if (StringUtils.isBlank(lyric)) {
                    continue;
                }
                String startTime = timeAndLyric[0];
                String endTime = timeAndLyric[1];

                coursewareModel.getUiSegment().add(SegmentUtil.createSubtitleUISegment(startTime, endTime, lyric));
            }
        } else {
            if (StringUtils.isNotBlank(context.getSubtitleResourceId())) {
                WordSegment subtitleSegment = new WordSegment();
                subtitleSegment.setTime("00:00.0");
                subtitleSegment.setEndTime(context.getTotalLength());
                subtitleSegment.setResourceId(context.getSubtitleResourceId());
                subtitleSegment.setResourceUrl(ndrService.getResourceUrl(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, context.getSubtitleResourceId(), "source"));
                subtitleSegment.setType("Subtitle");
                subtitleSegment.setReason("字幕");
                coursewareModel.getWordSegment().add(subtitleSegment);
            }
        }
    }

    /**
     * 生成互动
     */
    @SneakyThrows
    private void generateInteraction(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        // 所有互动的表演方案
        List<InteractionDTO> interactions = context.getTeachEventTypes()
                .stream()
                .map(TeachEventTypeDTO::getInteraction)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        VideoInteraction interactionResult = InteractiveQuestionUtil.buildVideoInteraction(interactions);
        if (interactionResult != null) {
            VideoInteraction copiedInteractionResult = new VideoInteraction();
            BeanUtil.copyProperties(interactionResult, copiedInteractionResult, true);
            coursewareModel.setInteractionResult(copiedInteractionResult);
            // 判断是否实验室业务
            if (StringUtils.equals(context.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)) {
                interactionResult.setRefResourceId(context.getResourceId());
                if (CollectionUtils.isNotEmpty(interactionResult.getActiveEvents())) {
                    for (VideoInteraction.ActiveEvent activeEvent : interactionResult.getActiveEvents()) {
                        activeEvent.setMediaTarget(context.getResourceId());
                    }
                }
                interactionResult = interactionFeignClient.submitInteraction(context.getResourceId(), interactionResult);

                List<UISegment> interactionSegments = new ArrayList<>();
                List<VideoInteraction.ActiveEvent> activeEvents = interactionResult.getActiveEvents();
                if (CollectionUtils.isNotEmpty(activeEvents)) {
                    for (VideoInteraction.ActiveEvent activeEvent : activeEvents) {
                        UISegment uiSegment = new UISegment();
                        uiSegment.time((long) (activeEvent.getTimeAnchor() * 1000))
                                .resourceId("928a7d34-8ba4-48d2-95dd-24d3b6c2c0c1")
                                .type("MetaUI")
                                .reason(activeEvent.getType());
                        uiSegment.perform("interaction-effect").performOption("condition", "Status==-1||Status==1");
                        String url = "https://ai-courseware-interaction.sdp.101.com/?platform=UE&id=" + interactionResult.getId() +"&lang=zh&outer_fullscreen=true#/exercises";
                        uiSegment.instanceId(UUIDUtil.upperUuid()).addProperty("Url", "string", url);
                        JSONObject msg = new JSONObject().fluentPut("Msg", "InteractiveQuestion")
                                .fluentPut("Data", WafJsonMapper.toJson(new JSONObject().fluentPut("Id", activeEvent.getId())));
                        uiSegment.instanceId(UUIDUtil.upperUuid()).addProperty("InitParam", "string", WafJsonMapper.toJson(msg));
                        uiSegment.instanceId(UUIDUtil.upperUuid()).addProperty("ShouldWaitForLoad", "bool", "false");
                        uiSegment.instanceId(UUIDUtil.upperUuid()).addProperty("ShouldShowCloseBtn", "bool", "true");
                        interactionSegments.add(uiSegment);
                    }
                }
                coursewareModel.getUiSegment().addAll(interactionSegments);
            }
        }
    }

    /**
     * 生成Arkit面部
     */
    @Deprecated
    @SneakyThrows
    private void generateArkitFace(final CoursewareModel coursewareModel, CompletableFuture<String> arkitFaceFuture, boolean enableArkitFace) {
        if (null == arkitFaceFuture) {
            return;
        }
        if (enableArkitFace) {
            arkitFaceFuture.thenAccept(arkitFaceResourceId -> {
                if (!UUIDUtil.isUuid(arkitFaceResourceId)) {
                    return;
                }
                String endTime = TimeUtils.timeConvert(coursewareModel.getLength(), null);
                RoutineSegment arkitFaceSegment = SegmentUtil.createArkitFaceSegmentUsingRoutine("00:00.0", endTime, arkitFaceResourceId, "人物口型");
                coursewareModel.getRoutineSegment().add(arkitFaceSegment);
            }).exceptionally(ex -> {
                log.error("口型生成失败", ex);
                return null;
            });
        }
        try {
            arkitFaceFuture.join();
        } catch (Exception e) {
            log.info("口型生成失败", e);
        }
    }

    private void resolveTeachEventType(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        List<TeachEventTypeDTO> teachEventTypeList = context.getTeachEventTypes()
                .stream()
                .filter(TeachEventTypeDTO::getValid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(teachEventTypeList)) {
            return;
        }
        // 计算教学事件的时间
        List<AtaUtteranceWithPos> ataUtterancesWithPos = speechTimingService.getAtaUtterancesWithPos(context.getResourceId(), context.getAudioUrl(), context.getAudioText());
        List<AtaWordWithPos> ataWords = ataUtterancesWithPos.stream()
                .map(AtaUtteranceWithPos::getWords)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        context.setAtaUtterances(ataUtterancesWithPos);
        context.setAtaWords(ataWords);
        int startPos = 0;
        for (int i = 0; i < teachEventTypeList.size(); i++) {
            TeachEventTypeDTO teachEventTypeDTO = teachEventTypeList.get(i);
            // 设置表演参数
            if (teachEventTypeDTO.getRawPerformanceParam() == null) {
                teachEventTypeDTO.setPerformanceParam(new HashMap<>(0));
            }
            String dialogue = teachEventTypeDTO.getDialogue();
            if (StringUtils.isEmpty(dialogue)) {
                continue;
            }

            if (StringUtils.isBlank(teachEventTypeDTO.getTime())) {
                if (i == 0) {
                    teachEventTypeDTO.setTime("00:00.00");
                } else {
                    int startTime = AtaWordUtil.getWordTiming(ataWords, startPos, true);
                    teachEventTypeDTO.setTime(TimeUtils.formatMilliseconds(startTime));
                }
            }
            teachEventTypeDTO.setDialogueStartPos(startPos);
            startPos += dialogue.length();
        }
        String lastEndTime = null;
        for (int i = 0; i < teachEventTypeList.size(); i++) {
            TeachEventTypeDTO teachEventTypeDTO = teachEventTypeList.get(i);
            String nextStartTime = i == teachEventTypeList.size() - 1 ? context.getTotalLength() : teachEventTypeList.get(i + 1).getTime();
            if (teachEventTypeDTO.getTime() == null) {
                teachEventTypeDTO.setTime(lastEndTime != null ? lastEndTime : "00:00.0");
            }
            if (teachEventTypeDTO.getEndTime() == null) {
                if (nextStartTime != null) {
                    teachEventTypeDTO.setEndTime(nextStartTime);
                } else {
                    PerformanceTypeEnum performanceTypeEnum = PerformanceTypeEnum.getByName(teachEventTypeDTO.getPerformanceType());
                    if (performanceTypeEnum.getDuration() > 0) {
                        teachEventTypeDTO.setEndTime(TimeUtils.formatMilliseconds(TimeUtils.addMilliseconds(teachEventTypeDTO.getTime(), performanceTypeEnum.getDuration())));
                    } else if (StringUtils.isNotEmpty(teachEventTypeDTO.getDialogue())) {
                        for (int j = ataWords.size() - 1; j >= 0; j--) {
                            if (teachEventTypeDTO.getDialogueStartPos() + (teachEventTypeDTO.getDialogue().length() - 1) > ataWords.get(j).getPos()) {
                                int endTime = ataWords.get(j).getEndTime();
                                teachEventTypeDTO.setEndTime(TimeUtils.formatMilliseconds(endTime));
                                break;
                            }
                        }
                    } else if (Objects.nonNull(teachEventTypeDTO.getResourceDuration())) {
                        teachEventTypeDTO.setEndTime(TimeUtils.formatMilliseconds(TimeUtils.addMilliseconds(teachEventTypeDTO.getTime(), teachEventTypeDTO.getResourceDuration())));
                    } else {
                        teachEventTypeDTO.setEndTime(teachEventTypeDTO.getTime());
                    }
                }
            }
            lastEndTime = teachEventTypeDTO.getEndTime();
        }

        String latestEndTime = teachEventTypeList.get(0).getEndTime();
        teachEventTypeList = context.getTeachEventTypes();
        for (TeachEventTypeDTO teachEventType : teachEventTypeList) {
            if (StringUtils.isBlank(teachEventType.getTime())) {
                teachEventType.setTime(latestEndTime);
                teachEventType.setEndTime(latestEndTime);
            } else {
                latestEndTime = teachEventType.getEndTime();
            }
        }

        for (TeachEventTypeDTO teachEventTypeDTO : teachEventTypeList) {
            boolean isProcessed = false;
            for (TeachEventTypeProcessor processor : teachEventTypeProcessors) {
                if (processor.isSupport(TeachEventTypeEnum.getByName(teachEventTypeDTO.getTeachEventType()))) {
                    processor.process(teachEventTypeDTO, context, coursewareModel);
                    isProcessed = true;
                }
            }
            if (!isProcessed) {
                log.warn("未找到对应的教学事件处理器，教学事件类型：{}", teachEventTypeDTO.getTeachEventType());
            }
        }
    }

    /**
     * 全局规范化 CoursewareModel
     */
    private void normalizeCoursewareModel(final CoursewareGenerationContext context, final CoursewareModel coursewareModel) {
        List<CharacterSegment> characterSegments = coursewareModel.getCharacterSegment();
        List<SceneSegment> sceneSegments = coursewareModel.getSceneSegment();
        List<BirthPointSegment> birthPointSegments = coursewareModel.getBirthPointSegment();
        List<RoutineSegment> routineSegments = coursewareModel.getRoutineSegment();
        List<CameraSegment> cameraSegments = coursewareModel.getCameraSegment();
        List<CameraFeatureSegment> cameraFeatureSegments = coursewareModel.getCameraFeatureSegment();
        List<LottieSegment> lottieSegments = coursewareModel.getLottieSegment();
        List<LightSegment> lightSegments = coursewareModel.getLightSegment();
        List<BgmSegment> backgroundMusicSegment = coursewareModel.getBackgroundMusicSegment();
        // 排序
        SegmentUtil.sortByTime(cameraSegments);
        SegmentUtil.sortByTime(cameraFeatureSegments);
        SegmentUtil.sortByTime(lottieSegments);
        SegmentUtil.sortByTime(lightSegments);
        SegmentUtil.sortByTime(backgroundMusicSegment);

        // 预处理
        if (CollectionUtils.isNotEmpty(characterSegments)) {
            characterSegments.forEach(characterSegment -> {
                if (TimeUtils.convertToMilliseconds(characterSegment.getTime()) == 0) {
                    characterSegment.perform(SegmentPerformConstant.MOVE_TO_PREPARE);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(sceneSegments)) {
            sceneSegments.forEach(sceneSegment -> {
                if (TimeUtils.convertToMilliseconds(sceneSegment.getTime()) == 0) {
                    // sceneSegment.perform(SegmentPerformConstant.MOVE_TO_PREPARE);
                    coursewareModel.getPrepareSegment().add(0, new PrepareSegment().setSceneSegment(Lists.newArrayList(sceneSegment)));
                }
            });
        }

        if (CollectionUtils.isNotEmpty(backgroundMusicSegment)) {
            List<TimeRangeUtils.TimeRange> playVideoTimeRanges = context.getTeachEventTypes().stream()
                    .filter(e -> StringUtils.equals(e.getPerformanceType(), PerformanceTypeEnum.PLAY_VIDEO.getName()))
                    .map(e -> {
                        long startTime = TimeUtils.convertToMilliseconds(e.getTime());
                        long endTime = TimeUtils.convertToMilliseconds(e.getEndTime());
                        return new TimeRangeUtils.TimeRange(startTime, endTime);
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(playVideoTimeRanges)) {
                List<BgmSegment> bgmSegments = TimeRangeUtils.splitBgmSegments(backgroundMusicSegment, playVideoTimeRanges);
                coursewareModel.getBackgroundMusicSegment().clear();
                coursewareModel.getBackgroundMusicSegment().addAll(bgmSegments);
            }
        }

        // 规范全屏板书
        // if (CollectionUtils.isNotEmpty(sceneChangeSegments) && CollectionUtils.isNotEmpty(cameraSegments)) {
        //     Iterator<SceneChangeSegment> sceneChangeIterator = sceneChangeSegments.iterator();
        //     while (sceneChangeIterator.hasNext()) {
        //         SceneChangeSegment sceneChangeSegment = sceneChangeIterator.next();
        //         if (StringUtils.isBlank(sceneChangeSegment.getTime()) || StringUtils.isBlank(sceneChangeSegment.getEndTime())) {
        //             continue;
        //         }
        //         if (!StringUtils.equals(sceneChangeSegment.getType(), "picinpic")) {
        //             continue;
        //         }
        //         long boardStartMs = TimeUtils.convertToMilliseconds(sceneChangeSegment.getTime());
        //         long boardEndMs = TimeUtils.convertToMilliseconds(sceneChangeSegment.getEndTime());
        //         for (CameraSegment cameraSegment : cameraSegments) {
        //             String cameraStartTime = cameraSegment.getTime();
        //             if (StringUtils.isBlank(cameraStartTime) || StringUtils.equals(cameraSegment.getSkillType(), "CharacterCameraSilent")) {
        //                 continue;
        //             }
        //             // 防止夹帧
        //             long cameraStartMs = TimeUtils.convertToMilliseconds(cameraStartTime);
        //             if (Math.abs(boardStartMs - cameraStartMs) < 1000 * 2) {
        //                 sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(cameraStartMs));
        //                 sceneChangeSegment.cTime(sceneChangeSegment.getDurationSec());
        //             }
        //             if (Math.abs(boardEndMs - cameraStartMs) < 1000 * 2) {
        //                 sceneChangeSegment.setEndTime(TimeUtils.formatMilliseconds(cameraStartMs));
        //                 sceneChangeSegment.cTime(sceneChangeSegment.getDurationSec());
        //             }
        //         }
        //     }
        // }

        // 规范化镜头
        if (CollectionUtils.isNotEmpty(cameraSegments)) {
            if (context.getCurrentNpcCharacter() != null) {
                for (CameraSegment cameraSegment : cameraSegments) {
                    JSONObject followParam = cameraSegment.getParam().getJSONObject("Follow");
                    if (followParam == null) {
                        followParam = new JSONObject();
                    }
                    // 防止cc角色镜头抖动
                    cameraSegment.getParam().put("Follow", followParam.fluentPut("FixOnTick", 1).fluentPut("Type", 2));
                }
            }
            CharacterEnum anEnum = CharacterEnum.getCharacterById(context.getCurrentCharacterResId());
            for (CameraSegment cameraSegment : cameraSegments) {
                // 不需要镜头结束时间
                cameraSegment.setEndTime(null);
                String targetId = cameraSegment.getTargetId();
                if (anEnum != null && anEnum.getCameraOffsetZ() != null && (StringUtils.isBlank(targetId) || StringUtils.equals(targetId, context.getCharacterInstanceId()))) {
                    JSONObject followParam = cameraSegment.getParam().getJSONObject("Follow");
                    if (followParam == null) {
                        followParam = new JSONObject();
                    }
                    // 设置偏移
                    cameraSegment.getParam().put("Follow", followParam.fluentPut("OffsetZ", anEnum.getCameraOffsetZ()));
                }
            }
        }

        // 合并看向
        if (CollectionUtils.isNotEmpty(cameraFeatureSegments)) {
            List<CameraFeatureSegment> filteredCameraFeatureSegments = cameraFeatureSegments.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getType(), "look_at_target"))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteredCameraFeatureSegments)) {
                List<CameraFeatureSegment> removedCameraFeatureSegments = new ArrayList<>();
                CameraFeatureSegment masterCameraFeatureSegment = filteredCameraFeatureSegments.get(0);
                for (int i = 1; i < filteredCameraFeatureSegments.size(); i++) {
                    CameraFeatureSegment cameraFeatureSegment = filteredCameraFeatureSegments.get(i);
                    // 开始时间和结束时间相同
                    if (StringUtils.equals(masterCameraFeatureSegment.getEndTime(), cameraFeatureSegment.getTime())) {
                        masterCameraFeatureSegment.setEndTime(cameraFeatureSegment.getEndTime());
                        removedCameraFeatureSegments.add(cameraFeatureSegment);
                    } else {
                        masterCameraFeatureSegment = cameraFeatureSegment;
                    }
                }
                cameraFeatureSegments.removeAll(removedCameraFeatureSegments);
            }
        }

        // 灯光处理
        if (CollectionUtils.isNotEmpty(lightSegments)) {
            LightSegment globalLightSegment = lightSegments.stream().filter(item -> StringUtils.isBlank(item.getEndTime())).findFirst().orElse(null);
            if (globalLightSegment != null) {
                List<LightSegment> newLightSegments = new ArrayList<>();
                for (int i = 1; i < lightSegments.size(); i++) {
                    LightSegment lightSegment = lightSegments.get(i);
                    boolean newLight = StringUtils.isNotBlank(lightSegment.getEndTime());
                    if (i != lightSegments.size() - 1) {
                        LightSegment nextLightSegment = lightSegments.get(i + 1);
                        if (StringUtils.equals(lightSegment.getEndTime(), nextLightSegment.getTime())) {
                            newLight = false;
                        }
                    }
                    if (newLight) {
                        newLightSegments.add(SegmentUtil.createLightSegment(lightSegment.getEndTime(), null, globalLightSegment.getResourceId(), globalLightSegment.getReason()));
                    }
                }
                lightSegments.addAll(newLightSegments);
            }
        }

        // 出生点位处理
        if (CollectionUtils.isNotEmpty(birthPointSegments)) {
            for (int i = 0; i < birthPointSegments.size() - 1; i++) {
                BirthPointSegment current = birthPointSegments.get(i);
                BirthPointSegment next = birthPointSegments.get(i + 1);
                current.setEndTime(next.getTime());
                // 传送特效
                if (StringUtils.equals(next.getPerformName(), SegmentPerformConstant.BIRTH_POINT_TRANSPORT)) {
                    current.perform(SegmentPerformConstant.BIRTH_POINT_TRANSPORT).performOption("hideEffect", true);
                }
            }
            // 最后一个元素的endTime
            birthPointSegments.get(birthPointSegments.size() - 1).setEndTime(context.getTotalLength());
        }

        // CC动作预加载
        if (CollectionUtils.isNotEmpty(routineSegments)) {
            List<RoutineSegment> filteredRoutineSegments = routineSegments.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getType(), RoutineTypeEnum.Animation.getName()))
                    .collect(Collectors.toList());
            for (int i = 0; i < filteredRoutineSegments.size() - 1; i++) {
                RoutineSegment current = filteredRoutineSegments.get(i);
                RoutineSegment next = filteredRoutineSegments.get(i + 1);
                current.getParam().put("PreloadAnimations", Lists.newArrayList(next.getResourceId()));
            }
        }

        // 屏蔽动作轨道
        if (CollectionUtils.isNotEmpty(routineSegments) && !StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.PIXAR.getId())
                && !StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.CHIBI.getId())
                && !StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.NEON.getId())
                && !StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.NEON2.getId())) {
            List<RoutineSegment> filteredRoutineSegments = routineSegments.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getType(), RoutineTypeEnum.BigRoutine.getName()) || StringUtils.equalsIgnoreCase(item.getType(), RoutineTypeEnum.CharacterAction.getName()))
                    .collect(Collectors.toList());
            for (RoutineSegment routineSegment : filteredRoutineSegments) {
                routineSegment.putParam("DontBindChar", true);
            }
        }

        // 大套路统一加StopAllSkill
        if (CollectionUtils.isNotEmpty(routineSegments)) {
            List<RoutineSegment> filteredRoutineSegments = routineSegments.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getSkillType(), RoutineTypeEnum.BigRoutine.getName()))
                    .collect(Collectors.toList());
            for (RoutineSegment routineSegment : filteredRoutineSegments) {
                routineSegment.putParam("StopAllSkill", false);
            }
        }

        // 全局Idle动作
        if (BooleanUtils.isTrue(context.getNewActionEnabled())) {
            RoutineSegment idleRoutineSegment = ActionUtil.genIdleActionSegment(context.getCompatibleActions(true));
            if (idleRoutineSegment != null) {
                coursewareModel.getRoutineSegment().add(idleRoutineSegment);
            }
        }

        if (StringUtils.equals(context.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)) {
            coursewareModel.getRoutineSegment().forEach(e -> e.removeParam("PauseAtEnd"));
        }

        SegmentUtil.sortByTime(coursewareModel.getRoutineSegment());
        SegmentUtil.sortByTime(coursewareModel.getActionSegment());

    }

    /**
     * 创建生成上下文
     */
    @SneakyThrows
    private CoursewareGenerationContext createCoursewareGenerationContext(final CoursewareGenerationReq coursewareGenerationReq, final CoursewareModel coursewareModel) {
        CoursewareGenerationContext context = new CoursewareGenerationContext();
        context.setTitle(coursewareModel.getTitle());
        // 配置信息
        context.setGlobalScene(coursewareGenerationReq.getGlobalScene());
        context.setGlobalCharacter(StringUtils.defaultIfBlank(coursewareGenerationReq.getGlobalCharacter(), CharacterEnum.COMMON.getId()));
        context.setGlobalClothing(coursewareGenerationReq.getGlobalClothing());
        context.setBirthPointName(coursewareGenerationReq.getBirthPointName());
        context.setBirthPosition(coursewareGenerationReq.getBirthPosition());
        context.setCharacterGenerationEnabled(coursewareGenerationReq.getCharacterGenerationEnabled());
        context.setClothingGenerationEnabled(coursewareGenerationReq.getClothingGenerationEnabled());
        context.setNewActionEnabled(coursewareGenerationReq.getNewActionEnabled());
        context.setTeachEventTypes(coursewareGenerationReq.getTeachEventTypes());
        context.setPptVideoResources(coursewareGenerationReq.getPptVideoResources());
        context.setBizFlag(coursewareGenerationReq.getBizFlag());
        context.setBackgroundMusicResourceId(coursewareGenerationReq.getBackgroundMusicResourceId());
        context.setAmbientSoundResource(coursewareGenerationReq.getAmbientSoundResource());
        context.setCoursewareGenerationReq(coursewareGenerationReq);
        if (CollectionUtils.isEmpty(context.getTeachEventTypes())) {
            throw AicException.ofIllegalArgument("节目单不能为空");
        }
        // 对白尾部增加回车
        context.getTeachEventTypes().forEach(item -> {
            if (StringUtils.isNotBlank(item.getDialogue()) && !StringUtils.endsWith(item.getDialogue(), "\n")) {
                item.setDialogue(item.getDialogue() + "\n");
            }
        });
        String audioText = context.getTeachEventTypes()
                .stream()
                .map(TeachEventTypeDTO::getDialogue)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining());
        context.setAudioText(audioText);
        if (StringUtils.isBlank(audioText)) {
            throw AicException.ofIllegalArgument("对白不能为空");
        }

        return context;
    }

    private void initCoursewareGenerationContext(final CoursewareTask task, final CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        // 获取课件资源信息
        String tenantId = task.getCoursewareGenerationReq().getTenantId();
        String resourceId = task.getCoursewareGenerationReq().getResourceId();
        String containerId = task.getCoursewareGenerationReq().getContainerId();
        context.setResourceId(resourceId);
        context.setContainerId(containerId);
        ResourceMetaTiListViewModel resourceMeta = ndrService.queryAudioMeta(tenantId, containerId, resourceId, null);
        Map<String, Object> customProperties = resourceMeta.getCustomProperties();
        coursewareModel.setLength((String) customProperties.get(NdrConstant.SONG_DURATION_PROP));
        if (StringUtils.isNotBlank(coursewareModel.getLength())) {
            context.setTotalLength(TimeUtils.timeConvert(coursewareModel.getLength(), null));
        }
        if (StringUtils.isBlank(context.getTotalLength())) {
            throw AicException.ofIllegalArgument("时长属性不存在");
        }
        // 获取歌词内容
        Map<String, DownloadUrlViewModel> resourceUrlModelMap = ndrService.queryAudioUrlModel(tenantId, containerId, resourceId, null);
        String audioUrl = NdrService.getUrlFromModel(resourceUrlModelMap.get("dry_sound") != null ? resourceUrlModelMap.get("dry_sound") : resourceUrlModelMap.get("source"));
        if (StringUtils.isBlank(audioUrl)) {
            throw AicException.ofIllegalArgument("音频不存在");
        }
        context.setAudioUrl(audioUrl);
        String lineLyricsUrl = NdrService.getUrlFromModel(resourceUrlModelMap.get("line_lyrics"));
        if (StringUtils.isBlank(lineLyricsUrl)) {
            throw AicException.ofIllegalArgument("行歌词不存在");
        }
        String content = wafHttpClient.getForObject(lineLyricsUrl, String.class);
        if (StringUtils.isBlank(content)) {
            throw AicException.ofIllegalArgument("行歌词不能为空");
        }
        List<String> lines = Arrays.stream(StringUtils.split(content, "\n"))/*.map(String::trim)*/.collect(Collectors.toList());
        context.setLineLyric(content);
        context.setLineLyricList(lines);
        // 获取资源配置信息
        CoursewareResourceConfigDTO coursewareResourceConfigDTO = fastGptRetryService.askForResourceConfig();
        context.setCoursewareResourceConfig(coursewareResourceConfigDTO);
    }

    private CoursewareTask createTask(CoursewareGenerationReq coursewareGenerationReq) {
        CoursewareTask task = new CoursewareTask();
        task.setCoursewareGenerationReq(coursewareGenerationReq);
        Long currentTime = System.currentTimeMillis();
        task.setCreateTime(currentTime);
        task.setUpdateTime(currentTime);
        return task;
    }

    /**
     * 更新任务状态
     */
    private void updateTask(final CoursewareTask task) {
        if (task != null) {
            task.setUpdateTime(System.currentTimeMillis());
            coursewareTaskService.update(task);
        }
    }
}
