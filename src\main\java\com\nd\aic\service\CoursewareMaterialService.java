package com.nd.aic.service;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.domain.Module;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.client.AIHubFeignClient;
import com.nd.aic.client.pojo.aihub.AISearchResponseDTO;
import com.nd.aic.entity.CoursewareMaterial;
import com.nd.aic.enums.MaterialTypeEnum;
import com.nd.aic.pojo.dto.MaterialAddDTO;
import com.nd.aic.pojo.dto.MaterialQueryDTO;
import com.nd.aic.pojo.dto.MaterialResponseDTO;
import com.nd.aic.pojo.dto.MaterialUpdateDTO;
import com.nd.aic.repository.CoursewareMaterialRepository;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 课件素材服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CoursewareMaterialService extends BaseService<CoursewareMaterial, String> {

    /**
     * 知识库ID
     */
    private static final String DATASET_ID = "969a3d42-8b37-492c-8cf2-5fa1213617df";

    /**
     * 文档ID
     */
    private static final String DOCUMENT_ID = "a0d53355-766d-4cb0-ad1d-fbd94b87a4a0";

    /**
     * UUID正则表达式模式
     */
    private static final Pattern UUID_PATTERN = Pattern.compile("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}");

    /**
     * AI搜索返回的top_k数量
     */
    private static final int AI_SEARCH_TOP_K = 20;

    /**
     * AI搜索的分数阈值
     */
    private static final double AI_SEARCH_SCORE_THRESHOLD = 0.7;

    private final CoursewareMaterialRepository coursewareMaterialRepository;
    private final MongoTemplate mongoTemplate;
    private final AIHubFeignClient aiHubFeignClient;

    @Override
    protected Module module() {
        return new Module("COURSEWARE_MATERIAL");
    }

    /**
     * 添加课件素材
     */
    public MaterialResponseDTO addMaterial(MaterialAddDTO addDTO) {
        CoursewareMaterial material = new CoursewareMaterial();
        BeanUtil.copyProperties(addDTO, material, CopyOptions.create().ignoreNullValue().ignoreCase());

        // 设置ID
        material.setId(UUID.randomUUID().toString());

        // 设置上传者信息
        String currentUserId = WafContext.getCurrentAccountId();
        material.setUploaderId(currentUserId);

        // 设置初始状态
        material.setStatus("ACTIVE");
        material.setCreateTime(new Date());
        material.setUpdateTime(new Date());

        CoursewareMaterial savedMaterial = super.add(material);

        JSONObject segment = new JSONObject().fluentPut("content", buildVectorContent(addDTO, savedMaterial.getId()));

        JSONObject resp = aiHubFeignClient.createDocumentSegments(DATASET_ID, DOCUMENT_ID, new JSONObject().fluentPut("segments", Lists.newArrayList(segment)));

        if (resp != null) {
            JSONArray dataArray = resp.getJSONArray("data");
            if (CollectionUtils.isNotEmpty(dataArray)) {
                String externalId = dataArray.getJSONObject(0).getString("id");
                if (StringUtils.isNotBlank(externalId)) {
                    savedMaterial.setExternalId(externalId);
                }
            }
        }

        savedMaterial = save(savedMaterial);
        return convertToResponseDTO(savedMaterial);
    }

    /**
     * 更新课件素材
     */
    public MaterialResponseDTO updateMaterial(String id, MaterialUpdateDTO updateDTO) {
        CoursewareMaterial existingMaterial = super.findOne(id);
        if (existingMaterial == null) {
            throw module().notFound();
        }

        BeanUtil.copyProperties(updateDTO, existingMaterial, CopyOptions.create().ignoreNullValue().ignoreCase());
        existingMaterial.setUpdateTime(new Date());

        CoursewareMaterial savedMaterial = save(existingMaterial);
        return convertToResponseDTO(savedMaterial);
    }

    /**
     * 删除课件素材
     */
    public void deleteMaterial(String id) {
        CoursewareMaterial material = findOne(id);
        if (material == null) {
            throw module().notFound();
        }
        try {
            aiHubFeignClient.deleteDocumentSegment(DATASET_ID, DOCUMENT_ID, material.getExternalId());
        } catch (Exception ignore) {
        }
        delete(material);
    }

    /**
     * 获取课件素材详情
     */
    public MaterialResponseDTO getMaterialDetail(String id) {
        CoursewareMaterial material = findOne(id);
        if (material == null) {
            throw module().notFound();
        }
        return convertToResponseDTO(material);
    }

    /**
     * 查询课件素材列表
     */
    public Items<MaterialResponseDTO> queryMaterials(MaterialQueryDTO queryDTO) {
        if (queryDTO == null) {
            queryDTO = new MaterialQueryDTO();
        }

        // 用于存储ID和对应的分值映射
        Map<String, Double> idScoreMap = new HashMap<>();

        if (StringUtils.isNotBlank(queryDTO.getKeyword())) {

            JSONObject query = getAiQuery(queryDTO.getKeyword());

            // 调用AI Hub接口进行搜索
            JSONObject response = aiHubFeignClient.datasetHitTesting(DATASET_ID, query);
            AISearchResponseDTO searchResponse = response.toJavaObject(AISearchResponseDTO.class);

            if (searchResponse != null && searchResponse.getRecords() != null) {
                // 过滤出分数大于0.7的最多10条数据，然后提取UUID和分值
                List<String> idList = searchResponse.getRecords()
                        .stream()
                        .filter(record -> record.getScore() != null && record.getScore() > AI_SEARCH_SCORE_THRESHOLD)
                        .limit(10)
                        .peek(record -> {
                            // 提取ID并保存分值映射
                            String id = extractIdFromContent(record.getSegment().getContent());
                            if (StringUtils.isNotBlank(id)) {
                                idScoreMap.put(id, record.getScore());
                            }
                        })
                        .map(AISearchResponseDTO.SearchRecord::getSegment)
                        .filter(Objects::nonNull)
                        .map(segment -> extractIdFromContent(segment.getContent()))
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(idList)) {
                    queryDTO.setIds(idList);
                }
            }
        }

        // 构建查询条件
        Query query = buildQuery(queryDTO);

        // 构建排序
        Sort.Direction direction = "asc".equalsIgnoreCase(queryDTO.getSortDirection()) ? Sort.Direction.ASC : Sort.Direction.DESC;
        Sort sort = new Sort(direction, queryDTO.getSortBy());
        query.with(sort);

        // 构建分页
        int skip = (queryDTO.getPage() - 1) * queryDTO.getPageSize();
        query.skip(skip).limit(queryDTO.getPageSize());

        // 执行查询
        List<CoursewareMaterial> materials = mongoTemplate.find(query, CoursewareMaterial.class);

        // 获取总数
        long totalCount = mongoTemplate.count(buildQuery(queryDTO), CoursewareMaterial.class);

        // 转换为DTO
        List<MaterialResponseDTO> resultList = materials.stream()
                .map(material -> convertToResponseDTO(material, idScoreMap))
                .collect(Collectors.toList());

        return Items.of(resultList, totalCount);
    }

    /**
     * 根据类型获取课件素材
     */
    public List<MaterialResponseDTO> getMaterialsByType(MaterialTypeEnum type) {
        List<CoursewareMaterial> materials = coursewareMaterialRepository.findByType(type);
        return materials.stream().map(this::convertToResponseDTO).collect(Collectors.toList());
    }

    /**
     * 构建AI查询请求
     */
    private static JSONObject getAiQuery(String keyword) {
        JSONObject retrievalModel = new JSONObject();
        retrievalModel.put("search_method", "hybrid_search");
        retrievalModel.put("reranking_enable", true);
        retrievalModel.put("reranking_model", new JSONObject().fluentPut("reranking_provider_name", "aict/ai_hub/ai_hub")
                .fluentPut("reranking_model_name", "bge-reranker-v2-m3"));
        retrievalModel.put("top_k", AI_SEARCH_TOP_K);
        retrievalModel.put("score_threshold_enabled", true);
        retrievalModel.put("score_threshold", AI_SEARCH_SCORE_THRESHOLD);

        JSONObject query = new JSONObject();
        query.put("query", StringUtils.trim(keyword));
        query.put("retrieval_model", retrievalModel);
        return query;
    }

    /**
     * 构建向量数据库存储的内容
     * 格式：资源ID":"资源ID值";"资源名称":"资源名称值";"资源描述":"资源描述值";
     */
    private String buildVectorContent(MaterialAddDTO addDTO, String materialId) {
        StringBuilder contentBuilder = new StringBuilder();

        // 添加资源ID
        contentBuilder.append("\"资源ID\":\"").append(StringUtils.defaultString(materialId)).append("\";");

        // 添加资源名称
        contentBuilder.append("\"资源名称\":\"").append(StringUtils.defaultString(addDTO.getName())).append("\";");

        // 添加资源描述，如果描述为空则使用名称
        String description = StringUtils.defaultIfBlank(addDTO.getDescription(), addDTO.getName());
        contentBuilder.append("\"资源描述\":\"").append(StringUtils.defaultString(description)).append("\";");

        return contentBuilder.toString();
    }

    /**
     * 从向量数据库内容中提取资源ID
     * 内容格式：资源ID":"资源ID值";"资源名称":"资源名称值";"资源描述":"资源描述值";
     */
    private String extractIdFromContent(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }

        // 匹配 "资源ID":"UUID" 格式中的UUID
        Pattern resourceIdPattern = Pattern.compile("资源ID\":\"([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})\"");
        Matcher matcher = resourceIdPattern.matcher(content);

        if (matcher.find()) {
            return matcher.group(1); // 返回第一个捕获组，即UUID部分
        }

        // 如果没有找到资源ID格式，则尝试提取任何UUID（向后兼容）
        Matcher uuidMatcher = UUID_PATTERN.matcher(content);
        if (uuidMatcher.find()) {
            return uuidMatcher.group();
        }

        return null;
    }

    /**
     * 转换为响应DTO
     */
    private MaterialResponseDTO convertToResponseDTO(CoursewareMaterial material) {
        return convertToResponseDTO(material, null);
    }

    /**
     * 转换为响应DTO（带分值映射）
     */
    private MaterialResponseDTO convertToResponseDTO(CoursewareMaterial material, Map<String, Double> idScoreMap) {
        MaterialResponseDTO responseDTO = new MaterialResponseDTO();
        BeanUtil.copyProperties(material, responseDTO, CopyOptions.create().ignoreNullValue().ignoreCase());

        // 如果有分值映射，设置对应的分值
        if (idScoreMap != null && material.getId() != null) {
            Double score = idScoreMap.get(material.getId());
            responseDTO.setScore(score);
        }

        return responseDTO;
    }

    /**
     * 构建MongoDB查询条件
     */
    private Query buildQuery(MaterialQueryDTO queryDTO) {
        Query query = new Query();

        // 素材ID列表查询
        if (CollectionUtils.isNotEmpty(queryDTO.getIds())) {
            query.addCriteria(Criteria.where("id").in(queryDTO.getIds()));
        }

        // 名称模糊查询
        if (StringUtils.isNotBlank(queryDTO.getName())) {
            query.addCriteria(Criteria.where("name").regex(queryDTO.getName(), "i"));
        }

        // 类型查询
        if (queryDTO.getType() != null) {
            query.addCriteria(Criteria.where("type").is(queryDTO.getType()));
        }

        // 标签查询
        if (queryDTO.getTags() != null && !queryDTO.getTags().isEmpty()) {
            query.addCriteria(Criteria.where("tags").in(queryDTO.getTags()));
        }

        // 创建时间范围查询
        if (queryDTO.getCreateTimeStart() != null && queryDTO.getCreateTimeEnd() != null) {
            query.addCriteria(Criteria.where("createTime").gte(queryDTO.getCreateTimeStart()).lte(queryDTO.getCreateTimeEnd()));
        } else if (queryDTO.getCreateTimeStart() != null) {
            query.addCriteria(Criteria.where("createTime").gte(queryDTO.getCreateTimeStart()));
        } else if (queryDTO.getCreateTimeEnd() != null) {
            query.addCriteria(Criteria.where("createTime").lte(queryDTO.getCreateTimeEnd()));
        }

        // 文件大小范围查询
        if (queryDTO.getFileSizeMin() != null && queryDTO.getFileSizeMax() != null) {
            query.addCriteria(Criteria.where("fileSize").gte(queryDTO.getFileSizeMin()).lte(queryDTO.getFileSizeMax()));
        } else if (queryDTO.getFileSizeMin() != null) {
            query.addCriteria(Criteria.where("fileSize").gte(queryDTO.getFileSizeMin()));
        } else if (queryDTO.getFileSizeMax() != null) {
            query.addCriteria(Criteria.where("fileSize").lte(queryDTO.getFileSizeMax()));
        }

        return query;
    }

}
