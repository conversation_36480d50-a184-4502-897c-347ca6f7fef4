package com.nd.aic.service;

import com.nd.aic.entity.CoursewareTask;
import com.nd.aic.repository.CoursewareTaskRepository;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class CoursewareTaskService {

    private final CoursewareTaskRepository coursewareTaskRepository;

    public CoursewareTask queryByTaskId(String taskId) {
        return coursewareTaskRepository.findByTaskId(taskId);
    }

    public void insert(CoursewareTask coursewareTask) {
        coursewareTask.setId(null);
        coursewareTaskRepository.save(coursewareTask);
    }

    public void update(CoursewareTask coursewareTask) {
        coursewareTask.setUpdateTime(System.currentTimeMillis());
        coursewareTaskRepository.save(coursewareTask);
    }
}
