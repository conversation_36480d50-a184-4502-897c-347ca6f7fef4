package com.nd.aic.service;

import com.nd.aic.pojo.cs.TokenParams;
import com.nd.gaea.rest.support.WafContext;
import com.nd.sdp.cs.bean.Policy;
import com.nd.sdp.cs.bean.SessionBean;
import com.nd.sdp.cs.bean.TokenEntity;
import com.nd.sdp.cs.bean.TokenParam;
import com.nd.sdp.cs.common.CsConfig;
import com.nd.sdp.cs.constant.TokenType;
import com.nd.sdp.cs.sdk.Dentry;
import com.nd.sdp.cs.sdk.DentryDirect;
import com.nd.sdp.cs.sdk.ExtendUploadData;
import com.nd.sdp.cs.sdk.Session;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

@Service
public class CsService {

    @Value("${cs.host}")
    private String host;

    @Value("${cs.accessKey}")
    private String accessKey;

    @Value("${cs.secretKey}")
    private String secretKey;

    @Value("${cs.serviceId}")
    private String serviceId;

    @Value("${cs.serviceName}")
    private String serviceName;

    @Value("${cs.expires:3600}")
    private long expires = 0;

    @PostConstruct
    public void postConstruct() {
        CsConfig.setHost(host);
        CsConfig.addTokenKey(serviceName, accessKey, secretKey);
    }

    public SessionBean getSession(Long userId) throws Exception {
        return Session.getSession(serviceName, serviceId, userId, "user", 1800L);
    }

    public Policy getPolicy(String path) throws Exception {
        TokenEntity tokenEntity = new TokenEntity();
        tokenEntity.setTokenType(TokenType.UPLOAD_NORMAL.name());
        tokenEntity.setPath(path);
        long expireAt = System.currentTimeMillis() + (expires * 1000L);
        long accountId = Long.parseLong(StringUtils.defaultIfBlank(WafContext.getCurrentAccountId(), "0"));
        return Policy.getPolicy(tokenEntity, accountId, Policy.ROLE.USER, Policy.SCOPE.PUBLIC, expireAt);
    }

    public Dentry uploadFile(Long userId, String fileName, File file) throws Exception {
        return uploadFile(userId, null, fileName, file);
    }

    public Dentry uploadFile(Long userId, String path, String fileName, File file) throws Exception {
        Dentry dentry = new Dentry();
        if (StringUtils.isNotBlank(path)) {
            dentry.setPath("/" + serviceName + "/upload/" + path);
        } else {
            dentry.setPath("/" + serviceName + "/uploads/");
        }
        dentry.setName(fileName);
        dentry.setScope(1);
        ExtendUploadData extendUploadData = new ExtendUploadData();
        dentry = dentry.upload(serviceName, file, extendUploadData, getSession(userId).getSession(), null);
        return dentry;
    }

    public Map<String, Object> getCsToken(long userId, TokenParams tokenParams) throws Exception {
        CsConfig.setHost(host);
        CsConfig.addTokenKey(serviceName, accessKey, secretKey);

        TokenEntity tokenEntity = new TokenEntity();
        tokenEntity.setTokenType(StringUtils.defaultIfBlank(tokenParams.getTokenType(), TokenType.UPLOAD_NORMAL.name()));
        String path = tokenParams.getPath();
        path = StringUtils.stripStart(path, "/");
        path = StringUtils.removeStart(path, serviceName);
        path = StringUtils.stripStart(path, "/");
        tokenEntity.setPath("/" + serviceName + "/" + StringUtils.defaultIfBlank(path, ""));
        tokenEntity.setDentryId(tokenParams.getDentryId());
        tokenEntity.setParams(tokenParams.getParams());
        long expireAt = tokenParams.getExpireAt() == null ? System.currentTimeMillis() + (expires * 1000L) : tokenParams.getExpireAt();
        Policy policy = Policy.getPolicy(tokenEntity, userId, Policy.ROLE.USER, tokenParams.getScope(), expireAt);
        TokenParam param = DentryDirect.getToken(serviceName, tokenEntity, policy);

        Map<String, Object> result = new HashMap<>(8);
        result.put("service_name", serviceName);
        result.put("path", StringUtils.removeEnd(tokenEntity.getPath(), "/"));
        result.put("date_time", param.getDateTime());
        result.put("token", param.getToken());
        result.put("policy", param.getPolicy());
        result.put("expire_at", param.getExpireAt() == null ? String.valueOf(policy.getExpireAt()) : param.getExpireAt());

        return result;
    }
}
