package com.nd.aic.service;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.Danmu;
import com.nd.aic.repository.DanmuRepository;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Date;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class DanmuService extends BaseService<Danmu, String> implements InitializingBean {

    private final DanmuRepository danmuRepository;

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    protected Module module() {
        return new Module("DAN_MU");
    }

    @Override
    public Danmu add(Danmu danmu) {
        danmu.setCreateAt(new Date());
        return super.add(danmu);
    }
}
