package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.nd.aic.client.FastGptFeignClient;
import com.nd.aic.client.pojo.chat.ChatCompletion;
import com.nd.aic.client.pojo.chat.ChatCompletionResponse;
import com.nd.aic.client.pojo.chat.ChatMessage;
import com.nd.aic.entity.basic.Pronunciation;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.entity.flow.LessonProgram;
import com.nd.aic.pojo.ai.InteractionQuestionReq;
import com.nd.aic.pojo.ai.StylizationScript;
import com.nd.aic.pojo.dto.CoursewareResourceConfigDTO;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.aic.pojo.dto.PlotInteractionDTO;
import com.nd.aic.util.JsonUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class FastGptRetryService {

    private final FastGptFeignClient fastGptFeignClient;

    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public List<String> askForKeyword(String content) {
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(content)));
        ChatCompletionResponse response = fastGptFeignClient.askForKeyword(chatCompletion);
        String data = trimJsonData(response.getChoices().get(0).getMessage().getContent());
        return JSON.parseObject(data, new TypeReference<List<String>>() {
        });
    }

    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public CoursewareResourceConfigDTO askForResourceConfig() {
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of("")));
        Map<String , Object> variables = Maps.newHashMap();
        variables.put("version", "2.0");
        chatCompletion.setVariables(variables);
        ChatCompletionResponse response = fastGptFeignClient.askForResourceConfig(chatCompletion);
        String data = StringUtils.trim(response.getChoices().get(0).getMessage().getContent());
        return JSON.parseObject(data, CoursewareResourceConfigDTO.class);
    }

    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public JSONObject askForPerformanceConfig() {
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of("")));
        ChatCompletionResponse response = fastGptFeignClient.askForPerformanceConfig(chatCompletion);
        String data = StringUtils.trim(response.getChoices().get(0).getMessage().getContent());
        return JSON.parseObject(data, Feature.OrderedField);
    }

    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public JSONArray askForFullScreenTimeTick(String content) {
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(content)));
        ChatCompletionResponse response = fastGptFeignClient.askForFullScreenTimeTick(chatCompletion);
        String data = response.getChoices().get(0).getMessage().getContent();
        return JSON.parseArray(trimJsonData(data));
    }

    /**
     * 停顿标注智能体
     * @param content 发音的内容
     * @return 停顿标注
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public List<Pronunciation> askForTTSPronunciation(String style,  String content) {
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(content)));
        ChatCompletionResponse response = fastGptFeignClient.askForTTSPronunciationAnnotation(chatCompletion);
        String data = response.getChoices().get(0).getMessage().getContent();
        return JSON.parseArray(trimJsonData(data), Pronunciation.class);
    }

    /**
     * 停顿标注智能体
     * @param content 发音的内容
     * @return 停顿标注
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public JSONArray askForPauseAnnotation(String style,  String content) {
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(content)));
        Map<String , Object> variables = Maps.newHashMap();
        variables.put("style", style);
        chatCompletion.setVariables(variables);
        ChatCompletionResponse response = fastGptFeignClient.askForTTSPauseAnnotation(chatCompletion);
        String data = response.getChoices().get(0).getMessage().getContent();
        return JSON.parseArray(trimJsonData(data));
    }


    /**
     * Words Check
     * @param text  input text
     * @return check results
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public JSONObject askForWordsCheck(String text){
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(text)));
        ChatCompletionResponse response = fastGptFeignClient.askForWordsCheck(chatCompletion);
        String data = response.getChoices().get(0).getMessage().getContent();
        return JSON.parseObject(trimJsonData(data));
    }


    /**
     * Ask for story rewrite
     * @param text input text
     * @return story rewrite
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public JSONObject askForStoryRewrite(String text){
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(text)));
        ChatCompletionResponse response = fastGptFeignClient.askForStoryWrite(chatCompletion);
        String data = response.getChoices().get(0).getMessage().getContent();
        return JSON.parseObject(trimJsonData(data));
    }


    /**
     * Ask for language type
     * @param text input text
     * @return language type 1 simplified chinese 2 english
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public String askForLanguageType(String text){
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(text)));
        ChatCompletionResponse response = fastGptFeignClient.askForLanguage(chatCompletion);
        String data = response.getChoices().get(0).getMessage().getContent();
        return trimJsonData(data);
    }

    /**
     * interaction question design
     * @param interactionQuestionReq interaction question request
     * @return interaction question
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public InteractionDTO askForInteractionQuestion(InteractionQuestionReq interactionQuestionReq){
        return askForInteractionQuestion(interactionQuestionReq, null);
    }

    /**
     * interaction question design
     * @param interactionQuestionReq interaction question request
     * @return interaction question
     */
    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public InteractionDTO askForInteractionQuestion(InteractionQuestionReq interactionQuestionReq, String gptKey){
        if (StringUtils.isEmpty(gptKey)) {
            gptKey = "fastgpt-rbZtmhQcAnS5XNmaVVy0x9a3VvLv9AGoi2pME56WZuTX4kA2C77KgLj8n3h4Q";
        }
        Map<String, Object> variables = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(interactionQuestionReq.getDetailList())) {
            String context = interactionQuestionReq.getDetailList().stream().map(AutomatePerformanceDetail::getDialogue).collect(Collectors.joining("\n"));
            variables.put("content", context);
        }
        return fastGptCallForJsonResult(interactionQuestionReq.getDialogue(), gptKey, variables, text -> JsonUtils.parseObject(text, InteractionDTO.class));
    }

    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public List<PlotInteractionDTO> addInteractionQuestion(List<PlotInteractionDTO> dialogues){
        String gptKey = "fastgpt-uPdHZ7kwtDPOXenV8ug16ZzpNnHOnfO8G7aJuxb10SueKCmuDU7sdiU";
        return fastGptCallForJsonResult(JsonUtils.toJson(dialogues), gptKey, null, text -> JsonUtils.parseList(text, PlotInteractionDTO.class));
    }

    private String trimJsonData(String data) {
        data = StringUtils.trim(data);
        data = StringUtils.stripStart(data, "```json");
        data = StringUtils.stripEnd(data, "```");
        return data;
    }

    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public String fastGptCall(String texts, String fastGptKey) {
        return fastGptCall(texts, fastGptKey, null);
    }

    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public String fastGptCall(String texts, String fastGptKey, Map<String, Object> variables) {
        String authorization = "Bearer " + fastGptKey;
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(texts)));
        chatCompletion.setVariables(variables);
        ChatCompletionResponse response = fastGptFeignClient.ask(authorization, chatCompletion);
        texts = response.getChoices().get(0).getMessage().getContent();
        texts = trimJsonData(texts);
        return texts;
    }

    @Retryable(value = {Exception.class}, maxAttempts = 5, backoff = @Backoff(delay = 1000))
    public <T> T fastGptCallForJsonResult(String texts, String fastGptKey, Map<String, Object> variables, Function<String, T> applyFunction) {
        String authorization = "Bearer " + fastGptKey;
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(texts)));
        chatCompletion.setVariables(variables);
        ChatCompletionResponse response = fastGptFeignClient.ask(authorization, chatCompletion);
        texts = response.getChoices().get(0).getMessage().getContent();
        texts = trimJsonData(texts);
        return applyFunction.apply(texts);
    }

    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public JSONObject queryConfigData(String texts, String fastGptKey, Map<String, Object> variables) {
        String authorization = "Bearer " + fastGptKey;
        ChatCompletion chatCompletion = new ChatCompletion();
        chatCompletion.setMessages(Lists.newArrayList(ChatMessage.of(StringUtils.defaultIfBlank(texts, "获取配置"))));
        chatCompletion.setVariables(variables);
        ChatCompletionResponse response = fastGptFeignClient.ask(authorization, chatCompletion);
        texts = response.getChoices().get(0).getMessage().getContent();
        texts = trimJsonData(texts);
        return JSON.parseObject(texts);
    }

    @Retryable(value = {Exception.class})
    public List<StylizationScript.Item> stylize(StylizationScript stylizationScript) {
        if (StringUtils.isEmpty(stylizationScript.getRequirement())) {
            return Lists.newArrayList();
        }
        String authorization = "fastgpt-dXPgKJ1rkq4IuOVUJALmi3lO5PuLWBcnN4eoyJcEUvB2VhmoYA4F";
        // String texts = JsonUtils.toJson(stylizationScript.getDetailList());
        // boolean stylize = stylizationScript.getDetailList().stream().anyMatch(item -> StringUtils.isNotBlank(item.getOriginalScript()));
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("style", stylizationScript.getStyle());
        variables.put("manuscript", stylizationScript.getRequirement());
        variables.put("fun", "风格化");

        Function<String, List<StylizationScript.Item>> applyFunction = data -> {
            if (StringUtils.isBlank(data)) {
                return Lists.newArrayList();
            }
            return JsonUtils.parseList(data, StylizationScript.Item.class);
        };

        return fastGptCallForJsonResult(stylizationScript.getRequirement(), authorization, variables, applyFunction);
    }

}
