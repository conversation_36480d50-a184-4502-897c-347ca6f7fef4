package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.utils.ValidatorUtils;
import com.nd.aic.client.BcsFeignClient;
import com.nd.aic.client.pojo.bcs.LackResourceReq;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.client.pojo.tts.TtsSegmentVo;
import com.nd.aic.pojo.ndr.NdrSearchVO;
import com.nd.aic.util.JsonUtils;
import com.nd.gaea.client.WafResourceAccessException;
import com.nd.ndr.model.NdrResourceSearchParameters;
import com.nd.ndr.service.ResourceService;
import com.nd.sdp.cs.sdk.Dentry;
import com.nd.sdp.uc.bts.common.util.DESUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.validation.Valid;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class GuestService {

    private final Pattern OBJECT_ID_PATTERN = Pattern.compile("^[a-fA-F0-9]{24}$");
    private final NdrSdkService ndrSdkService;
    private final CsService csService;
    private final MaterialResourceGrabService materialResourceGrabService;
    private final BcsFeignClient bcsFeignClient;
    private final AudioResourceService audioResourceService;

    public static String encryptToken(String str) throws Exception {
        return DESUtil.encrypt(str);
    }

    public Object searchNdrResource(NdrSearchVO searchVO) {
        NdrSearchVO.Headers headers;
        try {
            headers = JsonUtils.parseObject(DESUtil.decrypt(searchVO.getAccessToken()), NdrSearchVO.Headers.class);
        } catch (Exception e) {
            throw WafI18NException.of("INVALID_ACCESS_TOKEN", "无效的访问令牌", HttpStatus.BAD_REQUEST, e);
        }
        ValidatorUtils.validateAndThrow(headers);
        ResourceService resourceService = ndrSdkService.getResourceService(headers.getTenantId(), headers.getContainerId(), headers.getAk(), headers.getSk());

        List<String> tags = searchVO.getTags();
        List<String> labels = searchVO.getLabels();
        String keyword = searchVO.getKeyword();

        List<String> filters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tags)) {
            String filter = searchVO.getTags().stream().map(e -> String.format("resource_container.tags eq '%s'", e)).collect(Collectors.joining(" and ", "(", ")"));
            filters.add(filter);
        }
        if (CollectionUtils.isNotEmpty(labels)) {
            String filter = labels.stream().map(e -> String.format("resource.global_label eq '%s'", e)).collect(Collectors.joining(" and ", "(", ")"));
            filters.add(filter);
        }
        if (StringUtils.isNotBlank(keyword)) {
            String filter = String.format("resource.words like '%s'", keyword);
            filters.add(filter);
        }
        String filter = String.join(" and ", filters);
        log.info("search resource with: {}", filter);
        NdrResourceSearchParameters parameters = searchParameterWithFilter(filter);

        return resourceService.searchOnlineResources(headers.getContainerId(), true, parameters);
    }

    private static NdrResourceSearchParameters searchParameterWithFilter(String filter) {
        NdrResourceSearchParameters parameters = new NdrResourceSearchParameters();
        parameters.setOffset(0);
        parameters.setLimit(500);
        parameters.setLanguage("zh-CN");
        parameters.setInclude("TI,TAG");
        parameters.setOrder("resource.create_time desc");
        parameters.setFilter(filter);
        return parameters;
    }

    @SneakyThrows
    public Object uploadFile(List<MultipartFile> multipartFiles) {
        List<Map<String, Object>> dentries = Lists.newArrayList();
        for (MultipartFile multipartFile : multipartFiles) {
            String name = multipartFile.getName();
            File file = File.createTempFile("tmp", ".pptx");
            multipartFile.transferTo(file);
            Dentry dentry = csService.uploadFile(147507L, name, file);
            Map<String, Object> item = Maps.newHashMap();
            item.put("name", name);
            item.put("dentry_id", dentry.getDentryId());
            item.put("file_url", "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId());
            dentries.add(item);
        }
        return dentries;
    }

    public Object query101PptMaterialUrl(String resourceId) {
        return materialResourceGrabService.query101PptMaterialUrl(resourceId);
    }

    public Object submitLackRequirement(String suid, LackResourceReq lackResourceReq) {
        if (StringUtils.isEmpty(lackResourceReq.getId()) || !OBJECT_ID_PATTERN.matcher(lackResourceReq.getId()).matches()) {
            return null;
        }
        try {
            if (StringUtils.isEmpty(lackResourceReq.getTags()) && StringUtils.isNotEmpty(lackResourceReq.getDetails())) {
                lackResourceReq.setTags(lackResourceReq.getDetails());
            }
            return bcsFeignClient.submitLackRequirement(suid, lackResourceReq);
        } catch (WafResourceAccessException e) {
            log.error("submitLackRequirement failed", e);
            throw WafI18NException.of("SUBMIT_LACK_REQUIREMENT_FAILED", e.getMessage(), HttpStatus.BAD_REQUEST, e);
        }
    }

    public Object synthesisAudio(@Valid @RequestBody TtsSegmentVo ttsRequest) {
        TtsSegment ttsSegment = new TtsSegment();
        log.info("dialogueAudio ttsRequest: {}", ttsRequest);
        BeanUtils.copyProperties(ttsRequest, ttsSegment);
        return audioResourceService.mergeTts(ttsSegment);
    }
}
