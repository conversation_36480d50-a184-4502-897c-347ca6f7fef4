package com.nd.aic.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.ImAgentFeignClient;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class ImAgentService {

    private final ImAgentFeignClient imAgentFeignClient;
    private final Cache<String, String> cache = CacheBuilder.newBuilder().maximumSize(100).expireAfterWrite(20, TimeUnit.MINUTES).build();
    @Value("${im.agent.uri:281474976720471}")
    private String imAgentUri;
    @Value("${im.agent.password:4ac77cd3-550c-45d9-850f-a6c4f0aa899c}")
    private String imAgentPassword;

    private String login() {
        JSONObject reqBody = new JSONObject();
        reqBody.put("uri", imAgentUri);
        reqBody.put("password", imAgentPassword);
        JSONObject jsonObject = imAgentFeignClient.login(reqBody);
        return "MAC id=\"" + jsonObject.getString("access_token") + "\",nonce=\"" + jsonObject.getString("nonce") + "\",mac=\"" + jsonObject.getString("mac") + "\"";

    }

    private String getToken() {
        String tokenKey = "token";
        String token = cache.getIfPresent(tokenKey);
        if (token == null) {
            token = login();
            cache.put(tokenKey, token);
        }
        return token;
    }

    public JSONObject send(String reqBody) {
        String token = getToken();
        return imAgentFeignClient.send(token, reqBody);
    }
}
