package com.nd.aic.service;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.IntegratedFlowTaskParticle;
import com.nd.aic.repository.IntegratedFlowTaskParticleRepository;

import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class IntegratedFlowTaskParticleService extends BaseService<IntegratedFlowTaskParticle, String> {

    private final IntegratedFlowTaskParticleRepository integratedFlowTaskParticleRepository;

    @Override
    protected Module module() {
        return new Module("IntegratedFlowTaskParticleService");
    }

    @Override
    public Items<IntegratedFlowTaskParticle> list(ListParam<IntegratedFlowTaskParticle> listParam) {
        return super.list(listParam);
    }

    @Override
    public List<IntegratedFlowTaskParticle> findAll() {
        return integratedFlowTaskParticleRepository.findAll();
    }

    public IntegratedFlowTaskParticle findOne(String id) {
        IntegratedFlowTaskParticle task = super.findOne(id);
        if (task == null) {
            throw module().notFound();
        }
        return task;
    }

    public List<IntegratedFlowTaskParticle> findByTaskId(String taskId) {
        return integratedFlowTaskParticleRepository.findAllByTaskId(taskId);
    }

    public List<IntegratedFlowTaskParticle> deleteByTaskId(String taskId) {
        return integratedFlowTaskParticleRepository.deleteAllByTaskId(taskId);
    }

    @Override
    public IntegratedFlowTaskParticle save(IntegratedFlowTaskParticle integratedFlowTask) {
        if (null == integratedFlowTask.getCreateAt()) {
            integratedFlowTask.setCreateAt(new Date());
        }
        return integratedFlowTaskParticleRepository.save(integratedFlowTask);
    }

}
