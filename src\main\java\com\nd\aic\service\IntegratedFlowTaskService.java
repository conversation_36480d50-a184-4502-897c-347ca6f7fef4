package com.nd.aic.service;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.IntegratedFlowTask;
import com.nd.aic.repository.IntegratedFlowTaskRepository;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class IntegratedFlowTaskService extends BaseService<IntegratedFlowTask, String> {

    private final IntegratedFlowTaskRepository integratedFlowTaskRepository;

    @Override
    protected Module module() {
        return new Module("IntegratedFlowTaskService");
    }

    @Override
    public Items<IntegratedFlowTask> list(ListParam<IntegratedFlowTask> listParam) {
        return super.list(listParam);
    }

    @Override
    public List<IntegratedFlowTask> findAll() {
        List<IntegratedFlowTask> tasks = integratedFlowTaskRepository.findAll();
        Collections.reverse(tasks);
        return tasks;
    }

    public IntegratedFlowTask findOne(String id) {
        IntegratedFlowTask task = super.findOne(id);
        if (task == null) {
            throw module().notFound();
        }
        return task;
    }

    public IntegratedFlowTask deleteOne(String id) {
        IntegratedFlowTask task = integratedFlowTaskRepository.findOne(id);
        if (task != null) {
            integratedFlowTaskRepository.delete(task);
        }
        return task;
    }

    @Override
    public IntegratedFlowTask add(IntegratedFlowTask integratedFlowTask) {
        integratedFlowTask.setCreateAt(new Date());
        return integratedFlowTaskRepository.save(integratedFlowTask);
    }

    @Override
    public IntegratedFlowTask update(IntegratedFlowTask integratedFlowTask) {
        return integratedFlowTaskRepository.save(integratedFlowTask);
    }
}
