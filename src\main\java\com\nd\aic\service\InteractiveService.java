package com.nd.aic.service;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.client.InteractionFeignClient;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.entity.VideoInteraction;
import com.nd.aic.pojo.ai.InteractionQuestionReq;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.aic.pojo.interactive.InteractiveData;
import com.nd.aic.pojo.interactive.InteractiveInput;
import com.nd.aic.pojo.interactive.InteractiveReq;
import com.nd.aic.util.InteractiveDataBuilder;
import com.nd.aic.util.JsonUtils;
import com.nd.ndr.model.ResTechInfoViewModel;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class InteractiveService {

    private static final String AIGC_TENANT_ID = NdrConstant.AIGC_TENANT_ID;
    private static final String AIGC_CONTAINER_ID = NdrConstant.AIGC_COURSEWARE_CONTAINER_ID;
    private static final String INTERACTIVE_GPT_KEY = "fastgpt-lkQWhicEqbET1zVgQF1FyqMT3rxEkIH0SZSfzfJceudODYFi4W2Xb947Ja";

    private final NdrService ndrService;
    private final FastGptRetryService fastGptRetryService;
    private final InteractionFeignClient interactionFeignClient;

    public InteractiveService(NdrService ndrService, FastGptRetryService fastGptRetryService, InteractionFeignClient interactionFeignClient) {
        this.ndrService = ndrService;
        this.fastGptRetryService = fastGptRetryService;
        this.interactionFeignClient = interactionFeignClient;
    }

    public Object generateVideoInteraction(InteractiveReq interactiveReq) {
        final String tenantId = StringUtils.defaultString(interactiveReq.getTenantId(), AIGC_TENANT_ID);
        final String containerId = StringUtils.defaultString(interactiveReq.getContainerId(), AIGC_CONTAINER_ID);

        ResourceMetaTiListViewModel resourceMetaTiListViewModel = ndrService.getResourceMeta(tenantId, containerId, interactiveReq.getResourceId(), null);
        ResTechInfoViewModel sourceResInfo = resourceMetaTiListViewModel.getTiItems().stream().filter(e -> StringUtils.equals("source", e.getTiFileFlag())).findFirst().get();
        String videoUrl = sourceResInfo.getTiStorage();
        videoUrl = videoUrl.replace("cs_path:${ref-path}", "https://cdncs.101.com/v0.1/static");

        InteractionQuestionReq interactionQuestionReq = new InteractionQuestionReq();
        interactionQuestionReq.setDialogue(interactiveReq.getContent());
        InteractionDTO interactionDTO = fastGptRetryService.askForInteractionQuestion(interactionQuestionReq, INTERACTIVE_GPT_KEY);
        interactionDTO.setTriggerTime(interactiveReq.getTimeAnchor());
        InteractiveInput interactiveInput = JsonUtils.parseObject(JsonUtils.toJson(interactionDTO), InteractiveInput.class);
        interactiveInput.setTriggerTime(interactiveReq.getTimeAnchor());
        InteractiveData interactiveData = InteractiveDataBuilder.buildInteractiveData(interactiveInput);
        VideoInteraction interaction = JsonUtils.parseObject(JsonUtils.toJson(interactiveData), VideoInteraction.class);

        String resourceId = interactiveReq.getResourceId();
        interaction.setDefaultMedia(resourceId);
        interaction.setRefResourceId(resourceId);
        VideoInteraction.MediaSource mediaSource = new VideoInteraction.MediaSource();
        mediaSource.setId(resourceId);
        mediaSource.setSrc(videoUrl);
        mediaSource.setNdrTenantId(tenantId);
        mediaSource.setNdrContainerId(containerId);
        interaction.setMediaSources(Lists.newArrayList(mediaSource));
        for (VideoInteraction.ActiveEvent activeEvent : interaction.getActiveEvents()) {
            activeEvent.setMediaTarget(resourceId);
        }

        String id = interaction.getId();
        try {
            interaction = interactionFeignClient.submitInteraction(id, interaction);
        } catch (Exception e) {
            VideoInteraction defaultInteraction = interactionFeignClient.queryInteraction(resourceId, containerId);
            id = defaultInteraction.getId();
            interaction.setId(id);
            interaction.setMediaSources(defaultInteraction.getMediaSources());
            interaction = interactionFeignClient.submitInteraction(id, interaction);
        }
        String viewUrl = String.format("https://ai-courseware-interaction.sdp.101.com/?id=%s#/view", interaction.getRefResourceId());
        JSONObject result = new JSONObject();
        result.put("view_url", viewUrl);
        return result;
    }
}
