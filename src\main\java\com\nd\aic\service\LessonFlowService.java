package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.query.Items;
import com.nd.aic.base.query.ListParam;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.client.AiPptFeignClient;
import com.nd.aic.client.AicFeignClient;
import com.nd.aic.client.BcsFeignClient;
import com.nd.aic.client.pojo.bcs.AutomateLessonForm;
import com.nd.aic.common.BizConstant;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.common.UserHandler;
import com.nd.aic.entity.AutomateLesson;
import com.nd.aic.entity.LessonFlow;
import com.nd.aic.entity.RenderTask;
import com.nd.aic.entity.VideoConcat;
import com.nd.aic.entity.flow.MaterialFile;
import com.nd.aic.entity.lesson_flow.FlowCallbackArguments;
import com.nd.aic.entity.lesson_flow.LessonFlowTeachingActivity;
import com.nd.aic.entity.lesson_flow.MaterialNotes;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.repository.LessonFlowRepository;
import com.nd.aic.util.JsonUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.rest.support.WafContext;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class LessonFlowService extends BaseService<LessonFlow, String> implements InitializingBean {

    private static final int MAX_RETRY_COUNT = 3;
    private final CsService csService;
    private final RenderTaskService renderTaskService;
    private final AicFeignClient aicFeignClient;
    private final NdrService ndrService;
    private final AutomateLessonService automateLessonService;
    private final AiPptFeignClient aiPptFeignClient;
    private final LessonFlowRepository lessonFlowRepository;
    private final FastGptRetryService fastGptRetryService;
    private final VideoConcatService videoConcatService;
    private final BcsFeignClient bcsFeignClient;

    @Value("${ENABLE_VIDEO_CONCAT:true}")
    private Boolean enableVideoConcat;
    @Value("${app.web.bcs.host:https://affair-assemble-web.sdp.101.com/}")
    private String affairAssembleWeb;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化时可以进行一些必要的设置或检查
        log.info("LessonFlowService initialized with repository: {}", lessonFlowRepository.getClass().getName());
    }

    @Override
    protected Module module() {
        return new Module("LESSON_FLOW");
    }

    @Override
    public Items<LessonFlow> list(ListParam<LessonFlow> listParam) {
        // listParam.setExcludeFields(Lists.newArrayList("programs"));
        return super.list(listParam);
    }

    @Override
    public List<LessonFlow> findAll() {
        List<LessonFlow> automateLessons = lessonFlowRepository.findAll();
        Collections.reverse(automateLessons);
        return automateLessons;
    }

    @Override
    public LessonFlow findOne(String id) {
        return super.findOne(id);
    }

    public LessonFlow deleteOne(String id) {
        LessonFlow automateLesson = lessonFlowRepository.findOne(id);
        if (automateLesson != null) {
            lessonFlowRepository.delete(automateLesson);
        }
        return automateLesson;
    }

    @Override
    public LessonFlow add(LessonFlow automateLesson) {
        automateLesson.setId(null);
        automateLesson.setCreateAt(new Date());
        automateLesson.setUpdateAt(new Date());
        automateLesson.setStatus(LessonFlow.STATUS_INIT);
        if (StringUtils.isNotEmpty(WafContext.getCurrentAccountId())) {
            automateLesson.setCreator(WafContext.getCurrentAccountId());
        }
        return lessonFlowRepository.save(automateLesson);
    }

    public LessonFlow update(LessonFlow lessonFlow) {
        if (StringUtils.isEmpty(lessonFlow.getId())) {
            throw module().nullId();
        }
        LessonFlow one = findStrictOne(lessonFlow.getId());
        lessonFlow.setBizId(one.getBizId());
        lessonFlow.setCourseProperties(one.getCourseProperties());
        lessonFlow.setCallbackArguments(one.getCallbackArguments());
        lessonFlow.setVideoConcatId(one.getVideoConcatId());
        lessonFlow.setVideoUrl(one.getVideoUrl());
        lessonFlow.setScriptUrl(one.getScriptUrl());
        lessonFlow.setStatus(one.getStatus());
        lessonFlow.setUpdateAt(new Date());
        return super.update(lessonFlow);
    }

    public LessonFlow submitLessonTask(LessonFlow lessonFlow, Boolean withoutForm) {
        String user = WafContext.getCurrentAccountId();
        log.info("提交课时任务({})：当前用户: {}", lessonFlow.getTitle(), user);
        if (StringUtils.isBlank(user)) {
            throw WafI18NException.of("NOT_FOUND", "用户信息不能为空", HttpStatus.BAD_REQUEST);
        }

        List<LessonFlowTeachingActivity> submittedTeachingActivities = Lists.newArrayList();
        String source = BizConstant.SOURCE_FLOW_PREFIX + BizConstant.AIPPT_BIZ_TYPE_CRAFT;
        for (LessonFlowTeachingActivity teachingActivity : lessonFlow.getTeachingActivities()) {
            AutomateLesson lessonPart = new AutomateLesson();
            lessonPart.setSource(source);
            lessonPart.setCreator(lessonFlow.getCreator());
            lessonPart.setWorkflowInstanceId(lessonFlow.getId());
            lessonPart.setExecutionId(teachingActivity.getId());
            lessonPart.setTitle(teachingActivity.getTeachingStage());
            lessonPart.setTeachingActivityName(teachingActivity.getTeachingActivity2());
            lessonPart.setTeachingActivityId(null);
            lessonPart.getOriginalRequirement().setTeachingPurpose(teachingActivity.getTeachingPurpose());
            lessonPart.getOriginalRequirement().setTeacherIpRequirement(null == lessonFlow.getCourseProperties() ? null : lessonFlow.getCourseProperties().getTeacherIpRequirement());
            lessonPart.setOriginalTeachingDesignType("original_manuscript");
            lessonPart.setContent(teachingActivity.getOriginalScriptContent());
            lessonPart.setMaterialFiles(teachingActivity.getMaterialFiles());
            lessonPart = automateLessonService.add(lessonPart);

            teachingActivity.setAutomateLessonId(lessonPart.getId());
            teachingActivity.setTaskUrl(String.format("%s/#/operation/%s", affairAssembleWeb, lessonPart.getId()));
            teachingActivity.setStatus(LessonFlowTeachingActivity.STATUS_PREPARE);
            submittedTeachingActivities.add(teachingActivity);
        }
        if (BooleanUtils.isNotTrue(withoutForm)) {
            // 调用业务配置生成对应任务的流程表单
            submitLessonTaskForms(lessonFlow, submittedTeachingActivities);
            markLessonFlowStepCompleted(lessonFlow, "拆解教学活动");
        } else {
            log.warn("提交课时任务：{}({})，跳过生成教学活动颗粒表单", lessonFlow.getTitle(), lessonFlow.getId());
        }

        lessonFlow.setStatus(LessonFlow.STATUS_PREPARE);
        return save(lessonFlow);
    }

    private void submitLessonTaskForms(LessonFlow lessonFlow, List<LessonFlowTeachingActivity> submittedTeachingActivities) {
        String currentUser = WafContext.getCurrentAccountId();
        AutomateLessonForm automateLessonForm = new AutomateLessonForm();
        for (LessonFlowTeachingActivity teachingActivity : submittedTeachingActivities) {
            AutomateLessonForm.Activity activity = new AutomateLessonForm.Activity();
            activity.setResourceId(String.format("%s_%s", lessonFlow.getId(), teachingActivity.getAutomateLessonId()));
            activity.setResourceName(teachingActivity.getTeachingStage());
            activity.setSubmitter(Lists.newArrayList(Long.valueOf(currentUser)));
            activity.setOperationsId(teachingActivity.getAutomateLessonId());
            automateLessonForm.getActivities().add(activity);
        }
        automateLessonForm.setRecordingProdOperationsId(lessonFlow.getId());
        automateLessonForm.setRecordingProdBatchId(UUID.randomUUID().toString());
        log.info("提交录课颗粒流程表单: {}", JsonUtils.toJson(automateLessonForm));
        bcsFeignClient.submitLessonActivityForm(UUID.randomUUID().toString(), currentUser, automateLessonForm);
    }

    @SneakyThrows
    public LessonFlow generateTeachingActivities(LessonFlow lessonFlow) {
        log.info("开始生成教学活动颗粒：课时标题: {}, 课时ID: {}, 当前用户: {}", lessonFlow.getTitle(), lessonFlow.getId(), WafContext.getCurrentAccountId());
        if (StringUtils.isBlank(lessonFlow.getContent())) {
            throw WafI18NException.of("NOT_FOUND", "课时内容不能为空", HttpStatus.BAD_REQUEST);
        }
        final String key = "fastgpt-lmT80Memv39vBfKIqvxZhN3Ek2Vp3mCerEag2jIpYBZo3A1T3Fb1UNQi7jYW";
        final Function<String, List<LessonFlowTeachingActivity>> applyFunction = data -> {
            LessonFlow result = JsonUtils.parseObject(data, LessonFlow.class);
            return result.getTeachingActivities();
        };
        // 合并素材
        List<MaterialNotes> materialNotes = lessonFlow.getMaterialNotes();
        if (null == materialNotes) {
            materialNotes = Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(lessonFlow.getPptMaterialNotes())) {
            materialNotes.addAll(lessonFlow.getPptMaterialNotes());
        }
        if (CollectionUtils.isNotEmpty(lessonFlow.getImageMaterialNotes())) {
            materialNotes.addAll(lessonFlow.getImageMaterialNotes());
        }
        if (CollectionUtils.isNotEmpty(lessonFlow.getVideoMaterialNotes())) {
            materialNotes.addAll(lessonFlow.getVideoMaterialNotes());
        }
        materialNotes = materialNotes.stream()
                .filter(materialNote -> StringUtils.isNotBlank(materialNote.getAssociatedMaterial())).distinct()
                .filter(materialNote -> StringUtils.isNotBlank(materialNote.getText()))
                .filter(materialNote -> StringUtils.isNotBlank(materialNote.getType()))
                .collect(Collectors.toList());

        Map<String, MaterialFile> materialFileMap = Maps.newHashMap();
        List<LessonFlowTeachingActivity> teachingActivities = fastGptRetryService.fastGptCallForJsonResult(lessonFlow.getContent(), key, null, applyFunction);
        for (LessonFlowTeachingActivity teachingActivity : teachingActivities) {
            teachingActivity.setMaterialNotes(Lists.newArrayList());
            teachingActivity.setMaterialFiles(Lists.newArrayList());
            for (MaterialNotes materialNote : materialNotes) {
                if (StringUtils.containsAny(teachingActivity.getOriginalScriptContent(), materialNote.getDescription(), materialNote.getName())) {
                    // 将素材批注添加到教学活动颗粒中
                    MaterialFile materialFile = materialFileMap.get(materialNote.getAssociatedMaterial());
                    if (materialFile == null) {
                        // 如果素材文件不存在，则从NDR获取元数据和URL
                        ResourceMetaTiListViewModel meta = ndrService.getResourceMeta(NdrConstant.AIGC_TENANT_ID, NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID, materialNote.getAssociatedMaterial(), null);
                        String url = ndrService.getResourceUrl(NdrConstant.AIGC_TENANT_ID, NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID, materialNote.getAssociatedMaterial(), "source");

                        materialFile = new MaterialFile();
                        materialFile.setId(UUID.randomUUID().toString());
                        materialFile.setName(StringUtils.defaultString(materialNote.getName(), meta.getGlobalTitle().get("zh-CN")));
                        materialFile.setDescription(materialNote.getText());
                        materialFile.setType(materialNote.getType());
                        materialFile.setUrl(StringUtils.defaultString(materialNote.getUrl(), url));
                        materialFileMap.put(materialNote.getAssociatedMaterial(), materialFile);
                    }
                    teachingActivity.getMaterialNotes().add(materialNote);
                    teachingActivity.getMaterialFiles().add(materialFile);
                }
            }
        }
        lessonFlow.setTeachingActivities(teachingActivities);

        return save(lessonFlow);
    }

    // 每分钟触发一次检查
    @SchedulerLock(name = "lessonFlow_scheduleAutomate")
    @Scheduled(fixedDelay = 60000, initialDelay = 60000)
    public void scheduleAutomate() {
        log.info("开始提交自动化任务");
        List<LessonFlow> lessonFlows = lessonFlowRepository.findByStatus(LessonFlow.STATUS_PREPARE);
        if (CollectionUtils.isEmpty(lessonFlows)) {
            log.info("没有需要自动化的课时");
            return;
        }
        for (LessonFlow lessonFlow : lessonFlows) {
            try {
                log.info("提交自动化任务：开始处理课时: {}({})", lessonFlow.getTitle(), lessonFlow.getId());
                for (LessonFlowTeachingActivity teachingActivity : lessonFlow.getTeachingActivities()) {
                    automate(lessonFlow, teachingActivity);
                }
                // 更新课时状态
                lessonFlow.setStatus(LessonFlow.STATUS_AUTOMATING);
                lessonFlow.setUpdateAt(new Date());
                save(lessonFlow);
                log.info("提交自动化任务：课时 {}({}) 处理完成", lessonFlow.getTitle(), lessonFlow.getId());
            } catch (Exception e) {
                log.error("提交自动化任务：处理课时 {}({}) 失败: {}", lessonFlow.getTitle(), lessonFlow.getId(), e.getMessage(), e);
                if (null == lessonFlow.getRetryCount() || lessonFlow.getRetryCount() < MAX_RETRY_COUNT) {
                    // 如果重试次数小于3次，增加重试次数
                    lessonFlow.setRetryCount((lessonFlow.getRetryCount() == null ? 0 : lessonFlow.getRetryCount()) + 1);
                    lessonFlow.setUpdateAt(new Date());
                } else {
                    // 如果重试次数超过3次，标记为失败
                    lessonFlow.setStatus(LessonFlow.STATUS_FAILED);
                    lessonFlow.setUpdateAt(new Date());
                    lessonFlow.setErrMsg(e.getMessage());
                }
                save(lessonFlow);
            }
        }
    }

    private void automate(LessonFlow lessonFlow, LessonFlowTeachingActivity teachingActivity) {
        if (!StringUtils.equals(teachingActivity.getStatus(), LessonFlowTeachingActivity.STATUS_PREPARE)) {
            // 已经存在的教学活动颗粒，跳过
            return;
        }
        AutomateLesson lessonPart = automateLessonService.findStrictOne(teachingActivity.getAutomateLessonId());
        lessonPart = automateLessonService.generatePrograms(lessonPart);
        lessonPart = automateLessonService.submitLessonTask(lessonPart);
        lessonPart = automateLessonService.update(lessonPart);
        teachingActivity.setStatus(LessonFlowTeachingActivity.STATUS_AUTOMATING);
        save(lessonFlow);
    }

    public LessonFlow automateRetry(String lessonFlowId, String teachingActivityId) {
        LessonFlow lessonFlow = findStrictOne(lessonFlowId);
        log.info("重新提交自动化任务：课时标题: {}, 课时ID: {}", lessonFlow.getTitle(), lessonFlow.getId());
        if (CollectionUtils.isEmpty(lessonFlow.getTeachingActivities())) {
            throw WafI18NException.of("NOT_FOUND", "教学活动颗粒为空", HttpStatus.BAD_REQUEST);
        }
        for (LessonFlowTeachingActivity teachingActivity : lessonFlow.getTeachingActivities()) {
            if (StringUtils.equals(teachingActivity.getId(), teachingActivityId)) {
                automateRetry(lessonFlow, teachingActivity);
            }
        }
        // 更新课时状态
        lessonFlow.setStatus(LessonFlow.STATUS_AUTOMATING);
        return save(lessonFlow);
    }

    public LessonFlow automateRetry(LessonFlow lessonFlow, LessonFlowTeachingActivity teachingActivity) {
        automate(lessonFlow, teachingActivity);
        return lessonFlow;
    }

    // 每分钟触发一次检查
    // @SchedulerLock(name = "lessonFlow_scheduleConcatCheck")
    @Scheduled(fixedDelay = 60000, initialDelay = 60000)
    public void scheduleConcat() {
        if (!enableVideoConcat) {
            return;
        }
        log.info("开始检查视频合并任务");
        List<LessonFlow> lessonFlows = lessonFlowRepository.findByStatus(LessonFlow.STATUS_AUTOMATING);
        if (CollectionUtils.isEmpty(lessonFlows)) {
            log.info("没有需要视频合并的课时");
            return;
        }
        for (LessonFlow lessonFlow : lessonFlows) {
            try {
                log.info("视频合并任务：开始处理课时: {}({})", lessonFlow.getTitle(), lessonFlow.getId());
                if (StringUtils.isNotEmpty(lessonFlow.getVideoConcatId())) {
                    // 已经存在的合并任务，跳过
                    log.info("视频合并任务：课时 {}({}) 已经存在合并任务，跳过", lessonFlow.getTitle(), lessonFlow.getId());
                    continue;
                }
                boolean allCompleted = true;
                for (LessonFlowTeachingActivity teachingActivity : lessonFlow.getTeachingActivities()) {
                    boolean completed = teachingActivityConcatCheck(lessonFlow, teachingActivity);
                    if (!completed) {
                        log.warn("视频合并任务：课时 {}({}) 的教学活动颗粒 {}({}) 未完成", lessonFlow.getTitle(), lessonFlow.getId(), teachingActivity.getTeachingStage(), teachingActivity.getId());
                    }
                    allCompleted = allCompleted && completed;
                }
                if (allCompleted) {
                    // 更新课时状态
                    log.info("视频合并任务：课时 {}({}) 的所有教学活动颗粒已完成", lessonFlow.getTitle(), lessonFlow.getId());
                    markLessonFlowStepCompleted(lessonFlow, "生产教学活动");

                    VideoConcat videoConcat = concat(lessonFlow);
                    lessonFlow.setVideoConcatId(videoConcat.getId());
                    lessonFlow.setStatus(LessonFlow.STATUS_CONCAT);
                    save(lessonFlow);
                    log.info("视频合并任务：课时 {}({}) 提交合并任务", lessonFlow.getTitle(), lessonFlow.getId());
                } else {
                    log.warn("视频合并任务：课时 {}({}) 仍有未完成的教学活动颗粒", lessonFlow.getTitle(), lessonFlow.getId());
                }
                log.warn("视频合并任务：结束处理课时 {}({}) ", lessonFlow.getTitle(), lessonFlow.getId());
            } catch (Exception e) {
                log.error("视频合并任务：处理课时 {}({}) 失败: {}", lessonFlow.getTitle(), lessonFlow.getId(), e.getMessage(), e);
            }
        }
    }

    // 检查单个教学活动颗粒的合并状态
    private boolean teachingActivityConcatCheck(LessonFlow lessonFlow, LessonFlowTeachingActivity teachingActivity) {
        if (StringUtils.equals(teachingActivity.getStatus(), LessonFlowTeachingActivity.STATUS_COMPLETED)) {
            // 已经存在的教学活动颗粒，跳过
            return true;
        }
        AutomateLesson lessonPart = automateLessonService.findStrictOne(teachingActivity.getAutomateLessonId());
        lessonPart.getPrograms().forEach(program -> {
            if (StringUtils.isBlank(program.getAutomateCoursewareId())) {
                throw WafI18NException.of("NOT_FOUND", "教学活动颗粒视频未生成", HttpStatus.BAD_REQUEST);
            }
        });
        VideoConcat videoConcat = automateLessonService.concatResult(lessonPart.getId());
        if (videoConcat == null) {
            try {
                videoConcat = automateLessonService.concat(lessonPart, true);
            } catch (WafI18NException e) {
                if (e.getResponseEntity().getBody().getCode().equals("NOT_COMPLETED")) {
                    // 视频合并未完成，继续等待
                    return false;
                } else if (e.getResponseEntity().getBody().getCode().equals("RETRYABLE_FAILED")) {
                    // 可重试的失败，记录日志并继续等待
                    log.warn("教学活动颗粒 {}({}) 视频合并失败，等待重试: {}", teachingActivity.getTeachingStage(), teachingActivity.getId(), e.getMessage());
                    automateRetry(lessonFlow, teachingActivity);
                    return false;

                } else {
                    throw e; // 其他异常抛出
                }
            }
        }
        String finalVideo = videoConcat.getFinalVideo();
        if (StringUtils.isNotBlank(finalVideo)) {
            // 视频合并完成
            lessonPart.setVideoUrl(finalVideo);
            automateLessonService.save(lessonPart);

            teachingActivity.setRenderScriptIds(videoConcat.getRenderScriptIds());
            teachingActivity.setVideoUrl(finalVideo);
            teachingActivity.setStatus(LessonFlowTeachingActivity.STATUS_COMPLETED);
            log.info("教学活动颗粒 {}({}) 视频合并完成，视频地址: {}", teachingActivity.getTeachingStage(), teachingActivity.getId(), finalVideo);
            // TODO 标记业务配置实现操作完成当前流程
            markTeachingActivityFromCompleted(lessonFlow, teachingActivity);
            save(lessonFlow);
            return true;
        } else {
            // 视频合并未完成，继续等待
            return false;
        }
    }

    private void markTeachingActivityFromCompleted(LessonFlow lessonFlow, LessonFlowTeachingActivity teachingActivity) {
        // TODO 与业务配置实现操作完成当前流程
        Map<String, Object> body = Maps.newHashMap();
        body.put("operations_id", teachingActivity.getAutomateLessonId());
        body.put("status", "success");
        body.put("message", "教学活动颗粒已完成");
        log.info("标记教学活动颗粒完成：课时 {}({}) 教学活动 {}({}) 回调数据: {}", lessonFlow.getTitle(), lessonFlow.getId(), teachingActivity.getTeachingStage(), teachingActivity.getId(), JsonUtils.toJson(body));
        bcsFeignClient.markTeachingActivityCompleted(UUID.randomUUID().toString(), lessonFlow.getCreator(), body);
    }

    public VideoConcat concat(LessonFlow lessonFlow) {
        if (CollectionUtils.isEmpty(lessonFlow.getTeachingActivities())) {
            throw WafI18NException.of("NOT_FOUND", "教学活动颗粒为空", HttpStatus.BAD_REQUEST);
        }
        List<String> renderScriptIds = Lists.newArrayList();
        List<String> videoUrls = Lists.newArrayList();
        for (int i = 0; i < lessonFlow.getTeachingActivities().size(); i++) {
            LessonFlowTeachingActivity teachingActivity = lessonFlow.getTeachingActivities().get(i);
            if (StringUtils.isBlank(teachingActivity.getAutomateLessonId())) {
                throw WafI18NException.of("NOT_FOUND", String.format("教学活动颗粒(%d)未完成", i + 1), HttpStatus.BAD_REQUEST);
            }
            AutomateLesson lessonPart = automateLessonService.findOne(teachingActivity.getAutomateLessonId());
            if (lessonPart == null) {
                throw WafI18NException.of("NOT_FOUND", String.format("教学活动颗粒(%d)未生成", i + 1), HttpStatus.BAD_REQUEST);
            }
            if (StringUtils.isEmpty(lessonPart.getVideoUrl())) {
                throw WafI18NException.of("NOT_FOUND", String.format("教学活动颗粒(%d)视频未生成", i + 1), HttpStatus.BAD_REQUEST);
            }
            renderScriptIds.addAll(teachingActivity.getRenderScriptIds());
            videoUrls.add(lessonPart.getVideoUrl());
        }

        VideoConcat videoConcat = new VideoConcat();
        videoConcat.setTitle(lessonFlow.getTitle());
        videoConcat.setCreateAt(new Date());
        videoConcat.setKey("flow_concat:" + lessonFlow.getId());
        // 设置过期时间为1小时后
        videoConcat.setExpireAt(LocalDateTime.now().plusMinutes(60).toDate());
        videoConcat.setRenderScriptIds(renderScriptIds);
        videoConcat.setVideoUrls(videoUrls);
        videoConcat.setStatus(VideoConcat.STATUS_RUNNING);
        log.info("title {}, concat videoConcat:{}", lessonFlow.getTitle(), videoConcat);

        String env = ApplicationContextUtil.getApplicationContext().getEnvironment().getProperty("sdp.env");
        List<CoursewareModel> videoScripts = Lists.newArrayList();
        for (String renderScriptId : renderScriptIds) {
            RenderTask renderTask;
            if (StringUtils.equals(env, "test")) {
                renderTask = aicFeignClient.renderTaskDetail(renderScriptId);
            } else {
                renderTask = renderTaskService.findOne(renderScriptId);
            }
            videoScripts.add(renderTask.getCoursewareModel());
        }
        try {
            if (CollectionUtils.isNotEmpty(videoScripts)) {
                File tmpFile = File.createTempFile("concat_", ".json");
                FileUtils.write(tmpFile, JsonUtils.toJson(videoScripts), "UTF-8", false);
                Dentry dentry = csService.uploadFile(147507L, String.format("_render_script/%s", lessonFlow.getId()), "播放脚本.json", tmpFile);
                lessonFlow.setScriptUrl("https://gcdncs.101.com/v0.1/static/" + dentry.getPath());
            }
        } catch (Exception e) {
            throw WafI18NException.of("FILE_ERROR", "生成录课脚本失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        videoConcat = videoConcatService.addVideoConcat(videoConcat);
        return videoConcat;
    }

    public VideoConcat concatResult(String id) {
        return videoConcatService.findByKey("flow_concat:" + id);
    }

    // 每分钟触发一次检查
    @Scheduled(fixedDelay = 60000, initialDelay = 60000)
    public void scheduleCompleted() {
        if (!enableVideoConcat) {
            return;
        }
        log.info("开始检查已完成的课时");
        List<LessonFlow> lessonFlows = lessonFlowRepository.findByStatus(LessonFlow.STATUS_CONCAT);
        if (CollectionUtils.isEmpty(lessonFlows)) {
            log.info("没有需要检查的已完成课时");
            return;
        }
        for (LessonFlow lessonFlow : lessonFlows) {
            try {
                log.info("检查已完成课时：开始处理课时: {}({})", lessonFlow.getTitle(), lessonFlow.getId());
                VideoConcat videoConcat = concatResult(lessonFlow.getId());
                if (StringUtils.equals(videoConcat.getStatus(), VideoConcat.STATUS_FAILED)) {
                    videoConcatService.processVideoConcat(videoConcat);
                    log.warn("检查已完成课时：课时 {}({}) 的视频合成失败", lessonFlow.getTitle(), lessonFlow.getId());
                    failure(lessonFlow, videoConcat.getErrorMsg());
                    continue;
                }
                if (StringUtils.isNotBlank(videoConcat.getFinalVideo())) {
                    // 更新课时状态
                    lessonFlow.setVideoUrl(videoConcat.getFinalVideo());
                    // lessonFlow.setScriptUrl("https://gcdncs.101.com/v0.1/download?dentryId=5773818f-d203-42fb-aa1a-6b8284eb2504&serviceName=aic_service_scontent&attachment=true");
                    lessonFlow.setStatus(LessonFlow.STATUS_COMPLETED);
                    lessonFlow.setUpdateAt(new Date());

                    markLessonFlowFromCompleted(lessonFlow);

                    save(lessonFlow);
                    log.info("检查已完成课时：课时 {}({}) 已全部完成", lessonFlow.getTitle(), lessonFlow.getId());
                } else {
                    log.warn("检查已完成课时：课时 {}({}) 仍有未完成的教学活动视频缝合任务", lessonFlow.getTitle(), lessonFlow.getId());
                }
                log.info("检查已完成课时：课时 {}({}) 处理完成", lessonFlow.getTitle(), lessonFlow.getId());
            } catch (Exception e) {
                log.error("检查已完成课时：处理课时 {}({}) 失败: {}", lessonFlow.getTitle(), lessonFlow.getId(), e.getMessage(), e);
            }
        }
    }

    private void markLessonFlowTaskCompleted(LessonFlow lessonFlow) {
        // 数据回写课时
        FlowCallbackArguments callbackArguments = lessonFlow.getCallbackArguments();
        if (null != callbackArguments && StringUtils.isNotEmpty(callbackArguments.getDataId())) {
            Map<String, Object> body = Maps.newHashMap();
            body.put("status", 1);
            Object resp = aiPptFeignClient.updateClassHourStatus(callbackArguments.getSdpAppId(),
                    callbackArguments.getSdpBizType(),
                    callbackArguments.getSuid(),
                    callbackArguments.getDataId(),
                    body);
            log.info("回写课时状态：课时 {}({}) 回写结果: {}", lessonFlow.getTitle(), lessonFlow.getId(), JsonUtils.toJson(resp));
        }
    }

    private void markLessonFlowFromCompleted(LessonFlow lessonFlow) {
        // https://uhz6k7e3h6.feishu.cn/docx/EYtzd3zr6oqinJxO7uXcVvxvngh
        Map<String, Object> body = Maps.newHashMap();
        body.put("operations_id", lessonFlow.getId());
        body.put("operations_segment_id", "6083b64c53d94397be0c97328efeae99");
        body.put("status", "success");
        body.put("message", "成功");
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("performance_script", lessonFlow.getScriptUrl());
        // cs_path:${ref-path}/aic_service_scontent/upload/videos/静夜思_1752805854698.mp4
        ext.put("animated_video", StringUtils.replace(lessonFlow.getVideoUrl(), "cs_path:${ref-path}", "https://gcdncs.101.com/v0.1/static"));
        body.put("ext", ext);
        log.info("标记课时流程完成：课时 {}({}) 回调数据: {}", lessonFlow.getTitle(), lessonFlow.getId(), JsonUtils.toJson(body));
        bcsFeignClient.markLessonFlowCompleted(UUID.randomUUID().toString(), UserHandler.getUser("147507"), body);
    }

    public LessonFlow finishStep(LessonFlow lessonFlow, String step) {
        // TODO 与业务配置实现操作完成当前流程
        // Map<String, Object> body = Maps.newHashMap();
        // body.put("step", step);
        // log.info("标记课时流程步骤完成：课时 {}({}) 步骤: {}", lessonFlow.getTitle(), lessonFlow.getId(), step);
        // bcsFeignClient.markLessonFlowStepCompleted(UUID.randomUUID().toString(), UserHandler.getUser("147507"), body);
        return save(lessonFlow);
    }

    private void markLessonFlowStepCompleted(LessonFlow lessonFlow, String completedStep) {
        // operations_segment_id（AI执行工序id枚举）：
        // 拆解教学活动：6083b64c53d9439786ec75d7ae3e7a38
        // 生产教学活动：6083b64c53d94397880e1bfe67f5d88c
        // 组课工具：6083b64c53d94397be0c97328efeae99
        Map<String, String> stepCodeMapping = Maps.newHashMap();
        stepCodeMapping.put("拆解教学活动", "6083b64c53d9439786ec75d7ae3e7a38");
        stepCodeMapping.put("生产教学活动", "6083b64c53d94397880e1bfe67f5d88c");
        stepCodeMapping.put("组课工具", "6083b64c53d94397be0c97328efeae99");
        if (!stepCodeMapping.containsKey(completedStep)) {
            return;
        }
        Map<String, Object> body = Maps.newHashMap();
        body.put("operations_id", lessonFlow.getId());
        body.put("operations_segment_id", stepCodeMapping.get(completedStep));
        body.put("status", "success");
        log.info("标记课时生产线节点完成状态：课时 {}({}) 回调数据: {}", lessonFlow.getTitle(), lessonFlow.getId(), JsonUtils.toJson(body));
        String user = StringUtils.defaultString(lessonFlow.getCreator(), "147507");
        bcsFeignClient.markLessonFlowStepCompleted(UUID.randomUUID().toString(), user, body);
    }

    private void failure(LessonFlow lessonFlow, String errorMsg) {
        lessonFlow.setStatus(LessonFlow.STATUS_FAILED);
        lessonFlow.setUpdateAt(new Date());
        lessonFlow.setErrMsg(errorMsg);
        save(lessonFlow);
    }

    @Override
    public LessonFlow save(LessonFlow lessonFlow) {
        lessonFlow.setUpdateAt(new Date());
        return super.save(lessonFlow);
    }
}
