package com.nd.aic.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.MapType;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.entity.MaterialResource;
import com.nd.aic.repository.MaterialResourceRepository;
import com.nd.gaea.client.http.WafSecurityHttpClient;
import com.nd.gaea.util.WafJsonMapper;
import com.nd.ndr.model.DownloadUrlViewModel;
import com.nd.ndr.model.NdrResourceEx;
import com.nd.ndr.model.NdrResourceSearchParameters;
import com.nd.ndr.model.NdrTagCascadeHierarchyTree;
import com.nd.ndr.model.NdrTagCascadeTree;
import com.nd.ndr.model.NdrTagCascadeTreeInner;
import com.nd.ndr.model.PageData;
import com.nd.ndr.model.TagViewModel;
import com.nd.ndr.model.tree.TreeNode4ChildViewModel;
import com.nd.ndr.sdk.NdrSdk;
import com.nd.ndr.service.ResourceService;
import com.nd.ndr.service.TagService;
import com.nd.ndr.service.TreeNodeService;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.io.File;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class MaterialResourceGrabService {

    private static final String RESOURCE_GRAB_URL = "https://ppt-gateway.sdp.101.com/v0.1/resources/business/actions/search?tenant_id=1&container_id=d9acf421-dd5c-4507-925a-dee440f94c46,7772374c-92a5-4179-bd1d-7b6ae3337e8d";
    private static final String RESOURCE_DOWNLOAD_URL1 = "https://ppt-gateway.sdp.101.com/v0.1/resources/business/actions/resources/{RESOURCE_ID}/resource_url?tenant_id=1&container_id=7772374c-92a5-4179-bd1d-7b6ae3337e8d&ti_file_flag=source";
    private static final String RESOURCE_DOWNLOAD_URL2 = "https://ppt-gateway.sdp.101.com/v0.1/resources/business/actions/resources/{RESOURCE_ID}/resource_url?tenant_id=1&container_id=d9acf421-dd5c-4507-925a-dee440f94c46&ti_file_flag=source";
    private static final String TAG_VIEW_CODE = "K12";
    private static final String TAG_VIEW_TAG = "2036325519";
    private static final List<String> VALID_SUBJECTS = Lists.newArrayList("语文", "数学", "英语", "物理", "化学", "生物", "历史", "地理", "政治"/*, "音乐", "美术", "体育", "思想政治"*/);
    private static final String[] VALID_VERSIONS = new String[]{"统编版", "人民教育出版社", "人教版"};
    private static final String[] INVALID_VERSIONS = new String[]{"人教版全日制聋校实验教材", "义务教育课程标准盲人学校实验教科书", "培智教科书"};

    private final Cache<String, String> cache = CacheBuilder.newBuilder().maximumSize(500).expireAfterWrite(24, TimeUnit.HOURS).build();
    private final NdrSdk ndrSdk;
    private final WafSecurityHttpClient wafSecurityHttpClient = new WafSecurityHttpClient() {
        @Override
        protected HttpHeaders mergerHeaders(URI uri, HttpMethod method, HttpHeaders httpHeaders) {
            httpHeaders.add("sdp-app-id", "0ad3a484-aa38-45eb-ad70-0417317feacd");
            return super.mergerHeaders(uri, method, httpHeaders);
        }
    };
    private final MaterialResourceRepository materialResourceRepository;

    @SneakyThrows
    public Object grabTeachingMaterial() {
        TagService tagService = ndrSdk.getTagService("1", NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID);
        List<TagNode> tagNodes = parseTagNode(tagService, TAG_VIEW_CODE, null);
        File file = new File("tagNodes.json");
        log.info("write tags data to : {}", file.getAbsolutePath());
        FileUtils.writeStringToFile(file, WafJsonMapper.toJson(tagNodes), "UTF-8");
        // log.info("grabTeachingMaterial: {}", tagNodes);
        // traverseTagNodes(tagNodes);
        return tagNodes;
    }

    @SuppressWarnings("unchecked")
    private List<TagNode> parseTagNode(TagService tagService, String tagPath, TagNode parent) {
        NdrTagCascadeTree tagCascadeTree = tagService.getTagCascades(tagPath, null);
        NdrTagCascadeHierarchyTree tree = tagCascadeTree.getHierarchies().get(0);

        List<TagNode> tagNodes = Lists.newArrayList();
        String type = MapUtils.getString(tree.getGlobalHierarchyName(), "zh-CN");
        List<String> hasNextTagPath = (List<String>) MapUtils.getObject(tree.getExt(), "has_next_tag_path",  Lists.newArrayList());
        for (NdrTagCascadeTreeInner child : tree.getChildren()) {
            String name = MapUtils.getString(child.getGlobalTagName(), "zh-CN");
            if ("学科".equals(type) && !VALID_SUBJECTS.contains(name)) {
                continue;
            }
            if ("版本".equals(type) && !StringUtils.containsAny(name, VALID_VERSIONS)) {
                continue;
            }
            if ("版本".equals(type) && StringUtils.containsAny(name, INVALID_VERSIONS)) {
                continue;
            }
            TagNode tagNode = new TagNode();
            tagNode.setId(child.getTagId());
            tagNode.setName(name);
            tagNode.setPath(tagPath + "/" + child.getTagId());
            tagNode.setType(type);
            tagNode.setParentTag(parent);
            tagNodes.add(tagNode);
        }
        for (TagNode tagNode : tagNodes) {
            if (!hasNextTagPath.contains(tagNode.getId())) {
                continue;
            }
            List<TagNode> childNodes = parseTagNode(tagService, tagNode.getPath(), tagNode);
            tagNode.setChildTags(childNodes);
        }
        if (StringUtils.equals("子版本", type) || CollectionUtils.isEmpty(hasNextTagPath)) {
            for (TagNode tagNode : tagNodes) {
                List<String> teachingMaterialTags = Lists.newArrayList(StringUtils.split(tagNode.getPath(), "/"));
                teachingMaterialTags.remove(TAG_VIEW_CODE);
                teachingMaterialTags.add(TAG_VIEW_TAG);
                List<TeachingMaterialNode> teachingMaterialNodes = parseTeachingMaterial(teachingMaterialTags);
                tagNode.setTeachingMaterials(teachingMaterialNodes);
            }
        }
        return tagNodes;
    }

    private List<TeachingMaterialNode> parseTeachingMaterial(List<String> tags) {
        ResourceService resourceService = ndrSdk.getResourceService("1", NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID);
        PageData<NdrResourceEx> teachingMaterials = searchNdrResource(resourceService, tags, "teachingmaterials", "0");
        List<TeachingMaterialNode> teachingMaterialNodes = Lists.newArrayList();
        for (NdrResourceEx item : teachingMaterials.getItems()) {
            TeachingMaterialNode teachingMaterialNode = new TeachingMaterialNode();
            teachingMaterialNode.setId(item.getId());
            teachingMaterialNode.setName(MapUtils.getString(item.getGlobalTitle(), "zh-CN"));
            teachingMaterialNodes.add(teachingMaterialNode);
        }

        for (TeachingMaterialNode teachingMaterialNode : teachingMaterialNodes) {
            TreeNodeService treeNodeService = ndrSdk.getTreeNodeService("1");
            List<TreeNode4ChildViewModel> chapters = treeNodeService.treeNodeQueryChildren("none", teachingMaterialNode.getId(), true);
            if (CollectionUtils.isNotEmpty(chapters)) {
                List<ChapterNode> chapterNodes = parseChapterNodes(null, chapters);
                teachingMaterialNode.setChapters(chapterNodes);
            }
        }
        return teachingMaterialNodes;
    }

    private List<ChapterNode> parseChapterNodes(ChapterNode parentChapterNode, List<TreeNode4ChildViewModel> chapters) {
        List<ChapterNode> chapterNodes = Lists.newArrayList();
        for (TreeNode4ChildViewModel chapter : chapters) {
            ChapterNode chapterNode = new ChapterNode();
            chapterNode.setId(chapter.getId());
            chapterNode.setName(chapter.getTitle());
            chapterNode.setPath(chapter.getNodePath());
            if (null != parentChapterNode) {
                parentChapterNode.getChildChapters().add(chapterNode);
            }
            List<TreeNode4ChildViewModel> childChapterNodes = chapter.getChildNodes();
            if (CollectionUtils.isNotEmpty(childChapterNodes)) {
                parseChapterNodes(chapterNode, childChapterNodes);
            }
            chapterNodes.add(chapterNode);
        }
        return chapterNodes;
    }

    public PageData<NdrResourceEx> searchNdrResource(ResourceService resourceService, List<String> tags, String resType, String containerId) {
        List<String> filters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tags)) {
            String filter = tags.stream().map(e -> String.format("resource_container.tags eq '%s'", e)).collect(Collectors.joining(" and ", "(", ")"));
            filters.add(filter);
        }
        if (StringUtils.isNotBlank(resType)) {
            filters.add(String.format("resource.resource_type_code eq '%s'", resType));
        }
        String filter = String.join(" and ", filters);
        log.info("search resource with: {}", filter);
        NdrResourceSearchParameters parameters = searchParameterWithFilter(filter);

        return resourceService.searchOnlineResources(containerId, true, parameters);
    }

    private static NdrResourceSearchParameters searchParameterWithFilter(String filter) {
        NdrResourceSearchParameters parameters = new NdrResourceSearchParameters();
        parameters.setLimit(500);
        parameters.setOffset(0);
        parameters.setLanguage("zh-CN");
        parameters.setInclude("TI,TAG");
        parameters.setOrder("resource.create_time desc");
        parameters.setFilter(filter);
        return parameters;
    }

    public void traverseTagNodes(TraverseContext traverseContext, List<TagNode> tagNodes) {
        for (TagNode tagNode : tagNodes) {
            if (CollectionUtils.isNotEmpty(tagNode.getChildTags())) {
                traverseTagNodes(traverseContext, tagNode.getChildTags());
            }
            if (CollectionUtils.isNotEmpty(tagNode.getTeachingMaterials())) {
                traverseTeachingMaterials(traverseContext, tagNode, tagNode.getTeachingMaterials());
            }
        }
    }

    private void traverseTeachingMaterials(TraverseContext traverseContext, TagNode tagNode, List<TeachingMaterialNode> teachingMaterials) {
        for (TeachingMaterialNode teachingMaterial : teachingMaterials) {
            if (CollectionUtils.isNotEmpty(teachingMaterial.getChapters())) {
                traverseChapters(traverseContext, tagNode, teachingMaterial, null, teachingMaterial.getChapters());
            }
        }
    }

    private void traverseChapters(TraverseContext traverseContext, TagNode tagNode, TeachingMaterialNode teachingMaterial, ChapterNode parentChapter, List<ChapterNode> chapters) {
        for (ChapterNode chapter : chapters) {
            chapter.setParentChapter(parentChapter);
            if (CollectionUtils.isNotEmpty(chapter.getChildChapters())) {
                traverseChapters(traverseContext, tagNode, teachingMaterial, chapter, chapter.getChildChapters());
            } else {
                traverseContext.getTraverseNodes().add(new TraverseNode(tagNode, teachingMaterial, chapter));
            }
        }
    }

    public void processChapterNodes(TraverseContext traverseContext, TagNode tagNode, TeachingMaterialNode teachingMaterial, ChapterNode chapter) {
        JavaType javaType = WafJsonMapper.getMapper().getTypeFactory().constructParametricType(PageData.class, NdrResourceEx.class);
        // List<String> tags = Lists.newArrayList(StringUtils.split(tagNode.getPath(), "/"));
        // tags.remove(TAG_VIEW_CODE);
        // tags.add(TAG_VIEW_TAG);
        ResourceReq resourceReq = new ResourceReq();
        resourceReq.setChapterPaths(chapter.getPath());
        // resourceReq.setTags(StringUtils.join(tags, " and "));
        int limit = 500;
        int offset = 0;
        int count = 0;
        long time = 0;
        while (offset == 0 || offset < count) {
            if (count > 10000) {
                offset = 0;
                resourceReq.setProp(String.format("resource.create_time gt '%d'", time));
            }
            resourceReq.setOffset(offset);
            resourceReq.setLimit(limit);
            try {
                PageData<NdrResourceEx> pageData = wafSecurityHttpClient.postForObject(RESOURCE_GRAB_URL, resourceReq, javaType);
                time = grabImageResources(tagNode, teachingMaterial, chapter, pageData);
                count = (int) pageData.getCount();
                offset += limit;
            } catch (Exception e) {
                log.error("grabImageResources error", e);
            }
        }
    }

    private long grabImageResources(TagNode tagNode, TeachingMaterialNode teachingMaterial, ChapterNode chapter, PageData<NdrResourceEx> pageData) {
        List<NdrResourceEx> resourceExList = pageData.getItems();
        if (CollectionUtils.isEmpty(resourceExList)) {
            return 0;
        }
        List<String> chapterNames = Lists.newArrayList();
        ChapterNode chapterNode = chapter;
        while (null != chapterNode) {
            chapterNames.add(chapterNode.getName());
            chapterNode = chapterNode.getParentChapter();
        }
        chapterNames = Lists.reverse(chapterNames);

        List<MaterialResource> materialResources = Lists.newArrayList();
        long time = Long.MAX_VALUE;
        for (NdrResourceEx ndrResourceEx : resourceExList) {
            MaterialResource materialResource = new MaterialResource();
            materialResource.setResourceId(ndrResourceEx.getId());
            materialResource.setTitle(MapUtils.getString(ndrResourceEx.getGlobalTitle(), "zh-CN"));
            materialResource.setDescription(MapUtils.getString(ndrResourceEx.getGlobalDescription(), "zh-CN"));
            materialResource.setPeriod(getTagNameByType(tagNode, "学段"));
            materialResource.setGrade(getTagNameByType(tagNode, "年级"));
            materialResource.setSubject(getTagNameByType(tagNode, "学科"));
            materialResource.setVersion(getTagNameByType(tagNode, "版本"));
            materialResource.setSubVersion(getTagNameByType(tagNode, "子版本"));

            materialResource.setTeachingMaterial(teachingMaterial.getName());
            materialResource.setChapters(chapterNames);
            materialResource.setTags(queryTagNames(ndrResourceEx.getTags()));

            materialResource.setWidth(MapUtils.getString(ndrResourceEx.getCustomProperties(), "width"));
            materialResource.setHeight(MapUtils.getString(ndrResourceEx.getCustomProperties(), "height"));
            if (null == materialResource.getWidth() || null == materialResource.getHeight()) {
                String resolution = MapUtils.getString(ndrResourceEx.getCustomProperties(), "resolution");
                if (StringUtils.isNotBlank(resolution) && StringUtils.contains(resolution, "*")) {
                    String[] resolutions = StringUtils.split(resolution, "*");
                    materialResource.setWidth(resolutions[0]);
                    materialResource.setHeight(resolutions[1]);
                }
            }

            materialResources.add(materialResource);
            time = Math.min(ndrResourceEx.getCreateTime().getTime(), time);
        }
        saveMaterialResource(materialResources);
        return time;
    }

    private void saveMaterialResource(List<MaterialResource> materialResources) {
        try {
            materialResourceRepository.save(materialResources);
        } catch (Exception e) {
            log.error("saveMaterialResource error", e);
        }
    }

    private List<String> queryTagNames(List<String> tagIds) {
        List<String> tagNames = Lists.newArrayList();

        tagIds = Lists.newArrayList(tagIds);
        Map<String, String> tagMaps = cache.getAllPresent(tagIds);
        if (null != tagMaps) {
            tagIds.removeAll(tagMaps.keySet());
            tagNames.addAll(tagMaps.values());
        }
        if (CollectionUtils.isNotEmpty(tagIds)) {
            TagService tagService = ndrSdk.getTagService("1", NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID);
            try {
                PageData<TagViewModel> pageData = tagService.getTagBatch(Sets.newHashSet(tagIds), null);
                Map<String, String> tags = pageData.getItems().stream().collect(Collectors.toMap(TagViewModel::getId, item -> MapUtils.getString(item.getGlobalTagName(), "zh-CN")));
                cache.putAll(tags);
            } catch (Exception e) {
                log.error("queryTagNames error", e);
            }
        }
        return tagNames;
    }

    private String getTagNameByType(TagNode tagNode, String type) {
        while (tagNode != null) {
            if (StringUtils.equals(type, tagNode.getType())) {
                return tagNode.getName();
            }
            tagNode = tagNode.getParentTag();
        }
        return "无";
    }

    public Object query101PptMaterialUrl(String resourceId) {
        MapType mapType = WafJsonMapper.getMapper().getTypeFactory().constructMapType(Map.class, String.class, DownloadUrlViewModel.class);
        Map<String, DownloadUrlViewModel> downloadUrlViewModelMap;
        try {
            String url = StringUtils.replace(RESOURCE_DOWNLOAD_URL1, "{RESOURCE_ID}", resourceId);
            downloadUrlViewModelMap = wafSecurityHttpClient.getForObject(url, mapType);
        } catch (RuntimeException e) {
            String url = StringUtils.replace(RESOURCE_DOWNLOAD_URL2, "{RESOURCE_ID}", resourceId);
            downloadUrlViewModelMap = wafSecurityHttpClient.getForObject(url, mapType);
        }
        DownloadUrlViewModel downloadUrlViewModel = downloadUrlViewModelMap.get("source");
        String url = "https://" + downloadUrlViewModel.getHost() + downloadUrlViewModel.getUrl();
        url = StringUtils.replace(url, "gcdncs.101.com", "cdncs.101.com");
        url = StringUtils.substringBefore(url, "?");
        // Map<String, Object> result = Maps.newHashMap();
        // result.put("url", url);
        return url;
    }

    @Getter
    @Setter
    public static class TagNode {
        private String id;
        private String name;
        private String path;
        private String type;
        @JsonBackReference
        private TagNode parentTag;
        @JsonManagedReference
        private List<TagNode> childTags = Lists.newArrayList();
        private List<TeachingMaterialNode> teachingMaterials = Lists.newArrayList();
    }

    @Getter
    @Setter
    public static class TeachingMaterialNode {
        private String id;
        private String name;
        private List<ChapterNode> chapters = Lists.newArrayList();
    }

    @Getter
    @Setter
    public static class ChapterNode {
        private String id;
        private String name;
        private String path;
        @JsonBackReference
        private ChapterNode parentChapter;
        @JsonManagedReference
        private List<ChapterNode> childChapters = Lists.newArrayList();
    }

    @Getter
    @Setter
    public static class ResourceReq {
        private String resourceTypeCodes = "assets_picture";
        private String chapterPaths;
        private String bizOrder = "NEW";
        private String tags;
        private String prop;
        private String include = "TI,TAG,CP,LC_CP,REL";
        private String responseNorms = "1,2,3,4,5";
        private Boolean recordUserWord = false;
        private Boolean handleTm = false;
        private Boolean crossChapter = false;
        private int offset = 0;
        private int limit = 500;
    }

    @Getter
    @Setter
    public static class TraverseContext {
        private Integer traverseIndex;
        private List<TraverseNode> traverseNodes = Lists.newArrayList();
    }

    @Getter
    public static class TraverseNode {
        private final TagNode tagNode;
        private final TeachingMaterialNode teachingMaterial;
        private final ChapterNode chapter;

        public TraverseNode(TagNode tagNode, TeachingMaterialNode teachingMaterial, ChapterNode chapter) {
            this.tagNode = tagNode;
            this.teachingMaterial = teachingMaterial;
            this.chapter = chapter;
        }
    }
}
