package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.pojo.audio.BGMMapping;
import com.nd.aic.util.TimeUtils;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.SystemUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MediaService implements InitializingBean {

    private static final Pattern DURATION_PATTERN = Pattern.compile("Duration: (\\d{2}:\\d{2}:\\d{2}.\\d{2})");

    public static File TEMP_DIRECTORY = null;

    public static File BGM_DIRECTORY = null;

    public static String FFMPEG_PATH = null;

    private final CsService csService;

    public MediaService(CsService csService) {
        this.csService = csService;
    }

    @Override
    public void afterPropertiesSet() throws IOException, InterruptedException {
        FFMPEG_PATH = Objects.requireNonNull(this.getClass().getResource("/")).getPath() + "ffmpeg" + (SystemUtils.IS_OS_WINDOWS ? "_win" : "_linux");
        File root = new File(Objects.requireNonNull(this.getClass().getResource("/")).getPath());
        TEMP_DIRECTORY = new File(root, "temp");
        TEMP_DIRECTORY.mkdirs();
        BGM_DIRECTORY = new File(root, "bgm");
        BGM_DIRECTORY.mkdirs();

        if (!SystemUtils.IS_OS_WINDOWS) {
            addFfmpegPermission();
        }
    }

    public File getTempDirectory() {
        return TEMP_DIRECTORY;
    }

    public File getBgmDirectory() {
        return BGM_DIRECTORY;
    }

    private void addFfmpegPermission() throws InterruptedException, IOException {
        String command = MessageFormat.format(" chmod +x {0}/ffmpeg ", FFMPEG_PATH);
        executeCommand(command);
    }

    @SneakyThrows
    public File createSilent(float seconds) {
        String silentSeconds = String.format("%.2f", seconds);
        File silentFile = FileUtils.getFile(TEMP_DIRECTORY, String.format("silent_%s.mp3", silentSeconds));
        if (silentFile.length() == 0) {
            // 构建一个指定静音时间的空白音频
            String command = MessageFormat.format("{0}/ffmpeg -f lavfi -t {1} -i anullsrc=r=16000:cl=mono -ar 24000 -c:a libmp3lame -b:a 160k {2}", FFMPEG_PATH, silentSeconds, silentFile.getAbsolutePath());
            executeCommand(command);
            log.info("silent file: {}, length: {}", silentFile.length(), silentFile.length());
        }
        return silentFile;
    }

    @SneakyThrows
    public File joinSilent(File mp3File, float seconds) {
        return joinSilent(mp3File, seconds, false);
    }

    @SneakyThrows
    public File joinSilent(File mp3File, float seconds, boolean addAfter) {
        // String silentSeconds = String.format("%.1f", seconds);
        File silentFile = createSilent(seconds);

        List<File> filelist = Lists.newArrayList();
        if (!addAfter) {
            filelist.add(silentFile);
        }
        filelist.add(mp3File);
        if (addAfter) {
            filelist.add(silentFile);
        }
        return concatMp3(filelist);

    }

    @SneakyThrows
    public long durationMills(File file) {
        String command = MessageFormat.format("{0}/ffmpeg -i {1} -f null /dev/null", FFMPEG_PATH, file.getAbsolutePath());
        Process p = Runtime.getRuntime().exec(command);
        String result = readStreamResult(p.getInputStream());
        if (StringUtils.isEmpty(result)) {
            result = readStreamResult(p.getErrorStream());
        } else {
            readStreamResult(p.getErrorStream());
        }
        p.waitFor();
        p.destroy();

        Matcher matcher = DURATION_PATTERN.matcher(result);
        if (matcher.find()) {
            return convertMills(matcher.group(1));
        }
        return 0;
    }

    /**
     * 获取视频时长（秒）
     */
    private double durationSec(File file) {
        long ms = durationMills(file);
        return ms / 1000.0;
    }


    private static long convertMills(String time) {
        // 拆分时间字符串
        String[] parts = time.split(":");
        String hoursPart = parts[0];
        String minutesPart = parts[1];
        String secondsPart = parts[2];

        // 进一步拆分秒和毫秒部分
        String[] secondsParts = secondsPart.split("\\.");
        String seconds = secondsParts[0];
        String millisecondsPart = secondsParts[1];

        // 解析成整数
        int hours = Integer.parseInt(hoursPart);
        int minutes = Integer.parseInt(minutesPart);
        int secs = Integer.parseInt(seconds);
        int millis = Integer.parseInt(millisecondsPart);

        long millisecond = millis * 10L;
        millisecond += secs * 1000L;
        millisecond += (long) minutes * 60 * 1000;
        millisecond += (long) hours * 60 * 60 * 1000;
        return millisecond;
    }

    @SneakyThrows
    public File concatMp3(List<File> mp3List) {
        File fileList = File.createTempFile("mp3list_", ".txt", TEMP_DIRECTORY);
        String fileListContent = mp3List.stream().map(e -> String.format("file '%s'", e.getAbsolutePath())).collect(Collectors.joining("\n"));
        log.info("merge mp3 filelist:{}\n{}", fileList.getAbsolutePath(), fileListContent);
        FileUtils.write(fileList, fileListContent);

        File outputFile = File.createTempFile("concat_", ".mp3", TEMP_DIRECTORY);

        String command = MessageFormat.format("{0}/ffmpeg -f concat -safe 0 -i {1}  -c:a libmp3lame -b:a 160k -y {2}", FFMPEG_PATH, fileList.getAbsolutePath(), outputFile.getAbsolutePath());
        executeCommand(command, true);
        FileUtils.deleteQuietly(fileList);
        return outputFile;
    }

    public String mp4ToMp3(String dentryId) throws Exception {
        String mediaUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryId;
        long time = System.currentTimeMillis();
        File file = download(mediaUrl, String.format("%d.mp4", time));
        File mp3File = encodeToMp3(file, String.format("%d.mp3", time));
        Dentry dentry = csService.uploadFile(0L, String.format("%d.mp3", time), mp3File);
        try {
            return "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId();
        } finally {
            //清理本地文件
            FileUtils.deleteQuietly(file);
            FileUtils.deleteQuietly(mp3File);
        }
    }

    public File download(String downloadUrl, String fileName) throws IOException {
        return download(downloadUrl, fileName, false);
    }

    public File download(String downloadUrl, String fileName, boolean cacheAble) throws IOException {
        URL url = new URL(downloadUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        InputStream inputStream = conn.getInputStream();
        String realFileName = extractFileName(fileName);
        File mediaFile = FileUtils.getFile(TEMP_DIRECTORY, realFileName);
        if (cacheAble && mediaFile.length() > 0) {
            return mediaFile;
        }
        FileUtils.forceMkdir(mediaFile.getParentFile());
        // mediaFile.createNewFile();
        File tempFile = File.createTempFile("dl_tmp_", "." + getCleanExtension(realFileName), TEMP_DIRECTORY);
        FileOutputStream fos = new FileOutputStream(tempFile);
        boolean rename;
        try {
            IOUtils.copy(inputStream, fos);
        } catch (Exception e) {
            FileUtils.deleteQuietly(tempFile);
            throw e;
        } finally {
            IOUtils.close(conn);
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(fos);
            rename = tempFile.renameTo(mediaFile);
        }
        log.info("download file: {}, size: {}, rename: {}", mediaFile.getAbsolutePath(), FileUtils.byteCountToDisplaySize(mediaFile.length()), rename);
        return rename ? mediaFile : tempFile;
    }


    /**
     * 从可能包含URL参数的字符串中提取纯净的文件扩展名
     * @param originalName 原始文件名（可能含URL参数）
     * @return 正确的小写扩展名（如"jpg"），无扩展名时返回空字符串
     */
    public String getCleanExtension(String originalName) {

        if (StringUtils.isEmpty(originalName)) {
            return "";
        }

        // 分离URL参数和锚点
        String baseName = originalName.split("[?#]")[0];

        // 处理多层目录结构
        baseName = baseName.substring(baseName.lastIndexOf('/') + 1);

        // 获取标准扩展名
        String extension = FilenameUtils.getExtension(baseName);

        return extension.toLowerCase();
    }

    /**
     * 从可能含URL参数的字符串中提取完整文件名
     * @param fileName 原始URL（可能含参数和路径）
     * @return 纯净的文件名（如"4321b61d.jpg"），无法识别时返回空字符串
     */
    public String extractFileName(String fileName) {
        // 步骤1：判断URL是否为空
        if (StringUtils.isBlank(fileName)) {
            return UUID.randomUUID().toString();
        }

        // 步骤1：剥离URL参数和锚点
        String path = fileName.split("[?#]")[0];

        // 步骤2：提取路径末端文件名
        int lastSlashIndex = path.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            return path;
        }

        String extractName = path.substring(lastSlashIndex + 1);

        // 步骤3：处理空文件名情况（如URL以/结尾）
        return extractName.isEmpty() ? "" : extractName;
    }

    private File encodeToMp3(File source, String target) throws InterruptedException, IOException {
        File encodeFile = FileUtils.getFile(TEMP_DIRECTORY, target);

        String command = MessageFormat.format("{0}/ffmpeg -i {1} -f mp3 -acodec libmp3lame -ar 16000 -y {2}", FFMPEG_PATH, source.getAbsolutePath(), encodeFile.getAbsolutePath());
        executeCommand(command);

        return encodeFile;
    }

    private static void executeCommand(String command) throws IOException, InterruptedException {
        executeCommand(command, false);
    }

    private static void executeCommand(String command, final boolean print) throws IOException, InterruptedException {
        Process p = Runtime.getRuntime().exec(command);
        try {
            // 关闭进程的输入流，防止死锁
            if (p.getOutputStream() != null) {
                p.getOutputStream().close();
            }

            // 使用 CompletableFuture 并行处理流读取和进程等待
            CompletableFuture<Void> outputFuture = CompletableFuture.runAsync(() -> readStreamSync(p.getInputStream(), print));
            CompletableFuture<Void> errorFuture = CompletableFuture.runAsync(() -> readStreamSync(p.getErrorStream(), print));
            CompletableFuture<Integer> processFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return p.waitFor();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Process wait interrupted", e);
                }
            });

            // 并行等待所有任务完成
            CompletableFuture.allOf(outputFuture, errorFuture, processFuture).join();

            // 获取进程退出码
            int exitCode;
            try {
                exitCode = processFuture.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw e;
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof RuntimeException && cause.getCause() instanceof InterruptedException) {
                    throw (InterruptedException) cause.getCause();
                }
                throw new RuntimeException("Command execution error", cause);
            }

            // 如果进程执行失败，抛出异常
            if (exitCode != 0) {
                throw new RuntimeException("Command execution failed with exit code: " + exitCode);
            }
        } finally {
            IOUtils.closeQuietly(p.getInputStream());
            IOUtils.closeQuietly(p.getErrorStream());
            // 确保进程被销毁
            if (p.isAlive()) {
                p.destroyForcibly();
            }
        }
    }

    public static void readStream(InputStream ins) {
        readStream(ins, false);
    }

    public static void readStream(InputStream ins, final boolean print) {
        new Thread(() -> {
            BufferedReader br = new BufferedReader(new InputStreamReader(ins));
            StringBuilder builder = new StringBuilder();
            try {
                String line;
                while ((line = br.readLine()) != null) {
                    // System.out.println(line);
                    if (builder.length() > 0) {
                        builder.append("\n");
                    }
                    builder.append(line);
                }
                if (print && builder.length() > 0) {
                    log.info("ffmpeg command info: {}", builder);
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                IOUtils.closeQuietly(ins);
            }
        }).start();
    }

    private static void readStreamSync(InputStream ins, final boolean print) {
        try (BufferedReader br = new BufferedReader(new InputStreamReader(ins, StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                if (print) {
                    log.info(line);
                }
            }
        } catch (IOException e) {
            log.error("读取流时发生错误: {}", e.getMessage(), e);
        }
    }

    @SneakyThrows
    public String readStreamResult(InputStream ins) {
        StringBuilder builder = new StringBuilder();

        final CountDownLatch countDownLatch = new CountDownLatch(1);
        new Thread(() -> {
            BufferedReader br = new BufferedReader(new InputStreamReader(ins));
            try {
                String line;
                while ((line = br.readLine()) != null) {
                    // System.out.println(line);
                    builder.append(line).append("\n");
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                IOUtils.closeQuietly(ins);
                countDownLatch.countDown();
            }
        }).start();
        countDownLatch.await(10, TimeUnit.SECONDS);
        return builder.toString();
    }

    public File processBgm(File bgm) throws IOException, InterruptedException {
        File output = new File(TEMP_DIRECTORY, "bgm_" + bgm.getName());
        if (output.exists() && output.length() > 0) {
            return output;
        }
        File tempFile = File.createTempFile("tmp_bgm_", ".mp3", TEMP_DIRECTORY);
        String command = MessageFormat.format("{0}/ffmpeg -i {1}  -af 'loudnorm=I=-38:LRA=11' -c:a libmp3lame -b:a 160k -y {2}", FFMPEG_PATH, bgm.getAbsolutePath(), tempFile.getAbsolutePath());
        executeCommand(command);
        boolean rename = tempFile.renameTo(output);

        return rename ? output : tempFile;
    }

    public File addBgm(File audio, File bgm, boolean dryVoice) throws IOException, InterruptedException {
        if (null == bgm) {
            bgm = new File(Objects.requireNonNull(getClass().getClassLoader().getResource("bgm/bgm_common1.mp3")).getFile());
        }
        // ffmpeg -i audio.mp3 -i 05.mp3 -filter_complex "[1]aloop=loop=-1:size=2e+09,adelay=10000|10000,volume=0.5[bg];[0][bg]amix=inputs=2:duration=first:dropout_transition=3" -c:a libmp3lame -b:a 160k -y output_with_bg.mp3
        // ffmpeg -i audio.mp3 -i 05.mp3 -filter_complex "[1]aloop=loop=-1:size=2e+09,volume=0.5[a];[0][a]amix=inputs=2:duration=first:dropout_transition=3" -c:a libmp3lame -q:a 2 output_with_bg.mp3
        File output;
        String command;
        if (dryVoice) {
            output = new File(TEMP_DIRECTORY, "raw_" + audio.getName());
            command = MessageFormat.format("{0}/ffmpeg -i {1} -i {2} -filter_complex '[0]volume=2.0[a];[1]aloop=loop=-1:size=2e+09,volume=0[bg];[a][bg]amix=inputs=2:duration=first:dropout_transition=1' -c:a libmp3lame -b:a 160k -y {3}", FFMPEG_PATH, audio.getAbsolutePath(), bgm.getAbsolutePath(), output.getAbsolutePath());
        } else {
            output = new File(TEMP_DIRECTORY, "bgm_" + audio.getName());
            if (BGMMapping.FIXED_BGM) {
                command = MessageFormat.format("{0}/ffmpeg -i {1} -i {2} -filter_complex '[0]volume=2.0[a];[1]aloop=loop=-1:size=2e+09,volume=2[bg];[a][bg]amix=inputs=2:duration=first:dropout_transition=1' -c:a libmp3lame -b:a 160k -y {3}", FFMPEG_PATH, audio.getAbsolutePath(), bgm.getAbsolutePath(), output.getAbsolutePath());
            } else {
                command = MessageFormat.format("{0}/ffmpeg -i {1} -i {2} -filter_complex '[0]volume=2.0[a];[1]aloop=loop=-1:size=2e+09,volume=0.25[bg];[a][bg]amix=inputs=2:duration=first:dropout_transition=1' -c:a libmp3lame -b:a 160k -y {3}", FFMPEG_PATH, audio.getAbsolutePath(), bgm.getAbsolutePath(), output.getAbsolutePath());
            }
        }
        try {
            executeCommand(command, true);
        } catch (RuntimeException e) {
            log.info("bgm process failed: {}", e.getMessage());
            return audio;
        }

        if (!output.exists() || output.length() == 0) {
            log.info("bgm process failed, try again");
            return audio;
        }

        return output;
    }

    public File convertBgm(File file) throws IOException, InterruptedException {
        return convertAudio(getBgmDirectory(), file, null);
    }

    public File convertAudio(File dir, File file, File output) throws IOException, InterruptedException {
        if (null == output) {
            output = FileUtils.getFile(dir, file.getName());
        }

        String command = MessageFormat.format("{0}/ffmpeg -i {1} -vn -c:a libmp3lame -ac 1 -ar 24000 -b:a 160k -y {2}", FFMPEG_PATH, file.getAbsolutePath(), output.getAbsolutePath());
        executeCommand(command);
        return output;
    }

    public File image2Video(File file) throws IOException, InterruptedException {
        File mp4File = File.createTempFile("i2v_", ".mp4", TEMP_DIRECTORY);
        FileUtils.deleteQuietly(mp4File);
        BufferedImage image = ImageIO.read(file);
        int width = 3840;
        int height = 2160;
        if (null != image) {
            width = image.getWidth() / 2 * 2 ;
            height = image.getHeight() / 2 * 2;
        }
        String scale = String.format("%dx%d", width, height);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        String command = MessageFormat.format("{0}/ffmpeg -loop 1 -i {1} -t 1 -r 30 -c:v libx264 -pix_fmt yuv420p -vf scale={2}:flags=lanczos -y {3}", FFMPEG_PATH, file.getAbsolutePath(), scale, mp4File.getAbsolutePath());
        executeCommand(command);

        stopWatch.stop();
        log.info("image2video take {} ms", stopWatch.getTime());

        return mp4File;
    }

    public File cutAudio(File input, int startMs, int endMs) throws IOException, InterruptedException {
        File outFile = File.createTempFile("cut_", ".mp3", TEMP_DIRECTORY);
        String ss = TimeUtils.formatMilliseconds(startMs);
        String to = TimeUtils.formatMilliseconds(endMs);
        String command = MessageFormat.format("{0}/ffmpeg -ss {2} -to {3} -i {1} -ac 1 -ar 24000 -c:a libmp3lame -b:a 160k -y {4}", FFMPEG_PATH, input.getAbsolutePath(), ss, to, outFile.getAbsolutePath());
        executeCommand(command);
        return outFile;
    }

    public File mp32wav(File mp3) throws IOException, InterruptedException {
        File wav = new File(mp3.getParentFile(), mp3.getName().toLowerCase().replace(".mp3", ".wav"));
        String command = MessageFormat.format("{0}/ffmpeg -i {1} -acodec pcm_s16le -ac 1 -ar 24000 -b:a 160k -y {2}", FFMPEG_PATH, mp3.getAbsolutePath(), wav.getAbsolutePath());
        executeCommand(command);
        return wav;
    }

    public File image2Video(File file, long duration, int l2r, int t2b, boolean zoom) throws IOException, InterruptedException {
        log.info("image2video file: {}, duration: {}, l2r: {}, t2b: {}, zoom: {}", file.getAbsolutePath(), duration, l2r, t2b, zoom);
        BufferedImage image = ImageIO.read(file);
        image = resizeImageIfNeed(image, file);
        int width = image.getWidth();
        int height = image.getHeight();
        File mp4File = File.createTempFile("i2v_", ".mp4", TEMP_DIRECTORY);
        FileUtils.deleteQuietly(mp4File);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        float durationSec = duration / 1000f;
        int fps = 25;
        int frameCount = (int) (durationSec * fps);

        float fadeInterval = Math.max(1f, Math.min(2.5f, durationSec / 8));
        Map<String, Object> params = Maps.newHashMap();
        params.put("ffmpegPath", FFMPEG_PATH);
        params.put("inputFile", file.getAbsolutePath());

        params.put("frameCount", frameCount);
        params.put("duration", durationSec);
        params.put("fadeInterval", fadeInterval);
        params.put("fadeOutStart", durationSec - fadeInterval);
        // params.put("zoom", zoom);
        params.put("outputFile", mp4File.getAbsolutePath());
        params.put("zoom", String.format("min(1.2, zoom+%.6f)", .2f / frameCount));

        String command;
        if (zoom) {
            params.put("xAnim", "floor(iw/2-(iw/zoom/2))");
            params.put("yAnim", "floor(ih/2-(ih/zoom/2))");
            command = "{ffmpegPath}/ffmpeg -loop 1 -i {inputFile} -vf zoompan=z='{zoom}':x='{xAnim}':y='{yAnim}':d={frameCount}:fps=25,format=yuv420p,fade=in:st=0:d={fadeInterval},fade=out:st={fadeOutStart}:d={fadeInterval} -t {duration} -c:v libx264 -pix_fmt yuv420p -y {outputFile}";
        } else {
            String l2rAnim = animDirection(frameCount, l2r, (int) (width * 1.0));
            String t2bAnim = animDirection(frameCount, t2b, (int) (height * 1.0));
            params.put("xAnim", l2rAnim);
            params.put("yAnim", t2bAnim);
            command = "{ffmpegPath}/ffmpeg -loop 1 -i {inputFile} -vf zoompan=z='2':x='{xAnim}':y='{yAnim}':d={frameCount}:fps=25,format=yuv420p,fade=in:st=0:d={fadeInterval},fade=out:st={fadeOutStart}:d={fadeInterval} -t {duration} -c:v libx264 -pix_fmt yuv420p -y {outputFile}";
        }
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            command = command.replace("{" + entry.getKey() + "}", String.valueOf(entry.getValue()));
        }

        executeCommand(command, true);

        stopWatch.stop();
        log.info("image2video take {}ms", stopWatch.getTime());

        return mp4File;
    }

    private String animDirection(int frameCount, int value, int range) {
        float delta = Math.max(range * 1.0f / frameCount, 1.f);
        if (value > 0) {
            return String.format("min(%.1f*on,%d)", delta, range);
        } else if (value < 0) {
            return String.format("max(%d-%.1f*on,0)", range, delta);
        } else {
            return String.valueOf(range / 2);
        }
    }

    private static BufferedImage resizeImageIfNeed(BufferedImage originalImage, File outputFile) throws IOException {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        if (width % 2 == 0 || height % 2 == 0) {
            return originalImage;
        }
        int newWidth = originalImage.getWidth() / 2 * 2;
        int newHeight = originalImage.getHeight() / 2 * 2;

        // 3. 创建新图片并绘制调整后的内容
        BufferedImage resizedImage = new BufferedImage(
                newWidth, newHeight, originalImage.getType()
        );
        Graphics2D g2d = resizedImage.createGraphics();
        g2d.setRenderingHint(
                RenderingHints.KEY_INTERPOLATION,
                RenderingHints.VALUE_INTERPOLATION_BILINEAR
        );
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        // 4. 写回原文件（或另存为新文件）
        ImageIO.write(resizedImage, "jpg", outputFile);
        return resizedImage;
    }

    @SneakyThrows
    public File concatMp4(List<File> mp4File) {
        File fileList = File.createTempFile("mp4list_", ".txt", TEMP_DIRECTORY);
        String fileListContent = mp4File.stream().map(e -> String.format("file '%s'", e.getAbsolutePath())).collect(Collectors.joining("\n"));
        log.info("concatMp4 filelist:{}\n{}", fileList.getAbsolutePath(), fileListContent);
        FileUtils.write(fileList, fileListContent);

        File outputFile = File.createTempFile("concat_", ".mp4", TEMP_DIRECTORY);

        String command = MessageFormat.format("{0}/ffmpeg -f concat -safe 0 -i {1} -c:v libx264 -c:a aac -movflags +faststart -y {2}", FFMPEG_PATH, fileList.getAbsolutePath(), outputFile.getAbsolutePath());
        executeCommand(command, true);
        FileUtils.deleteQuietly(fileList);
        return outputFile;
    }

    /**
     * 多段mp4拼接，自动插入不重复的转场，音视频同步，重叠区间转场
     *
     * @param mp4Files              输入视频列表
     * @param transitionDurationSec 转场重叠时长（秒）
     * @return 合成后的视频文件
     */
    @SneakyThrows
    public synchronized File concatMp4WithTransitions(List<File> mp4Files, double transitionDurationSec) {
        if (CollectionUtils.isEmpty(mp4Files)) {
            return null;
        }
        if (mp4Files.size() < 2) {
            return mp4Files.get(0);
        }
        log.info("concatMp4WithTransitions mp4Files size:{}", mp4Files.size());
        List<String> transitions = Lists.newArrayList("fade", "wipeleft", "wiperight", "wipeup", "wipedown", "slideleft", "slideright", "slideup", "slidedown", "circlecrop", "rectcrop", "distance", "fadeblack", "fadewhite", "radial", "smoothleft", "smoothright", "smoothup", "smoothdown", "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose", "dissolve", "pixelize", "diagtl", "diagtr", "diagbl", "diagbr", "hlslice", "hrslice", "vuslice", "vdslice");
        Collections.shuffle(transitions);
        int transitionIdx = 0;
        List<File> segments = Lists.newArrayList();
        List<File> tempFiles = Lists.newArrayList();
        File result;
        try {
            for (int i = 0; i < mp4Files.size(); i++) {
                File current = mp4Files.get(i);
                double duration = durationSec(current);
                File main;
                if (i == 0) {
                    // 第一个视频，去掉尾部重叠
                    main = cutVideo(current, 0, duration - transitionDurationSec);
                } else if (i == mp4Files.size() - 1) {
                    // 最后一个视频，去掉头部重叠
                    main = cutVideo(current, transitionDurationSec, duration);
                } else {
                    // 中间视频，去掉头尾重叠
                    main = cutVideo(current, transitionDurationSec, duration - transitionDurationSec);
                }
                segments.add(main);
                tempFiles.add(main);
                // 生成转场片段（除了最后一个）
                if (i < mp4Files.size() - 1) {
                    File firstOverlap = cutVideo(current, duration - transitionDurationSec, duration);
                    File secondOverlap = cutVideo(mp4Files.get(i + 1), 0, transitionDurationSec);
                    tempFiles.add(firstOverlap);
                    tempFiles.add(secondOverlap);
                    String transition = transitions.get(transitionIdx++ % transitions.size());
                    File transitionClip = makeTransition(firstOverlap, secondOverlap, transition, transitionDurationSec);
                    segments.add(transitionClip);
                    tempFiles.add(transitionClip);
                }
            }
            result = concatMp4(segments);
            return result;
        } finally {
            for (File f : tempFiles) {
                if (f != null && f.exists()) {
                    FileUtils.deleteQuietly(f);
                }
            }
        }
    }

    /**
     * 裁剪视频片段
     */
    @SneakyThrows
    private File cutVideo(File input, double startSec, double endSec) {
        File outFile = File.createTempFile("cut_", ".mp4", TEMP_DIRECTORY);
        String command = MessageFormat.format("{0}/ffmpeg -ss {1} -to {2} -i {3} -c copy -y {4}", FFMPEG_PATH, String.format("%.3f", startSec), String.format("%.3f", endSec), input.getAbsolutePath(), outFile.getAbsolutePath());
        executeCommand(command);
        return outFile;
    }

    /**
     * 生成转场片段，音视频同步
     */
    @SneakyThrows
    private File makeTransition(File v1, File v2, String transition, double duration) {
        File outFile = File.createTempFile("transition_", ".mp4", TEMP_DIRECTORY);
        // 只保留第二个片段的音频
        // String command = MessageFormat.format("{0}/ffmpeg -i {1} -i {2} -filter_complex " + "[0:v][1:v]xfade=transition={3}:duration={4}:offset=0[v];[1:a]anull[a]" + " -map '[v]' -map '[a]' -y {5}", FFMPEG_PATH, v1.getAbsolutePath(), v2.getAbsolutePath(), transition, String.format("%.2f", duration), outFile.getAbsolutePath());
        String command = MessageFormat.format("{0}/ffmpeg -threads 1 -i {1} -i {2} -filter_complex " + "[0:v][1:v]xfade=transition={3}:duration={4}:offset=0[v];[1:a]anull[a]" + " -map '[v]' -map '[a]' -y {5}", FFMPEG_PATH, v1.getAbsolutePath(), v2.getAbsolutePath(), transition, String.format("%.2f", duration), outFile.getAbsolutePath());
        executeCommand(command);
        return outFile;
    }
}
