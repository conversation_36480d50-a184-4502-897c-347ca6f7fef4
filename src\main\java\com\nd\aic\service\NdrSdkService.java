package com.nd.aic.service;

import com.alibaba.fastjson.JSONObject;
import com.nd.ndr.config.NdrConfig;
import com.nd.ndr.config.TokenKey;
import com.nd.ndr.model.NdrTempToken;
import com.nd.ndr.sdk.NdrSdk;
import com.nd.ndr.service.ResourceService;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * NDR接口封装
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NdrSdkService {

    private final NdrSdk ndrSdk;

    public NdrSdkService(NdrSdk ndrSdk) {
        this.ndrSdk = ndrSdk;
    }


    /**
     * 获取access token,,跳转到ndr页面用
     *
     * @return Object
     */
    public Object getNdrConfig(String tenantId, String containerId) {
        TokenKey tokenKey = NdrConfig.getTokenKeyByTenantId(tenantId);
        JSONObject result = new JSONObject();
        result.put("tenant_id", tenantId);
        result.put("container_id", containerId);
        result.put("ak", tokenKey.getAccessKey());
        result.put("sk", tokenKey.getSecretKey());
        result.put("ndr_host", NdrConfig.getNdrUrl());
        return result;
    }

    /**
     * 获取access token,,跳转到ndr页面用
     *
     * @param tenantId    租户参数
     * @param containerId 容器id
     * @return token
     */
    public NdrTempToken getAccessToken(String tenantId, String containerId) {
        TokenKey tokenKey = NdrConfig.getTokenKeyByTenantId(tenantId);
        ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId, tokenKey.getAccessKey(), tokenKey.getSecretKey());
        return resourceService.refreshTempToken();
    }

    public ResourceService getResourceService(String tenantId, String containerId, String ak, String sk) {
        return ndrSdk.getResourceService(tenantId, containerId, ak, sk);
    }

    public static String getNdrToken(String ak, String sk, String tenant, String method, String path) {
        long time = System.currentTimeMillis();
        String signContent = time + tenant + path + method;
        try {
            byte[] hmac = HmacUtils.hmacSha1(sk, signContent);
            String signResult = Base64.encodeBase64URLSafeString(hmac);
            return time + ";" + ak + ";" + signResult;
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

}