package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.common.NdrConstant;
import com.nd.aic.common.UserHandler;
import com.nd.aic.exception.AicException;
import com.nd.aic.util.Iso8601Utils;
import com.nd.aic.util.TimeUtils;
import com.nd.ndr.model.CsTokenParam;
import com.nd.ndr.model.CsTokenViewModel;
import com.nd.ndr.model.DownloadUrlViewModel;
import com.nd.ndr.model.NdrResourceEx;
import com.nd.ndr.model.NdrResourceSearchParameters;
import com.nd.ndr.model.PageData;
import com.nd.ndr.model.ResTechInfoViewModel;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.ndr.model.UploadUrlViewModel;
import com.nd.ndr.sdk.NdrSdk;
import com.nd.ndr.service.ResourceService;
import com.nd.sdp.cs.bean.TokenInfo;
import com.nd.sdp.cs.core.CSClient;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class NdrService {

    private final NdrSdk ndrSdk;

    /**
     * 上传AI课件音频到NDR
     */
    public ResourceMetaTiListViewModel createAudioMeta(String title, Long duration, File mixingAudio, String lrcContent, String klrcContent) {
        return createAudioMeta(title, duration, mixingAudio, mixingAudio, lrcContent, klrcContent);
    }

    /**
     * 上传AI课件音频到NDR
     */
    public ResourceMetaTiListViewModel createAudioMeta(String title, Long duration, File mixingAudio, File dryAudio, String lrcContent, String klrcContent) {
        String currentAccountId = currentAccountId();

        String resourceId = UUID.randomUUID().toString();
        String resourceTypeCode = NdrConstant.KARAOKE_RESOURCE_TYPE;

        ResourceService resourceService = ndrSdk.getResourceService(NdrConstant.KARAOKE_TENANT_ID, NdrConstant.KARAOKE_COURSEWARE_CONTAINER_ID);
        ResourceMetaTiListViewModel meta = new ResourceMetaTiListViewModel();
        // 必填项
        meta.setId(resourceId);
        meta.setResourceTypeCode(resourceTypeCode);
        // 标题
        Map<String, String> globalTitle = Maps.newHashMap();
        globalTitle.put(Locale.CHINA.toLanguageTag(), title);
        meta.setGlobalTitle(globalTitle);
        meta.setCreator(currentAccountId);
        meta.setLanguage(Locale.CHINA.toLanguageTag());
        // ---end 必填项
        meta.setStatus("ONLINE");
        // tags
        meta.setTags(Lists.newArrayList(NdrConstant.KARAOKE_COURSEWARE_TAG, "Mx00189", "**********"));
        // custom_properties
        if (duration != null) {
            Map<String, Object> customProperties = Maps.newHashMap();
            customProperties.put(NdrConstant.SONG_DURATION_PROP, TimeUtils.formatMilliseconds4Duration(duration));
            meta.setCustomProperties(customProperties);
        }

        // tiItems
        List<ResTechInfoViewModel> tiItems = Lists.newArrayList();
        UploadUrlViewModel uploadUrlViewModel = resourceService.getResourceUploadUrl(resourceId, resourceTypeCode);
        Dentry audioDentry = uploadFileToCs(resourceService, uploadUrlViewModel, mixingAudio, "audio.mp3");
        tiItems.add(createTechInfoFromDentry(audioDentry, "source"));
        Dentry dryDentry = uploadFileToCs(resourceService, uploadUrlViewModel, dryAudio, "dry_sound.mp3");
        tiItems.add(createTechInfoFromDentry(dryDentry, "dry_sound"));
        if (StringUtils.isNotBlank(lrcContent)) {
            Dentry lrcDentry = uploadFileToCs(resourceService, uploadUrlViewModel, lrcContent, "lrc.txt");
            tiItems.add(createTechInfoFromDentry(lrcDentry, "line_lyrics"));
        }
        if (StringUtils.isNotBlank(klrcContent)) {
            Dentry klrcDentry = uploadFileToCs(resourceService, uploadUrlViewModel, klrcContent, "klrc.txt");
            tiItems.add(createTechInfoFromDentry(klrcDentry, "klrc"));
        }
        meta.setTiItems(tiItems);

        meta = resourceService.createResourceMeta(false, meta);
        return meta;
    }

    public ResourceMetaTiListViewModel createBGMMeta(String title, Long duration, File mixingAudio, boolean useInAudioSegment) {
        String currentAccountId = currentAccountId();

        String resourceId = UUID.randomUUID().toString();
        String resourceTypeCode = NdrConstant.KARAOKE_RESOURCE_TYPE;

        ResourceService resourceService = ndrSdk.getResourceService(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID);
        ResourceMetaTiListViewModel meta = new ResourceMetaTiListViewModel();
        // 必填项
        meta.setId(resourceId);
        meta.setResourceTypeCode(resourceTypeCode);
        // 标题
        Map<String, String> globalTitle = Maps.newHashMap();
        globalTitle.put(Locale.CHINA.toLanguageTag(), title);
        meta.setGlobalTitle(globalTitle);
        meta.setCreator(currentAccountId);
        meta.setLanguage(Locale.CHINA.toLanguageTag());
        // ---end 必填项
        meta.setStatus("ONLINE");
        // tags
        // FIXME 暂时不加AUDIO相关标签  BGM($COURSEWARE:AI课件，Mx00189:BGM，**********:不限版本)； AUDIO(Mx00024:音效，Zylx00002:音频，**********:不限版本)
        if (useInAudioSegment) {
            meta.setTags(Lists.newArrayList("**********", "Mx00024"/*, "Zylx00002"*/));
        } else {
            meta.setTags(Lists.newArrayList(NdrConstant.KARAOKE_COURSEWARE_TAG, "Mx00189", "**********"/*, "Mx00024", "Zylx00002"*/));
        }
        // custom_properties
        if (duration != null) {
            Map<String, Object> customProperties = Maps.newHashMap();
            customProperties.put(NdrConstant.SONG_DURATION_PROP, TimeUtils.formatMilliseconds4Duration(duration));
            meta.setCustomProperties(customProperties);
        }

        // tiItems
        List<ResTechInfoViewModel> tiItems = Lists.newArrayList();
        UploadUrlViewModel uploadUrlViewModel = resourceService.getResourceUploadUrl(resourceId, resourceTypeCode);
        Dentry audioDentry = uploadFileToCs(resourceService, uploadUrlViewModel, mixingAudio, "audio.wav");
        tiItems.add(createTechInfoFromDentry(audioDentry, "source"));
        // Dentry dryDentry = uploadFileToCs(resourceService, uploadUrlViewModel, dryAudio, "dry_sound.mp3");
        // tiItems.add(createTechInfoFromDentry(dryDentry, "dry_sound"));
        // Dentry lrcDentry = uploadFileToCs(resourceService, uploadUrlViewModel, lrcContent, "lrc.txt");
        // tiItems.add(createTechInfoFromDentry(lrcDentry, "line_lyrics"));
        // Dentry klrcDentry = uploadFileToCs(resourceService, uploadUrlViewModel, klrcContent, "klrc.txt");
        // tiItems.add(createTechInfoFromDentry(klrcDentry, "klrc"));
        meta.setTiItems(tiItems);

        meta = resourceService.createResourceMeta(false, meta);
        return meta;
    }

    public ResourceMetaTiListViewModel createVideoMeta(String title, File videoFile, Long duration) {
        return createVideoMeta(title, videoFile, null, duration, NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID);
    }

    public ResourceMetaTiListViewModel createVideoMeta(String title, File videoFile, Dentry srcDentry, Long duration, String tenantId, String containerId) {
        String currentAccountId = currentAccountId();

        String resourceId = UUID.randomUUID().toString();
        String resourceTypeCode = "assets_video";

        ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId);
        ResourceMetaTiListViewModel meta = new ResourceMetaTiListViewModel();
        // 必填项
        meta.setId(resourceId);
        meta.setResourceTypeCode(resourceTypeCode);
        meta.setCreator(currentAccountId);
        String language = Locale.CHINA.toLanguageTag();
        meta.setLanguage(language);
        // 标题
        Map<String, String> globalTitle = Maps.newHashMap();
        globalTitle.put(language, title);
        meta.setGlobalTitle(globalTitle);
        // ---end 必填项
        meta.setStatus("ONLINE");
        // tags
        meta.setTags(Lists.newArrayList("**********", "Zylx00004", "**********", "**********", "Bs00001"));
        // custom properties
        // Map<String, Object> customProperties = Maps.newHashMap();
        // customProperties.put("duration", Iso8601Utils.millisecondsToIso8601(duration));
        // meta.setCustomProperties(customProperties);
        // 资源标注
        if (duration != null) {
            Map<String, List<String>> globalLabels = Maps.newHashMap();
            globalLabels.put(language, Lists.newArrayList(String.format("DURATION:%d", duration)));
            meta.setGlobalLabel(globalLabels);
        }

        // tiItems
        List<ResTechInfoViewModel> tiItems = Lists.newArrayList();
        UploadUrlViewModel uploadUrlViewModel = resourceService.getResourceUploadUrl(resourceId, resourceTypeCode);
        Dentry videoDentry;
        boolean useUrl = false;
        if (null != srcDentry) {
            useUrl = true;
            videoDentry = srcDentry;
        } else {
            videoDentry = uploadFileToCs(resourceService, uploadUrlViewModel, videoFile, String.format("video.%s", FilenameUtils.getExtension(videoFile.getName())));
        }
        tiItems.add(createTechInfoFromDentry(videoDentry, "source", null, null, useUrl));
        meta.setTiItems(tiItems);

        meta = resourceService.createResourceMeta(false, meta);
        // meta.getCustomProperties().put("duration", Iso8601Utils.millisecondsToIso8601(duration));
        // meta = resourceService.updateResourceMeta(resourceId, meta);
        // resourceService.updateResourceStatus(resourceId, "AUDIT_PASS", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        // resourceService.updateResourceStatus(resourceId, "ONLINE", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        return meta;
    }

    public ResourceMetaTiListViewModel createFrontVideoMeta(String title, File videoFile, File frontFile, Long duration) {
        String currentAccountId = currentAccountId();

        String resourceId = UUID.randomUUID().toString();
        String resourceTypeCode = "assets_3dsemifinished";

        ResourceService resourceService = ndrSdk.getResourceService(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID);
        ResourceMetaTiListViewModel meta = new ResourceMetaTiListViewModel();
        // 必填项
        meta.setId(resourceId);
        meta.setResourceTypeCode(resourceTypeCode);
        meta.setCreator(currentAccountId);
        String language = Locale.CHINA.toLanguageTag();
        meta.setLanguage(language);
        // 标题
        Map<String, String> globalTitle = Maps.newHashMap();
        globalTitle.put(language, title);
        meta.setGlobalTitle(globalTitle);
        // ---end 必填项
        meta.setStatus("ONLINE");
        // tags  参考资源95d64582-558d-4912-a318-c5dbf339d230的标签
        meta.setTags(Lists.newArrayList("**********", "**********", "Zdy00003", "RT008", "ZYK", "**********", "**********"));
        // 资源标注
        List<String> labels = Lists.newArrayList("云端Cook资源", "自动打包");
        if (duration != null) {
            labels.add(String.format("DURATION:%d", duration));
        }
        Map<String, List<String>> globalLabels = Maps.newHashMap();
        globalLabels.put(language, labels);
        meta.setGlobalLabel(globalLabels);

        // tiItems
        List<ResTechInfoViewModel> tiItems = Lists.newArrayList();
        UploadUrlViewModel uploadUrlViewModel = resourceService.getResourceUploadUrl(resourceId, resourceTypeCode);
        Dentry videoDentry = uploadFileToCs(resourceService, uploadUrlViewModel, videoFile, "video.mp4");
        tiItems.add(createTechInfoFromDentry(videoDentry, "source"));
        Dentry frontDentry = uploadFileToCs(resourceService, uploadUrlViewModel, frontFile, "video.FrontVideo");
        tiItems.add(createTechInfoFromDentry(frontDentry, "pc_UE503", "default", "frontvideo"));
        meta.setTiItems(tiItems);

        meta = resourceService.createResourceMeta(true, meta);
        // meta.getCustomProperties().put("duration", Iso8601Utils.millisecondsToIso8601(duration));
        // meta = resourceService.updateResourceMeta(resourceId, meta);
        // resourceService.updateResourceStatus(resourceId, "AUDIT_PASS", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        // resourceService.updateResourceStatus(resourceId, "ONLINE", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        return meta;
    }

    public ResourceMetaTiListViewModel createImgSeqMeta(String title, File imageZipFile, File frontImgSeqFile) {
        String currentAccountId = currentAccountId();

        String resourceId = UUID.randomUUID().toString();
        String resourceTypeCode = "3d_assets_lib";

        ResourceService resourceService = ndrSdk.getResourceService(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID);
        ResourceMetaTiListViewModel meta = new ResourceMetaTiListViewModel();
        String language = Locale.CHINA.toLanguageTag();
        // 必填项
        meta.setId(resourceId);
        meta.setResourceTypeCode(resourceTypeCode);
        meta.setCreator(currentAccountId);
        meta.setLanguage(language);
        // 标题
        Map<String, String> globalTitle = Maps.newHashMap();
        globalTitle.put(language, title);
        meta.setGlobalTitle(globalTitle);
        // ---end 必填项
        meta.setStatus("ONLINE");
        // tags  参考资源95d64582-558d-4912-a318-c5dbf339d230的标签
        meta.setTags(Lists.newArrayList("**********", "RT022", "Mx00078", "Zylx00001", "**********", "ZYK", "**********"));
        // 资源标注
        Map<String, List<String>> globalLabels = Maps.newHashMap();
        globalLabels.put(language, Lists.newArrayList("自动打包"));
        meta.setGlobalLabel(globalLabels);

        // tiItems
        List<ResTechInfoViewModel> tiItems = Lists.newArrayList();
        UploadUrlViewModel uploadUrlViewModel = resourceService.getResourceUploadUrl(resourceId, resourceTypeCode);
        Dentry videoDentry = uploadFileToCs(resourceService, uploadUrlViewModel, imageZipFile, String.format("%s.zip", title));
        tiItems.add(createTechInfoFromDentry(videoDentry, "source"));
        Dentry frontDentry = uploadFileToCs(resourceService, uploadUrlViewModel, frontImgSeqFile, String.format("%s.FrontImgSeq", title));
        tiItems.add(createTechInfoFromDentry(frontDentry, "pc_Unlimited", null, "frontimgseq"));
        tiItems.add(createTechInfoFromDentry(frontDentry, "pc_UE503", null, "frontimgseq"));
        meta.setTiItems(tiItems);

        meta = resourceService.createResourceMeta(true, meta);
        // meta.getCustomProperties().put("duration", Iso8601Utils.millisecondsToIso8601(duration));
        // meta = resourceService.updateResourceMeta(resourceId, meta);
        // resourceService.updateResourceStatus(resourceId, "AUDIT_PASS", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        // resourceService.updateResourceStatus(resourceId, "ONLINE", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        return meta;
    }

    /**
     * 创建字幕资源元数据
     */
    public ResourceMetaTiListViewModel createSubtitleMeta(String title, String assContent) {
        String currentAccountId = currentAccountId();

        String resourceId = UUID.randomUUID().toString();
        String resourceTypeCode = NdrConstant.ASSETS_RESOURCE_TYPE;

        ResourceService resourceService = ndrSdk.getResourceService(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID);
        ResourceMetaTiListViewModel meta = new ResourceMetaTiListViewModel();
        // 必填项
        meta.setId(resourceId);
        meta.setResourceTypeCode(resourceTypeCode);
        // 标题
        Map<String, String> globalTitle = Maps.newHashMap();
        globalTitle.put(Locale.CHINA.toLanguageTag(), title);
        meta.setGlobalTitle(globalTitle);
        meta.setCreator(currentAccountId);
        meta.setLanguage(Locale.CHINA.toLanguageTag());
        // ---end 必填项
        meta.setStatus("ONLINE");
        // tags
        meta.setTags(Lists.newArrayList(NdrConstant.KARAOKE_COURSEWARE_TAG, NdrConstant.VERSION_UNLIMITED_TAG, "Mx00209"));

        // tiItems
        List<ResTechInfoViewModel> tiItems = Lists.newArrayList();
        UploadUrlViewModel uploadUrlViewModel = resourceService.getResourceUploadUrl(resourceId, resourceTypeCode);
        Dentry assDentry = uploadFileToCs(resourceService, uploadUrlViewModel, assContent, "subtitle.ass");
        tiItems.add(createTechInfoFromDentry(assDentry, "source"));
        meta.setTiItems(tiItems);

        meta = resourceService.createResourceMeta(false, meta);
        return meta;
    }


    @SneakyThrows
    public static String getUrlFromModel(DownloadUrlViewModel downloadUrlViewModel) {
        if (downloadUrlViewModel == null) {
            return null;
        }
        return "https://" + downloadUrlViewModel.getHost() + URLDecoder.decode(downloadUrlViewModel.getUrl(), "UTF-8");
    }

    public Long getResourceDuration(String tenantId, String containerId, String resourceId) {
        ResourceMetaTiListViewModel resourceMeta = getResourceMeta(tenantId, containerId, resourceId, null);
        return Iso8601Utils.parseResourceDuration(resourceMeta.getGlobalLabel().get("zh-CN"), MapUtils.getString(resourceMeta.getCustomProperties(), "duration"));
    }

    public ResourceMetaTiListViewModel getResourceMeta(String tenantId, String containerId, String resourceId, List<String> include) {
        if (include == null) {
            include = Lists.newArrayList("TI", "CP");
        }
        ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId);
        return resourceService.getResourceMeta(resourceId, null, null, include, null);
    }

    public Map<String, DownloadUrlViewModel> getResourceUrlModel(String tenantId, String containerId, String resourceId, String tiFileFlag) {
        ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId);
        return resourceService.getResourceUrl(resourceId, tiFileFlag, null, false, null, null, false, false);
    }

    public String getResourceUrl(String tenantId, String containerId, String resourceId, String tiFileFlag) {
        Map<String, DownloadUrlViewModel> urlViewModelMap = getResourceUrlModel(tenantId, containerId, resourceId, tiFileFlag);
        DownloadUrlViewModel downloadUrlViewModel = urlViewModelMap.get(tiFileFlag);
        if (downloadUrlViewModel != null) {
            String url = "https://" + downloadUrlViewModel.getHost() + downloadUrlViewModel.getUrl();
            url = StringUtils.replace(url, "gcdncs.101.com", "cdncs.101.com");
            url = StringUtils.substringBefore(url, "?");
            return url;
        }
        return null;
    }

    public ResTechInfoViewModel createTechInfoFromDentry(Dentry dentry, String flag) {
        return createTechInfoFromDentry(dentry, flag, null, null, false);
    }

    public ResTechInfoViewModel createTechInfoFromDentry(Dentry dentry, String flag, String lcTiFormat, String tiFormat) {
        return createTechInfoFromDentry(dentry, flag, lcTiFormat, tiFormat, false);
    }

    public ResTechInfoViewModel createTechInfoFromDentry(Dentry dentry, String flag, String lcTiFormat, String tiFormat, boolean useUrl) {
        ResTechInfoViewModel item = new ResTechInfoViewModel();
        item.setTiFileFlag(flag);
        item.setTiIsSourceFile(StringUtils.equals(flag, "source"));
        if (useUrl) {
            item.setTiStorage("url:" + String.format("https://cdncs.101.com/v0.1/static/%s", dentry.getPath()));
        } else {
            item.setTiStorage("cs_path:${ref-path}" + dentry.getPath());
        }
        item.setTiSize(dentry.getInode().getSize());
        item.setTiMd5(StringUtils.defaultIfBlank(dentry.getInode().getMd5(), null));
        if (StringUtils.isNotBlank(lcTiFormat)) {
            item.setLcTiFormat(lcTiFormat);
        }
        if (StringUtils.isNotBlank(tiFormat)) {
            item.setTiFormat(tiFormat);
        }
        return item;
    }

    public ResourceMetaTiListViewModel createImageMeta(String title, File imageFile, Dentry srcDentry, String tenantId, String containerId) {
        String currentAccountId = currentAccountId();

        String resourceId = UUID.randomUUID().toString();
        String resourceTypeCode = "assets_picture";

        ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId);
        ResourceMetaTiListViewModel meta = new ResourceMetaTiListViewModel();
        // 必填项
        meta.setId(resourceId);
        meta.setResourceTypeCode(resourceTypeCode);
        meta.setCreator(currentAccountId);
        String language = Locale.CHINA.toLanguageTag();
        meta.setLanguage(language);
        // 标题
        Map<String, String> globalTitle = Maps.newHashMap();
        globalTitle.put(language, title);
        meta.setGlobalTitle(globalTitle);
        // ---end 必填项
        meta.setStatus("ONLINE");
        // tags
        // 贴纸
        // 图片
        // 不限版本
        meta.setTags(Lists.newArrayList("Mx00163", "Zylx00003", "**********"));

        // tiItems
        List<ResTechInfoViewModel> tiItems = Lists.newArrayList();
        UploadUrlViewModel uploadUrlViewModel = resourceService.getResourceUploadUrl(resourceId, resourceTypeCode);
        Dentry videoDentry;
        boolean useUrl = false;
        if (null != srcDentry) {
            useUrl = true;
            videoDentry = srcDentry;
        } else {
            videoDentry = uploadFileToCs(resourceService, uploadUrlViewModel, imageFile, String.format("video.%s", FilenameUtils.getExtension(imageFile.getName())));
        }
        tiItems.add(createTechInfoFromDentry(videoDentry, "source", null, null, useUrl));
        meta.setTiItems(tiItems);

        meta = resourceService.createResourceMeta(false, meta);
        // meta.getCustomProperties().put("duration", Iso8601Utils.millisecondsToIso8601(duration));
        // meta = resourceService.updateResourceMeta(resourceId, meta);
        // resourceService.updateResourceStatus(resourceId, "AUDIT_PASS", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        // resourceService.updateResourceStatus(resourceId, "ONLINE", Long.valueOf(currentAccountId), "更新资源时长审核通过");
        return meta;
    }

    /**
     * 上传文件
     */
    @SneakyThrows
    public Dentry uploadFileToCs(final ResourceService resourceService, final UploadUrlViewModel uploadUrlViewModel, byte[] data, String fileName) {
        File tempFile = null;
        try {
            tempFile = File.createTempFile("temp", "");
            FileUtils.writeByteArrayToFile(tempFile, data);
            return uploadFileToCs(resourceService, uploadUrlViewModel, tempFile, fileName);
        } finally {
            FileUtils.deleteQuietly(tempFile);
        }
    }


    /**
     * 上传文件
     */
    @SneakyThrows
    public Dentry uploadFileToCs(final ResourceService resourceService, final UploadUrlViewModel uploadUrlViewModel, String fileContent, String fileName) {
        File tempFile = null;
        try {
            tempFile = File.createTempFile("temp", ".txt");
            FileUtils.writeStringToFile(tempFile, fileContent, StandardCharsets.UTF_8);
            return uploadFileToCs(resourceService, uploadUrlViewModel, tempFile, fileName);
        } finally {
            FileUtils.deleteQuietly(tempFile);
        }
    }

    /**
     * 上传文件
     */
    public Dentry uploadFileToCs(final ResourceService resourceService, final UploadUrlViewModel uploadUrlViewModel, File file, String fileName) {
        if (StringUtils.isBlank(fileName)) {
            fileName = file.getName();
        }

        return doUploadFileToCs(resourceService, uploadUrlViewModel.getServiceName(), file.getAbsolutePath(), uploadUrlViewModel.getDistPath() + "/" + fileName);
    }

    /**
     * 上传到NDR的CS
     */
    @SneakyThrows
    private Dentry doUploadFileToCs(final ResourceService resourceService, String serviceName, String localPath, String remoteDstPath) {
        final long uid = Long.parseLong(UserHandler.getUser("10017913"));
        return CSClient.uploadSync(serviceName, localPath, remoteDstPath, null, 1, (type, path, dentryId, params) -> {
            CsTokenParam param = new CsTokenParam();
            param.setPath(path);
            param.setRole("admin");
            param.setUid(uid > 0 ? uid : 10017913L);
            param.setScope(1);
            param.setTokenType(type.name());
            param.setParams(params);
            // 获取需要使用的cs token
            CsTokenViewModel csTokenViewModel = resourceService.getCsToken(param);
            CsTokenViewModel.TokenParam tokenParam = csTokenViewModel.getTokenParam();

            TokenInfo tokenInfo = new TokenInfo();
            tokenInfo.setDateTime(tokenParam.getDateTime());
            tokenInfo.setToken(tokenParam.getToken());
            tokenInfo.setPolicy(tokenParam.getPolicy());
            tokenInfo.setExpireAt(tokenParam.getExpireAt());
            // 返回 cs sdk需要对象
            return tokenInfo;
        }, null);
    }

    public ResourceMetaTiListViewModel queryAudioMeta(String tenantId, String containerId, String resourceId, List<String> include) {
        if (include == null) {
            include = Lists.newArrayList("TI", "CP");
        }
        log.info("query audio resource with tenantId:{}, containerId:{}, resourceId:{}", tenantId, containerId, resourceId);
        try {
            ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId);
            return resourceService.getResourceMeta(resourceId, null, null, include, null);
        } catch (Exception e) {
            ResourceService resourceService = ndrSdk.getResourceService(NdrConstant.KARAOKE_TENANT_ID, NdrConstant.KARAOKE_COURSEWARE_CONTAINER_ID);
            return resourceService.getResourceMeta(resourceId, null, null, include, null);
        }
    }

    public Map<String, DownloadUrlViewModel> queryAudioUrlModel(String tenantId, String containerId, String resourceId, String tiFileFlag) {
        try {
            ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId);
            return resourceService.getResourceUrl(resourceId, tiFileFlag, null, false, null, null, false, false);
        } catch (Exception e) {
            ResourceService resourceService = ndrSdk.getResourceService(NdrConstant.KARAOKE_TENANT_ID, NdrConstant.KARAOKE_COURSEWARE_CONTAINER_ID);
            return resourceService.getResourceUrl(resourceId, tiFileFlag, null, false, null, null, false, false);
        }
    }

    public PageData<NdrResourceEx> searchResource(String tenantId, String containerId, List<String> labels, List<String> tags) {
        log.info("search resource with tenantId:{}, containerId:{}, labels:{}, tags:{}", tenantId, containerId, labels, tags);
        ResourceService resourceService = ndrSdk.getResourceService(tenantId, containerId);

        String filter = tags.stream().map(e -> String.format("resource_container.tags eq '%s'", e)).collect(Collectors.joining(" and ", "(", ")"));
        if (labels != null && !labels.isEmpty()) {
            String labelFilter = labels.stream().map(e -> String.format("resource.global_label eq '%s'", e)).collect(Collectors.joining(" and ", "(", ")"));
            filter += " and " + labelFilter;
        }
        log.info("search resource with: {}", filter);
        NdrResourceSearchParameters parameters = searchParameterWithFilter(filter);

        return resourceService.searchOnlineResources(containerId, true, parameters);
    }

    private static NdrResourceSearchParameters searchParameterWithFilter(String filter) {
        NdrResourceSearchParameters parameters = new NdrResourceSearchParameters();
        parameters.setFilter(filter);
        parameters.setOffset(0);
        parameters.setLimit(500);
        parameters.setLanguage("zh-CN");
        parameters.setInclude("TI,TAG");
        parameters.setOrder("resource.create_time desc");
        return parameters;
    }

    private String currentAccountId() {
        String currentAccountId = UserHandler.getUser(null);
        if (StringUtils.isBlank(currentAccountId)) {
            log.error("登录用户不能为空");
            throw AicException.ofIllegalArgument("登录用户不能为空！");
        }
        return currentAccountId;
    }
}
