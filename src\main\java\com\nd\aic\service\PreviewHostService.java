package com.nd.aic.service;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.PreviewHost;
import com.nd.aic.enums.PreviewHostStatusEnum;
import com.nd.aic.repository.PreviewHostRepository;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class PreviewHostService extends BaseService<PreviewHost, String> {

    private static final long MAX_OCCUPY_MINUTES = 60;

    private final PreviewHostRepository previewHostRepository;
    private final MongoTemplate mongoTemplate;

    public PreviewHost savePreviewHost(PreviewHost previewHost) {
        PreviewHost oldPreviewHost = previewHostRepository.findByIpAddress(previewHost.getIpAddress());
        if (oldPreviewHost != null) {
            if (previewHost.getPushStreamUrl() != null) {
                oldPreviewHost.setPushStreamUrl(previewHost.getPushStreamUrl());
            }
            if (previewHost.getPullStreamUrl() != null) {
                oldPreviewHost.setPullStreamUrl(previewHost.getPullStreamUrl());
            }
            return save(oldPreviewHost);
        }
        if (previewHost.getCurrentStatus() == null) {
            previewHost.setCurrentStatus(PreviewHostStatusEnum.IDLE);
        }
        if (previewHost.getEnabled() == null) {
            previewHost.setEnabled(true);
        }
        previewHost.setOccupyTime(null);
        previewHost.setCreateTime(new Date());
        return add(previewHost);
    }

    public PreviewHost applyHost(String occupyId) {
        PreviewHost previewHost = previewHostRepository.findByOccupyId(occupyId);
        if (previewHost != null) {
            return previewHost;
        }
        List<PreviewHost> list = mongoTemplate.find(new Query(Criteria.where("occupyTime")
                .lt(Instant.now().minusSeconds(MAX_OCCUPY_MINUTES))
                .and("enabled")
                .is(true)), PreviewHost.class);
        if (CollectionUtils.isNotEmpty(list)) {
            for (PreviewHost host : list) {
                releaseHost(host.getId(), null, null);
            }
        }

        Query query = new Query(Criteria.where("currentStatus").is(PreviewHostStatusEnum.IDLE).and("enabled").is(true));
        Update update = new Update().set("occupyId", occupyId).set("currentStatus", PreviewHostStatusEnum.PREVIEWING).set("occupyTime", new Date());
        return mongoTemplate.findAndModify(query, update, PreviewHost.class);
    }

    public void releaseHost(String hostId, String occupyId, String ipAddress) {
        Criteria criteria;
        if (StringUtils.isNotBlank(hostId)) {
            criteria = Criteria.where("id").is(hostId);
        } else if (StringUtils.isNotBlank(occupyId)) {
            criteria = Criteria.where("occupyId").is(occupyId);
        } else {
            criteria = Criteria.where("ipAddress").is(ipAddress);
        }
        Query query = new Query(criteria);
        Update update = new Update().set("occupyId", null).set("currentStatus", PreviewHostStatusEnum.IDLE).set("occupyTime", null);
        mongoTemplate.updateMulti(query, update, PreviewHost.class);
    }

    public PreviewHost toggleHost(String hostId, String ipAddress, Boolean enabled) {
        Criteria criteria;
        if (StringUtils.isNotBlank(hostId)) {
            criteria = Criteria.where("id").is(hostId);
        } else {
            criteria = Criteria.where("ipAddress").is(ipAddress);
        }
        Query query = new Query(criteria);
        Update update = new Update().set("enabled", enabled);
        return mongoTemplate.findAndModify(query, update, new FindAndModifyOptions().returnNew(true), PreviewHost.class);
    }

    public void dropCollection() {
        mongoTemplate.dropCollection(PreviewHost.class);
    }

    @Override
    protected Module module() {
        return new Module("PreviewHostService");
    }
}
