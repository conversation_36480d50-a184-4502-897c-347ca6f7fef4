package com.nd.aic.service;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.RenderHost;
import com.nd.aic.enums.RenderHostStatusEnum;
import com.nd.aic.exception.AicException;
import com.nd.aic.repository.RenderHostRepository;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class RenderHostService extends BaseService<RenderHost, String> {

    private static final long MINUTES_TO_OFFLINE = 2;
    private static final long DAYS_TO_AUTO_DELETE = 1;

    private final RenderHostRepository renderHostRepository;
    private final MongoTemplate mongoTemplate;

    public RenderHost autoAddRenderHost(String ipAddress) {
        RenderHost renderHost = new RenderHost();
        renderHost.setIpAddress(ipAddress);
        return saveRenderHost(renderHost, true);
    }

    public RenderHost saveRenderHost(RenderHost renderHost, boolean auto) {
        String id = renderHost.getId();
        renderHost.setIpAddress(StringUtils.trim(renderHost.getIpAddress()));
        RenderHost oldRenderHost = null;
        if (StringUtils.isNotBlank(id)) {
            oldRenderHost = renderHostRepository.findOne(id);
            if (oldRenderHost == null) {
                throw AicException.ofIllegalArgument("RenderHost not found");
            }
        }
        if (oldRenderHost == null) {
            oldRenderHost = renderHostRepository.findByIpAddress(renderHost.getIpAddress());
        }
        if (oldRenderHost != null) {
            if (auto) {
                return oldRenderHost;
            }
            oldRenderHost.setAutoAdd(false);
            if (renderHost.getConfiguration() != null) {
                oldRenderHost.setConfiguration(renderHost.getConfiguration());
            }
            if (renderHost.getLevel() != null) {
                oldRenderHost.setLevel(renderHost.getLevel());
            }
            if (renderHost.getBizType() != null) {
                oldRenderHost.setBizType(renderHost.getBizType());
            }
            if (renderHost.getEnabled() != null) {
                oldRenderHost.setEnabled(renderHost.getEnabled());
            }
            return save(oldRenderHost);
        }
        renderHost.setEnabled(true);
        if (renderHost.getCurrentStatus() == null) {
            renderHost.setCurrentStatus(RenderHostStatusEnum.IDLE);
        }
        renderHost.setAutoAdd(auto);
        renderHost.setLastUpdateTime(new Date());
        renderHost.setCurrentTaskId(null);
        renderHost.setStartWorkTime(null);
        renderHost.setCreateTime(new Date());
        return add(renderHost);
    }

    public List<RenderHost> findAllRenderHost() {
        List<RenderHost> renderHosts = renderHostRepository.findAll();
        if (CollectionUtils.isEmpty(renderHosts)) {
            return renderHosts;
        }
        for (Iterator<RenderHost> iterator = renderHosts.iterator(); iterator.hasNext(); ) {
            RenderHost renderHost = iterator.next();
            if (RenderHostStatusEnum.IDLE == renderHost.getCurrentStatus() && renderHost.getLastUpdateTime() != null) {
                Instant instant1 = renderHost.getLastUpdateTime().toInstant();
                Instant instant2 = new Date().toInstant();
                Duration duration = Duration.between(instant1, instant2);
                if (duration.toMinutes() > MINUTES_TO_OFFLINE) {
                    if (BooleanUtils.isTrue(renderHost.getAutoAdd()) && duration.toDays() >= DAYS_TO_AUTO_DELETE) {
                        delete(renderHost.getId());
                        iterator.remove();
                        continue;
                    }
                    renderHost.setCurrentStatus(RenderHostStatusEnum.OFFLINE);
                }
            }
        }
        return renderHosts;
    }

    public RenderHost findRenderHostByIpAddress(String ipAddress) {
        return renderHostRepository.findByIpAddress(ipAddress);
    }

    public RenderHost findRenderHostByTaskId(String taskId) {
        return renderHostRepository.findFirstByCurrentTaskId(taskId);
    }

    @Async
    public void addAndUpdateStatusAsync(String ipAddress, RenderHostStatusEnum status, String currentTaskId) {
        if (StringUtils.isBlank(ipAddress)) {
            return;
        }
        autoAddRenderHost(ipAddress);
        updateStatus(ipAddress, status, currentTaskId);
    }

    public void updateStatus(String ipAddress, RenderHostStatusEnum status, String currentTaskId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("ipAddress").is(StringUtils.trim(ipAddress)));

        Update update = new Update();
        update.set("currentStatus", status);
        Date currentTime = new Date();
        update.set("lastUpdateTime", currentTime);
        if (RenderHostStatusEnum.RENDERING == status) {
            update.set("startWorkTime", currentTime);
            update.set("currentTaskId", currentTaskId);
        } else {
            update.set("startWorkTime", null);
            update.set("currentTaskId", null);
        }
        mongoTemplate.updateMulti(query, update, RenderHost.class);
    }

    public void toggle(String id, Boolean enabled) {
        if (StringUtils.isBlank(id) || enabled == null) {
            return;
        }
        Query query = new Query(Criteria.where("id").is(id));
        Update update = Update.update("enabled", enabled);
        mongoTemplate.updateFirst(query, update, RenderHost.class);
    }

    @Override
    protected Module module() {
        return new Module("RenderHostService");
    }
}
