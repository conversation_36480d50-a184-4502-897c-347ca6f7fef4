package com.nd.aic.service;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.client.AigcFeignClient;
import com.nd.aic.client.InteractionFeignClient;
import com.nd.aic.client.pojo.aigc.ResResp;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.entity.RenderHost;
import com.nd.aic.entity.RenderTask;
import com.nd.aic.entity.RenderTaskHistory;
import com.nd.aic.entity.SgTask;
import com.nd.aic.entity.VideoInteraction;
import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.enums.RenderHostStatusEnum;
import com.nd.aic.enums.RenderTaskStatusEnum;
import com.nd.aic.enums.SgTaskStatusEnum;
import com.nd.aic.exception.AicException;
import com.nd.aic.monitor.ExceptionEvent;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.render.RenderPrepareResult;
import com.nd.aic.pojo.render.RenderTaskAddReq;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.repository.RenderTaskRepository;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.rest.support.WafContext;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class RenderTaskService extends BaseService<RenderTask, String> {

    private static final String DEFAULT_TASK_BIZ_TYPE = "ai_courseware";
    private static final int MAX_RETRY_COUNT = 3;

    private final RenderTaskRepository renderTaskRepository;
    private final MongoTemplate mongoTemplate;
    private final RenderHostService renderHostService;
    private final SgTaskService sgTaskService;
    private final MediaService mediaService;
    private final NdrService ndrService;
    private final InteractionFeignClient interactionFeignClient;
    private final AigcFeignClient aigcFeignClient;

    public RenderTask addTask(RenderTaskAddReq renderTaskAddReq) {
        CoursewareModel coursewareModel = renderTaskAddReq.getCoursewareModel();
        RenderTask renderTask = new RenderTask();
        renderTask.setTaskName(StringUtils.defaultIfBlank(renderTaskAddReq.getTaskName(), coursewareModel.getTitle()));
        renderTask.setBizType(StringUtils.defaultIfBlank(coursewareModel.getBizType(), StringUtils.defaultIfBlank(renderTaskAddReq.getBizType(), DEFAULT_TASK_BIZ_TYPE)));
        renderTask.setBizId(renderTaskAddReq.getBizId());
        renderTask.setUserId(WafContext.getCurrentAccountId());
        renderTask.setPriority(renderTaskAddReq.getPriority());
        renderTask.setIp(renderTaskAddReq.getIp());
        renderTask.setMrqSetting(renderTaskAddReq.getMrqSetting());
        renderTask.setCreateTime(new Date());
        renderTask.setCoursewareModel(coursewareModel);
        renderTask.setInteraction(renderTaskAddReq.getInteraction());
        renderTask.setEnableArkitFace(renderTaskAddReq.getEnableArkitFace());
        renderTask.setArkitNdTech(renderTaskAddReq.getArkitNdTech());
        renderTask.setEnableSubtitle(renderTaskAddReq.getEnableSubtitle());
        if (StringUtils.isBlank(renderTaskAddReq.getRenderMode())) {
            renderTask.setRenderMode("OBS");
        } else {
            renderTask.setRenderMode(renderTaskAddReq.getRenderMode());
        }
        renderTask.setBotId(renderTaskAddReq.getBotId());
        renderTask.setConversationId(renderTaskAddReq.getConversationId());
        renderTask.setWorkflowRunId(renderTaskAddReq.getWorkflowRunId());
        renderTask.setProgress(0d);
        renderTask.setStatus(RenderTaskStatusEnum.PREPARING);
        boolean isPrepareReady;
        if (BooleanUtils.isTrue(renderTaskAddReq.getEnableArkitFace())) {
            RenderPrepareResult result = prepareArkitFaceSegment(renderTask, true);
            isPrepareReady = result.getReady();
        } else {
            isPrepareReady = true;
        }
        if (isPrepareReady) {
            RenderPrepareResult result = prepareAigc(renderTask);
            isPrepareReady = result.getReady();
        }
        if (isPrepareReady) {
            renderTask.setStatus(RenderTaskStatusEnum.PENDING);
        }

        if (BooleanUtils.isFalse(renderTaskAddReq.getEnableSubtitle()) && CollectionUtils.isNotEmpty(coursewareModel.getWordSegment())) {
            coursewareModel.getWordSegment().clear();
        }
        return add(renderTask);
    }

    public RenderTask updateTask(RenderTask renderTask) {
        return update(renderTask);
    }

    public RenderTask getPendingTask(String ip) {
        boolean autoAddHost = false;
        if (StringUtils.isNotBlank(ip)) {
            // 查询主机状态
            RenderHost renderHost = renderHostService.findRenderHostByIpAddress(StringUtils.trim(ip));
            if (renderHost != null) {
                if (BooleanUtils.isFalse(renderHost.getEnabled())) {
                    // 更新主机状态
                    renderHostService.addAndUpdateStatusAsync(ip, RenderHostStatusEnum.IDLE, null);
                    return null;
                }
                autoAddHost = BooleanUtils.isTrue(renderHost.getAutoAdd());
            } else {
                autoAddHost = true;
            }

            // 更新主机状态
            renderHostService.addAndUpdateStatusAsync(ip, RenderHostStatusEnum.IDLE, null);
        }

        List<Criteria> ipCriteria = Lists.newArrayList();
        // 自动添加的主机，则只查询指定ip的任务
        if (!autoAddHost) {
            ipCriteria.addAll(Lists.newArrayList(Criteria.where("ip").exists(false), Criteria.where("ip").is(null)));
        }

        // 如果渲染端有传ip，则查询指定ip的任务
        if (StringUtils.isNotBlank(ip)) {
            ipCriteria.add(Criteria.where("ip").is(ip));
        }
        Criteria criteria = new Criteria().andOperator(Criteria.where("status")
                .is(RenderTaskStatusEnum.PENDING), new Criteria().orOperator(ipCriteria.toArray(new Criteria[0])));
        Query query = new Query(criteria);
        // 设置排序
        query.with(new Sort(Sort.Direction.DESC, "priority")).with(new Sort(Sort.Direction.ASC, "createTime"));
        // 查找第一条数据
        query.limit(1);
        return mongoTemplate.findOne(query, RenderTask.class);
    }

    public RenderTask startTask(String id, String hostIp) {
        RenderTask renderTask = renderTaskRepository.findOne(id);
        if (renderTask == null) {
            throw AicException.of("任务不存在", HttpStatus.BAD_REQUEST);
        }
        if (renderTask.getStatus() != RenderTaskStatusEnum.PENDING && renderTask.getStatus() != RenderTaskStatusEnum.FAILED) {
            throw AicException.of("状态不正确", HttpStatus.BAD_REQUEST);
        }
        renderTask.setHostIp(hostIp);
        renderTask.setStartTime(new Date());
        renderTask.setStatus(RenderTaskStatusEnum.IN_PROGRESS);
        renderTask.setProgress(0d);
        renderTaskRepository.save(renderTask);
        // 更新主机状态
        renderHostService.addAndUpdateStatusAsync(hostIp, RenderHostStatusEnum.RENDERING, renderTask.getId());
        return renderTask;
    }

    public RenderTask finishTask(String id, String videoUrl, String errorMsg) {
        RenderTaskStatusEnum status;
        if (StringUtils.isBlank(videoUrl)) {
            status = RenderTaskStatusEnum.FAILED;
        } else {
            status = RenderTaskStatusEnum.COMPLETED;
        }
        RenderTask renderTask = renderTaskRepository.findOne(id);
        if (renderTask != null) {
            if (RenderTaskStatusEnum.COMPLETED.equals(renderTask.getStatus()) || RenderTaskStatusEnum.FAILED.equals(renderTask.getStatus())) {
                throw AicException.of("状态不正确", HttpStatus.BAD_REQUEST);
            }
            renderTask.setFinishTime(new Date());
            renderTask.setStatus(status);
            renderTask.setVideoUrl(videoUrl);
            renderTask.setErrorMsg(errorMsg);
            if (status == RenderTaskStatusEnum.COMPLETED) {
                renderTask.setProgress(100d);
            }
            try {
                submitInteractions(renderTask);
            } catch (Exception e) {
                log.error("submit interaction error", e);
            }

            // 更新主机状态
            renderHostService.addAndUpdateStatusAsync(renderTask.getHostIp(), RenderHostStatusEnum.IDLE, null);

            // 异常任务
            handleFailedTask(renderTask, true);

            renderTaskRepository.save(renderTask);
        }
        return renderTask;
    }

    public RenderTask deleteTask(String id) {
        RenderTask renderTask = renderTaskRepository.findOne(id);
        if (renderTask != null) {
            renderTaskRepository.delete(renderTask);
            try {
                RenderTaskHistory renderTaskHistory = new RenderTaskHistory();
                BeanUtils.copyProperties(renderTask, renderTaskHistory);
                renderTaskHistory.setDeletedTime(new Date());
                renderTaskHistory.setDeletedUserId(WafContext.getCurrentAccountId());
                mongoTemplate.save(renderTaskHistory);
            } catch (Exception e) {
                log.error("save render task history error", e);
            }
        }
        return renderTask;
    }

    public List<RenderTask> deleteTasks(List<String> ids) {
        List<RenderTask> deletedTasks = new ArrayList<>(ids.size());
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> deletedTasks.add(this.deleteTask(id)));
        }
        return deletedTasks;
    }

    public void retryTask(final RenderTask renderTask) {
        if (!RenderTaskStatusEnum.FAILED.equals(renderTask.getStatus())) {
            return;
        }
        renderTask.setRetryTime(renderTask.getRetryTime() + 1);
        renderTask.setStatus(RenderTaskStatusEnum.PREPARING);
        renderTask.setErrorMsg(null);
        renderTask.setProgress(0d);
        update(renderTask);
    }

    public long getPendingTaskRank(String id) {
        RenderTask renderTask = renderTaskRepository.findOne(id);
        if (renderTask == null) {
            throw AicException.of("任务不存在", HttpStatus.BAD_REQUEST);
        }
        if (renderTask.getStatus() != RenderTaskStatusEnum.PENDING) {
            return -1;
        }
        Criteria criteria = new Criteria().and("status")
                .is(RenderTaskStatusEnum.PENDING)
                .orOperator(Criteria.where("priority").gt(renderTask.getPriority()), new Criteria().andOperator(Criteria.where("priority")
                        .is(renderTask.getPriority()), Criteria.where("createTime").lt(renderTask.getCreateTime())));

        Query query = new Query(criteria);
        return mongoTemplate.count(query, RenderTask.class);
    }

    public List<RenderTask> findByStatus(RenderTaskStatusEnum status, int limit) {
        Criteria criteria = new Criteria().and("status").is(status);
        Query query = new Query(criteria);
        query.limit(limit);
        return mongoTemplate.find(query, RenderTask.class);
    }

    public void updateTasksPriority(List<RenderTask> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, RenderTask.class);
        for (RenderTask task : tasks) {
            if (StringUtils.isBlank(task.getId()) || task.getPriority() == null) {
                continue;
            }
            bulkOps.updateOne(new Query(Criteria.where("id").is(task.getId())), new Update().set("priority", task.getPriority()));
        }
        bulkOps.execute();
    }

    public RenderTask reportTaskProgress(String id, Double progress) {
        Query query = new Query(Criteria.where("id").is(id).and("status").is(RenderTaskStatusEnum.IN_PROGRESS));
        Update update = new Update().set("progress", progress);
        return mongoTemplate.findAndModify(query, update, new FindAndModifyOptions().returnNew(true), RenderTask.class);
    }

    @SneakyThrows
    @SchedulerLock(name = "PrepareRenderTask")
    @Scheduled(fixedDelay = 5000)
    public void doPrepare() {
        if (StringUtils.equals(WafContext.getEnvironment(), "test")) {
            return;
        }
        Criteria criteria = new Criteria().and("status").is(RenderTaskStatusEnum.PREPARING);
        Query query = new Query(criteria);
        query.limit(500);
        List<RenderTask> renderTasks = mongoTemplate.find(query, RenderTask.class);
        if (CollectionUtils.isNotEmpty(renderTasks)) {
            for (RenderTask renderTask : renderTasks) {
                RenderPrepareResult arkitResult = prepareArkitFaceSegment(renderTask, false);
                if (arkitResult.getNeedUpdate()) {
                    mongoTemplate.save(renderTask);
                }
                // 准备aigc
                if (arkitResult.getReady()) {
                    RenderPrepareResult aigcResult;
                    try {
                        aigcResult = prepareAigc(renderTask);
                    } catch (Exception e) {
                        renderTask.setStatus(RenderTaskStatusEnum.FAILED);
                        renderTask.setErrorMsg("准备AIGC失败：" + e.getMessage());
                        // 异常任务
                        handleFailedTask(renderTask, false);
                        mongoTemplate.save(renderTask);
                        continue;
                    }
                    if (aigcResult.getReady()) {
                        renderTask.setStatus(RenderTaskStatusEnum.PENDING);
                        mongoTemplate.save(renderTask);
                    } else if (aigcResult.getNeedUpdate()) {
                        mongoTemplate.save(renderTask);
                    }
                }

                // 异常任务
                handleFailedTask(renderTask, false);
            }
        }
    }

    /**
     * 生成口型
     */
    private RenderPrepareResult prepareArkitFaceSegment(final RenderTask renderTask, boolean submit) {
        CoursewareModel coursewareModel = renderTask.getCoursewareModel();
        if (!RenderTaskStatusEnum.PREPARING.equals(renderTask.getStatus()) || coursewareModel == null || StringUtils.isBlank(coursewareModel.getResourceId())) {
            return new RenderPrepareResult(true);
        }
        if (CollectionUtils.isNotEmpty(coursewareModel.getRoutineSegment()) && coursewareModel.getRoutineSegment()
                .stream()
                .anyMatch(e -> StringUtils.equals(e.getReason(), "人物口型"))) {
            return new RenderPrepareResult(true);
        }
        if (CollectionUtils.isEmpty(coursewareModel.getCharacterSegment())) {
            return new RenderPrepareResult(true);
        }

        CharacterEnum characterEnum = CharacterEnum.getCharacterById(coursewareModel.getCharacterSegment().get(0).getResourceId());
        if (characterEnum == null || StringUtils.isBlank(characterEnum.getFaceModel())) {
            coursewareModel.getRoutineSegment().add(SegmentUtil.createArkitFaceSegmentUsingRoutine("00:00.0", "01:00.0", null, "角色口型未配置"));
            return new RenderPrepareResult(true);
        }

        SgTask sgTask;
        boolean isPixar = StringUtils.equals(characterEnum.getId(), CharacterEnum.PIXAR.getId());
        // 自研口型
        boolean ndTech = CollectionUtils.isNotEmpty(characterEnum.getFaceTools()) && !BooleanUtils.isFalse(renderTask.getArkitNdTech());
        if (submit) {
            // 仅皮克斯生成表情
            int emotionFlag = isPixar ? 1 : 0;
            sgTask = sgTaskService.getOrSubmitTask(coursewareModel.getTitle(), coursewareModel.getContainerId(), coursewareModel.getResourceId(), characterEnum.getFaceModel(), characterEnum.getFaceTools(), ndTech, emotionFlag);
        } else {
            sgTask = sgTaskService.findSgTask(coursewareModel.getResourceId(), characterEnum.getFaceModel(), ndTech);
        }

        if (sgTask == null) {
            return new RenderPrepareResult(true);
        }
        if (SgTaskStatusEnum.COMPLETED.equals(sgTask.getStatus())) {
            if (StringUtils.isNotBlank(sgTask.getSgNdrId())) {
                String endTime = TimeUtils.timeConvert(coursewareModel.getLength(), null);
                RoutineSegment arkitFaceSegment = SegmentUtil.createArkitFaceSegmentUsingRoutine("00:00.0", endTime, sgTask.getSgNdrId(), "人物口型")
                        .playType(0);
                coursewareModel.getSceneObjectSegment()
                        .stream()
                        .filter(item -> CharacterEnum.getCharacterById(item.getResourceId()) != null)
                        .findFirst()
                        .ifPresent(e -> {
                            // CC4角色添加TargetId
                            arkitFaceSegment.targetId(e.getInstanceId());
                        });
                coursewareModel.getRoutineSegment().add(arkitFaceSegment);
            }
            renderTask.setStatusMsg(null);
            return new RenderPrepareResult(true, true);
        } else if (SgTaskStatusEnum.FAILED.equals(sgTask.getStatus())) {
            String errorMsg = "生成口型失败：" + StringUtils.defaultString(sgTask.getErrorMsg());
            renderTask.setErrorMsg(errorMsg);
            renderTask.setStatus(RenderTaskStatusEnum.FAILED);
            renderTask.setStatusMsg(null);
            return new RenderPrepareResult(false, true);
        } else {
            SgTaskStatusEnum sgTaskStatusEnum = sgTask.getStatus();
            if (sgTaskStatusEnum != null) {
                String statusMsg = null;
                switch (sgTaskStatusEnum) {
                    case SG_PENDING:
                        statusMsg = "口型等待中(" + sgTask.getSgTaskOrder() + ")";
                        break;
                    case GENERATING:
                        statusMsg = "口型生成中";
                        break;
                    case COOKING:
                        statusMsg = "口型COOK中";
                        break;
                    default:
                        break;
                }
                if (!StringUtils.equals(statusMsg, renderTask.getStatusMsg())) {
                    renderTask.setStatusMsg(statusMsg);
                    return new RenderPrepareResult(false, true);
                }
            }
        }
        return new RenderPrepareResult(false, false);
    }

    /**
     * 准备AIGC
     */
    private RenderPrepareResult prepareAigc(final RenderTask renderTask) {
        CoursewareModel coursewareModel = renderTask.getCoursewareModel();
        if (!RenderTaskStatusEnum.PREPARING.equals(renderTask.getStatus()) || coursewareModel == null) {
            return new RenderPrepareResult(true);
        }
        List<LottieSegment> lottieSegments = coursewareModel.getLottieSegment();
        if (CollectionUtils.isEmpty(lottieSegments)) {
            return new RenderPrepareResult(true);
        }
        boolean isReady = true;
        boolean needUpdate = false;
        for (LottieSegment lottieSegment : lottieSegments) {
            JSONObject params = lottieSegment.getLottieParams();
            if (params == null) {
                continue;
            }
            // 遍历所有参数
            for (String key : params.keySet()) {
                JSONObject valueObj;
                try {
                    valueObj = params.getJSONObject(key);
                } catch (Exception ignore) {
                    continue;
                }
                String value = valueObj.getString("value");
                if (!StringUtils.startsWithIgnoreCase(value, "taskid=")) {
                    continue;
                }
                String aigcTaskId = value.replaceFirst("(?i)^taskid=", "");
                ResResp resResp = aigcFeignClient.result(aigcTaskId);
                List<ResResp.GenInfo> genInfos = resResp.getInfo().getGenInfos();
                if (CollectionUtils.isNotEmpty(genInfos)) {
                    ResResp.GenInfo genInfo = genInfos.get(0);
                    valueObj.put("value", genInfo.getUrl());
                    valueObj.put("aigc_task_id", aigcTaskId);
                    renderTask.setStatusMsg(null);
                    continue;
                }
                if (StringUtils.containsIgnoreCase(resResp.getInfo().getStatus(), "Failed")) {
                    renderTask.setStatus(RenderTaskStatusEnum.FAILED);
                    renderTask.setErrorMsg("AIGC生成失败");
                    return new RenderPrepareResult(false, true);
                }
                if (!StringUtils.equals("AIGC生成中", renderTask.getStatusMsg())) {
                    renderTask.setStatusMsg("AIGC生成中");
                    needUpdate = true;
                }
                isReady = false;
                break;
            }
        }
        return new RenderPrepareResult(isReady, needUpdate);
    }

    @SneakyThrows
    @Async
    public void submitInteractions(RenderTask renderTask) {
        String videoUrl = renderTask.getVideoUrl();
        if (StringUtils.isEmpty(videoUrl)) {
            return;
        }
        // videoUrl = videoUrl.replace("${ref-path}", "https://cdncs.101.com/v0.1/static");
        VideoInteraction interaction = renderTask.getInteraction();
        if (null == interaction || CollectionUtils.isEmpty(interaction.getActiveEvents())) {
            return;
        }

        // File videoFile = null;
        ResourceMetaTiListViewModel mete;
        try {
            String path = videoUrl.replace("${ref-path}", "");
            Dentry srcDentry = Dentry.getByPath("aic_scontent", path, null);
            // videoFile = mediaService.download(videoUrl, String.format("%s.mp4", renderTask.getId()), true);
            mete = ndrService.createVideoMeta(renderTask.getTaskName(), null, srcDentry, null, NdrConstant.AIGC_TENANT_ID, NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID);
        } finally {
            // if (videoFile != null) {
            //     FileUtils.deleteQuietly(videoFile);
            // }
        }
        String resourceId = mete.getId();
        renderTask.setVideoResourceId(resourceId);

        interaction.setDefaultMedia(resourceId);
        interaction.setRefResourceId(resourceId);
        VideoInteraction.MediaSource mediaSource = new VideoInteraction.MediaSource();
        mediaSource.setId(resourceId);
        mediaSource.setSrc(videoUrl);
        mediaSource.setNdrTenantId(NdrConstant.AIGC_TENANT_ID);
        mediaSource.setNdrContainerId(NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID);
        interaction.setMediaSources(Lists.newArrayList(mediaSource));
        for (VideoInteraction.ActiveEvent activeEvent : interaction.getActiveEvents()) {
            activeEvent.setMediaTarget(resourceId);
        }
        VideoInteraction defaultInteraction = interactionFeignClient.queryInteraction(resourceId);
        interaction.setMediaSources(defaultInteraction.getMediaSources());

        String id = interaction.getId();
        if (StringUtils.isEmpty(id)) {
            id = defaultInteraction.getId();
            interaction.setId(id);
        }
        interactionFeignClient.submitInteraction(id, interaction);
        renderTaskRepository.save(renderTask);
    }

    private boolean isValidFailedTask(RenderTask renderTask) {
        return RenderTaskStatusEnum.FAILED.equals(renderTask.getStatus()) && StringUtils.equals(DEFAULT_TASK_BIZ_TYPE, renderTask.getBizType()) && StringUtils.length(renderTask.getBizId()) > 16;
    }

    /**
     * 处理失败的任务
     */
    private void handleFailedTask(final RenderTask renderTask, boolean needRetry) {
        if (isValidFailedTask(renderTask)) {
            int retryTime = renderTask.getRetryTime();
            boolean retry = needRetry && retryTime < MAX_RETRY_COUNT;
            String extraMsg = "主机IP：" + StringUtils.defaultString(renderTask.getHostIp());
            if (retry) {
                extraMsg += "，开始第" + (retryTime + 1) + "次重试";
            }
            ApplicationContextUtil.getApplicationContext()
                    .publishEvent(ExceptionEvent.builder()
                            .type("1")
                            .errorMsg(renderTask.getErrorMsg())
                            .extraMsg(extraMsg)
                            .renderTaskId(renderTask.getId())
                            .build());
            if (retry) {
                retryTask(renderTask);
            }
        }
    }

    @Override
    protected Module module() {
        return new Module("RenderTaskService");
    }

    public List<RenderTask> findByBizId(String bizId) {
        return renderTaskRepository.findByBizIdOrderByIdDesc(bizId);
    }
}
