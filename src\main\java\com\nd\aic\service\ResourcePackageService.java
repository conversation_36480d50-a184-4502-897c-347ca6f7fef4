package com.nd.aic.service;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.flow.AutomateTransactionOutcomes;
import com.nd.aic.pojo.resource.ResDescript;
import com.nd.aic.pojo.resource.ResInfo;
import com.nd.aic.service.material.entity.MediaResInfo;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.JsonUtils;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.regexp.RE;
import org.joda.time.LocalDateTime;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ResourcePackageService {

    private final MediaService mediaService;
    private final NdrService ndrService;
    private final AutomateTransactionService automateTransactionService;

    @Async
    public void submitFrontVideo(AutomateTransaction automateTransaction) {
        try {
            if (null == automateTransaction.getResourceUrl()) {
                throw WafI18NException.of("BAD_REQUEST", "输入不能为空", HttpStatus.BAD_REQUEST);
            }
            String title = StringUtils.defaultString(automateTransaction.getTitle(), Long.toHexString(System.currentTimeMillis()));
            String url = automateTransaction.getResourceUrl();
            String urlHash = UUID.nameUUIDFromBytes(url.getBytes()).toString();
            String filename = getFilenameFromURL(url, String.format("%s.mp4", urlHash));
            File videoFile = mediaService.download(url, filename, true);
            long duration = mediaService.durationMills(videoFile);
            File frontFile = packageAlphaVideo(videoFile, title);
            ResourceMetaTiListViewModel resourceMeta = ndrService.createFrontVideoMeta(title, videoFile, frontFile, duration);
            if (null == automateTransaction.getOutcomes()) {
                automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
            }
            automateTransaction.getOutcomes().setOutputResource(resourceMeta.getId());
            automateTransaction.setStatus("success");
        } catch (Exception e) {
            log.error("generateCoursewareAsync fail", e);
            automateTransaction.setErrorX(e);
        } finally {
            automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
    }

    public MediaResInfo submitFrontVideo(String url) {
        try {
            String filename = ImageUtil.getFileName(url);
            File videoFile = mediaService.download(url, filename, true);
            long duration = mediaService.durationMills(videoFile);
            File frontFile = packageAlphaVideo(videoFile, filename);
            ResourceMetaTiListViewModel resourceMeta = ndrService.createFrontVideoMeta(filename, videoFile, frontFile, duration);
            MediaResInfo mediaResInfo = new MediaResInfo();
            mediaResInfo.setResourceId(resourceMeta.getId());
            mediaResInfo.setName(filename);
            mediaResInfo.setDuration(duration);
            return mediaResInfo;
        }catch (Exception e){
           log.error("视频转NDR异常", e);
           throw WafI18NException.of("BAD_REQUEST", "视频转NDR异常", HttpStatus.BAD_REQUEST);
        }
    }

    public File packageAlphaVideo(File videoFile, String resName) throws IOException {
        String resId = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        resName = StringUtils.defaultIfBlank(resName, Long.toHexString(System.currentTimeMillis()));
        String outputFileName = String.format("%s.zip", resName);
        File outputFile = new File(mediaService.getTempDirectory(), outputFileName);
        try (FileOutputStream fos = new FileOutputStream(outputFile); ZipOutputStream zos = new ZipOutputStream(fos); FileInputStream fis = new FileInputStream(videoFile)) {

            ZipEntry videoEntry = new ZipEntry(String.format("%s/FrontVideo/%s/video.mp4", resName, resId));
            writeEntry(zos, videoEntry, fis);

            ZipEntry infoEntry = new ZipEntry(String.format("%s/FrontVideo/%s/ResInfo.cfg", resName, resId));
            writeEntry(zos, infoEntry, new ByteArrayInputStream(JsonUtils.toJson(ResInfo.createFrontVideo(resId, resName)).getBytes()));
            ZipEntry descriptEntry = new ZipEntry(String.format("%s/FrontVideo/%s/ResDescript.cfg", resName, resId));
            writeEntry(zos, descriptEntry, new ByteArrayInputStream(JsonUtils.toJson(ResDescript.createFrontVideo(resId, resName)).getBytes()));
        }
        return outputFile;
    }

    @SneakyThrows
    public String packageImgSeqFromCs(String dentryId) {
        Dentry dentry = Dentry.get("null", dentryId, null);
        String resName = StringUtils.substringBefore(dentry.getName(), ".");
        resName = StringUtils.defaultIfBlank(resName, Long.toHexString(System.currentTimeMillis()));
        String imageUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryId;
        File imageFile =  mediaService.download(imageUrl, String.format("%s.png", dentryId), true);

        File frontImgSeqFile = packageImgSeqFile(dentryId, imageFile, resName);
        File imagesZipFile = packageImageZip(dentryId, imageFile, resName);
        return ndrService.createImgSeqMeta(resName, imagesZipFile, frontImgSeqFile).getId();
    }

    private File packageImgSeqFile(String folder, File imageFile, String resName) throws IOException {
        String resId = UUID.randomUUID().toString().replace("-", "").toUpperCase();
        resName = StringUtils.defaultIfBlank(resName, Long.toHexString(System.currentTimeMillis()));
        String outputFileName = String.format("%s.FrontImgSeq", resName);
        File outputFile = new File(new File(mediaService.getTempDirectory(), folder), outputFileName);
        FileUtils.forceMkdir(outputFile.getParentFile());
        try (FileOutputStream fos = new FileOutputStream(outputFile); ZipOutputStream zos = new ZipOutputStream(fos); FileInputStream fis = new FileInputStream(imageFile)) {
            ZipEntry videoEntry = new ZipEntry(String.format("%s/FrontImgSeq/%s/images/%s_00001.png", resName, resId, resName));
            writeEntry(zos, videoEntry, fis);

            ZipEntry infoEntry = new ZipEntry(String.format("%s/FrontImgSeq/%s/ResInfo.cfg", resName, resId));
            writeEntry(zos, infoEntry, new ByteArrayInputStream(JsonUtils.toJson(ResInfo.createImgSeq(resId, resName)).getBytes()));
            ZipEntry descriptEntry = new ZipEntry(String.format("%s/FrontImgSeq/%s/ResDescript.cfg", resName, resId));
            writeEntry(zos, descriptEntry, new ByteArrayInputStream(JsonUtils.toJson(ResDescript.createImgSeq(resId, resName)).getBytes()));
        }
        return outputFile;
    }

    private File packageImageZip(String folder, File imageFile, String resName) throws IOException {
        resName = StringUtils.defaultIfBlank(resName, Long.toHexString(System.currentTimeMillis()));
        String outputFileName = String.format("%s.zip", resName);
        File outputFile = new File(new File(mediaService.getTempDirectory(), folder), outputFileName);
        FileUtils.forceMkdir(outputFile.getParentFile());
        try (FileOutputStream fos = new FileOutputStream(outputFile); ZipOutputStream zos = new ZipOutputStream(fos); FileInputStream fis = new FileInputStream(imageFile)) {
            ZipEntry imageEntry = new ZipEntry(String.format("%s/%s_00001.png", resName, resName));
            writeEntry(zos, imageEntry, fis);
        }
        return outputFile;
    }

    private static void writeEntry(ZipOutputStream zos, ZipEntry zipEntry, InputStream is) throws IOException {
        zos.putNextEntry(zipEntry);

        byte[] buffer = new byte[1024];
        int length;
        while ((length = is.read(buffer)) >= 0) {
            zos.write(buffer, 0, length);
        }
        zos.closeEntry();
    }

    public AutomateTransaction queryAutomateTransaction(String transactionId) {
        AutomateTransaction automateTransaction = automateTransactionService.findOne(transactionId);
        if (automateTransaction.getCreateAt() == null) {
            automateTransaction.setCreateAt(new Date());
            automateTransaction = automateTransactionService.updateAutomateTransaction(automateTransaction);
        }
        if (StringUtils.equals(automateTransaction.getStatus(), "process")) {
            boolean timeout;
            if (null != automateTransaction.getExpireAt()) {
                timeout = LocalDateTime.now().isAfter(LocalDateTime.fromDateFields(automateTransaction.getExpireAt()));
            } else {
                timeout = LocalDateTime.now().minusMinutes(10).isAfter(LocalDateTime.fromDateFields(automateTransaction.getCreateAt()));
            }
            if (timeout) {
                automateTransaction.setStatus("fail");
                automateTransaction.setErrorCode("TIMEOUT");
                automateTransaction.setErrorMessage("事务处理超时");
                automateTransaction = automateTransactionService.updateAutomateTransaction(automateTransaction);
            }
        }
        return automateTransaction;
    }

    public static String getFilenameFromURL(String url, String defName) throws URISyntaxException, UnsupportedEncodingException {
        URI uri = new URI(url);
        String query = uri.getQuery();
        Map<String, String> queryParams = parseQuery(query);
        return queryParams.getOrDefault("fileName", defName);
    }

    private static Map<String, String> parseQuery(String query) throws UnsupportedEncodingException {
        Map<String, String> queryParams = new HashMap<>();
        if (query != null) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length > 1) {
                    queryParams.put(URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8.displayName()), URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8.displayName()));
                } else {
                    queryParams.put(URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8.displayName()), "");
                }
            }
        }
        return queryParams;
    }
}
