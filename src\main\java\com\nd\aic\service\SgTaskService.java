package com.nd.aic.service;

import com.mongodb.WriteResult;
import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.SgTask;
import com.nd.aic.enums.SgTaskStatusEnum;
import com.nd.aic.exception.AicException;
import com.nd.aic.repository.SgTaskRepository;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class SgTaskService extends BaseService<SgTask, String> {

    private final SgTaskRepository sgTaskRepository;
    private final MongoTemplate mongoTemplate;

    public SgTask getOrSubmitTask(String name, String containerId, String resourceId, String sgModel, List<String> tools, boolean ndTech, int emotionFlag) {
        SgTask sgTask = sgTaskRepository.findFirstByResourceIdAndSgModelAndNdTech(resourceId, sgModel, ndTech);
        if (sgTask != null && !SgTaskStatusEnum.COMPLETED.equals(sgTask.getStatus())) {
            delete(sgTask.getId());
            sgTask = null;
        }
        // 超期重新生成
        if (sgTask != null && sgTask.getCreateTime() != null && sgTask.getCreateTime()
                .toInstant()
                .isBefore(Instant.now().minus(7, ChronoUnit.DAYS))) {
            delete(sgTask.getId());
            sgTask = null;
        }
        if (sgTask == null) {
            sgTask = new SgTask();
            sgTask.setId(UUID.randomUUID().toString());
            sgTask.setName(name);
            sgTask.setResourceId(resourceId);
            sgTask.setResourceContainerId(containerId);
            sgTask.setSgModel(sgModel);
            sgTask.setToolTypes(tools);
            sgTask.setNdTech(ndTech);
            sgTask.setEmotionFlag(emotionFlag);
            sgTask.setStatus(SgTaskStatusEnum.PENDING);
            sgTask.setCreateTime(new Date());
            return sgTaskRepository.save(sgTask);
        }
        return sgTask;
    }

    public int updateTask(SgTask sgTask) {
        if (StringUtils.isBlank(sgTask.getId()) && StringUtils.isBlank(sgTask.getSgTaskId())) {
            throw AicException.ofIllegalArgument("id or sgTaskId is null");
        }
        Criteria criteria;
        if (StringUtils.isNotBlank(sgTask.getId())) {
            criteria = Criteria.where("id").is(sgTask.getId());
        } else {
            criteria = Criteria.where("sgTaskId").is(sgTask.getSgTaskId());
        }
        Query query = new Query(criteria);
        Update update = new Update();
        if (sgTask.getAudioUrl() != null) {
            update.set("audioUrl", sgTask.getAudioUrl());
        }
        if (sgTask.getExpressionUrl() != null) {
            update.set("expressionUrl", sgTask.getExpressionUrl());
        }
        if (sgTask.getSgTaskId() != null) {
            update.set("sgTaskId", sgTask.getSgTaskId());
        }
        if (sgTask.getSgTaskOrder() != null) {
            update.set("sgTaskOrder", sgTask.getSgTaskOrder());
        }
        if (sgTask.getSgNdrId() != null) {
            update.set("sgNdrId", sgTask.getSgNdrId());
        }
        if (sgTask.getStatus() != null) {
            update.set("status", sgTask.getStatus());
        }
        if (sgTask.getErrorMsg() != null) {
            update.set("errorMsg", sgTask.getErrorMsg());
        }
        if (sgTask.getStartTime() != null) {
            update.set("startTime", sgTask.getStartTime());
        }
        if (sgTask.getCookTime() != null) {
            update.set("cookTime", sgTask.getCookTime());
        }
        if (sgTask.getFinishTime() != null) {
            update.set("finishTime", sgTask.getFinishTime());
        }
        WriteResult writeResult = mongoTemplate.updateMulti(query, update, SgTask.class);
        return writeResult.getN();
    }

    public SgTask getPendingTaskWithLock() {
        // 待执行或锁定时间大于5分钟
        Criteria criteria = new Criteria().orOperator(Criteria.where("status").is(SgTaskStatusEnum.PENDING), Criteria.where("status")
                .is(SgTaskStatusEnum.LOCKING)
                .and("lockTime")
                .lt(new Date(System.currentTimeMillis() - 5 * 60 * 1000)));

        Query query = new Query(criteria);
        query.limit(1);
        Update update = new Update().set("status", SgTaskStatusEnum.LOCKING).set("lockTime", new Date());
        return mongoTemplate.findAndModify(query, update, new FindAndModifyOptions().returnNew(true), SgTask.class);
    }

    public SgTask findSgTask(String resourceId, String sgModel, boolean ndTech) {
        return sgTaskRepository.findFirstByResourceIdAndSgModelAndNdTech(resourceId, sgModel, ndTech);
    }

    public SgTask findBySgTaskId(String sgTaskId) {
        return sgTaskRepository.findFirstBySgTaskId(sgTaskId);
    }

    public List<SgTask> findAllCookingTask() {
        Query query = new Query(Criteria.where("status").is(SgTaskStatusEnum.COOKING));
        query.limit(100);
        return mongoTemplate.find(query, SgTask.class);
    }

    @Override
    protected Module module() {
        return new Module("SgTaskService");
    }
}
