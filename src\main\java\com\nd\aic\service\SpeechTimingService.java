package com.nd.aic.service;

import com.google.common.collect.Lists;

import com.nd.aic.client.SpeechTextTimingClient;
import com.nd.aic.client.pojo.asr.AsrTask;
import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.client.pojo.asr.AtaUtterance;
import com.nd.aic.client.pojo.asr.AtaWord;
import com.nd.aic.entity.SpeechTimingResult;
import com.nd.aic.pojo.AtaUtteranceWithPos;
import com.nd.aic.pojo.AtaWordWithPos;
import com.nd.aic.repository.SpeechTimingResultRepository;
import com.nd.gaea.client.http.WafHttpClient;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class SpeechTimingService {

    private final WafHttpClient wafHttpClient;
    private final SpeechTextTimingClient speechTextTimingClient;
    private final SpeechTimingResultRepository speechTimingResultRepository;

    /**
     * 通过ID或者关联的音频ID获取打轴结果
     */
    public List<AtaResult> queryAtaResult(String id) {
        SpeechTimingResult timingResult = speechTimingResultRepository.findOne(id);
        if (timingResult == null) {
            String audioKey = DigestUtils.md5Hex(id);
            return speechTimingResultRepository.findByAudioKey(audioKey).stream().map(SpeechTimingResult::getAtaResult).collect(Collectors.toList());
        }
        return Lists.newArrayList(timingResult.getAtaResult());
    }

    /**
     * 计算文本在音频中对应的时间点
     */
    public List<AtaUtteranceWithPos> getAtaUtterancesWithPos(String audioResourceId, String audioUrl, String audioText) {
        AtaResult ataResult = getAtaResult(audioResourceId, audioUrl, audioText);
        List<AtaUtteranceWithPos> ataUtteranceWithPosList = new ArrayList<>();
        List<AtaUtterance> utterances = ataResult.getUtterances();
        if (CollectionUtils.isEmpty(utterances)) {
            return ataUtteranceWithPosList;
        }
        int startPos = 0;
        for (AtaUtterance utterance : utterances) {
            AtaUtteranceWithPos ataUtteranceWithPos = new AtaUtteranceWithPos();
            String utteranceText = utterance.getText();
            int startIndex = StringUtils.indexOf(audioText, utteranceText, startPos);
            if (startIndex != -1) {
                ataUtteranceWithPos.setText(utteranceText);
                ataUtteranceWithPos.setStartTime(utterance.getStartTime());
                ataUtteranceWithPos.setEndTime(utterance.getEndTime());
                ataUtteranceWithPos.setPos(startIndex);
                List<AtaWordWithPos> ataWordWithPosList = new ArrayList<>();
                startPos = startIndex;
                List<AtaWord> words = utterance.getWords();
                for (int i = 0; i < words.size(); i++) {
                    AtaWord word = words.get(i);
                    String wordText = word.getText();
                    AtaWordWithPos wordWithPos = new AtaWordWithPos();
                    wordWithPos.setText(wordText);
                    wordWithPos.setStartTime(word.getStartTime());
                    wordWithPos.setEndTime(word.getEndTime());
                    wordWithPos.setPos(startPos);
                    wordWithPos.setLineStart(i == 0);
                    wordWithPos.setLineEnd(i == words.size() - 1);
                    ataWordWithPosList.add(wordWithPos);
                    startPos += wordText.length();
                }
                ataUtteranceWithPos.setWords(ataWordWithPosList);
                ataUtteranceWithPosList.add(ataUtteranceWithPos);
            }
        }
        return ataUtteranceWithPosList;
    }

    /**
     * 获取文本打轴结果
     */
    public AtaResult getAtaResult(String audioUrl, String audioText) {
        return getAtaResult(null, audioUrl, audioText);
    }


    /**
     * 获取文本打轴结果
     */
    public AtaResult getAtaResult(String audioResourceId, String audioUrl, String audioText) {
        int questionMarkIndex = audioUrl.indexOf('?');
        if (questionMarkIndex != -1) {
            audioUrl = audioUrl.substring(0, questionMarkIndex);
        }
        String audioKey = StringUtils.isNotBlank(audioResourceId) ? DigestUtils.md5Hex(audioResourceId) : DigestUtils.md5Hex(audioUrl);
        String audioTextKey = DigestUtils.md5Hex(audioText);
        SpeechTimingResult result = speechTimingResultRepository.findByAudioKeyAndAudioTextKey(audioKey, audioTextKey);
        if (result != null && result.getAtaResult() != null) {
            return result.getAtaResult();
        }
        AsrTask task;
        try {
            task = speechTextTimingClient.ataSubmit(audioUrl, audioText);
        } catch (Exception e) {
            ResponseEntity<byte[]> responseEntity = wafHttpClient.getForEntity(audioUrl, byte[].class);
            task = speechTextTimingClient.ataSubmit(responseEntity.getBody(), audioText);
        }
        AtaResult ataResult = speechTextTimingClient.ataQuery(task.getId());
        // 缓存
        saveAtaResult(audioResourceId, audioUrl, audioText, ataResult);
        return ataResult;
    }

    /**
     * 保存打轴结果
     */
    public SpeechTimingResult saveAtaResult(String audioResourceId, String audioUrl, String audioText, AtaResult ataResult) {
        String audioKey = StringUtils.isNotBlank(audioResourceId) ? DigestUtils.md5Hex(audioResourceId) : DigestUtils.md5Hex(audioUrl);
        String audioTextKey = DigestUtils.md5Hex(audioText);
        SpeechTimingResult result = speechTimingResultRepository.findByAudioKeyAndAudioTextKey(audioKey, audioTextKey);
        if (result != null) {
            return result;
        }
        result = new SpeechTimingResult();
        result.setAudioKey(audioKey);
        result.setAudioTextKey(audioTextKey);

        result.setAudioResourceId(audioResourceId);
        result.setAudioUrl(audioUrl);
        result.setAudioText(audioText);

        result.setAtaResult(ataResult);
        result.setCreateTime(new Date());
        return speechTimingResultRepository.save(result);
    }
}
