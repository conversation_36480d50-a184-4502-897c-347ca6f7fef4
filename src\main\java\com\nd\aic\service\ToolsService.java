package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.mongodb.WriteResult;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.service.automate.PptAutomateProcessor;
import com.nd.aic.service.automate.PptHandler;
import com.nd.aic.util.OfficeUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class ToolsService {
    private final MongoTemplate mongoTemplate;
    private final PptHandler pptHandler;
    private final CsService csService;
    private final NdrService ndrService;
    private final MediaService mediaService;

    public int patchField(String tableName, String key, String value) {
        key = StringUtils.defaultString(key, "productionLine");
        value = StringUtils.defaultString(value, "courseware");
        log.info("patch {} {} {}", tableName, key, value);
        Criteria criteria = Criteria.where(key).exists(false);
        Query query = new Query(criteria);
        WriteResult result = mongoTemplate.updateMulti(query, new Update().set(key, value), tableName);
        return result.getN();
    }

    @SneakyThrows
    public Object parsePptx(String language, MultipartFile multipartFile) {
        String name = multipartFile.getName();
        File file = File.createTempFile("tmp", ".pptx");
        multipartFile.transferTo(file);
        return ppt2ImageTexts(name, language, file);
    }

    @SneakyThrows
    public Object ppt2ImageTexts(String name, String language, File pptxFile) {
        List<String> pptxNotes = PptAutomateProcessor.pptxNotes(pptxFile);
        log.info("PPT备注提取完成（{}）: {}", name, pptxNotes);
        Dentry dentry = csService.uploadFile(1L, name, pptxFile);
        String pptxUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId();
        List<File> imageFiles = pptHandler.convertPpt2Images(pptxUrl);

        List<Map<String, Object>> result = Lists.newArrayList();
        for (int i = 0; i < imageFiles.size(); i++) {
            File imageFile = imageFiles.get(i);
            dentry = csService.uploadFile(1L, name + "_" + i, imageFile);
            Map<String, Object> item = Maps.newHashMap();
            item.put("image_uri", "https://cdncs.101.com/v0.1/download?dentryId=" + dentry.getDentryId());
            item.put("memo", pptxNotes.get(i));
            result.add(item);
        }
        return result;
    }

    @SneakyThrows
    public Object parsePptScript(String id) {
        // 从NDR获取PPT文件下载链接
        String url = ndrService.getResourceUrl(NdrConstant.AIGC_TENANT_ID, NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID, id, "source");
        File pptxFile = mediaService.download(url, String.format("%s.pptx", id), true);
        List<String> pptxNotes = OfficeUtils.parsePptxNotes(pptxFile);
        String text = pptxNotes.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("\n"));
        return buildScriptResult(id, text);
    }

    @SneakyThrows
    public Object parseWordScript(String id) {
        // 从NDR获取PPT文件下载链接
        String url = ndrService.getResourceUrl(NdrConstant.AIGC_TENANT_ID, NdrConstant.AI_COURSEWARE_MAIN_CONTAINER_ID, id, "source");
        File docFile = mediaService.download(url, String.format("%s.docx", id), true);
        String text = OfficeUtils.readWordContent(docFile);
        return buildScriptResult(id, text);
    }

    private Object buildScriptResult(String id, String text) {
        if (StringUtils.isBlank(text)) {
            throw WafI18NException.of("BAD_REQUEST", "抱歉！未识别到讲稿，请确定文件中是否包含讲稿信息。", HttpStatus.BAD_REQUEST);
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("id", id);
        result.put("text", text);
        return result;
    }
}
