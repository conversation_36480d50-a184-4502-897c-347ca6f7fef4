package com.nd.aic.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.domain.Module;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.client.AiPptFeignClient;
import com.nd.aic.entity.VideoConcat;
import com.nd.aic.entity.automate.CallbackArguments;
import com.nd.aic.repository.VideoConcatRepository;
import com.nd.aic.util.JsonUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class VideoConcatService extends BaseService<VideoConcat, String> {

    @Value("${ioa.ai.aippt.tools.invalidFilename:\\/:*?<>|}")
    private String invalidFilename;

    private final VideoConcatRepository videoConcatRepository;
    private final MediaService mediaService;
    private final CsService csService;
    private final AiPptFeignClient aiPptFeignClient;

    @Override
    protected Module module() {
        return new Module("VIDEO_CONCAT");
    }

    public VideoConcat addVideoConcat(VideoConcat videoConcat) {
        videoConcat = add(videoConcat);
        log.info("Added video concat with ID: {}", videoConcat.getId());
        // Process the video concatenation asynchronously
        ApplicationContextUtil.getApplicationContext().getBean(VideoConcatService.class).processVideoConcat(videoConcat);
        return videoConcat;
    }

    @Async
    public void processVideoConcat(VideoConcat videoConcat) {
        log.info("Processing video concat for: {}", videoConcat.getId());
        // Implement the logic to process the video concatenation
        // This could involve calling external services, manipulating video files, etc.
        // For now, we just log the action.
        try {
            concatAndCallback(videoConcat, videoConcat.getVideoUrls());
        } catch (WafI18NException e) {
            log.error("Error processing video concat: {}", e.getMessage(), e);
            videoConcat.setStatus(VideoConcat.STATUS_FAILED);
            videoConcat.setErrorMsg(e.getMessage());
            update(videoConcat);
        } catch (Exception e) {
            log.error("Unexpected error during video concat processing: {}", e.getMessage(), e);
            videoConcat.setStatus(VideoConcat.STATUS_FAILED);
            videoConcat.setErrorMsg(e.getMessage());
            update(videoConcat);
        }
    }

    public void concatAndCallback(VideoConcat videoConcat, List<String> videoUrls) {
        Dentry dentry;
        if (videoUrls.size() > 1) {
            dentry = doConcat(videoConcat, videoUrls);
        } else {
            String path = videoUrls.get(0);
            path = StringUtils.replace(path, "cs_path:${ref-path}", "");
            path = StringUtils.replace(path, "${ref-path}", "");
            try {
                dentry = Dentry.getByPath("aic_service_scontent", path, null);
            } catch (Exception e) {
                throw WafI18NException.of("GET_DENTRY_ERROR", "获取入库视频失败，请稍后重试", HttpStatus.INTERNAL_SERVER_ERROR, e);
            }
        }
        videoConcat.setFinalVideo(String.format("cs_path:${ref-path}%s", dentry.getPath()));
        videoConcat.setStatus(VideoConcat.STATUS_SUCCESS);
        if (null != videoConcat.getCallbackArguments()) {
            CallbackArguments callbackArguments = videoConcat.getCallbackArguments();
            String name = String.format("%s.mp4", videoConcat.getTitle());
            callback(callbackArguments, dentry, name);
        }
        update(videoConcat);
    }

    public Dentry doConcat(VideoConcat videoConcat, List<String> videoUrls) {
        List<File> videoFiles = Lists.newArrayList();
        try {
            for (String videoPath : videoUrls) {
                try {
                    String filename = String.format("%s.mp4", UUID.nameUUIDFromBytes(videoPath.getBytes()));
                    String url = StringUtils.replace(videoPath, "cs_path:${ref-path}", "https://cdncs.101.com/v0.1/static");
                    url = StringUtils.replace(url, "${ref-path}", "https://cdncs.101.com/v0.1/static");
                    File videoFile = mediaService.download(url, filename, false);
                    videoFiles.add(videoFile);
                } catch (IOException ex) {
                    throw WafI18NException.of("UPLOAD_ERROR", "视频下载失败，请稍后重试", HttpStatus.INTERNAL_SERVER_ERROR, ex);
                }
            }
        } catch (WafI18NException e) {
            videoFiles.forEach(FileUtils::deleteQuietly);
            throw e;
        }
        File finalVideo;
        if (BooleanUtils.isFalse(videoConcat.getWithTransition())) {
            finalVideo = mediaService.concatMp4(videoFiles);
        } else {
            finalVideo = mediaService.concatMp4WithTransitions(videoFiles, 1.0f);
        }
        log.info("Final video created: {}, length: {}", finalVideo.getAbsolutePath(), FileUtils.byteCountToDisplaySize(finalVideo.length()));
        videoFiles.forEach(FileUtils::deleteQuietly);
        Dentry dentry;
        try {
            String title = StringUtils.defaultIfBlank(videoConcat.getTitle(), videoConcat.getId());
            for (int i = 0; i < invalidFilename.length(); i++) {
                char c = invalidFilename.charAt(i);
                title = StringUtils.replace(title, String.valueOf(c), "-");
            }
            String filename = String.format("%s.mp4", title);
            dentry = csService.uploadFile(147507L, "videos", filename, finalVideo);
        } catch (Exception e) {
            throw WafI18NException.of("UPLOAD_ERROR", "视频上传失败，请稍后重试", HttpStatus.INTERNAL_SERVER_ERROR, e);
        } finally {
            FileUtils.deleteQuietly(finalVideo);
        }
        log.info("Video uploaded successfully: {}", dentry.getPath());
        return dentry;
    }

    public void callback(CallbackArguments callbackArguments, Dentry dentry, String name) {
        Map<String, String> body = Maps.newHashMap();
        JSONObject preFile = new JSONObject();
        preFile.put("uid", String.format("rc-upload-%d-18", System.currentTimeMillis()));
        preFile.put("percent", 100);
        preFile.put("name", StringUtils.defaultString(name, dentry.getName()));
        preFile.put("id", dentry.getDentryId());
        preFile.put("dentry_id", dentry.getDentryId());
        List<JSONObject> preFiles = Lists.newArrayList(preFile);
        body.put("pre_file", JsonUtils.toJson(preFiles));
        body.put("aippt_editor_url", String.format("https://aic-editor.sdp.101.com/#/courseware-new/lesson/tool?task_id=%d", callbackArguments.getTaskId()));
        aiPptFeignClient.updateDataBus(callbackArguments.getSdpAppId(), callbackArguments.getSdpBizType(), callbackArguments.getSuid(), callbackArguments.getTaskId(), body);
    }

    public VideoConcat findByKey(String key) {
        VideoConcat videoConcat = videoConcatRepository.findFirstByKeyOrderByIdDesc(key);
        if (null == videoConcat) {
            return null;
        }
        if (VideoConcat.STATUS_RUNNING.equals(videoConcat.getStatus()) && videoConcat.getExpireAt() != null && videoConcat.getExpireAt().before(new Date())) {
            videoConcat.setStatus(VideoConcat.STATUS_FAILED);
            videoConcat.setErrorMsg("视频合并超时，请稍后重试");
            update(videoConcat);
        }
        return videoConcat;
    }
}
