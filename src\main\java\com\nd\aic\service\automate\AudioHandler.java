package com.nd.aic.service.automate;

import com.google.common.collect.Lists;

import com.nd.aic.common.UserHandler;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.pojo.AtaUtteranceWithPos;
import com.nd.aic.pojo.AtaWordWithPos;
import com.nd.aic.service.CsService;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.SpeechTimingService;
import com.nd.aic.util.TextUtils;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AudioHandler {

    private final MediaService mediaService;
    private final CsService csService;
    private final SpeechTimingService speechTimingService;

    public AudioHandler(MediaService mediaService, CsService csService, SpeechTimingService speechTimingService) {
        this.mediaService = mediaService;
        this.csService = csService;
        this.speechTimingService = speechTimingService;
    }

    @SneakyThrows
    public List<AutomatePerformanceDetail> splitAudio(String audioUrl, AutomateTransaction automateTransaction) {
        List<AutomatePerformanceDetail> detailList = Lists.newArrayList();
        for (AutomatePerformanceDetail detail : automateTransaction.getDetailList()) {
            AutomatePerformanceDetail newDetail = new AutomatePerformanceDetail();
            BeanUtils.copyProperties(detail, newDetail);
            detailList.add(newDetail);
        }

        // 计算教学事件的时间
        String audioText = detailList.stream().map(AutomatePerformanceDetail::getDialogue)
                // 清除停顿
                .map(TextUtils::clearBreak)
                // 排除null
                .filter(StringUtils::isNotBlank).collect(Collectors.joining());
        List<AtaUtteranceWithPos> ataUtterancesWithPos = speechTimingService.getAtaUtterancesWithPos(null, audioUrl, audioText);
        List<AtaWordWithPos> ataWordsWithPos = ataUtterancesWithPos.stream().map(AtaUtteranceWithPos::getWords).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        // wordTimes中是每个字的时间点，需要根据detailList中的对话内容，计算每个对话的开始和结束时间

        List<DetailWrapper> detailWrappers = calcDetailTime(detailList, ataWordsWithPos);
        File wholeAudio = mediaService.download(audioUrl, String.format("%s.mp3", UUID.nameUUIDFromBytes(audioUrl.getBytes(StandardCharsets.UTF_8))), true);
        // long duration = mediaService.durationMills(wholeAudio);
        for (DetailWrapper detailWrapper : detailWrappers) {
            if (-1 == detailWrapper.getStartTime() || -1 == detailWrapper.getEndTime()) {
                continue;
            }
            File mp3File = mediaService.cutAudio(wholeAudio, detailWrapper.getStartTime(), detailWrapper.getEndTime());
            // 上传到CS
            String filename = String.format("%s_%d.mp3", automateTransaction.getTitle(), detailWrapper.getIndex());
            Dentry dentry = csService.uploadFile(Long.valueOf(UserHandler.getUser("0")), automateTransaction.getId(), filename, mp3File);
            AutomatePerformanceDetail detail = detailWrapper.getDetail();
            detail.setAudioId(dentry.getDentryId());
            detail.setAudioGenType("cut");
        }

        return detailList;
    }

    private List<DetailWrapper> calcDetailTime(List<AutomatePerformanceDetail> detailList, List<AtaWordWithPos> ataWordsWithPos) {
        List<DetailWrapper> detailWrappers = detailList.stream().map(DetailWrapper::wrap).collect(Collectors.toList());

        String fullText = ataWordsWithPos.stream().map(AtaWordWithPos::getText).collect(Collectors.joining());
        int startPos = 0;
        for (int i = 0; i < detailWrappers.size(); i++) {
            DetailWrapper detail = detailWrappers.get(i);
            detail.setIndex(i);
            String dialogue = detail.getDetail().getDialogue();
            if (StringUtils.isEmpty(dialogue)) {
                continue;
            }

            String text = dialogue.replaceAll("[、，；。？！：.,!?;:()“”'「」【】{}\\[\\]<>《》（）+/]", " ").trim();
            // 匹配开始字位置
            int startIndex = nearestStartIndex(fullText, text, startPos);
            if (-1 == startIndex) {
                log.error("对白开始位置匹配失败: {}", dialogue);
                continue;
            }
            // 匹配结束字位置
            String fullTextSegment = StringUtils.reverse(fullText.substring(startIndex, Math.min(startIndex + dialogue.length(), fullText.length())));
            int tmpIndex = nearestStartIndex(fullTextSegment, StringUtils.reverse(text), 0);
            if (-1 == tmpIndex) {
                log.error("对白结束位置匹配失败: {}", dialogue);
                continue;
            }
            int endIndex = startIndex + (fullTextSegment.length() - 1) - tmpIndex;

            int startTime = ataWordsWithPos.get(startIndex).getStartTime();
            int endTime = ataWordsWithPos.get(endIndex).getEndTime();
            detail.setStartTime(startTime);
            detail.setEndTime(endTime);
            startPos = endIndex + 1;

            String keyWords = String.format("%s-%s", ataWordsWithPos.get(startIndex).getText(), ataWordsWithPos.get(endIndex).getText());
            System.out.printf("DEBUG idx:%d  pos:%03d-%03d  time: %05d-%05d text: %s\t%s\n", i, startIndex, endIndex, startTime, endIndex, keyWords, detail.getDetail().getDialogue());
        }
        return detailWrappers;
    }

    private int nearestStartIndex(String fullText, String text, int startPos) {
        text = text.trim();
        int pos = -1;
        while (text.length() > 0) {
            pos = StringUtils.indexOf(fullText, text, startPos);
            if (pos != -1) {
                break;
            }
            text = text.substring(0, text.length() - 1);
        }
        return pos;
    }

    @Getter
    @Setter
    public static class DetailWrapper {
        private AutomatePerformanceDetail detail;

        private int index;
        private int startTime = -1;
        private int endTime = -1;

        public static DetailWrapper wrap(AutomatePerformanceDetail detail) {
            DetailWrapper wrapper = new DetailWrapper();
            wrapper.detail = detail;
            return wrapper;
        }
    }

    public static class BestSubstringMatcher {
        // 计算两个字符串的编辑距离
        public static int calculateEditDistance(String str1, String str2) {
            int m = str1.length();
            int n = str2.length();
            int[][] dp = new int[m + 1][n + 1];

            for (int i = 0; i <= m; i++) {
                for (int j = 0; j <= n; j++) {
                    if (i == 0) {
                        dp[i][j] = j; // 插入所有字符
                    } else if (j == 0) {
                        dp[i][j] = i; // 删除所有字符
                    } else if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                        dp[i][j] = dp[i - 1][j - 1]; // 字符匹配
                    } else {
                        dp[i][j] = 1 + Math.min(dp[i - 1][j - 1], // 替换字符
                                Math.min(dp[i - 1][j],      // 删除字符
                                        dp[i][j - 1]));    // 插入字符
                    }
                }
            }
            return dp[m][n];
        }

        // 在 fullText 中找到与 text 最接近的匹配
        public static int[] findClosestMatch(String fullText, String text, int pos) {
            if (pos < 0 || pos >= fullText.length()) {
                throw new IllegalArgumentException("Invalid start position");
            }

            int minDistance = Integer.MAX_VALUE;
            int bestStart = -1;
            int bestEnd = -1;

            // 从 pos 开始滑动窗口匹配
            for (int i = pos; i <= fullText.length() - text.length(); i++) {
                String sub = fullText.substring(i, i + text.length());
                int distance = calculateEditDistance(sub, text);

                if (distance < minDistance) {
                    minDistance = distance;
                    bestStart = i;
                    bestEnd = i + text.length() - 1;
                }
            }

            return new int[]{bestStart, bestEnd};
        }

        public static void main(String[] args) {
            String fullText = "Hello, this is a test for substring matching!";
            String text = "subst.ract.";
            int pos = 10;

            int[] result = findClosestMatch(fullText, text, pos);
            if (result[0] != -1) {
                System.out.println("Closest match found from index " + result[0] + " to " + result[1]);
                System.out.println("Matched substring: " + fullText.substring(result[0], result[1] + 1));
            } else {
                System.out.println("No suitable match found.");
            }
        }
    }
}
