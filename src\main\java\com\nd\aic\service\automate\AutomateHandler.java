package com.nd.aic.service.automate;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.basic.PerformancePlan;
import com.nd.aic.entity.basic.TeachingEvent;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.entity.flow.AutomateTransactionTemporary;
import com.nd.aic.pojo.automate.GptPerformance;
import com.nd.aic.pojo.automate.GptTeachingEvent;
import com.nd.aic.pojo.automate.GptVariablePerformances;
import com.nd.aic.pojo.automate.GptVariableTeachingEvent;
import com.nd.aic.pojo.dto.PlotInteractionDTO;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.basic.PerformancePlanService;
import com.nd.aic.service.basic.TeachingEventService;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.JsonUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.util.WafJsonMapper;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@Component
public class AutomateHandler {

    // 文字稿转节目单
    private static final String GPT_KEY_TEXT2EVENT = "fastgpt-aPS9uO3EDvO66tsVfSQslmRufsUsqh4ZExr2Y9icH2vwMviOsF4ZUrJEpH";
    // 节目单转批注
    private static final String GPT_KEY_DETAIL2EVENT = "fastgpt-oWUwaVdQCwmyfPdXSHchjKVWt6AFgRiftWha4QSFkVGJqBQ7kulPtGeJ";
    // 节目单转表演方案
    private static final String GPT_KEY_EVENT2PERFORM = "fastgpt-rfVJDuDlQqRmqWr58R3S036qME5uNVXiLSN2EX2NI7wUOGmOXPAP7cpQE5eIbuOrz";

    private final MediaService mediaService;
    private final NdrService ndrService;
    private final FastGptRetryService fastGptRetryService;

    private final TeachingEventService teachingEventService;
    private final PerformancePlanService performancePlanService;

    public List<AutomatePerformanceDetail> disassembleTeachingActivity(AutomateTransaction automateTransaction, String manuscript) {
        List<GptTeachingEvent> gptTeachings = callDisassembleTeachingActivity(manuscript, automateTransaction.getProductionLine());
        String teachingActivitiesContent = JsonUtils.toJson(gptTeachings);
        log.info("teachingActivitiesContent: {}", teachingActivitiesContent);
        if (null == automateTransaction.getTemporary()) {
            automateTransaction.setTemporary(new AutomateTransactionTemporary());
        }
        automateTransaction.getTemporary().setGptTeachings(gptTeachings);

        Function<String, List<AutomatePerformanceDetail>> applyFunc = (String data) -> {
            try {
                log.info("performancePlanContent: {}", data);
                List<GptPerformance> performanceParticles = JsonUtils.parseList(data, GptPerformance.class);
                if (null == automateTransaction.getTemporary()) {
                    automateTransaction.setTemporary(new AutomateTransactionTemporary());
                }
                automateTransaction.getTemporary().setGptPerformances(performanceParticles);

                return ObjectConverter.performanceParticle2DetailList(performanceParticles);
            } catch (Exception e) {
                log.error("GPT生成内容异常", e);
                throw WafI18NException.of("GPT_GENERATE_ERROR", "GPT生成内容异常", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        };
        String event2PerformanceVariable = event2PerformanceVariable(automateTransaction, ObjectConverter.eventParticle2DetailList(gptTeachings), automateTransaction.getCharacterResource());
        return relatedPerformancePlan(teachingActivitiesContent, null, event2PerformanceVariable, applyFunc);
    }

    public List<GptTeachingEvent> callDisassembleTeachingActivity(String texts, String productionLine) {
        if (StringUtils.isBlank(texts)) {
            return null;
        }

        Function<String, List<GptTeachingEvent>> applyFunc = (String data) -> {
            try {
                return JsonUtils.parseList(data, GptTeachingEvent.class);
            } catch (Exception e) {
                log.error("GPT拆解教学节目单异常", e);
                // if (texts.length() < 10) {
                //     throw WafI18NException.of("BAD_ARGUMENT", "请提供具体的文字稿内容，我将根据内容进行拆解和批注。", HttpStatus.BAD_REQUEST);
                // }
                // throw WafI18NException.of("BAD_ARGUMENT", data, HttpStatus.BAD_REQUEST);
                GptTeachingEvent gptTeachingEvent = new GptTeachingEvent();
                gptTeachingEvent.setTeachingEvent("讲授新知");
                gptTeachingEvent.setDialogue(texts);
                return Lists.newArrayList(gptTeachingEvent);
            }
        };

        Map<String, Object> variables = Maps.newHashMap();
        variables.put("teaching_events", activity2EventVariable(productionLine));
        return fastGptRetryService.fastGptCallForJsonResult(texts, GPT_KEY_TEXT2EVENT, variables, applyFunc);
    }

    @SneakyThrows
    public List<AutomatePerformanceDetail> annotateTeachingEvents(AutomateTransaction automateTransaction) {
        List<AutomatePerformanceDetail> detailList = automateTransaction.getDetailList();
        List<Map<String, Object>> gptDetailList = detailList.stream().map(e -> {
            Map<String, Object> detail = Maps.newHashMap();
            detail.put("dialogue", e.getDialogue());
            detail.put("teach_event", e.getTeachingEvent());
            return detail;
        }).collect(Collectors.toList());

        Function<String, List<AutomatePerformanceDetail>> applyFunc = (String data) -> {
            try {
                List<GptTeachingEvent> eventParticles = JsonUtils.parseList(data, GptTeachingEvent.class);
                List<AutomatePerformanceDetail> details = ObjectConverter.eventParticle2DetailList(eventParticles);
                if (details.size() != detailList.size()) {
                    throw WafI18NException.of("GPT_GENERATE_ERROR", "GPT生成内容长度与用户输入不相等", HttpStatus.INTERNAL_SERVER_ERROR);
                }
                for (int i = 0; i < details.size(); i++) {
                    details.get(i).setUuid(detailList.get(i).getUuid());
                }
                return details;
            } catch (Exception e) {
                log.error("GPT生成内容异常", e);
                throw WafI18NException.of("GPT_GENERATE_ERROR", "GPT生成内容异常", HttpStatus.INTERNAL_SERVER_ERROR, e);
            }
        };

        return fastGptAnnotateTeachingEvents(WafJsonMapper.toJson(gptDetailList), automateTransaction.getProductionLine(), GPT_KEY_DETAIL2EVENT, applyFunc);
    }

    private List<AutomatePerformanceDetail> fastGptAnnotateTeachingEvents(String texts, String productionLine, String fastGptKey, Function<String, List<AutomatePerformanceDetail>> applyFunc) {
        if (texts == null || fastGptKey == null) {
            return null;
        }
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("teaching_events", activity2EventVariable(productionLine));
        return fastGptRetryService.fastGptCallForJsonResult(texts, GPT_KEY_DETAIL2EVENT, variables, applyFunc);
    }

    @SneakyThrows
    public List<AutomatePerformanceDetail> relatedPerformancePlan(AutomateTransaction automateTransaction, List<AutomatePerformanceDetail> detailList, String characterResource) {
        List<GptTeachingEvent> eventParticles = ObjectConverter.detailList2EventParticles(detailList);
        String performancePlanVariable = event2PerformanceVariable(automateTransaction, detailList, characterResource);
        return relatedPerformancePlan(WafJsonMapper.toJson(eventParticles), GPT_KEY_EVENT2PERFORM, performancePlanVariable, null);
        // List<GptPerformances> performanceParticles = JsonUtils.parseList(performancePlanContent, GptPerformances.class);
        // return performanceParticle2DetailList(performanceParticles);
    }

    private List<AutomatePerformanceDetail> relatedPerformancePlan(String texts, String fastGptKey, String performancePlanVariable, Function<String, List<AutomatePerformanceDetail>> applyFunc) {
        if (StringUtils.isEmpty(fastGptKey)) {
            // AI关联表演方案KEY
            fastGptKey = GPT_KEY_EVENT2PERFORM;
        }

        if (null == applyFunc) {
            applyFunc = (String data) -> {
                List<GptPerformance> performanceParticles = JsonUtils.parseList(data, GptPerformance.class);
                return ObjectConverter.performanceParticle2DetailList(performanceParticles);
            };
        }

        Map<String, Object> variables = Maps.newHashMap();
        variables.put("add_opening", "false");
        if (StringUtils.isNotBlank(performancePlanVariable)) {
            variables.put("performance_plans", performancePlanVariable);
        }
        try {
            return fastGptRetryService.fastGptCallForJsonResult(texts, fastGptKey, variables, applyFunc);
        } catch (Exception e) {
            log.error("GPT生成内容异常", e);
            throw WafI18NException.of("GPT_GENERATE_ERROR", "GPT生成内容异常", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public List<File> images2Videos(List<File> imageFiles) throws IOException, InterruptedException {
        List<File> videoFiles = new ArrayList<>();
        for (File imageFile : imageFiles) {
            if (imageFile.getName().endsWith(".mp4")) {
                videoFiles.add(imageFile);
            } else {
                File videoFile = mediaService.image2Video(imageFile);
                videoFiles.add(videoFile);
            }
        }
        return videoFiles;
    }

    public List<ResourceMetaTiListViewModel> submitVideo2Ndr(String name, List<File> mp4Files) {
        List<ResourceMetaTiListViewModel> resourceMetas = Lists.newArrayList();
        for (int i = 0; i < mp4Files.size(); i++) {
            File mp4File = mp4Files.get(i);
            String title;
            if (mp4Files.size() > 1) {
                title = String.format("%s-%03d", name, i);
            } else {
                title = name;
            }
            long duration = mediaService.durationMills(mp4File);
            ResourceMetaTiListViewModel meta = ndrService.createVideoMeta(title, mp4File, duration);
            resourceMetas.add(meta);
        }
        return resourceMetas;
    }

    /**
     * 将图片转为视频的NDR ID
     * @param imageFile 图片的ID 或者图片的地址
     * @return NDR ID
     */
    public String image2Video(String name,  String imageFile) throws IOException {
            // 生成视频
        try {
            String downloadUrl = ImageUtil.getImageUrl(imageFile);
            File fileInfo = mediaService.download(downloadUrl, ImageUtil.getFileName(imageFile));
            return image2Video(name, fileInfo);
        } catch (Exception e){
            log.error("图片转视频异常", e);
            throw WafI18NException.of("BAD_ARGUMENT", "图片转视频异常", HttpStatus.BAD_REQUEST);
        }

    }

    public String image2Video(String url) {
        try {
            String fileName = ImageUtil.getFileName(url);
            File download = mediaService.download(url, fileName);
            return image2Video(fileName,download);
        } catch (Exception e){
            log.error("图片转视频异常", e);
            throw WafI18NException.of("BAD_ARGUMENT", "图片转视频异常", HttpStatus.BAD_REQUEST);
        }
    }

    public String image2Video(String name,File fileInfo){
        try {
            List<File> mediaInfo = images2Videos(Collections.singletonList(fileInfo));
            List<ResourceMetaTiListViewModel> resInfos = submitVideo2Ndr(name, mediaInfo);
            return resInfos.get(0).getId();
        } catch (Exception e){
            log.error("图片转视频异常", e);
            throw WafI18NException.of("BAD_ARGUMENT", "图片转视频异常", HttpStatus.BAD_REQUEST);
        }
    }

    private String activity2EventVariable(String productionLine) {
        productionLine = StringUtils.defaultString(productionLine, "courseware");
        List<GptVariableTeachingEvent> gptTeachingEvents = Lists.newArrayList();
        List<TeachingEvent> teachingEvents = teachingEventService.findAll();
        final String finalProductionLine = productionLine;
        teachingEvents = teachingEvents.stream().filter(e -> StringUtils.equals(e.getProductionLine(), finalProductionLine)).collect(Collectors.toList());
        for (TeachingEvent teachingEvent : teachingEvents) {
            GptVariableTeachingEvent gptTeachingEvent = new GptVariableTeachingEvent();
            gptTeachingEvent.setTeachingEvent(teachingEvent.getName());
            gptTeachingEvent.setDescription(teachingEvent.getDescription());
            gptTeachingEvent.setManuscript(teachingEvent.getManuscript());
            gptTeachingEvents.add(gptTeachingEvent);
        }

        return JsonUtils.toJson(gptTeachingEvents);
    }

    private String event2PerformanceVariable(AutomateTransaction automateTransaction, List<AutomatePerformanceDetail> detailList, String characterResource) {
        Map<String, GptVariablePerformances> eventMap = Maps.newHashMap();
        // for (AutomatePerformanceDetail detail : detailList) {
        //     String key = detail.getTeachingEvent();
        //     if (StringUtils.isEmpty(key)) {
        //         throw WafI18NException.of("BAD_ARGUMENT", "教学节目单为空", HttpStatus.BAD_REQUEST);
        //     }
        //     if (eventMap.containsKey(key)) {
        //         continue;
        //     }
        //     GptVariablePerformances gptVariablePerformance = new GptVariablePerformances();
        //     // gptVariablePerformance.setTeachingActivity(detail.getTeachingActivity());
        //     gptVariablePerformance.setTeachingEvent(detail.getTeachingEvent());
        //     gptVariablePerformance.setPerformancePlans(Lists.newArrayList());
        //     eventMap.put(key, gptVariablePerformance);
        // }
        List<TeachingEvent> events = teachingEventService.findAll();
        for (TeachingEvent event : events) {
            String key = event.getName();
            if (eventMap.containsKey(key)) {
                continue;
            }
            GptVariablePerformances gptVariablePerformance = new GptVariablePerformances();
            // gptVariablePerformance.setTeachingActivity(detail.getTeachingActivity());
            gptVariablePerformance.setTeachingEvent(event.getName());
            gptVariablePerformance.setPerformancePlans(Lists.newArrayList());
            eventMap.put(key, gptVariablePerformance);
        }

        List<String> eventNames = eventMap.values().stream().map(GptVariablePerformances::getTeachingEvent).collect(Collectors.toList());
        // List<PerformancePlan> performancePlanList = performancePlanService.findByTeachingEvents(eventNames);
        List<PerformancePlan> performancePlanList = performancePlanService.findAll();
        if (null != automateTransaction && CollectionUtils.isNotEmpty(automateTransaction.getAvailablePerformancePlans())) {
            Set<String> availablePerformancePlans = Sets.newHashSet(automateTransaction.getAvailablePerformancePlans());
            performancePlanList.removeIf(e -> !availablePerformancePlans.contains(e.getName()));
        }

        List<GptVariablePerformances> result = Lists.newArrayList();
        for (GptVariablePerformances event : eventMap.values()) {
            for (PerformancePlan performancePlan : performancePlanList) {
                if (performancePlan.getTeachingEvents().contains(event.getTeachingEvent())) {
                    GptVariablePerformances.PP pp = new GptVariablePerformances.PP();
                    pp.setPerformancePlan(performancePlan.getName());
                    pp.setDescription(performancePlan.getDescription());
                    event.getPerformancePlans().add(pp);

                    if (!result.contains(event)) {
                        result.add(event);
                    }
                }
            }
        }
        for (GptVariablePerformances event : result) {
            log.info("{} performance plans: {}", event.getTeachingEvent(), JsonUtils.toJson(event));
        }

        return JsonUtils.toJson(result);
    }

    @SneakyThrows
    public List<String> ppt2VideoResources(String dentryId) {
        Dentry dentry = Dentry.get("null", dentryId, "");
        String pptxUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryId;
        PptHandler pptHandler = ApplicationContextUtil.getApplicationContext().getBean(PptHandler.class);
        List<File> imageFiles = pptHandler.convertPpt2Images(pptxUrl);
        log.info("PPT转图片完成（{}）: {}", dentryId, imageFiles.size());
        List<File> videoFiles = images2Videos(imageFiles);
        log.info("PPT转视频完成（{}）: {}", dentryId, videoFiles.size());
        List<ResourceMetaTiListViewModel> resourceMates = submitVideo2Ndr(dentry.getName(), videoFiles);
        log.info("PPT视频入库完成（{}）: {}", dentryId, resourceMates.size());
        return resourceMates.stream().map(ResourceMetaTiListViewModel::getId).collect(Collectors.toList());
    }

    public List<PlotInteractionDTO> generateInteractions(List<PlotInteractionDTO> dialogues) {
        List<PlotInteractionDTO> plotInteractionDTOS = fastGptRetryService.addInteractionQuestion(dialogues);
        int index = 0;
        for (PlotInteractionDTO plotInteractionDTO : plotInteractionDTOS) {
            if (null == plotInteractionDTO.getInteraction() || StringUtils.isEmpty(plotInteractionDTO.getInteraction().getQuestion())) {
                index++;
                continue;
            }
            plotInteractionDTO.setIndex(index);
        }
        return plotInteractionDTOS;
    }

    public static void main(String[] args) throws IOException {
        String text = FileUtils.readFileToString(new File("D:\\workshop\\脚本\\tmp\\tmp.json"));
        List<AutomatePerformanceDetail> detailList = ObjectConverter.eventParticle2DetailList(JsonUtils.parseList(text, GptTeachingEvent.class));
        System.out.println(JsonUtils.toJson(detailList));
    }
}
