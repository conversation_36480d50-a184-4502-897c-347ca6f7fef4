package com.nd.aic.service.automate;

import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.flow.AutomateTransactionOutcomes;

public interface IAutomateProcessor {

    String productMode();

    void validate(AutomateTransaction automateTransaction);

    void process(AutomateTransaction automateTransaction);

    default void ensureOutcomes(AutomateTransaction automateTransaction) {
        if (null == automateTransaction.getOutcomes()) {
            automateTransaction.setOutcomes(new AutomateTransactionOutcomes());
        }
    }
}
