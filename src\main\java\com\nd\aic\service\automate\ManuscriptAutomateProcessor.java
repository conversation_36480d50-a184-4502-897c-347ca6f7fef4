package com.nd.aic.service.automate;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class ManuscriptAutomateProcessor implements IAutomateProcessor {

    private final AutomateHandler automateHandler;

    @Override
    public String productMode() {
        return "manuscript";
    }

    @Override
    public void validate(AutomateTransaction automateTransaction) {
        if (StringUtils.isBlank(automateTransaction.getFinalManuscript())) {
            throw WafI18NException.of("BAD_REQUEST", "对白稿不能为空", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public void process(AutomateTransaction automateTransaction) {
        // throw new RuntimeException("努力支持中");
        List<AutomatePerformanceDetail> detailList = automateHandler.disassembleTeachingActivity(automateTransaction, automateTransaction.getFinalManuscript());
        ensureOutcomes(automateTransaction);
        automateTransaction.getOutcomes().setDetailList(detailList);
    }

}
