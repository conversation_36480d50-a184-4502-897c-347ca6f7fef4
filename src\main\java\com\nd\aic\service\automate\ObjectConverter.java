package com.nd.aic.service.automate;

import com.google.common.collect.Lists;

import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.pojo.automate.GptPerformance;
import com.nd.aic.pojo.automate.GptTeachingEvent;

import java.util.List;

public class ObjectConverter {

    public static List<AutomatePerformanceDetail> performanceParticle2DetailList(List<GptPerformance> particles) {
        List<AutomatePerformanceDetail> detailList = Lists.newArrayList();
        for (GptPerformance particle : particles) {
            AutomatePerformanceDetail detail = new AutomatePerformanceDetail();
            detail.setTeachingActivity("结合案例学习新知");
            detail.setTeachingEvent(particle.getTeachingEvent());
            detail.setPerformancePlan(particle.getPerformancePlan());
            detail.setDialogue(particle.getDialogue());
            // pp.getPerformanceConfig()
            detail.setMaterial(null);
            detailList.add(detail);
        }
        return detailList;
    }

    public static List<AutomatePerformanceDetail> eventParticle2DetailList(List<GptTeachingEvent> particles) {
        List<AutomatePerformanceDetail> detailList = Lists.newArrayList();
        for (GptTeachingEvent particle : particles) {
            AutomatePerformanceDetail detail = new AutomatePerformanceDetail();
            detail.setTeachingActivity("结合案例学习新知");
            detail.setTeachingEvent(particle.getTeachingEvent());
            detail.setDialogue(particle.getDialogue());
            // pp.getPerformanceConfig()
            detail.setMaterial(null);
            detailList.add(detail);
        }
        return detailList;
    }

    public static List<GptTeachingEvent> detailList2EventParticles(List<AutomatePerformanceDetail> detailList) {
        List<GptTeachingEvent> particles = Lists.newArrayList();

        for (AutomatePerformanceDetail detail : detailList) {
            GptTeachingEvent gptEvent = new GptTeachingEvent();
            gptEvent.setTeachingEvent(detail.getTeachingEvent());
            gptEvent.setDialogue(detail.getDialogue());
            particles.add(gptEvent);
        }
        return particles;
    }

}
