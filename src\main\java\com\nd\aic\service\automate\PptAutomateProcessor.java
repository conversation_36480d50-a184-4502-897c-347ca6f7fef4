package com.nd.aic.service.automate;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.aic.service.AutomateTransactionService;
import com.nd.aic.service.MediaService;
import com.nd.aic.util.OfficeUtils;
import com.nd.gaea.client.http.WafSecurityHttpClient;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@Component
public class PptAutomateProcessor implements IAutomateProcessor {

    private final AutomateHandler automateHandler;
    private final MediaService mediaService;
    private final AutomateTransactionService automateTransactionService;
    private final PptHandler pptHandler;
    private final WafSecurityHttpClient wafSecurityHttpClient = new WafSecurityHttpClient(5000, 300000);

    @Override
    public String productMode() {
        return "ppt";
    }

    @SneakyThrows
    @Override
    public void validate(AutomateTransaction automateTransaction) {
        if (CollectionUtils.isEmpty(automateTransaction.getPptDentryInfos())) {
            throw WafI18NException.of("BAD_REQUEST", "PPT不能为空", HttpStatus.BAD_REQUEST);
        }
        // DentryInfo dentryInfo = automateTransaction.getPptDentryInfos().get(0);
        // String dentryId = dentryInfo.getDentryId();
        // String pptxUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryInfo.getDentryId();
        // File pptxFile = mediaService.download(pptxUrl, String.format("%s.pptx", dentryId), true);
    }

    @Override
    public void process(AutomateTransaction automateTransaction) {
        DentryInfo dentryInfo = automateTransaction.getPptDentryInfos().get(0);
        String dentryId = dentryInfo.getDentryId();
        String name = dentryInfo.getName();
        String pptxUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryInfo.getDentryId();
        try {
            File pptxFile = mediaService.download(pptxUrl, String.format("%s.pptx", dentryId), true);
            List<String> pptxNotes = pptxNotes(pptxFile);
            log.info("PPT备注提取完成（{}）: {}", dentryId, pptxNotes);
            // boolean empty = pptxNotes.stream().allMatch(StringUtils::isBlank);
            // if (empty) {
            //     throw WafI18NException.of("INVALID_PPT", "当前PPT不含对白稿，请参考【PPT制作规范】完善PPT", HttpStatus.BAD_REQUEST);
            // }
            List<File> imageFiles = convertPpt2Images(pptxUrl);
            log.info("PPT转图片完成（{}）: {}", dentryId, imageFiles.size());
            if (imageFiles.size() > 30) {
                automateTransaction.setExpireAt(LocalDateTime.fromDateFields(automateTransaction.getCreateAt()).plusMinutes(imageFiles.size()).toDate());
                automateTransactionService.updateAutomateTransaction(automateTransaction);
            }
            if (imageFiles.size() != pptxNotes.size()) {
                throw WafI18NException.of("INVALID_PPT", "PPT转图片异常", HttpStatus.BAD_REQUEST);
            }
            List<File> videoFiles = automateHandler.images2Videos(imageFiles);
            log.info("PPT转视频完成（{}）: {}", dentryId, videoFiles.size());
            List<ResourceMetaTiListViewModel> resourceMates = automateHandler.submitVideo2Ndr(name, videoFiles);
            log.info("PPT视频入库完成（{}）: {}", dentryId, resourceMates.size());

            List<AutomatePerformanceDetail> detailList = Lists.newArrayList();
            for (int i = 0; i < pptxNotes.size(); i++) {
                boolean isVideo = false;
                String note = pptxNotes.get(i);
                if (imageFiles.get(i).getName().toLowerCase().endsWith(".mp4")) {
                    isVideo = true;
                } else if (StringUtils.isBlank(note)) {
                    List<String> previousContents = pptxNotes.subList(0, i);
                    note = pptHandler.generatePptxTeachingScript(automateTransaction.getTitle(), automateTransaction.getLanguage(), previousContents, Lists.newArrayList(imageFiles.get(i)));
                    pptxNotes.set(i, note);
                }
                String resourceId = resourceMates.get(i).getId();
                AutomatePerformanceDetail detail = pptxPerformanceDetail(note, resourceId, isVideo);
                detailList.add(detail);
            }
            ensureOutcomes(automateTransaction);
            automateTransaction.getOutcomes().setManuscript(String.join("\n", pptxNotes));
            automateTransaction.getOutcomes().setDetailList(detailList);
            log.info("PPT事务处理完成（{}）: {}", dentryId, detailList.size());
        } catch (Exception e) {
            if (e instanceof WafI18NException) {
                throw new RuntimeException(e.getMessage(), e);
            }
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    private AutomatePerformanceDetail pptxPerformanceDetail(String note, String resourceId, boolean video) {
        AutomatePerformanceDetail detail = new AutomatePerformanceDetail();
        detail.setTeachingActivity("结合案例学习新知");
        detail.setTeachingEvent("讲授新知");
        detail.setPerformancePlan(video ? "播放视频" : "板书讲解");
        detail.setDialogue(note);

        Map<String, Object> material = Maps.newHashMap();
        material.put("video", resourceId);
        detail.setMaterial(material);
        return detail;
    }

    public List<File> convertPpt2Images(String pptUrl) throws IOException {
        return pptHandler.convertPpt2Images(pptUrl, true);
    }

    public static List<String> pptxNotes(File pptxFile) throws IOException {
        return OfficeUtils.parsePptxNotes(pptxFile);
    }

    public static void main(String[] args) throws IOException {
        File pptFile = new File("D:\\Users\\Public\\Documents\\imData\\im\\147507@nd\\RecvFile\\AI课件内部开发群_5035763690526692\\入职报到当天培训材料V4.pptx");
        List<String> result = pptxNotes(pptFile);
        for (int i = 0; i < result.size(); i++) {
            log.info("Slide {}: {}", i, result.get(i));
        }
    }
}
