package com.nd.aic.service.automate;

import com.google.common.collect.Maps;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.pojo.automate.PptxTeachingScriptReq;
import com.nd.aic.pojo.automate.PptxTeachingScriptResp;
import com.nd.aic.common.UserHandler;
import com.nd.aic.service.CsService;
import com.nd.aic.service.MediaService;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.gaea.client.http.WafSecurityHttpClient;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@Component
public class PptHandler {

    @Value("${app.pptx.host:http://**************:8085}")
    private String pptxHost;
    @Value("${app.aicTools.host:https://aic-tools.debug.ndaeweb.com/}")
    private String aicAgentHost;
    private final CsService csService;
    private final MediaService mediaService;
    private final AutomateHandler automateHandler;
    private final WafSecurityHttpClient wafSecurityHttpClient = new WafSecurityHttpClient(5000, 300000);

    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 3000))
    public List<File> convertPpt2Images(String pptUrl) throws IOException {
        return convertPpt2Images(pptUrl, false);
    }
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 3000))
    public List<File> convertPpt2Images(String pptUrl, boolean useVideo) throws IOException {
        List<File> imageFiles = new ArrayList<>();
        List<String> duplicateFiles = new ArrayList<>();

        String requestUrl = pptxHost + "/api/tools/presentations/actions/convert2images";
        Map<String, String> body = Maps.newHashMap();
        body.put("file_name", UUID.randomUUID().toString());
        body.put("uri", pptUrl);
        ResponseEntity<byte[]> imageZipEntity = wafSecurityHttpClient.postForEntity(requestUrl, body, byte[].class);
        try (ZipInputStream zis = new ZipInputStream(new ByteArrayInputStream(imageZipEntity.getBody()))) {
            ZipEntry entry;
            String dir = UUID.nameUUIDFromBytes(pptUrl.getBytes()).toString();
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory() && isImage(entry.getName())) {
                    File imageFile = new File(mediaService.getTempDirectory(), String.format("pptx/%s/%s", dir, entry.getName()));
                    Files.createDirectories(imageFile.getParentFile().toPath());
                    Files.copy(zis, imageFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    imageFiles.add(imageFile);
                } else if (useVideo && !entry.isDirectory() && entry.getName().endsWith(".mp4")) {
                    // 同时存在图片和视频，视频文件名与图片文件名相同，只保留视频文件
                    File videoFile = new File(mediaService.getTempDirectory(), String.format("pptx/%s/%s", dir, entry.getName()));
                    Files.createDirectories(videoFile.getParentFile().toPath());
                    Files.copy(zis, videoFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    imageFiles.add(videoFile);
                    duplicateFiles.add(videoFile.getName().replace("_video.mp4", ""));
                }
            }

            // Sort files by name
            imageFiles.sort(Comparator.comparing(File::getName));
        }
        if (useVideo) {
            // Remove duplicate image files
            imageFiles.removeIf(e -> duplicateFiles.contains(FilenameUtils.getBaseName(e.getName())));
        }

        return imageFiles;
    }

    private boolean isImage(String name) {
        name = name.toLowerCase();
        return name.endsWith(".jpeg") || name.endsWith(".jpg") || name.endsWith(".png");
    }

    @SneakyThrows
    public String generatePptxTeachingScript(String title, String language, List<String> previousContents, List<File> imageFiles) {
        PptxTeachingScriptReq pptxTeachingScriptReq = new PptxTeachingScriptReq();
        pptxTeachingScriptReq.setTitle(title);
        pptxTeachingScriptReq.setSlideNumber(previousContents.size() + 1);
        pptxTeachingScriptReq.setPreviousTeachingScripts(previousContents);
        pptxTeachingScriptReq.setImageUrls(new ArrayList<>());
        for (File imageFile : imageFiles) {
            Dentry dentry = csService.uploadFile(Long.valueOf(UserHandler.getUser("0")), "pptx_images", imageFile.getName(), imageFile);
            pptxTeachingScriptReq.getImageUrls().add(String.format("https://gcdncs.101.com/v0.1/static/%s", dentry.getPath()));
        }
        if (StringUtils.isNotEmpty(language)) {
            PptxTeachingScriptReq.PromptSettings promptSettings = new PptxTeachingScriptReq.PromptSettings();
            promptSettings.setLanguage(language);
            pptxTeachingScriptReq.setPromptSettings(promptSettings);
        }
        String url = aicAgentHost + "/v1/presentations/gen_image_teaching_script";
        PptxTeachingScriptResp pptxTeachingScriptResp = wafSecurityHttpClient.postForObject(url, pptxTeachingScriptReq, PptxTeachingScriptResp.class);
        if (CollectionUtils.isEmpty(pptxTeachingScriptResp.getImageTeachingScripts())) {
            throw WafI18NException.of("BAD_REQUEST", "生成PPTX教学脚本失败");
        }
        return pptxTeachingScriptResp.getImageTeachingScripts().get(0);
    }
}
