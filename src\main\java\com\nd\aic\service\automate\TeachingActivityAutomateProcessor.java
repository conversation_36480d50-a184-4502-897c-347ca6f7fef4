package com.nd.aic.service.automate;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.entity.flow.AutomatePerformanceDetail;
import com.nd.aic.entity.flow.AutomateTransactionOutcomes;
import com.nd.aic.pojo.cs.DentryInfo;
import com.nd.aic.pojo.flow.XlsxTeachingActivity;
import com.nd.aic.service.MediaService;
import com.nd.aic.util.XlsxUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@Component
public class TeachingActivityAutomateProcessor implements IAutomateProcessor {

    private final MediaService mediaService;

    @Override
    public String productMode() {
        return "teaching_activity";
    }

    @Override
    public void validate(AutomateTransaction automateTransaction) {
        if (CollectionUtils.isEmpty(automateTransaction.getTeachingActivityDentryInfos())) {
            throw WafI18NException.of("BAD_REQUEST", "教学活动清单不能为空", HttpStatus.BAD_REQUEST);
        }
    }

    @SneakyThrows
    @Override
    public void process(AutomateTransaction automateTransaction) {
        List<AutomatePerformanceDetail> performanceDetails;
        try {
            performanceDetails = parseWorksheet(automateTransaction.getTeachingActivityDentryInfos().get(0));
        } catch (Exception e) {
            log.error("教学活动解析失败", e);
            throw e;
        }
        String manuscript = performanceDetails.stream()
                .map(AutomatePerformanceDetail::getDialogue)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("\n"));
        AutomateTransactionOutcomes outcome = automateTransaction.getOutcomes();
        if (outcome == null) {
            outcome = new AutomateTransactionOutcomes();
            automateTransaction.setOutcomes(outcome);
        }
        outcome.setManuscript(manuscript);
        outcome.setDetailList(performanceDetails);
    }

    public List<AutomatePerformanceDetail> parseWorksheet(DentryInfo dentryInfo) throws IOException {
        String xlsxUrl = "https://cdncs.101.com/v0.1/download?dentryId=" + dentryInfo.getDentryId();
        File xlsxFile = mediaService.download(xlsxUrl, String.format("%d.xlsx", System.currentTimeMillis()));
        List<XlsxTeachingActivity> teachingActivities = XlsxUtils.parseTeachActivityList(xlsxFile);
        FileUtils.deleteQuietly(xlsxFile);

        List<AutomatePerformanceDetail> performanceDetails = Lists.newArrayList();
        for (XlsxTeachingActivity teachingActivity : teachingActivities) {
            AutomatePerformanceDetail performanceDetail = new AutomatePerformanceDetail();

            performanceDetail.setDialogue(teachingActivity.getScript());
            performanceDetail.setTeachingActivity(teachingActivity.getActivity());
            performanceDetail.setTeachingEvent(teachingActivity.getEvent());
            performanceDetail.setPerformancePlan(teachingActivity.getPerformancePlan());
            performanceDetail.setDialogue(StringUtils.isNotBlank(teachingActivity.getScript()) ? teachingActivity.getScript() : null);
            try {
                if (StringUtils.isNotBlank(teachingActivity.getMaterial())) {
                    performanceDetail.setMaterial(JSON.parseObject(teachingActivity.getMaterial()));
                }
            } catch (Exception e) {
                //log.error("param format error", e);
                JSONObject performanceConfig = new JSONObject();
                performanceConfig.put("description", teachingActivity.getMaterial());
                performanceDetail.setMaterial(performanceConfig);
            }
            performanceDetails.add(performanceDetail);
        }

        return performanceDetails;
    }
}
