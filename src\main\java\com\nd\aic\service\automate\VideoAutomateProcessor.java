package com.nd.aic.service.automate;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.client.SpeechTextTimingClient;
import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.client.pojo.asr.AsrTask;
import com.nd.aic.client.pojo.asr.AtaUtterance;
import com.nd.aic.entity.AutomateTransaction;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.MediaService;
import com.nd.gaea.util.WafJsonMapper;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@Component
public class VideoAutomateProcessor implements IAutomateProcessor {

    private final MediaService mediaService;
    private final SpeechTextTimingClient speechTextTimingClient;
    private final FastGptRetryService fastGptRetryService;
    private final ManuscriptAutomateProcessor manuscriptAutomateProcessor;

    @Override
    public String productMode() {
        return "video";
    }

    @Override
    public void validate(AutomateTransaction automateTransaction) {
        if (CollectionUtils.isEmpty(automateTransaction.getVideoDentryInfos())) {
            throw WafI18NException.of("BAD_REQUEST", "复刻视频不能为空", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public void process(AutomateTransaction automateTransaction) {
        if (StringUtils.isEmpty(automateTransaction.getFinalManuscript())) {
            String manuscript = extractText(automateTransaction);
            automateTransaction.setManuscript(manuscript);
        }
        manuscriptAutomateProcessor.process(automateTransaction);
    }

    private String mp3Url(String videoDentryId) throws Exception {
        return mediaService.mp4ToMp3(videoDentryId);
    }

    @SneakyThrows
    public String extractText(AutomateTransaction automateTransaction) {
        log.info("extractText data: {}", WafJsonMapper.toJson(automateTransaction));
        // String fastGptKey = "fastgpt-sUcsfH5OsFAhYDdX4pLUSpIU8GUUOQBVb6Ags24UbtkBr7ILRENbJcm";
        String fastGptKey = null;
        return doExtractText(automateTransaction.getVideoDentryInfos().get(0).getDentryId(), fastGptKey);
    }

    public String doExtractText(String videoDentryId, String fastgptKey) throws Exception {
        String mediaUrl = mp3Url(videoDentryId);
        AsrTask task = speechTextTimingClient.vcSubmit(mediaUrl);
        AtaResult result = speechTextTimingClient.vcQuery(task.getId());
        String texts = result.getUtterances().stream().map(AtaUtterance::getText).collect(Collectors.joining("\n"));
        if (StringUtils.isNotBlank(fastgptKey)) {
            log.info("extractText optimize by fastgpt");
            // GPT文字纠错
            texts = fastGptRetryService.fastGptCall(texts, fastgptKey);
        }
        return texts;
    }
}


