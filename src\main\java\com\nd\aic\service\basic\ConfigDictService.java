package com.nd.aic.service.basic;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.basic.ConfigDict;
import com.nd.aic.entity.basic.TeachingEvent;
import com.nd.aic.repository.basic.ConfigDictRepository;
import com.nd.aic.repository.basic.TeachingEventRepository;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class ConfigDictService extends BaseService<ConfigDict, String> {

    private final ConfigDictRepository configDictRepository;

    @Override
    protected Module module() {
        return new Module("CONFIG_DICT");
    }

    @Override
    public ConfigDict add(ConfigDict configDict) {
        configDict.setCreator(WafContext.getCurrentAccountId());
        configDict.setCreateTime(new Date());
        return super.add(configDict);
    }

    @Override
    public ConfigDict save(ConfigDict configDict) {
        if (StringUtils.isEmpty(configDict.getCreator())) {
            configDict.setCreator(WafContext.getCurrentAccountId());
        }
        if (configDict.getCreator() == null) {
            configDict.setCreateTime(new Date());
        }

        return super.save(configDict);
    }
}
