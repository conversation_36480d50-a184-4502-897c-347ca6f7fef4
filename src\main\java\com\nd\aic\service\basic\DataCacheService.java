package com.nd.aic.service.basic;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.basic.DataCache;
import com.nd.aic.repository.basic.DataCacheRepository;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class DataCacheService extends BaseService<DataCache, String> {

    private final DataCacheRepository dataCacheRepository;

    @Override
    protected Module module() {
        return new Module("DATA_CACHE");
    }

    @Override
    public DataCache save(DataCache dataCache) {
        if (StringUtils.isEmpty(dataCache.getCreator())) {
            dataCache.setCreator(WafContext.getCurrentAccountId());
        }
        if (dataCache.getCreateTime() == null) {
            dataCache.setCreateTime(new Date());
        }
        if (null == dataCache.getExpireTime()) {
            dataCache.setExpireTime(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24 + 7));
        }
        DataCache dbOne = findByKeyAndCategory(dataCache.getKey(), dataCache.getCategory());
        if (null != dbOne) {
            dataCache.setId(dbOne.getId());
        }

        return super.save(dataCache);
    }

    public DataCache findByKeyAndCategory(String key, String category) {
        return dataCacheRepository.findFirstByKeyAndCategory(key, category);
    }
}
