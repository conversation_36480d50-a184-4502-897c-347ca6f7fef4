package com.nd.aic.service.basic;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.basic.PerformanceAddition;
import com.nd.aic.repository.basic.PerformanceAdditionRepository;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class PerformanceAdditionService extends BaseService<PerformanceAddition, String> {

    private final PerformanceAdditionRepository performanceAdditionRepository;

    @Override
    public PerformanceAddition save(PerformanceAddition performanceAddition) {

        if (StringUtils.isBlank(performanceAddition.getId())) {
            PerformanceAddition existed = performanceAdditionRepository.findFirstByNameAndProductionLine(performanceAddition.getName(), performanceAddition.getProductionLine());
            if (existed != null) {
                performanceAddition.setId(existed.getId());
            }
        }
        if (performanceAddition.getCreateTime() == null) {
            performanceAddition.setCreateTime(new Date());
        }
        return super.save(performanceAddition);
    }

    @Override
    protected Module module() {
        return new Module("PerformanceAdditionalService");
    }
}
