package com.nd.aic.service.basic;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.basic.PerformancePlan;
import com.nd.aic.entity.basic.Pronunciation;
import com.nd.aic.repository.basic.PerformancePlanRepository;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class PerformancePlanService extends BaseService<PerformancePlan, String> {

    private final PerformancePlanRepository performancePlanRepository;

    @Override
    protected Module module() {
        return new Module("PERFORMANCE_PLAN");
    }

    @Override
    public PerformancePlan add(PerformancePlan performancePlan) {
        return save(performancePlan);
    }

    @Override
    protected PerformancePlan update(PerformancePlan performancePlan) {
        return save(performancePlan);
    }

    @Override
    public PerformancePlan save(PerformancePlan performancePlan) {
        if (StringUtils.isBlank(performancePlan.getId())) {
            PerformancePlan dbOne = performancePlanRepository.findFirstByNameAndProductionLine(performancePlan.getName(), performancePlan.getProductionLine());
            if (dbOne != null) {
                performancePlan.setId(dbOne.getId());
            }
        }
        if (null == performancePlan.getCreateTime()) {
            performancePlan.setCreateTime(new Date());
        }
        if (performancePlan.getOrderNo() == null) {
            performancePlan.setOrderNo(System.currentTimeMillis());
        }
        return super.save(performancePlan);
    }

    public void deleteAll() {
        performancePlanRepository.deleteAll();
    }

    public List<PerformancePlan> deleteByTeachingEvent(String teachingEvent) {
        List<PerformancePlan> performancePlans = performancePlanRepository.findByTeachingEventsIn(Lists.newArrayList(teachingEvent));
        performancePlanRepository.delete(performancePlans);
        return performancePlans;
    }

    public PerformancePlan findByName(String name) {
        return performancePlanRepository.findFirstByName(name);
    }

    public void patchProductionLine() {
        List<PerformancePlan> performancePlans = findAll();
        for (PerformancePlan performancePlan : performancePlans) {
            boolean update = false;
            if (StringUtils.isEmpty(performancePlan.getProductionLine())) {
                if (StringUtils.isNotEmpty(performancePlan.getProductionLine())) {
                    continue;
                }
                performancePlan.setProductionLine("courseware");
                update = true;
            }
            List<JSONObject> paramInfos = performancePlan.getParamInfo();
            if (CollectionUtils.isNotEmpty(paramInfos)) {
                for (JSONObject paramInfo : paramInfos) {
                    if (StringUtils.isEmpty(paramInfo.getString("id"))) {
                        paramInfo.put("id", UUID.randomUUID().toString());
                        update = true;
                    }
                }
            }
            if (update) {
                performancePlanRepository.save(performancePlan);
            }
        }
    }
}
