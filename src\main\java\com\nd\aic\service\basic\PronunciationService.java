package com.nd.aic.service.basic;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.basic.Pronunciation;
import com.nd.aic.entity.basic.TeachingEvent;
import com.nd.aic.repository.basic.PronunciationRepository;
import com.nd.gaea.rest.support.WafContext;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class PronunciationService extends BaseService<Pronunciation, String> {

    private final PronunciationRepository pronunciationRepository;

    @Override
    protected Module module() {
        return new Module("PRONUNCIATION");
    }

    @Override
    public Pronunciation add(Pronunciation pronunciation) {
        pronunciation.setCreator(WafContext.getCurrentAccountId());
        pronunciation.setCreateTime(new Date());
        return super.add(pronunciation);
    }

    @Override
    public Pronunciation save(Pronunciation pronunciation) {
        if (StringUtils.isEmpty(pronunciation.getCreator())) {
            pronunciation.setCreator(WafContext.getCurrentAccountId());
        }
        if (pronunciation.getCreator() == null) {
            pronunciation.setCreateTime(new Date());
        }
        pronunciationRepository.findFirstByText(pronunciation.getText()).ifPresent(exist -> {
            if (!exist.getId().equals(pronunciation.getId())) {
                throw new IllegalArgumentException("发音文本已存在");
            }
        });

        return super.save(pronunciation);
    }

    public List<Pronunciation> list() {
        List<Pronunciation> pronunciations = findAll();
        // 把pronunciations按元素的text长度降序排序
        pronunciations.sort((o1, o2) -> o2.getText().length() - o1.getText().length());
        return pronunciations;
    }

    public void patchProductionLine() {
        List<Pronunciation> pronunciations = findAll();
        for (Pronunciation pronunciation : pronunciations) {
            if (StringUtils.isEmpty(pronunciation.getProductionLine())) {
                if (StringUtils.isNotEmpty(pronunciation.getProductionLine())) {
                    continue;
                }
                pronunciation.setProductionLine("courseware");
                pronunciationRepository.save(pronunciation);
            }
        }
    }
}
