package com.nd.aic.service.basic;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.basic.PerformancePlan;
import com.nd.aic.entity.basic.TeachingActivity;
import com.nd.aic.repository.basic.TeachingActivityRepository;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class TeachingActivityService extends BaseService<TeachingActivity, String> {

    private final TeachingActivityRepository teachingActivityRepository;

    @Override
    protected Module module() {
        return new Module("TEACHING_ACTIVITY");
    }

    @Override
    public TeachingActivity add(TeachingActivity teachingActivity) {
        return (teachingActivity);
    }

    @Override
    protected TeachingActivity update(TeachingActivity activity) {
        return save(activity);
    }

    @Override
    public TeachingActivity save(TeachingActivity activity) {
        if (StringUtils.isEmpty(activity.getId())) {
            TeachingActivity dbOne = teachingActivityRepository.findFirstByName(activity.getName());
            if (dbOne != null) {
                activity.setId(dbOne.getId());
            }
        }
        if (null == activity.getCreateTime()) {
            activity.setCreateTime(new Date());
        }
        return super.save(activity);
    }

    public void deleteAll() {
        teachingActivityRepository.deleteAll();
    }
}
