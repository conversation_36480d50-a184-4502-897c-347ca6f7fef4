package com.nd.aic.service.basic;

import com.nd.aic.base.domain.Module;
import com.nd.aic.base.service.BaseService;
import com.nd.aic.entity.basic.TeachingEvent;
import com.nd.aic.repository.basic.TeachingEventRepository;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class TeachingEventService extends BaseService<TeachingEvent, String> {

    private final TeachingEventRepository teachingEventRepository;

    @Override
    protected Module module() {
        return new Module("TEACHING_EVENT");
    }

    @Override
    public TeachingEvent add(TeachingEvent teachingEvent) {
        return save(teachingEvent);
    }

    @Override
    protected TeachingEvent update(TeachingEvent teachingEvent) {
        return save(teachingEvent);
    }

    @Override
    public TeachingEvent save(TeachingEvent teachingEvent) {
        if (StringUtils.isEmpty(teachingEvent.getId())) {
            TeachingEvent dbOne = teachingEventRepository.findFirstByName(teachingEvent.getName());
            if (dbOne != null) {
                teachingEvent.setId(dbOne.getId());
            }
        }
        if (null == teachingEvent.getCreateTime()) {
            teachingEvent.setCreateTime(new Date());
        }
        return super.save(teachingEvent);
    }

    // public List<TeachingEvent> deleteByTeachingActivity(String teachingActivity) {
    //     List<TeachingEvent> teachingEvents = teachingEventRepository.findByTeachingActivities(Lists.newArrayList(teachingActivity));
    //     teachingEventRepository.delete(teachingEvents);
    //     return teachingEvents;
    // }
    //
    // public void deleteAll() {
    //     teachingEventRepository.deleteAll();
    // }
    public void patchProductionLine() {
        List<TeachingEvent> teachingEventList = findAll();
        for (TeachingEvent teachingEvent : teachingEventList) {
            if (StringUtils.isEmpty(teachingEvent.getProductionLine())) {
                if (StringUtils.isNotEmpty(teachingEvent.getProductionLine())) {
                    continue;
                }
                teachingEvent.setProductionLine("courseware");
                teachingEventRepository.save(teachingEvent);
            }
        }
    }
}
