package com.nd.aic.service.event;

import com.nd.aic.enums.PerformanceAdditionTypeEnum;
import com.nd.aic.enums.PerformanceTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.CoursewareResourceDTO;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.PerformanceAdditionHandler;
import com.nd.aic.service.performance.PerformanceHandler;
import com.nd.aic.service.performance.param.AnimationTransitionParam;
import com.nd.aic.util.AtaWordUtil;
import com.nd.aic.util.CoursewareModelHelper;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class BaseTeachEventTypeProcessor implements TeachEventTypeProcessor {

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void process(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context, CoursewareModel coursewareModel) {
        PerformanceTypeEnum performanceType = PerformanceTypeEnum.getByNameAndAlias(teachEventType.getPerformanceType(), teachEventType.getPerformanceTypeAlias());
        Object handler = applicationContext.getBean(performanceType.getHandlerCls());
        if (handler instanceof PerformanceHandler) {
            PerformanceHandler performanceHandler = (PerformanceHandler) handler;
            // 选择出生点
            selectPerformanceBirthPoint(teachEventType.getTime(), performanceHandler, coursewareModel, context);
            // 先准备资源
            performanceHandler.prepare(teachEventType.getTime(), teachEventType.getEndTime(), teachEventType, coursewareModel, context);
            // 再应用资源
            performanceHandler.apply(teachEventType.getTime(), teachEventType.getEndTime(), teachEventType, coursewareModel, context);
        }
        // 增强表演
        List<PerformanceAdditionDTO> additions = teachEventType.getPerformanceAdditional();
        if (CollectionUtils.isNotEmpty(additions) && StringUtils.isNotBlank(teachEventType.getDialogue())) {
            for (PerformanceAdditionDTO performanceAdditionDTO : additions) {
                PerformanceAdditionTypeEnum performanceAdditionType = PerformanceAdditionTypeEnum.getByName(performanceAdditionDTO.getName());
                if (performanceAdditionType == null) {
                    continue;
                }
                Object additionHandler = applicationContext.getBean(performanceAdditionType.getHandlerCls());
                if (additionHandler instanceof PerformanceAdditionHandler) {
                    PerformanceAdditionHandler performanceAdditionHandler = (PerformanceAdditionHandler) additionHandler;
                    Integer startPos = performanceAdditionDTO.getStartPos();
                    Integer endPos = performanceAdditionDTO.getEndPos();
                    if (startPos == null || startPos < 0) {
                        continue;
                    }
                    Integer startTime = AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + startPos, true);
                    Integer endTime = null;
                    if (endPos != null) {
                        endTime = AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + endPos - 1, false);
                    }
                    performanceAdditionHandler.apply(startTime, endTime, performanceAdditionDTO, coursewareModel, context);
                }
            }
        }

        postProcess(teachEventType, context, coursewareModel);
    }

    /**
     * 选择出生点
     */
    protected void selectPerformanceBirthPoint(String time, PerformanceHandler performanceHandler, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean requireBoardPoint = performanceHandler.requireBoardPoint();
        // 当前出生点
        BirthPointSegment birthPointSegment = context.getCurrentBirthPoint();
        if (birthPointSegment == null && !requireBoardPoint) {
            return;
        }
        if (birthPointSegment != null && birthPointSegment.isBoardPoint() == requireBoardPoint) {
            return;
        }
        CoursewareModelHelper.randomChangesScenePosition(time, requireBoardPoint, coursewareModel, context);
        // CC4 角色特殊处理
        if (context.getCurrentNpcCharacter() != null && !context.getCurrentNpcCharacter().hasLocation()) {
            BirthPointSegment birthPoint = context.getCurrentBirthPoint();
            if (birthPoint != null) {
                context.getCurrentNpcCharacter().getCustom().putAll(birthPoint.cloneTransform());
            }
        }
    }

    protected void postProcess(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context, CoursewareModel coursewareModel) {
        AnimationTransitionParam performanceParam = teachEventType.getPerformanceParam(AnimationTransitionParam.class);
        boolean transition = BooleanUtils.isTrue(performanceParam.getTransition());
        // 增加转场
        if (transition) {
            if (StringUtils.equals(performanceParam.getTransitionName(), AnimationTransitionParam.CLOUD_MIST_TRANSITION_NAME)) {
                LottieSegment lottieSegment = SegmentUtil.createCloudMistTransitionLottieSegment(teachEventType.getEndTime());
                coursewareModel.getLottieSegment().add(lottieSegment);
            } else {
                addTransitionEffect(performanceParam.getTransitionResource(), teachEventType.getEndTime(), context, coursewareModel);
            }
        }
    }

    protected void addTransitionEffect(String transitionResource, String time, CoursewareGenerationContext context, CoursewareModel coursewareModel) {
        if (CollectionUtils.isEmpty(context.getCoursewareResourceConfig().getTransitionAnimations())) {
            coursewareModel.getSceneChangeSegment().add(SegmentUtil.randomCreateTransitionEffect(time));
            return;
        }
        Map<String, CoursewareResourceDTO> transitionMap = context.getCoursewareResourceConfig()
                .getTransitionAnimations()
                .stream()
                .collect(Collectors.toMap(CoursewareResourceDTO::getId, Function.identity()));
        if (StringUtils.isNotEmpty(transitionResource)) {
            CoursewareResourceDTO transitionAnimation = transitionMap.get(transitionResource);
            if (transitionAnimation != null) {
                coursewareModel.getSceneChangeSegment()
                        .add(SegmentUtil.createGeneralSceneChangeSegment(time, transitionAnimation.getId(), transitionAnimation.getName(), 2, 1));
                return;
            }
        }

        coursewareModel.getSceneChangeSegment().add(SegmentUtil.randomCreateTransitionEffect(time));
    }
}
