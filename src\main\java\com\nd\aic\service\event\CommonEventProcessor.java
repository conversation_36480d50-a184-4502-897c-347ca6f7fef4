package com.nd.aic.service.event;

import com.nd.aic.enums.EnhancePerformanceTypeEnum;
import com.nd.aic.enums.TeachEventTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceEnhanceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.performance.enhance.AbstractEnhancePerformanceHandler;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class CommonEventProcessor extends BaseTeachEventTypeProcessor {

    private ApplicationContext applicationContext;

    @Autowired
    public CommonEventProcessor(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public boolean isSupport(TeachEventTypeEnum teachEventTypeEnum) {
        return TeachEventTypeEnum.COMMON.equals(teachEventTypeEnum);
    }

    @Override
    protected void postProcess(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context, CoursewareModel coursewareModel) {
        super.postProcess(teachEventType, context, coursewareModel);
        List<PerformanceEnhanceDTO> performanceEnhances = teachEventType.getPerformanceEnhances();
        if (CollectionUtils.isNotEmpty(performanceEnhances)) {
            for (PerformanceEnhanceDTO performanceEnhance : performanceEnhances) {
                EnhancePerformanceTypeEnum enhanceType = EnhancePerformanceTypeEnum.getByName(performanceEnhance.getEnhanceType(), performanceEnhance.getEnhanceName());
                if (null != enhanceType) {
                    AbstractEnhancePerformanceHandler enhanceHandler = applicationContext.getBean(enhanceType.getHandlerCls());
                    enhanceHandler.enhance(teachEventType, performanceEnhance, coursewareModel, context);
                }
            }
        }
    }
}
