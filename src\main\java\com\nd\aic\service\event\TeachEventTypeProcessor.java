package com.nd.aic.service.event;

import com.nd.aic.enums.TeachEventTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;

public interface TeachEventTypeProcessor {

    boolean isSupport(TeachEventTypeEnum teachEventTypeEnum);

    void process(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context, CoursewareModel coursewareModel);

}
