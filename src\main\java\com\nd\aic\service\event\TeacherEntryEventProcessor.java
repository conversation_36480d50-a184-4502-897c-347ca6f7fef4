package com.nd.aic.service.event;

import com.nd.aic.enums.TeachEventTypeEnum;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class TeacherEntryEventProcessor extends BaseTeachEventTypeProcessor {

    @Override
    public boolean isSupport(TeachEventTypeEnum teachEventTypeEnum) {
        return TeachEventTypeEnum.TEACHER_ENTRY.equals(teachEventTypeEnum);
    }

}
