package com.nd.aic.service.material;

import com.google.common.base.Strings;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.material.entity.MediaResInfo;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

/**
 * 媒体资源推荐
 */
@AllArgsConstructor
@Service
public class MediaAssembleService {

    private final FastGptRetryService fastGptService;

    /**
     * 根据参数获取资源详情
     * @param teachEventType 教学事件类型
     * @param context 上下文
     * @return 资源列表
     */
    public MediaResInfo getMedia(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {

        MediaResInfo mediaResInfo = new MediaResInfo();

        if(teachEventType == null) return null;

        // 如果投料的里面有这个数据，则直接从输入中获取
        if (teachEventType.getPerformanceConfig() != null && !Strings.isNullOrEmpty(teachEventType.getPerformanceConfig().getVideoId())) {
            mediaResInfo.setResourceId(teachEventType.getPerformanceConfig().getVideoId());
            mediaResInfo.setDuration(teachEventType.getPerformanceConfig().getVideoDuration());
            mediaResInfo.setReason("使用参数指定的资源");
            return mediaResInfo;
        }

        return null;
    }


}
