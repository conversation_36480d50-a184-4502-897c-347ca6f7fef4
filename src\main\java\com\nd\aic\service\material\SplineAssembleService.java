package com.nd.aic.service.material;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.material.entity.SplineResInfo;
import org.springframework.stereotype.Service;

@Service
public class SplineAssembleService {


    /**
     *
     * @param context
     * @param teachEventTypeDTO
     * @return
     */
    public SplineResInfo findSplineByContext(CoursewareGenerationContext context, TeachEventTypeDTO teachEventTypeDTO){

        SplineResInfo splineResInfo = new SplineResInfo();

         if( "a1c04877-67b8-4530-8c90-c93399f710be".equals(context.getCurrentSceneId())){
             splineResInfo.setResourceId("84c2573e-94cd-4d9b-809d-16d51bf83d13");
             splineResInfo.setReason("卡通城样条线");
             return splineResInfo;
         } else if ("94290eb2-c891-4664-bafd-fca2224b67d9".equals(context.getCurrentSceneId())){
             splineResInfo.setResourceId("995f6ae3-25f1-48ca-8e3e-6479004f57e1");
                splineResInfo.setReason("乐高漫威大楼样条线");
             return splineResInfo;
         }

         return null;

    }



}
