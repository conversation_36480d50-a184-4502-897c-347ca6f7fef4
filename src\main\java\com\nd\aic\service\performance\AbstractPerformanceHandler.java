package com.nd.aic.service.performance;

import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.enums.ShotSizeEnum;
import com.nd.aic.pojo.AtaUtteranceWithPos;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.SentenceTiming;
import com.nd.aic.pojo.common.Rotation;
import com.nd.aic.pojo.common.Transform;
import com.nd.aic.pojo.common.Vector3D;
import com.nd.aic.pojo.dto.CameraResourceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.CameraFeatureSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.CharacterSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.util.ActionUtil;
import com.nd.aic.util.AtaWordUtil;
import com.nd.aic.util.CameraUtil;
import com.nd.aic.util.DeepCopyUtils;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public abstract class AbstractPerformanceHandler implements PerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        long startMs = TimeUtils.convertToMilliseconds(time);
        long endMs = TimeUtils.convertToMilliseconds(endTime);
        randomGenerateCameraSegment(startMs, endMs, coursewareModel, context);
        randomGenerateActionSegment(startMs, endMs, teachEventType.getDialogue(), teachEventType.getDialogueStartPos(), coursewareModel, context);
    }

    /**
     * 随机生成角色镜头
     */
    protected void randomGenerateCameraSegment(long startMs, long endMs, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        randomGenerateCameraSegment(startMs, endMs, false, false, false, coursewareModel, context);
    }

    /**
     * 随机生成角色镜头
     */
    protected void randomGenerateCameraSegment(String time, String endTime, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        randomGenerateCameraSegment(TimeUtils.convertToMilliseconds(time), TimeUtils.convertToMilliseconds(endTime), false, false, false, coursewareModel, context);
    }

    /**
     * 随机生成板书镜头
     */
    protected void randomGenerateBoardCameraSegment(String time, String endTime, boolean isOpening, boolean isEnding, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        randomGenerateCameraSegment(TimeUtils.convertToMilliseconds(time), TimeUtils.convertToMilliseconds(endTime), true, isOpening, isEnding, coursewareModel, context);
    }

    /**
     * 随机生成镜头
     */
    private void randomGenerateCameraSegment(long startMs, long endMs, boolean board, boolean isOpening, boolean isEnding, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        List<CameraResourceDTO> cameras = board ? context.getCoursewareResourceConfig()
                .getBoardCharacterCameras() : context.getCoursewareResourceConfig().getCharacterCameras();
        int minSplit = board ? 7000 : 5000;
        int maxSplit = board ? 15000 : 10000;
        // 对白点
        List<Long> dialogueTimes = context.getAtaUtterances()
                .stream()
                .map(AtaUtteranceWithPos::getEndTime)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<CameraSegment> randomCameras = CameraUtil.genCameraSegments(startMs, endMs, board, minSplit, maxSplit, cameras, dialogueTimes, coursewareModel.getCameraSegment(), isOpening, isEnding);
        // List<CameraSegment> randomCameras = PerformanceUtil.randomCreateCameras(startMs, endMs, minSplit, maxSplit, cameras, isOpening, isEnding, context.getCurrentCameraId(), null);
        if (CollectionUtils.isNotEmpty(randomCameras)) {
            for (CameraSegment randomCamera : randomCameras) {
                boolean isLongShot = StringUtils.equalsIgnoreCase(ShotSizeEnum.LONG_SHOT.getName(), randomCamera.getShotSize()) || StringUtils.equalsIgnoreCase(ShotSizeEnum.EXTREME_LONG_SHOT.getName(), randomCamera.getShotSize());
                if (board && StringUtils.isNotBlank(context.getCurrentBoardInstanceId()) && isLongShot) {
                    randomCamera.targetId(context.getCurrentBoardInstanceId());
                } else if (context.getCharacterInstanceId() != null) {
                    randomCamera.targetId(context.getCharacterInstanceId());
                }
            }
            // 看向镜头
            List<CameraFeatureSegment> cameraFeatureSegments = CameraUtil.createLookAtFeature(randomCameras);
            cameraFeatureSegments.forEach(cameraFeatureSegment -> {
                if (StringUtils.isNotBlank(context.getCharacterInstanceId())) {
                    cameraFeatureSegment.targetId(context.getCharacterInstanceId());
                }
            });
            coursewareModel.getCameraFeatureSegment().addAll(cameraFeatureSegments);
            coursewareModel.getCameraSegment().addAll(randomCameras);
            context.setCurrentCameraId(randomCameras.get(randomCameras.size() - 1).getResourceId());
        }
    }

    /**
     * 随机生成角色动作
     */
    protected void randomGenerateActionSegment(String time, String endTime, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        randomGenerateActionSegment(TimeUtils.convertToMilliseconds(time), TimeUtils.convertToMilliseconds(endTime), null, 0, coursewareModel, context);
    }

    /**
     * 随机生成角色动作
     */
    protected void randomGenerateActionSegment(long startMs, long endMs, String text, Integer textStartPos, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        if (endMs <= startMs) {
            return;
        }
        CharacterEnum currentCharacter = CharacterEnum.getCharacterById(context.getCurrentCharacterResId());
        if (BooleanUtils.isTrue(context.getNewActionEnabled()) && textStartPos != null && CharacterEnum.PIXAR.equals(currentCharacter) && StringUtils.isNotBlank(currentCharacter.getNewActionKey())) {
            List<SentenceTiming> sentenceTimings = AtaWordUtil.splitTextAndCalculateTiming(text, textStartPos, context.getAtaWords());
            coursewareModel.getActionSegment().addAll(ActionUtil.genActionSegments(sentenceTimings, coursewareModel.getActionSegment(), context.getCompatibleActions(true)));
        } else {
            if (context.getCurrentNpcCharacter() != null) {
                List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(startMs, endMs, 8000, 12000, context.getCompatibleActions());
                if (CollectionUtils.isNotEmpty(randomActions) && context.getCharacterInstanceId() != null) {
                    for (RoutineSegment randomAction : randomActions) {
                        randomAction.targetId(context.getCharacterInstanceId());
                    }
                }
                coursewareModel.getRoutineSegment().addAll(randomActions);
            } else {
                List<ActionSegment> randomActions = PerformanceUtil.randomCreateActions(startMs, endMs, 5000, 10000, context.getCompatibleActions());
                coursewareModel.getActionSegment().addAll(randomActions);
            }
        }
    }

    /**
     * 根据上下文获取当前的坐标参数，如果没有，则返回一个朝向是90度的0点坐标
     *
     * @param context 课件生成上下文
     */
    protected Transform getPerformTransform(CoursewareGenerationContext context, Double yaw) {

        Transform transform = new Transform();
        BirthPointSegment birthPointSegment = context.getCurrentBirthPoint();
        if (birthPointSegment != null && birthPointSegment.getCustom() != null) {
            transform.setPosition(new Vector3D().x(birthPointSegment.getCustom().getJSONObject("location").getDouble("x"))
                    .y(birthPointSegment.getCustom().getJSONObject("location").getDouble("y"))
                    .z(birthPointSegment.getCustom().getJSONObject("location").getDouble("z")));

        } else {
            transform.setPosition(new Vector3D().x(0).y(0).z(0));
            transform.setRotation(new Rotation().pitch(0).yaw(yaw == null ? 80 : yaw).roll(0));
            transform.setScale(new Vector3D().x(0).y(0).z(80));
        }
        return transform;
    }

    /**
     * 通过引用的文本，计算出引用的时间
     *
     * @param aliasText      引用的文本
     * @param context        课件生成上下文
     * @param teachEventType 教学事件类型
     * @return 时间区间 0 开始时间，1 结束时间
     */
    protected String[] getTimeAreaByAliasTexts(String aliasText, CoursewareGenerationContext context, TeachEventTypeDTO teachEventType) {
        if (StringUtils.isNotEmpty(aliasText)) {
            // 处理时间问题
            int index = StringUtils.indexOf(teachEventType.getDialogue(), aliasText);
            if (index != -1) {
                String lottieTime = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index, true));
                String lottieEndTime = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index + aliasText.length(), false));
                return new String[]{lottieTime, lottieEndTime};
            }
        }
        return new String[]{teachEventType.getTime(), teachEventType.getEndTime()};
    }

    protected void hideCharacter(String time, String endTime, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        if (CollectionUtils.isNotEmpty(coursewareModel.getCharacterSegment())) {
            // get the last character
            CharacterSegment segment = coursewareModel.getCharacterSegment().get(coursewareModel.getCharacterSegment().size() - 1);
            CharacterSegment hideSegment = DeepCopyUtils.copy(segment, CharacterSegment.class);
            if (hideSegment == null) {
                log.error("Copy from character segment error");
                return;
            }
            hideSegment.setType("hide");
            hideSegment.setTime(time);
            hideSegment.setEndTime(endTime);
            coursewareModel.getCharacterSegment().add(hideSegment);

            CharacterSegment showSegment = DeepCopyUtils.copy(segment, CharacterSegment.class);
            if (showSegment == null) {
                log.error("Copy from character segment error");
                return;
            }
            showSegment.setTime(endTime);
            showSegment.setEndTime(null);
            showSegment.setType("show");
            coursewareModel.getCharacterSegment().add(showSegment);
        }
    }
}
