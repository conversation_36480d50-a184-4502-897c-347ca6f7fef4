package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.ActionResourceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.AdmissionParam;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
/**
 * 入场
 */
@AllArgsConstructor
@Service
public class AdmissionHandler extends AbstractPerformanceHandler{
    /**
     * 大套路资源ID
     */
    private static final String BIG_ROUTINE_RES_ID = "74b9841c-2c58-4af9-b1d2-1a99fb6d7796";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        //判断是否是白猫角色
        boolean useWhiteCat = CharacterUtils.isWhiteCat(context);
        boolean useNeon = CharacterUtils.isNeon(context);
        boolean useNeonNew = CharacterUtils.isNeonNew(context);

        if (useWhiteCat){
            AdmissionParam performanceParam = teachEventType.getPerformanceParam(AdmissionParam.class);

            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "入场");
            routineSegment.targetId("");
            if (StringUtils.isNotBlank(performanceParam.getImage())){
                routineSegment.addProperty("ImgName","ImgSeq",performanceParam.getImage());
                routineSegment.dependency(performanceParam.getImage());
            }
            routineSegment.bindObject("TestRole", context.getCharacterInstanceId());

            coursewareModel.getRoutineSegment().add(routineSegment);
        } else if (useNeon) {
            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, "0ae9a937-d360-48d6-a03a-c9681466b850", "入场(CHIBI)");
            routineSegment.targetId("");
            routineSegment.putParam("OriginDuration", 11.7).putParam("ParamVersion", 1).putParam("StopAllSkill", false).putParam("PauseAtEnd", true);
            coursewareModel.getRoutineSegment().add(routineSegment);

            long startMs = TimeUtils.convertToMilliseconds(time);
            long endMs = TimeUtils.convertToMilliseconds(endTime);
            List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(startMs, endMs, 8000, 12000, context.getCompatibleActions());
            coursewareModel.getRoutineSegment().addAll(randomActions);
        } else if (useNeonNew) {
            // neon:c4a261c6-3518-47c8-ac89-4b74e2f80579
            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, "c4a261c6-3518-47c8-ac89-4b74e2f80579", "入场（NEON）");
            routineSegment.targetId("");
            routineSegment.bindObject("char", context.getCharacterInstanceId());
            routineSegment.putParam("OriginDuration", 11.7).putParam("ParamVersion", 1).putParam("StopAllSkill", false).putParam("PauseAtEnd", false);
            coursewareModel.getRoutineSegment().add(routineSegment);

            List<ActionResourceDTO> actions = context.getCompatibleActions().stream().filter(action -> StringUtils.equalsIgnoreCase(action.getType(), "idle")).collect(Collectors.toList());
            List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(TimeUtils.convertToMilliseconds(time), TimeUtils.convertToMilliseconds(endTime), 8000, 12000, actions, context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().addAll(randomActions);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

}
