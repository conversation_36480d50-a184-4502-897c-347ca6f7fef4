package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.common.Transform;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.AppearBookParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Collections;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 变成书本
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppearBookHandler extends AbstractPerformanceHandler {

    private final MediaService mediaService;
    private final AutomateHandler automateHandler;

    private static final String BIG_ROUTINE_RES_ID = "c91fba35-e6f9-44e5-8ccc-66c81430dacb";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        AppearBookParam appearBookParam = prepareParams(teachEventType, context);
        String videoResId = appearBookParam.getVideo();
        if (StringUtils.isBlank(videoResId)) {
            log.warn("视频资源ID为空，无法生成抛出书本动作， 退化为普通讲解");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "抛出书本").playType(0);
        routineSegment.setParamVersion(1);
        routineSegment.addProperty("Texture-Plane-Texture", "Video", videoResId);
        routineSegment.dependency(videoResId);
        coursewareModel.getRoutineSegment().add(routineSegment);

        // 结束后回到原地
        Transform transform = getPerformTransform(context, 78.0);
        BirthPointSegment birthPointSegment = SegmentUtil.createBirthPointSegmentByTransform(endTime, transform);
        coursewareModel.getBirthPointSegment().add(birthPointSegment);
    }

    @SneakyThrows
    private AppearBookParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        AppearBookParam appearBookParam = teachEventType.getPerformanceParam(AppearBookParam.class);
        String videoResId = appearBookParam.getVideo();
        if (StringUtils.isBlank(videoResId) && StringUtils.isNotBlank(appearBookParam.getImage())) {
            // 生成视频
            String downloadUrl = ImageUtil.getImageUrl(appearBookParam.getImage());
            File fileInfo = mediaService.download(downloadUrl, appearBookParam.getImage());
            List<File> mediaInfo = automateHandler.images2Videos(Collections.singletonList(fileInfo));
            List<ResourceMetaTiListViewModel> resInfos = automateHandler.submitVideo2Ndr(context.getTitle() + teachEventType.getTeachEventType(), mediaInfo);
            videoResId = resInfos.get(0).getId();
            appearBookParam.setVideo(videoResId);
        }
        return appearBookParam;
    }
}
