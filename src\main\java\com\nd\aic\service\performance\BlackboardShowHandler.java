package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LightSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.BlackboardShowParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 小黑板展示
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BlackboardShowHandler extends AbstractPerformanceHandler {

    /**
     * 大套路资源ID
     */
    private static final String BIG_ROUTINE_RES_ID = "7f8e0563-73ee-4a0c-8ed7-2207a9a9ac48";

    /**
     * 屏幕特效资源ID
     */
    private static final String SCREEN_EFFECT_RES_ID = "D718A6194AAD491B95F21C83FD99E96A";
    private final AutomateHandler automateHandler;


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        BlackboardShowParam blackboardShowParam = prepareParam(teachEventType, context);
        if (StringUtils.isEmpty(blackboardShowParam.getVideo())) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        // BigRoutine
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "小黑板展示定义");
        routineSegment.playType(0);
        List<JSONObject> properties = Lists.newArrayList(
                new JSONObject()
                        .fluentPut("name", "Texture-Plane-Texture")
                        .fluentPut("type", "Video")
                        .fluentPut("value", blackboardShowParam.getVideo())
        );
        routineSegment.getCustom()
                .fluentPut("Param", new JSONObject().fluentPut("Properties", properties));
        routineSegment.setParamVersion(1);
        routineSegment.setDependencies(Lists.newArrayList(blackboardShowParam.getVideo()));
        coursewareModel.getRoutineSegment().add(routineSegment);
    }

    private BlackboardShowParam prepareParam(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        BlackboardShowParam blackboardShowParam = teachEventType.getPerformanceParam(BlackboardShowParam.class);
        if (StringUtils.isEmpty(blackboardShowParam.getVideo()) && StringUtils.isNotEmpty(blackboardShowParam.getImage())) {
            String video = null;
            try {
                video = automateHandler.image2Video(String.format("%s-%s", context.getTitle(), "小黑板展示定义"), blackboardShowParam.getImage());
            } catch (IOException e) {
                log.error("图片转视频失败", e);
            }
            blackboardShowParam.setVideo(video);
        }
        return blackboardShowParam;
    }
}
