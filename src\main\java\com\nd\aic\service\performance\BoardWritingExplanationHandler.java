package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nd.aic.common.SegmentPerformConstant;
import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.enums.RoutineTypeEnum;
import com.nd.aic.exception.AicException;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneChangeSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.BoardWritingExplanationParam;
import com.nd.aic.util.AtaWordUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 板书讲解
 */
@SuppressWarnings("CommentedOutCode")
@AllArgsConstructor
@Service
@Slf4j
public class BoardWritingExplanationHandler extends AbstractPerformanceHandler {

    /**
     * 板书实例ID
     */
    private static final String BOARD_INSTANCE = "734BE894879845A3B1E0CF996D34C228";

    /**
     * 板书资源ID
     */
    private static final String BOARD_RES_ID = "E48B04B84D41C1CFFFBA8EAE6C775F53";

    private static final String BOARD_MOVE_UP_RES_ID = "2AFBFABA4E001DC076E557A9BEE725D9";

    /**
     * 镜头资源
     */
    private static final String PIP_CAMERA_RES_ID = "9B2646F840BD459B9FB78280C4962E59";

    /**
     * 画中画模板资源ID
     */
    private static final String PIP_RES_ID = "C26D87024D746D97622C3D9941EC03DD";
    private static final String FULL_SCREEN_RES_ID = "C75CC7334A58053880939982C2D0A9C7";
    private static final String SPLIT_SCREEN_RES_ID = "EFC6E53546DF2541E6A8B5BF3CE07FC4";

    /**
     * 动作资源
     */
    // private static final String WALK_LEFT_RESOURCE_ID = "1DE82EFB4C01A3E77F930AA8F64C192A";
    // private static final String WALK_RIGHT_RESOURCE_ID = "40CACFB54B67461BBF30DE980E07071D";
    // private static final double WALK_DURATION = 24.2;

    private final FastGptRetryService fastGptRetryService;
    private final AutomateHandler automateHandler;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        long startMs = TimeUtils.convertToMilliseconds(time);
        long endMs = TimeUtils.convertToMilliseconds(endTime);
        // 创建板书
        JSONObject boardTransform = context.getCurrentBirthPoint() != null ? context.getCurrentBirthPoint().cloneBoardTransform() : null;
        createBoardObject(boardTransform, coursewareModel, context);

        // 板书参数
        BoardWritingExplanationParam param = prepareParams(teachEventType, coursewareModel, context);
        if (StringUtils.isNotBlank(param.getVideo()) && context.getCurrentBoardSegment() != null) {
            context.getCurrentBoardSegment().addVideo(time, endTime, param.getVideo(), 0.2);
            // PPT全屏
            generateBoardCamera(time, endTime, teachEventType, param, coursewareModel, context);
        }

        // 走路动作
        // if (!context.isFirstTeachEventType(teachEventType) && !context.isLastTeachEventType(teachEventType) && context.getCharacterTargetId() != null) {
        //     if (durationSec != null && durationSec > WALK_DURATION * 2) {
        //         RoutineSegment leftActionSegment = SegmentUtil.createActionRoutineSegment(time, WALK_LEFT_RESOURCE_ID, "向左转");
        //         leftActionSegment.targetId(context.getCharacterTargetId());
        //         coursewareModel.getRoutineSegment().add(leftActionSegment);
        //         RoutineSegment rightActionSegment = SegmentUtil.createActionRoutineSegment(time, WALK_RIGHT_RESOURCE_ID, "向右走");
        //         rightActionSegment.targetId(context.getCharacterTargetId());
        //         coursewareModel.getRoutineSegment().add(rightActionSegment);
        //         // 随机生成动作片段
        //         randomGenerateActionSegment(TimeUtils.addMilliseconds(time, (long) WALK_DURATION * 2 * 1000), TimeUtils.convertToMilliseconds(endTime), coursewareModel, context);
        //     } else if (durationSec != null && durationSec > WALK_DURATION) {
        //         RoutineSegment actionSegment = SegmentUtil.createActionRoutineSegment(time, context.isCurrentNpcCharacterInRight() ? WALK_LEFT_RESOURCE_ID : WALK_RIGHT_RESOURCE_ID, context.isCurrentNpcCharacterInRight() ? "向左走" : "向右走");
        //         actionSegment.targetId(context.getCharacterTargetId());
        //         coursewareModel.getRoutineSegment().add(actionSegment);
        //         context.setCurrentNpcCharacterInRight(!context.isCurrentNpcCharacterInRight());
        //         // 随机生成动作片段
        //         randomGenerateActionSegment(TimeUtils.addMilliseconds(time, (long) WALK_DURATION * 1000), TimeUtils.convertToMilliseconds(endTime), coursewareModel, context);
        //     } else {
        //         randomGenerateActionSegment(time, endTime, coursewareModel, context);
        //     }
        // } else {
        //     randomGenerateActionSegment(time, endTime, coursewareModel, context);
        // }

        randomGenerateActionSegment(startMs, endMs, teachEventType.getDialogue(), teachEventType.getDialogueStartPos(), coursewareModel, context);
    }

    /**
     * 准备参数
     *
     * @param teachEventType  教学事件
     * @param coursewareModel 课件模型
     * @param context         课件生成上下文
     * @return 板书讲解参数
     */
    private BoardWritingExplanationParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        // 获取参数
        BoardWritingExplanationParam performanceParam = teachEventType.getPerformanceParam(BoardWritingExplanationParam.class);
        if (StringUtils.isBlank(performanceParam.getVideo())) {
            if (StringUtils.isNotBlank(performanceParam.getImage())) {
                try {
                    String video = automateHandler.image2Video(coursewareModel.getTitle() + teachEventType.getTeachEventType(), performanceParam.getImage());
                    performanceParam.setVideo(video);
                } catch (IOException e) {
                    log.info("通过图片生成视频失败");
                    return performanceParam;
                }
            } else if (CollectionUtils.isNotEmpty(context.getPptVideoResources()) && Objects.nonNull(performanceParam.getPptIndex())) {
                // 通过ppt生成视频
                try {
                    if (context.getPptVideoResources().size() <= performanceParam.getPptIndex()) {
                        throw AicException.ofIllegalArgument("pptIndex超出范围");
                    }
                    String video = context.getPptVideoResources().get(performanceParam.getPptIndex());
                    performanceParam.setVideo(video);
                } catch (Exception e) {
                    log.error("通过ppt生成视频失败", e);
                }
            }
        }
        return performanceParam;
    }

    @SuppressWarnings("AlibabaMethodTooLong")
    public void generateBoardCamera(String time, String endTime, TeachEventTypeDTO teachEventType, BoardWritingExplanationParam param, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        String cameraStartTime = time;
        long startMs = TimeUtils.convertToMilliseconds(time);
        long endMs = TimeUtils.convertToMilliseconds(endTime);
        boolean aigc = false;
        // 显示全屏的内容
        List<String> showPptContents = Lists.newArrayList(param.getShowPpt1(), param.getShowPpt2(), param.getShowPpt3());
        showPptContents = showPptContents.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<Integer> pptTypes = Lists.newArrayList(param.getPptType1(), param.getPptType2(), param.getPptType3());
        if (CollectionUtils.isEmpty(showPptContents)) {
            try {
                JSONArray ticks = fastGptRetryService.askForFullScreenTimeTick(teachEventType.getDialogue());
                if (CollectionUtils.isNotEmpty(ticks)) {
                    showPptContents = ticks.stream().map(tick -> {
                        JSONObject tickJson = (JSONObject) tick;
                        return tickJson.getString("content");
                    }).collect(Collectors.toList());
                }
                aigc = true;
            } catch (Exception e) {
                log.error("askForFullScreenTimeTick error", e);
            }
        }

        // 画中画
        if (CollectionUtils.isNotEmpty(showPptContents)) {
            int startPos = 0;
            for (int i = 0; i < showPptContents.size(); i++) {
                String content = StringUtils.trim(showPptContents.get(i));
                if (StringUtils.isBlank(content)) {
                    continue;
                }
                Integer pptStart;
                Integer pptEnd;
                if (StringUtils.equals(StringUtils.trim(teachEventType.getDialogue()), content)) {
                    pptStart = Math.toIntExact(startMs);
                    pptEnd = Math.toIntExact(endMs);
                } else {
                    int index = StringUtils.indexOf(teachEventType.getDialogue(), content, startPos);
                    if (index == -1) {
                        continue;
                    }
                    pptStart = AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index, true);
                    pptEnd = AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index + content.length(), false);
                }

                if (pptStart != null && pptEnd != null && pptEnd - pptStart > 0) {
                    String pipType = "picinpic";
                    long lastPptEndMs = 0;
                    SegmentUtil.sortByTime(coursewareModel.getSceneChangeSegment());
                    List<SceneChangeSegment> pipSegments = coursewareModel.getSceneChangeSegment()
                            .stream()
                            .filter(item -> StringUtils.equals(item.getType(), pipType))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(pipSegments)) {
                        lastPptEndMs = TimeUtils.convertToMilliseconds(pipSegments.get(pipSegments.size() - 1).getEndTime());
                    }

                    // ai 生成的
                    if (aigc) {
                        // 前7s禁止板书
                        if (pptStart - startMs < 1000 * 7) {
                            continue;
                        }
                        // 后7s禁止板书
                        if (endMs - pptEnd < 1000 * 7) {
                            continue;
                        }
                        // 禁止小于5秒的板书
                        if (pptEnd - pptStart < 1000 * 5) {
                            continue;
                        }
                        // 移除两个太近的全屏板书
                        if (pptStart - lastPptEndMs < 1000 * 15) {
                            continue;
                        }
                    }
                    String pptStartTime = TimeUtils.formatMilliseconds(pptStart);
                    String pptEndTime = TimeUtils.formatMilliseconds(pptEnd);

                    // 画中画
                    String[] resIds = new String[]{FULL_SCREEN_RES_ID, PIP_RES_ID, SPLIT_SCREEN_RES_ID};
                    Integer resIdIndex = pptTypes.size() > i ? pptTypes.get(i) : null;
                    if (resIdIndex == null) {
                        resIdIndex = RandomUtils.nextInt(0, resIds.length);
                    }
                    SceneChangeSegment pipSceneChange = new SceneChangeSegment();
                    pipSceneChange.time(pptStartTime)
                            // 增加200毫秒，防止夹帧
                            .endTime(TimeUtils.add(pptEndTime, 200))
                            .resourceId(resIds[resIdIndex])
                            .perform(SegmentPerformConstant.PIC_IN_PIC)
                            .type(pipType)
                            .reason("画中画");

                    pipSceneChange.playType(1).skillType("ScreenEffect").cTime(pipSceneChange.getDurationSec());
                    pipSceneChange.getCustom()
                            .fluentPut("playerInstanceID", context.getCurrentBoardInstanceId())
                            .fluentPut("cameraResourceId", PIP_CAMERA_RES_ID)
                            .fluentPut("cameraTargetId", context.getCharacterInstanceId());
                    if (StringUtils.equals(PIP_RES_ID, pipSceneChange.getResourceId())) {
                        double offset = -0.2;
                        if (StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.CHIBI.getId())) {
                            offset = -0.1;
                        }
                        pipSceneChange.addProperty("Offset", ParamPropertyTypeEnum.FLOAT, offset);
                    }
                    coursewareModel.getSceneChangeSegment().add(pipSceneChange);

                    // 板书镜头
                    randomGenerateBoardCameraSegment(cameraStartTime, pptStartTime, context.isFirstTeachEventType(teachEventType) && cameraStartTime.equals(time), false, coursewareModel, context);

                    // 看向镜头
                    // CameraFeatureSegment cameraFeatureSegment = SegmentUtil.createLookAtCameraFeatureSegment(pptStartTime, pptEndTime);
                    // if (StringUtils.isNotBlank(context.getCharacterTargetId())) {
                    //     cameraFeatureSegment.targetId(context.getCharacterTargetId());
                    // }
                    // coursewareModel.getCameraFeatureSegment().add(cameraFeatureSegment);
                    cameraStartTime = pptEndTime;
                }
            }
        }
        randomGenerateBoardCameraSegment(cameraStartTime, endTime, context.isFirstTeachEventType(teachEventType) && cameraStartTime.equals(time), context.isLastTeachEventType(teachEventType), coursewareModel, context);
    }

    public void createBoardObject(JSONObject boardTransform, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        if (StringUtils.isNotBlank(context.getCurrentBoardInstanceId())) {
            return;
        }
        // 板
        SceneObjectSegment board = new SceneObjectSegment();
        board.time("00:00.00")
                .endTime(context.getTotalLength())
                .resourceId(BOARD_RES_ID)
                .perform(SegmentPerformConstant.BOARD)
                .type("ActorObject")
                .reason("板");
        board.instanceId(BOARD_INSTANCE);
        if (MapUtils.isNotEmpty(boardTransform)) {
            board.getCustom().putAll(boardTransform);
        } else {
            board.relativeCharacter().location(-100, 290, 175);
        }

        coursewareModel.getSceneObjectSegment().add(board);
        context.setCurrentBoardSegment(board);
        context.setCurrentBoardInstanceId(board.getInstanceId());
        // 上升效果
        RoutineSegment moveUp = new RoutineSegment();
        moveUp.time(board.getTime())
                .endTime(board.getEndTime())
                .resourceId(BOARD_MOVE_UP_RES_ID)
                .type(RoutineTypeEnum.CharacterAction.getName())
                .reason("ScreenMoveUp")
                .skillType(RoutineTypeEnum.CharacterAction.getName())
                .playType(1)
                .targetId(board.getInstanceId());
        coursewareModel.getRoutineSegment().add(moveUp);

    }

    @Override
    public boolean requireBoardPoint() {
        return true;
    }
}
