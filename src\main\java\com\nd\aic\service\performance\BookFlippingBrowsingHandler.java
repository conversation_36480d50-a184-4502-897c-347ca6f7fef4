package com.nd.aic.service.performance;

import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.BookFlippingBrowsingParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class BookFlippingBrowsingHandler extends AbstractPerformanceHandler {

    /**
     * 书本翻页
     */
    private static final String ROUTINE_RES_ID = "512f403f-c2c8-42e9-bc61-4f1282aec723";

    @SuppressWarnings("AlibabaUndefineMagicConstant")
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        Double durationSec = TimeUtils.getDurationSec(time, endTime);
        BookFlippingBrowsingParam bookFlippingBrowsingParam = teachEventType.getPerformanceParam(BookFlippingBrowsingParam.class);
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "书本翻页");
        routineSegment.playType(0).setParamVersion(0);
        // 书本内容参数
        // Trigger_0，可以看到第一页（Texture_0） 基本没用
        // Trigger_1，可以看到第二页（Texture_1~Texture_2）
        // Trigger_2，可以看到第三页（Texture_3~Texture_4）
        // 以此类推
        // 套路默认翻第二页 (Trigger_0~5;Texture_0~10)
        int divide = 0;
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture1())) {
            routineSegment.addProperty("Texture_1", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture1());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture1());
        }
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture2())) {
            routineSegment.addProperty("Texture_2", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture2());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture2());
        }
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture3())) {
            divide = 2;
            routineSegment.addProperty("Texture_3", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture3());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture3());
        }
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture4())) {
            divide = 2;
            routineSegment.addProperty("Texture_4", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture4());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture4());
        }
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture5())) {
            divide = 3;
            routineSegment.addProperty("Texture_5", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture5());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture5());
        }
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture6())) {
            divide = 3;
            routineSegment.addProperty("Texture_6", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture6());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture6());
        }
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture7())) {
            divide = 4;
            routineSegment.addProperty("Texture_7", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture7());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture7());
        }
        if (StringUtils.isNotBlank(bookFlippingBrowsingParam.getTexture8())) {
            divide = 4;
            routineSegment.addProperty("Texture_8", ParamPropertyTypeEnum.VIDEO, bookFlippingBrowsingParam.getTexture8());
            routineSegment.dependency(bookFlippingBrowsingParam.getTexture8());
        }
        // 书本翻页点
        boolean hasTrigger = false;
        if (bookFlippingBrowsingParam.getTrigger2() != null) {
            hasTrigger = true;
            routineSegment.addProperty("Trigger_2", ParamPropertyTypeEnum.FLOAT, bookFlippingBrowsingParam.getTrigger2());
        }
        if (bookFlippingBrowsingParam.getTrigger3() != null) {
            hasTrigger = true;
            routineSegment.addProperty("Trigger_3", ParamPropertyTypeEnum.FLOAT, bookFlippingBrowsingParam.getTrigger3());
        }
        if (bookFlippingBrowsingParam.getTrigger4() != null) {
            hasTrigger = true;
            routineSegment.addProperty("Trigger_4", ParamPropertyTypeEnum.FLOAT, bookFlippingBrowsingParam.getTrigger4());
        }
        if (!hasTrigger && divide > 0 && durationSec != null) {
            Double secPerDivide = new java.math.BigDecimal(durationSec / divide).setScale(3, java.math.RoundingMode.HALF_UP).doubleValue();
            routineSegment.addProperty("Trigger_2", ParamPropertyTypeEnum.FLOAT, secPerDivide);
            if (divide >= 3) {
                routineSegment.addProperty("Trigger_3", ParamPropertyTypeEnum.FLOAT, secPerDivide);
            }
            if (divide >= 4) {
                routineSegment.addProperty("Trigger_4", ParamPropertyTypeEnum.FLOAT, secPerDivide);
            }
        }
        coursewareModel.getRoutineSegment().add(routineSegment);
        // 角色隐藏和显示
        coursewareModel.getCharacterSegment().add(SegmentUtil.createToggleCharacterSegment(time, true));
        coursewareModel.getCharacterSegment().add(SegmentUtil.createToggleCharacterSegment(endTime, false));
    }
}
