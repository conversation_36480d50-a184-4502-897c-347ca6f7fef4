package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

/**
 * 云雾转场
 */
@AllArgsConstructor
@Service
public class CloudMistTransitionHandler extends AbstractPerformanceHandler {

    /**
     * 云雾转场
     */
    private static final String LOTTIE_RES_ID = "b39cf060-4bb3-41f7-a203-5bae45fbf18f";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);

        // 云雾转场
        coursewareModel.getLottieSegment().add(SegmentUtil.createLottieSegment(TimeUtils.add(time, 0), TimeUtils.add(endTime, 200), LOTTIE_RES_ID, "云雾转场", null));
    }
}
