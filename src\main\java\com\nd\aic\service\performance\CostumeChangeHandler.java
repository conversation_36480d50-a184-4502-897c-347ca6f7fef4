package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.CoursewareResourceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.ClothingSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;

/**
 * 撒花换装
 */
@RequiredArgsConstructor
@Service
public class CostumeChangeHandler extends AbstractPerformanceHandler {

    /**
     * 镜头资源
     */
    private static final String CAMERA_RES_ID = "c00b52a4-4537-46a6-a458-7868402e94dd";

    /**
     * 角色换装
     */
    private static final String ROUTINE_RES_ID = "3be92aaa-d95b-4500-aaa2-aea3586fb957";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        // 转身换装镜头
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, endTime, CAMERA_RES_ID, "鲜花爆开变身_镜头").targetId(context.getCharacterInstanceId());
        cameraSegment.setLookAt(true);
        coursewareModel.getCameraSegment().add(cameraSegment);

        long duration = TimeUtils.getDuration(time, endTime);
        long routineTime = 6000;
        if (duration < routineTime) {
            // 时长不足，不生成角色动作
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        String realEndTime = TimeUtils.add(time, routineTime);
        if (duration > routineTime) {
            // 补上之后的动作
            super.apply(realEndTime, endTime, teachEventType, coursewareModel, context);
        }

        // 换装套路
        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTimeInMilliseconds(TimeUtils.addMilliseconds(time, 100));
        routineSegment.setEndTime(realEndTime);
        routineSegment.setResourceId(ROUTINE_RES_ID);
        routineSegment.setReason("鲜花爆开变身_角色部分");
        routineSegment.playType(0).skillType("CharacterAction").cTime(routineSegment.getDurationSec());
        coursewareModel.getRoutineSegment().add(routineSegment);
        // 衣服更换
        List<CoursewareResourceDTO> clothes = context.getCompatibleClothes();
        if (CollectionUtils.isNotEmpty(clothes)) {
            // 已存在的服装
            List<String> clothingIdsExists = coursewareModel.getClothingSegment()
                    .stream()
                    .map(ClothingSegment::getResourceId)
                    .collect(Collectors.toList());
            List<CoursewareResourceDTO> filteredClothesResourceIds = clothes.stream()
                    .filter(item -> !clothingIdsExists.contains(item.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteredClothesResourceIds)) {
                // 随机选择服装
                CoursewareResourceDTO newClothRes = filteredClothesResourceIds.get(RandomUtils.nextInt(0, filteredClothesResourceIds.size()));
                ClothingSegment clothingSegment = new ClothingSegment();
                // 换装，偏移1.9秒
                clothingSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.addMilliseconds(time, 1900)));
                clothingSegment.setResourceId(newClothRes.getId());
                clothingSegment.setReason(newClothRes.getName());
                coursewareModel.getClothingSegment().add(clothingSegment);
            }
        }
    }
}
