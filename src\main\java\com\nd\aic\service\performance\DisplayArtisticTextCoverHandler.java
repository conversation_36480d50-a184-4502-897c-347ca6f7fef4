package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.CoursewareResourceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.performance.param.ArtisticTextCoverParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.AllArgsConstructor;

/**
 * 展示艺术字封面
 */
@AllArgsConstructor
@Service
public class DisplayArtisticTextCoverHandler extends AbstractPerformanceHandler {


    private static final String ALPHA_VIDEO_ID = "536e7999-e4f9-4f69-a3f7-f52c29c4fd83";
    private static final String LOTTIE_ID = "199b8653-cad9-43de-a8b8-41ef155a073d";


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        ArtisticTextCoverParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if (performanceParam == null || !performanceParam.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }


        // 透明视频
        VideoSegment videoSegment = SegmentUtil.createAlphaVideoSegment(time, TimeUtils.add(time, 6700), ALPHA_VIDEO_ID, "固定资源").turnOnAudio();
        coursewareModel.getVideoSegment().add(videoSegment);

        // 标题文字
        LottieSegment lottieSegment = new LottieSegment();
        lottieSegment.setTime(time);
        lottieSegment.setEndTimeInMilliseconds(TimeUtils.subMilliseconds(endTime, 500));
        lottieSegment.setResourceId(LOTTIE_ID);
        lottieSegment.setReason("艺术字");
        lottieSegment.setType("LottieScreen");
        JSONObject params = new JSONObject().fluentPut("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getMainTitle()));
        params.fluentPut("#Text2", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getSubTitle()));
        params.fluentPut("#Text3", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getAuthor()));
        lottieSegment.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("params", params).fluentPut("audio", new JSONObject().fluentPut("volume", 40)));
        coursewareModel.getLottieSegment().add(lottieSegment);

        // 空镜列表
        List<CoursewareResourceDTO> sceneCameraResources = context.getCoursewareResourceConfig().getSceneCamerasMap().get(context.getCurrentSceneId());
        if (CollectionUtils.isNotEmpty(sceneCameraResources)) {
            // 随机选空镜
            CoursewareResourceDTO sceneCameraResource = sceneCameraResources.get(RandomUtils.nextInt(0, sceneCameraResources.size()));
            // 空镜
            CameraSegment cameraSegment = SegmentUtil.createSceneCameraSegment(time, endTime, sceneCameraResource.getId());
            coursewareModel.getCameraSegment().add(cameraSegment);
            context.setCurrentCameraId(sceneCameraResource.getId());
        }

        // 设置套路的点位
        BirthPointSegment birthPointSegment = context.getCurrentBirthPoint();
        if (birthPointSegment != null) {
            birthPointSegment.setEndTime(endTime);
        }
    }

    /**
     * 准备参数
     * @param teachEventType 教学事件类型
     * @param coursewareModel 课件模型
     * @param context      课件生成上下文
     * @return  艺术字封面参数
     */
    private ArtisticTextCoverParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        ArtisticTextCoverParam performanceParam = teachEventType.getPerformanceParam(ArtisticTextCoverParam.class);
        if(performanceParam == null ) {
            return null;
        }
        if( StringUtils.isEmpty(performanceParam.getMainTitle()) && StringUtils.isEmpty(performanceParam.getSubTitle())){
            performanceParam.setIsSatisfy(false);
            return performanceParam;
        }

        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }
}
