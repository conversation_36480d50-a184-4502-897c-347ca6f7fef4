package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.performance.param.DisplayCaseNameParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import lombok.AllArgsConstructor;

/**
 * 云雾转场
 */
@AllArgsConstructor
@Service
public class DisplayCaseNameHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);

        DisplayCaseNameParam displayCaseNameParam = prepareParam(teachEventType, context);
        if (null == displayCaseNameParam) {
            return;
        }
        boolean anyBlank = Lists.newArrayList(displayCaseNameParam.getText1(), displayCaseNameParam.getText2(), displayCaseNameParam.getText3(), displayCaseNameParam.getText4(), displayCaseNameParam.getText5(), displayCaseNameParam.getText6()).stream().anyMatch(StringUtils::isBlank);
        if (anyBlank) {
            return;
        }

        JSONObject lottieParam = new JSONObject();
        lottieParam.put("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", displayCaseNameParam.getText1()));
        lottieParam.put("#Text2", new JSONObject().fluentPut("type", "text").fluentPut("value", displayCaseNameParam.getText2()));
        lottieParam.put("#Text3", new JSONObject().fluentPut("type", "text").fluentPut("value", displayCaseNameParam.getText3()));
        lottieParam.put("#Text4", new JSONObject().fluentPut("type", "text").fluentPut("value", displayCaseNameParam.getText4()));
        lottieParam.put("#Text5", new JSONObject().fluentPut("type", "text").fluentPut("value", displayCaseNameParam.getText5()));
        lottieParam.put("#Text6", new JSONObject().fluentPut("type", "text").fluentPut("value", displayCaseNameParam.getText6()));
        // 展示案例名称
        // if (TimeUtils.getDuration(time, endTime) > 3900) {
        //     coursewareModel.getLottieSegment().add(SegmentUtil.createLottieSegment(TimeUtils.add(time, 0), TimeUtils.add(time, 3000), "5a0ab20e-3016-4fa2-bd9a-b8ac6e2bd157", "展示案例名称", lottieParam));
        // } else {
        //     coursewareModel.getLottieSegment().add(SegmentUtil.createLottieSegment(TimeUtils.add(time, 0), endTime, "5a0ab20e-3016-4fa2-bd9a-b8ac6e2bd157", "展示案例名称", lottieParam));
        // }
        coursewareModel.getLottieSegment().add(SegmentUtil.createLottieSegment(TimeUtils.add(time, 0), endTime, "5a0ab20e-3016-4fa2-bd9a-b8ac6e2bd157", "展示案例名称", lottieParam));
    }

    private DisplayCaseNameParam prepareParam(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        DisplayCaseNameParam displayCaseNameParam = teachEventType.getPerformanceParam(DisplayCaseNameParam.class);
        if (null == displayCaseNameParam || StringUtils.isEmpty(displayCaseNameParam.getText())) {
            return null;
        }
        if (displayCaseNameParam.getText().length() < 6) {
            displayCaseNameParam.setText(displayCaseNameParam.getText() + "      ");
        }
        String[] texts = displayCaseNameParam.getText().split("");
        displayCaseNameParam.setText1(texts[0]);
        displayCaseNameParam.setText2(texts[1]);
        displayCaseNameParam.setText3(texts[2]);
        displayCaseNameParam.setText4(texts[3]);
        displayCaseNameParam.setText5(texts[4]);
        displayCaseNameParam.setText6(texts[5]);
        return displayCaseNameParam;
    }
}
