package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

/**
 * 展示身份信息
 */
@AllArgsConstructor
@Service
public class DisplayIdentityInfoHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);

        long startTime = TimeUtils.getDuration("00:00.00", time);

        // 使用Lottie的方式替换
        coursewareModel.getLottieSegment().add(SegmentUtil.createLottieSegment(startTime > 600 ? TimeUtils.add(time, -600) : time, TimeUtils.add(endTime, 600), "9704cf5c-6de6-4a51-ab6c-b9e35bd33cbd", "使用默认的身份信息动画", null));
    }
}
