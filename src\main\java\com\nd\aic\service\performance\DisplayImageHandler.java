package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.performance.param.DisplayImageParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.SegmentUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * 展示图片
 */
@Slf4j
@Service
@AllArgsConstructor
public class DisplayImageHandler extends AbstractPerformanceHandler {

    private static final String HORI_SIZE_TYPE = "1";
    private static final String HORI_LOTTIE = "df444cae-4153-494c-94e4-cc5239065096";
    private static final String VERT_LOTTIE = "58bfb3ab-1534-4bee-8f59-bce3d2e5e706";
    private static final String FIX_LEFT_POSITION_CAMERA = "5ab46e70-b065-43da-b75a-7759f053e0b9";
    private final MediaService mediaService;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        DisplayImageParam displayImageParam = teachEventType.getPerformanceParam(DisplayImageParam.class);
        if (displayImageParam == null || displayImageParam.getImage() == null) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 图片
        LottieSegment lottieSegment = new LottieSegment();
        lottieSegment.setTime(time);
        lottieSegment.setEndTime(endTime);

        if (StringUtils.isBlank(displayImageParam.getSizeType())){
            try {
                File file = mediaService.download(ImageUtil.getImageUrl(displayImageParam.getImage()), "", true);
                BufferedImage image = ImageIO.read(file);
                int width = image.getWidth();
                int height = image.getHeight();
                displayImageParam.setSizeType(width > height ? "1" : "2");
            } catch (IOException e) {
                log.error("图片信息读取失败", e);
                throw WafI18NException.of("BAD_REQUEST", "图片信息读取失败", HttpStatus.BAD_REQUEST);
            }
        }

        if(HORI_SIZE_TYPE.equals(displayImageParam.getSizeType())) {
            lottieSegment.setResourceId(HORI_LOTTIE);
        } else{
            lottieSegment.setResourceId(VERT_LOTTIE);
        }


        lottieSegment.setType("LottieScreen");
        JSONObject params = new JSONObject().fluentPut("#Image", new JSONObject().fluentPut("type", "image")
                .fluentPut("value", ImageUtil.getImageUrl(displayImageParam.getImage())));
        lottieSegment.setCustom(new JSONObject().fluentPut("windowId", "1")
                .fluentPut("audio", new JSONObject().fluentPut("volume", 40))
                .fluentPut("params", params));

        coursewareModel.getLottieSegment().add(lottieSegment);

        // 镜头
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, endTime, FIX_LEFT_POSITION_CAMERA, "展示图片").targetId(context.getCharacterInstanceId());
        cameraSegment.targetId(context.getCharacterInstanceId()).setLookAt(true);
        coursewareModel.getCameraSegment().add(cameraSegment);
        context.setCurrentCameraId(cameraSegment.getResourceId());

        randomGenerateActionSegment(time, endTime, coursewareModel, context);
    }
}
