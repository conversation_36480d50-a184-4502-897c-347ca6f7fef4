package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.FastGptRetryService;
import com.nd.aic.service.performance.param.DisplayKeywordsParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.AtaWordUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * 关键字辅助讲解
 */
@Primary
@RequiredArgsConstructor
@Service
public class DisplayKeywordsHandler extends AbstractPerformanceHandler {

    private final FastGptRetryService fastGptRetryService;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);
        DisplayKeywordsParam displayKeywordsParam = teachEventType.getPerformanceParam(DisplayKeywordsParam.class);
        String keyword = displayKeywordsParam.getKeyword();
        String rangeText = displayKeywordsParam.getRangeText();
        if (StringUtils.isEmpty(keyword)) {
            List<String> keywords = fastGptRetryService.askForKeyword(teachEventType.getDialogue());
            if (CollectionUtils.isEmpty(keywords)) {
                return;
            }
            keyword = keywords.get(RandomUtils.nextInt(0, keywords.size()));
        }
        if (StringUtils.isBlank(rangeText)) {
            rangeText = keyword;
        }
        JSONObject lottieParam = new JSONObject();
        lottieParam.put("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", keyword));
        lottieParam.put("#Text2", new JSONObject().fluentPut("type", "text").fluentPut("value", keyword));
        lottieParam.put("#Text3", new JSONObject().fluentPut("type", "text").fluentPut("value", keyword));

        int index = StringUtils.indexOf(teachEventType.getDialogue(), rangeText);
        String lottieTime = time;
        if (index != -1) {
            lottieTime = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index, true));
        }
        LottieSegment lottieSegment = SegmentUtil.createLottieSegment(lottieTime, TimeUtils.add(lottieTime, 3000), "f9e1667d-45b3-4d24-b4c1-3da80bafda43", "关键字辅助讲解", lottieParam);
        coursewareModel.getLottieSegment().add(lottieSegment);
    }
}
