package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.*;
import com.nd.aic.service.performance.param.DisplayQuestionsParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.UUIDUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 展示思考题
 */
@Service
@AllArgsConstructor
@Slf4j
public class DisplayQuestionsHandler extends AbstractPerformanceHandler {

    /**
     * 版面主题lottie资源参数
     */
    private static final String FORUM_TOPIC_RES_ID = "ab28631a-c733-4ae8-946e-192476417dba";

    /**
     * 问题标题lottie资源参数
     */
    private static final String QUESTION_TITLE_RES_ID = "a6808bc1-b16e-4a92-bef4-cbc69b6d1b7d";

    /**
     * 选项A的lottie资源参数
     */
    private static final String OPTIONS_A_RES_ID = "fa450098-2603-4539-b764-89ce084b4584";

    /**
     * 选项B的lottie资源参数
     */
    private static final String OPTIONS_B_RES_ID = "677ccb9c-692c-48fc-bba3-9c1e9f45e8dc";

    /**
     * 选项C的lottie资源参数
     */
    private static final String OPTIONS_C_RES_ID = "af27c069-3170-4922-86f2-cb3ef383d414";

    /**
     * 选项D的lottie资源参数
     */
    private static final String OPTIONS_D_RES_ID = "dacca66e-10be-4fa7-8d83-4e7f82a9cdfc";

    /**
     * 背景视频资源参数
     */
    private static final String BACK_VIDEO_RES_ID = "151b9258-e5b3-4251-9b2d-69050daf51db";

    /**
     * 走入屏幕讲解大套路资源参数
     */
    private static final String BIG_ROUTINE_RES_ID = "c8ebb550-acd2-4f03-8935-6030e2e3eca2";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        DisplayQuestionsParam param = prepareParams(teachEventType, coursewareModel, context);
        if (param == null || !param.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        //走入屏幕讲解大套路
        String routineEndTime = TimeUtils.add(time, 2200);
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "走入屏幕讲解").playType(0);
        routineSegment.putParam("Rotation",new JSONObject().fluentPut("roll",0).fluentPut("pitch",0).fluentPut("yaw",18.743698));
        routineSegment.putParam("Location",new JSONObject().fluentPut("X",2938.274498).fluentPut("Y",-708.174826).fluentPut("Z",0));

        //动作资源,
        randomGenerateActionSegment(routineEndTime, endTime, coursewareModel, context);
        //视频资源
        VideoSegment backVideoSegment = SegmentUtil.createBackVideoSegment(time, endTime, BACK_VIDEO_RES_ID);
        backVideoSegment.custom(new JSONObject().fluentPut("Volumn",1));

        //添加lottie
        List<LottieSegment> lottieSegments = new ArrayList<>();
        //版面主题lottie  一个字一个Text
        JSONObject forumTopicParam = new JSONObject();
        for (int i = 1; i <= param.getForumtopic().length(); i++) {
            forumTopicParam.put("#Text" + i, new JSONObject().fluentPut("type", "text").fluentPut("value", param.getForumtopic().charAt(i - 1)));
        }
        LottieSegment forumTopic = SegmentUtil.createLottieSegment(routineEndTime, endTime, FORUM_TOPIC_RES_ID, "版面主题", forumTopicParam).zIndex(1).volume(80);
        lottieSegments.add(forumTopic);
        //问题标题lottie
        JSONObject questionTitleParam = new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", param.getQuestiontitle()));
        LottieSegment questionTitle = SegmentUtil.createLottieSegment(routineEndTime, endTime, QUESTION_TITLE_RES_ID, "问题标题", questionTitleParam).zIndex(1).volume(80);
        lottieSegments.add(questionTitle);

        int count = 0;
        for (int i = 97; i <= 100; i++) {
            count += StringUtils.isBlank(param.get("options" + (char)i)) ? 0 : 1;
        }
        //判断时间是否足够各个选项一个个展开，若不够则一起展开
        if (TimeUtils.getDuration(routineEndTime,endTime) - 2000*count > 0){
            for (int i = 97; i <= 100; i++) {
                char options = (char) i;
                if (StringUtils.isNotEmpty(param.get("options" + options))) {
                    LottieSegment lottieSegment = SegmentUtil.createLottieSegment(TimeUtils.add(routineEndTime, 2000*(i-96)), endTime, get("OPTIONS_"+(char)(options-32)+"_RES_ID"), "选项" + options,
                            new JSONObject().fluentPut("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", param.get("options" + options)))).zIndex(1).volume(80);
                    lottieSegments.add(lottieSegment);
                }
            }
        }else {
            for (int i = 97; i <= 100; i++) {
                char options = (char) i;
                if (StringUtils.isNotEmpty(param.get("options" + options))) {
                    LottieSegment lottieSegment = SegmentUtil.createLottieSegment(routineEndTime, endTime, get("OPTIONS_"+(char)(options-32)+"_RES_ID"), "选项" + options,
                            new JSONObject().fluentPut("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", param.get("options" + options)))).zIndex(1).volume(80);
                    lottieSegments.add(lottieSegment);
                }
            }
        }


        String uuid = UUIDUtil.upperUuid();
        // 触发视频播放
        BuffSegment buffSegment = SegmentUtil.createBuffSegment(time, endTime, uuid, "开始Play", new JSONObject().fluentPut("name","MetaRoleBuff.PlayVideo")
                .fluentPut("Param",new JSONObject().fluentPut("Action",new JSONObject()
                        .fluentPut("Resource",BACK_VIDEO_RES_ID)
                        .fluentPut("StartTime",0)
                        .fluentPut("PlayRate",1)
                        .fluentPut("bAutoPlay",true)
                        .fluentPut("bLoop",true)
                        .fluentPut("SoundVolume",0.1))));

        //在角色后面创建一块白板，用来播放视频
        SceneObjectSegment setParamSegment = SegmentUtil.createSetParamSegment(time,endTime,"","",new JSONObject()
                .fluentPut("MetaRoleComponentName","VideoMediaRenderTarget")
                .fluentPut("MetaRoleType","MetaRoleComponent")
                .fluentPut("Param",new JSONObject().fluentPut("Properties",new JSONObject()
                        .fluentPut("Resource",BACK_VIDEO_RES_ID)
                        .fluentPut("StartTime",0)
                        .fluentPut("PlayRate",1)
                        .fluentPut("bAutoPlay",false)
                        .fluentPut("bLoop",true)
                        .fluentPut("SoundVolume",0.1))));
        setParamSegment.dependency(BACK_VIDEO_RES_ID);
        setParamSegment.type("ActorObject");
        setParamSegment.instanceId(uuid);

        coursewareModel.getLottieSegment().addAll(lottieSegments);
        coursewareModel.getVideoSegment().add(backVideoSegment);
        coursewareModel.getRoutineSegment().add(routineSegment);
        coursewareModel.getBuffSegment().add(buffSegment);
        if (TimeUtils.convertToMilliseconds(time) == 0){
            coursewareModel.getPrepareSegment().add(new PrepareSegment().setSceneObjectSegment(Lists.newArrayList(setParamSegment)));
        }else {
            coursewareModel.getSceneObjectSegment().add(setParamSegment);
        }


    }

    private DisplayQuestionsParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        DisplayQuestionsParam param = teachEventType.getPerformanceParam(DisplayQuestionsParam.class);
        if (param == null) {
            return null;
        }
        if (StringUtils.isEmpty(param.getForumtopic()) || StringUtils.isEmpty(param.getQuestiontitle())
                || StringUtils.isEmpty(param.getOptionsa()) || StringUtils.isEmpty(param.getOptionsb())) {
            param.setIsSatisfy(false);
            return param;
        }
        param.setIsSatisfy(true);
        return param;
    }


    // 动态获取字段值
    private String get(String fieldName) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return (String) field.get(this);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法获取字段值: " + fieldName, e);
        }
    }
}
