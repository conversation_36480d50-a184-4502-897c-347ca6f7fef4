package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.SingleKeywordParam;
import com.nd.aic.util.SegmentUtil;

import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

@Service
public class DisplaySingleKeywordHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        super.apply(time, endTime, teachEventType, coursewareModel, context);
        SingleKeywordParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if (performanceParam == null || !performanceParam.getIsSatisfy()) {
            return;
        }

        String lottieResourceId = "ED4DD8F2ADC248C59ED70F4D73EB7038";
        if (performanceParam.getStyle().equals("1")) {
            lottieResourceId = "F12CBD15E87D429D85E431960C665038";
        }


        String[]timeArray = getTimeAreaByAliasTexts(performanceParam.getAliasText(), context, teachEventType);
        String lottieTime = timeArray[0];
        String lottieEndTime = timeArray[1];

        LottieSegment lottieSegment = SegmentUtil.createLottieSegment(lottieTime, lottieEndTime, lottieResourceId, Strings.isEmpty(performanceParam.getReason()) ? "素材填写" : performanceParam.getReason(), new JSONObject());
        lottieSegment.addLottieParam("#Text1", "text", performanceParam.getKeyword()).addLottieParam("#Text2", "text", performanceParam.getKeyword());
        coursewareModel.getLottieSegment().add(lottieSegment);
    }

    /**
     * 获取关键字出现的时间点
     * @param teachEventType 课件事件类型
     * @param context 课件生成上下文
     * @param  coursewareModel 课件模型
     * @return 参数
     */
    private SingleKeywordParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        SingleKeywordParam performanceParam = teachEventType.getPerformanceParam(SingleKeywordParam.class);
        if (performanceParam == null || Strings.isEmpty(performanceParam.getKeyword())) {
            return null;
        }

        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }
}
