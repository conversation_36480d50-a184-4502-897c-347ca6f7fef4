package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.material.entity.MediaResInfo;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 展示教材内容(抛出书本)
 */
@Service
public class DisplayTextbookContentHandler extends AbstractPerformanceHandler {

    private MediaResInfo getResource(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        if (null != teachEventType.getPerformanceConfig() && StringUtils.contains(teachEventType.getPerformanceConfig().getDescription(), "小A适合推荐的相机类型是：")) {
            // 现在，请以小组为单位，完成《任务手册》中的任务：相机推荐。
            // TODO L45大套路资源ID
            String resourceId = "144394B64F6F3DEDF8D2E2B5E33BC0BE";
            MediaResInfo mediaResInfo = new MediaResInfo();
            mediaResInfo.setResourceId(resourceId);
            mediaResInfo.setReason("小A适合推荐的相机类型是：");
            return mediaResInfo;
        }
        if (null != teachEventType.getPerformanceConfig() && StringUtils.contains(teachEventType.getPerformanceConfig().getDescription(), "字幕：能力测试")) {
            // 希望同学们在课后及时完成《学习手册》中的能力测试部分，巩固对本课内容的学习。
            MediaResInfo mediaResInfo = new MediaResInfo();
            // TODO L97大套路资源ID
            mediaResInfo.setResourceId("2A5D8A574078B068742369AD769D4DD8");
            mediaResInfo.setReason("希望同学们在课后及时完成《学习手册》中的能力测试部分，巩固对本课内容的学习。");
            return mediaResInfo;
        }
        if (null != teachEventType.getPerformanceConfig() && StringUtils.contains(teachEventType.getPerformanceConfig().getDescription(), "创意的生成")) {
            // 希望同学们在课后及时完成《学习手册》中的能力测试部分，巩固对本课内容的学习。
            MediaResInfo mediaResInfo = new MediaResInfo();
            // 无处不在的设计 L16 创意的生成
            mediaResInfo.setResourceId("A0457B8146CD6D9DB40757AF961502F1");
            mediaResInfo.setReason("创意的生成");
            return mediaResInfo;
        }
        return null;
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        MediaResInfo mediaResInfo = getResource(teachEventType, context);
        if (null != mediaResInfo) {
            // 现在，请以小组为单位，完成《任务手册》中的任务：相机推荐。
            // TODO L45大套路资源ID
            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, TimeUtils.add(endTime, 200), mediaResInfo.getResourceId(), mediaResInfo.getReason());
            routineSegment.getCustom().fluentPut("PlayType", 0);
            coursewareModel.getRoutineSegment().add(routineSegment);
            return;
        }
        
        // TODO。。。通过资源来替换参数
        if(StringUtils.contains(teachEventType.getDialogue(), "翻开《学习手册》")){
            coursewareModel.getRoutineSegment().add(SegmentUtil.createBigRoutineSegment(time, endTime, "7522966648CAF656FF5A598B1ED5E003", "变出书本"));
            return;
        }
        

        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTime(time);
        routineSegment.setEndTime(endTime);
        routineSegment.setResourceId("7369FAE34C3333FBFE0C739A55A58781");
        routineSegment.setType("BigRoutine");
        routineSegment.setReason("变出书本");
        JSONObject params = new JSONObject().fluentPut("VideoResId", "46A1DDD841A88D80E890A4B84B37E00F");
        routineSegment.setCustom(new JSONObject().fluentPut("PlayType", 1).fluentPut("SkillType", "BigRoutine").fluentPut("Param", params).fluentPut("Time", routineSegment.getDuration() / 1000.0));
        coursewareModel.getRoutineSegment().add(routineSegment);


        String videoResId = "97EE78F99E6C49149A3ABD60FE6500C7";
        float videoDurationSec = 15.34f;
        VideoSegment videoSegment = SegmentUtil.createAlphaVideoSegment(TimeUtils.add(time,5000), endTime, videoResId, "展示教材内容-背景视频");
        float segmentDurationSec = videoSegment.getDuration() * 1.0f / 1000;
        // 播放速率
        if (segmentDurationSec > videoDurationSec) {
            float mediaRate = (videoDurationSec / segmentDurationSec);
            videoSegment.getCustom().put("MediaRate", mediaRate);
        }
        coursewareModel.getVideoSegment().add(videoSegment);

        // Lottie
        String lottieResId = "B99FAFA0ACDA437F9AEC5E3271AA0074";
        LottieSegment lottieSegment = SegmentUtil.createLottieSegment(TimeUtils.add(time,5000), endTime, lottieResId, "展示教材内容-文字", new JSONObject());
        coursewareModel.getLottieSegment().add(lottieSegment);
    }
}
