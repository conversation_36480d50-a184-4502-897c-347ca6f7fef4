package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import org.springframework.stereotype.Service;

@Service
public class DisplayThinkingSymbolHandler extends AbstractPerformanceHandler {


    private static final String middleStandCameraResourceId = "66faa0de-77c4-4be7-8820-efc7a9916671";
    private static final String thinkingSymbolResourceId = "8d5bf2bc-40ca-4a25-b11c-694199c3cf06";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        long duration = TimeUtils.getDuration(time, endTime);

        // add action segments
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, middleStandCameraResourceId, "选用固定中间镜头").targetId(context.getCharacterInstanceId()));

        randomGenerateActionSegment(time, endTime, coursewareModel, context);

        // add thinking symbol
        coursewareModel.getVideoSegment().add(SegmentUtil.createAlphaVideoSegment(time, duration > 10000 ? TimeUtils.add(time, 10000) :  endTime, thinkingSymbolResourceId, "思考符号"));



    }
}
