package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 空的表演处理器
 */
@RequiredArgsConstructor
@Service
public class EmptyPerformanceHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
    }
}
