package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SettingSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.performance.param.EncouragingVisualCandyParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 给出鼓励视觉糖果
 */
@RequiredArgsConstructor
@Service
public class EncouragingVisualCandyHandler extends AbstractPerformanceHandler {


    // middle camera, fixed position
    private static final String MIDDLE_POSITION_CAMERA = "66faa0de-77c4-4be7-8820-efc7a9916671";
    private static final String ROUTINE_RES_ID = "a22da091-26a2-47f1-b604-ce4df74d58c2";
    private static final String ROUTINE_RES_ID_WITHOUT_HEART = "70288e36-1597-4eaf-80cf-950f98979190";
    private static final String ALPHA_VIDEO = "d0d80dcb-3013-4d76-9be4-cb850ebf8872";
    private static final String ALPHA_VIDEO_2 = "89f1115a-8522-4259-8759-535bf8761e4b";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        EncouragingVisualCandyParam encouragingVisualCandyParam = teachEventType.getPerformanceParam(EncouragingVisualCandyParam.class);

        switch (encouragingVisualCandyParam.getVisualType()) {
            case "3": // confetti
                applyConfettiVisualCandy(time, endTime, teachEventType, coursewareModel, context);
                break;
            case "2": // light fly
                applyLightVisualCandy(time, endTime, teachEventType, coursewareModel, context);
                break;
            case "1":  // little heart
            default:
                applyHeartVisualCandy(time, endTime, teachEventType, coursewareModel, context);
                break;
        }
    }

    /**
     *
     * @param time
     * @param endTime
     * @param teachEventType
     * @param coursewareModel
     * @param context
     */
    private void applyConfettiVisualCandy(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        // add action segment
        randomGenerateActionSegment(time, endTime,  coursewareModel, context);

        // add confetti segment
        coursewareModel.getVideoSegment().add(SegmentUtil.createAlphaVideoSegment(time, endTime,ALPHA_VIDEO, "给出鼓励视觉糖果-彩带"));

        // add camera segment
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, MIDDLE_POSITION_CAMERA, "给出鼓励视觉糖果-彩带").targetId(context.getCharacterInstanceId()));

        // add look at segment
        coursewareModel.getCameraFeatureSegment().add(SegmentUtil.createLookAtCameraFeatureSegment(time, endTime));
    }


    /**
     * apply light visual candy
     * @param time  start time
     * @param endTime end time
     * @param teachEventType teach event data
     * @param coursewareModel   courseware model
     * @param context courseware generation context
     */
    private void applyLightVisualCandy(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context){
        long performanceDuration = 6000;
        long minDuration = 4000;

        if (TimeUtils.getDuration(time, endTime) < minDuration) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }


        String routineEndTime = endTime;
        if(TimeUtils.getDuration(time, endTime) > performanceDuration){
            routineEndTime = TimeUtils.add(time, performanceDuration);
            super.apply(routineEndTime, endTime, teachEventType, coursewareModel, context);
        }

        // create big routine segment
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, routineEndTime, ROUTINE_RES_ID_WITHOUT_HEART, "给出鼓励视觉糖果-点赞比心").playType(1);
        BirthPoint(routineSegment,context);
        coursewareModel.getRoutineSegment().add(routineSegment);

        // add video segment
        VideoSegment alphaVideoSegment = SegmentUtil.createAlphaVideoSegment(time, routineEndTime, ALPHA_VIDEO_2, "给出鼓励视觉糖果-点赞比心").turnOnAudio();
        coursewareModel.getVideoSegment().add(alphaVideoSegment);

    }


    /**
     * apply big routine
     * it's suitable for the case that the performance duration is between 4 and 7 seconds
     *
     * @param time start time
     * @param endTime end time
     * @param teachEventType teach event data
     * @param coursewareModel courseware model
     * @param context courseware generation context
     */
    private void applyHeartVisualCandy(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        String realEndTime = endTime;

        // use the base performance handler if the duration is less than 4 seconds
        if(TimeUtils.getDuration(time, endTime) < 4000){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // use the base performance handler if the duration is more than 7 seconds
        if(TimeUtils.getDuration(time, endTime) > 5000){
            realEndTime = TimeUtils.add(time, 5000);
            super.apply(realEndTime, endTime, teachEventType, coursewareModel, context);
        }

        // create big routine segment
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, realEndTime, ROUTINE_RES_ID, "给出鼓励视觉糖果-点赞比心").playType(1);
        BirthPoint(routineSegment,context);
        coursewareModel.getRoutineSegment().add(routineSegment);
    }

    /**
     * \
     */
    private void BirthPoint(RoutineSegment routineSegment,CoursewareGenerationContext context){
        BirthPointSegment currentBirthPoint = context.getCurrentBirthPoint();
        if (currentBirthPoint != null){
            routineSegment.putParam("Rotation",currentBirthPoint.cloneTransform().get("rotation"));
            routineSegment.putParam("Location",currentBirthPoint.cloneTransform().get("location"));
        }
    }
}
