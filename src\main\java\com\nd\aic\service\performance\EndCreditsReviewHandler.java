package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.EndCreditsReviewParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 展示人物传记
 */
@Service
@AllArgsConstructor
@Slf4j
public class EndCreditsReviewHandler extends AbstractPerformanceHandler{

    /**
     * lottie资源ID(人物传记）
     */
    private static final String Lottie_Res_Id = "3db2f057-ca66-4f9e-9eae-cf35ac74284d";

    /**
     * 镜头资源ID
     */
    private static final String Camera_Res_Id = "66faa0de-77c4-4be7-8820-efc7a9916671";

    /**
     * 动作资源ID
     */
    private static final String Action_Res_Id = "8f8a0884-08da-4b1b-85e5-608dc8a186f3";



    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        EndCreditsReviewParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if (performanceParam == null || !performanceParam.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 基础的动作与镜头
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, Camera_Res_Id, "中景-正面-居中-固定").targetId(context.getCharacterInstanceId()));
        coursewareModel.getActionSegment().add(SegmentUtil.createActionSegment(time, endTime, Action_Res_Id, "讲述",""));

        // 添加lottie
        LottieSegment lottieSegment = new LottieSegment();
        lottieSegment.setTime(time);
        lottieSegment.setEndTimeInMilliseconds(TimeUtils.subMilliseconds(endTime, 500));
        lottieSegment.setResourceId(Lottie_Res_Id);
        lottieSegment.setReason("人物传记");
        lottieSegment.setType("LottieScreen");
        JSONObject params = new JSONObject();

        params.fluentPut("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getTexture1()));
        params.fluentPut("#Text2", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getTexture2()));
        params.fluentPut("#Text3", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getTexture3()));
        params.fluentPut("#Image1", new JSONObject().fluentPut("type", "image").fluentPut("align", "center").fluentPut("value", ImageUtil.getImageUrl(performanceParam.getImage1())));
        params.fluentPut("#Image2", new JSONObject().fluentPut("type", "image").fluentPut("align", "center").fluentPut("value", ImageUtil.getImageUrl(performanceParam.getImage2())));

        lottieSegment.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("params", params).fluentPut("audio", new JSONObject().fluentPut("volume", 80)));
        coursewareModel.getLottieSegment().add(lottieSegment);

    }

    private EndCreditsReviewParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context){
        EndCreditsReviewParam performanceParam = teachEventType.getPerformanceParam(EndCreditsReviewParam.class);
        if (performanceParam==null){
            return null;
        }

        if (StringUtils.isEmpty(performanceParam.getImage1()) || StringUtils.isEmpty(performanceParam.getImage2())
        || StringUtils.isEmpty(performanceParam.getTexture1()) || StringUtils.isEmpty((performanceParam.getTexture2()))
        || StringUtils.isEmpty(performanceParam.getTexture3())){
            performanceParam.setIsSatisfy(false);
            return performanceParam;
        }

        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }
}
