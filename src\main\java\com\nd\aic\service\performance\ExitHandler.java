package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.ActionResourceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;

/**
 * 退场
 */
@AllArgsConstructor
@Service
public class ExitHandler extends AbstractPerformanceHandler {
    /**
     * 大套路资源ID
     */
    private static final String BIG_ROUTINE_RES_ID = "03ba4a10-674c-4132-a1e3-9d01ff03eab0";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useWhiteCat = CharacterUtils.isWhiteCat(context);
        boolean useNeonNew = CharacterUtils.isNeonNew(context);
        if (useWhiteCat) {

            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "退场");
            routineSegment.targetId("");
            routineSegment.putParam("PauseAtEnd", false);
            routineSegment.bindObject("TestRole", context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().add(routineSegment);

        } else if (useNeonNew) {
            // 霓-退场大套路资源：1495c9ad-ca87-4644-a74e-80d913307d60
            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, "1495c9ad-ca87-4644-a74e-80d913307d60", "退场");
            routineSegment.targetId("");
            routineSegment.putParam("PauseAtEnd", false);
            routineSegment.bindObject("char", context.getCharacterInstanceId());
            routineSegment.putParam("OriginDuration", 8.4).putParam("ParamVersion", 1).putParam("StopAllSkill", false).putParam("PauseAtEnd", true);
            // routineSegment.bindObject("TestRole", context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().add(routineSegment);

            List<ActionResourceDTO> actions = context.getCompatibleActions().stream().filter(action -> StringUtils.equalsIgnoreCase(action.getType(), "idle")).collect(Collectors.toList());
            List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(TimeUtils.convertToMilliseconds(time), TimeUtils.convertToMilliseconds(endTime), 8000, 12000, actions, context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().addAll(randomActions);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }

    }
}
