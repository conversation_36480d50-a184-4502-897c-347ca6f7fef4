package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.CameraSegment;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.AllArgsConstructor;

/**
 * 结束画面
 */
@AllArgsConstructor
@Service
public class FadeoutHandler extends AbstractPerformanceHandler {


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        final List<CameraSegment> cameraSegments = coursewareModel.getCameraSegment();
        if (CollectionUtils.isNotEmpty(cameraSegments)) {
            CameraSegment latestCamera = cameraSegments.get(cameraSegments.size() - 1);
            if (StringUtils.isBlank(latestCamera.getEndTime())) {
                latestCamera.setEndTime(endTime);
            }
        }
        final List<ActionSegment> actionSegments = coursewareModel.getActionSegment();
        if (CollectionUtils.isNotEmpty(actionSegments)) {
            ActionSegment latestActionSegment = actionSegments.get(actionSegments.size() - 1);
            if (StringUtils.isBlank(latestActionSegment.getEndTime())) {
                latestActionSegment.setEndTime(time);
            }
        }
    }
}


