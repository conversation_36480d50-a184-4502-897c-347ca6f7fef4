package com.nd.aic.service.performance;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.FloatingBoardLectureParam;
import com.nd.aic.util.SegmentUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

import java.io.IOException;

@RequiredArgsConstructor
@Service
@Slf4j
public class FloatingBoardLectureHandler extends AbstractPerformanceHandler {

    /**
     * 悬浮使用黑板讲解
     */
    private static final String ROUTINE_RES_ID = "91b2fa8c-75dc-4328-8416-d4c9dd1fbbed";

    private final AutomateHandler automateHandler;
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        FloatingBoardLectureParam param = teachEventType.getPerformanceParam(FloatingBoardLectureParam.class);
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "悬浮使用黑板讲解");
        routineSegment.playType(0);
        String video = getVideo(param.getVideo(), param.getImage(), context.getTitle());
        if (StringUtils.isNotBlank(video)) {
            routineSegment.addProperty("Texture-Plane-Texture", ParamPropertyTypeEnum.VIDEO, video).dependency(param.getVideo());
        }
        coursewareModel.getRoutineSegment().add(routineSegment);
    }

    private String getVideo(String video, String image, String title) {
        if (StringUtils.isNotBlank(video)) {
            return video;
        }
        if (StringUtils.isNotBlank(image)) {
            try {
                return automateHandler.image2Video(title, image);
            } catch (IOException e) {
                log.error("图片转视频失败", e);
                throw WafI18NException.of("BAD_REQUEST", "图片转视频失败", HttpStatus.BAD_REQUEST, e);
            }
        }
        return null;
    }

}
