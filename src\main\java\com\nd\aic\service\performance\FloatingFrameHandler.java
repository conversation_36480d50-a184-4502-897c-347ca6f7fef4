package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.FloatingFrameParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.nd.aic.util.TimeUtils.getDurationSec;

/**
 * 悬浮画框
 */
@Service
@AllArgsConstructor
@Slf4j
public class FloatingFrameHandler extends AbstractPerformanceHandler {
    /**
     * 大套路资源ID
     */
    private static final String BIG_ROUTINE_RES_ID = "64e320aa-7ef9-4b3a-ac43-47e394997770";


    private final AutomateHandler automateHandler;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        FloatingFrameParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if (performanceParam == null || !performanceParam.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        //动作资源
        randomGenerateActionSegment(TimeUtils.add(time,200),endTime,coursewareModel,context);

        //悬浮画框套路
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "悬浮画框");
        routineSegment.playType(0);

        Map<String, Object> map = new HashMap<>();
        for (int i = 1; i < 6; i++) {
            String videoResId = performanceParam.get("video"+i);
            if (!StringUtils.isBlank(videoResId)) {
                map.put("playerid", String.valueOf(i));
                String textureKey = String.format("Texture0%d-Plane-Picture", i);

                routineSegment.addProperty(textureKey,"Video",videoResId,map);

                routineSegment.dependency(performanceParam.get("video"+i));
            }
        }

        for (int i = 0; i < 5; i++) {
            String duration = performanceParam.get("duration" + (i + 1));
            if (!StringUtils.isBlank(duration)){
                routineSegment.addShotTrack(i,Double.valueOf(duration));
            }else {
                routineSegment.addShotTrack(i, 5.0);
            }
        }

        coursewareModel.getRoutineSegment().add(routineSegment);
    }

    @SneakyThrows
    private FloatingFrameParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        FloatingFrameParam performanceParam = teachEventType.getPerformanceParam(FloatingFrameParam.class);
        if (performanceParam == null) {
            return null;
        }
        for (int i = 1; i <= 5; i++) {

            String videoField = "video" + i;
            String imageField = "picture" + i;
            String durationField = "duration" + i;

            String videoResId = performanceParam.get(videoField);
            String image = performanceParam.get(imageField);
            String duration = performanceParam.get(durationField);

            // 检查条件  把图像转视频
            if (StringUtils.isBlank(videoResId) && StringUtils.isNotEmpty(image)) {
                videoResId = automateHandler.image2Video(String.format("%s-%s", context.getTitle(), "悬浮画框"),image);
                performanceParam.set(videoField,videoResId);
            }

            if (StringUtils.isNotEmpty(duration)) {
                //通过引用的文本，计算出引用的时间
                String[] timeAreaByAliasTexts = getTimeAreaByAliasTexts(duration, context, teachEventType);
                //获取格式为 "06:68.00" 的时长，单位为秒
                Double durationSec = getDurationSec(timeAreaByAliasTexts[0], timeAreaByAliasTexts[1]);
                if (durationSec != null) {
                    performanceParam.set(durationField, durationSec.toString());
                } else {
                    performanceParam.set(durationField, "5");
                }
            }

        }

        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }

}
