package com.nd.aic.service.performance;

import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.FlyToBlackboardParam;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@AllArgsConstructor
@Slf4j
public class FlyToBlackboardHandler extends AbstractPerformanceHandler {

    /**
     * 飞向小黑板_全屏版
     */
    private static final String ROUTINE_RES_ID = "0d81a5ed-d0b4-4127-9d2c-81b09a7eb8f1";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        FlyToBlackboardParam param = teachEventType.getPerformanceParam(FlyToBlackboardParam.class);
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "飞向小黑板_全屏版");
        routineSegment.playType(0);
        if (StringUtils.isNotBlank(param.getVideo())) {
            routineSegment.addProperty("Texture-Plane-Texture", ParamPropertyTypeEnum.VIDEO, param.getVideo()).dependency(param.getVideo());
        }
        coursewareModel.getRoutineSegment().add(routineSegment);
    }
}
