package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.performance.param.FrontOfShowIssueParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 角色身前展示课题
 */
@Slf4j
@Service
public class FrontOfShowIssueHandler extends AbstractPerformanceHandler{

    private static final String ROUTINE_RES_ID = "108e77a5-571c-4c06-b7ef-279cc6efed24";
    private static final String CAMERA_RES_ID = "618bba3f-39f5-49d6-9e37-a3910b238205";
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        FrontOfShowIssueParam param = teachEventType.getPerformanceParam(FrontOfShowIssueParam.class);
        if (param == null || StringUtils.isEmpty(param.getVideo())){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }


        String routineEndTime = endTime;
        if (TimeUtils.getDuration(time, endTime) > 2080){
            routineEndTime = TimeUtils.add(time,2080);
            super.apply(routineEndTime, endTime, teachEventType, coursewareModel, context);
        }
        //镜头
        CameraSegment characterCameraSegment = SegmentUtil.createCharacterCameraSegment(time, endTime, CAMERA_RES_ID, "中全景-固定").targetId(context.getCharacterInstanceId());
        characterCameraSegment.setNdrResourceId(CAMERA_RES_ID);
        coursewareModel.getCameraSegment().add(characterCameraSegment);

        //套路
        RoutineSegment routineSegment = SegmentUtil.createCharacterActionSegment(time, routineEndTime, ROUTINE_RES_ID, "抛出课题").playType(0);
        coursewareModel.getRoutineSegment().add(routineSegment);

        //视频
        VideoSegment alphaVideoSegment = SegmentUtil.createAlphaVideoSegment(time, routineEndTime, param.getVideo(), "身前展示课题");
        alphaVideoSegment.rate(1.17903).turnOnAudio();
        coursewareModel.getVideoSegment().add(alphaVideoSegment);
    }
}
