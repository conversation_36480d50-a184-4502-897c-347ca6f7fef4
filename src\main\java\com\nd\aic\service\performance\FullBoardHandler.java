package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.FullBoardParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * 全屏板书
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class FullBoardHandler extends AbstractPerformanceHandler {

    private final AutomateHandler automateHandler;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        FullBoardParam fullBoardParam = prepareParam(teachEventType, context);
        if (StringUtils.isBlank(fullBoardParam.getVideo())){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        VideoSegment videoSegment = SegmentUtil.createFrontVideoSegment(TimeUtils.formatMilliseconds(Math.max(TimeUtils.subMilliseconds(time, 300), 0)), endTime, fullBoardParam.getVideo(), "全屏板书").turnOnAudio();
        coursewareModel.getVideoSegment().add(videoSegment);
    }

    private FullBoardParam prepareParam(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        FullBoardParam param = teachEventType.getPerformanceParam(FullBoardParam.class);
        if (StringUtils.isNotBlank(param.getImage())) {
            String video = null;
            try {
                video = automateHandler.image2Video(String.format("%s-%s", context.getTitle(), "小黑板展示定义"), param.getImage());
            } catch (IOException e) {
                log.error("图片转视频失败", e);
            }
            param.setVideo(video);
        }
        return param;
    }

}
