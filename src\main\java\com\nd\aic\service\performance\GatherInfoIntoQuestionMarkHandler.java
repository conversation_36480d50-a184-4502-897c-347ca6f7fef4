package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;

import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.performance.param.GatherInfoIntoQuestionMarkParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 关键信息汇聚成问号
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class GatherInfoIntoQuestionMarkHandler extends AbstractPerformanceHandler {

    private static final String BIG_ROUTINE_RESOURCE_ID = "236ec05c-1667-45b0-b144-d17ab99f38b9";

    private static final String BOTTOM_LOTTIE_ID = "6a827fde-d490-464e-aefe-13a54ec45af3";
    private static final String TOP_LEFT_LOTTIE_ID = "466b9597-748f-4565-83ef-dda92180a0b0";
    private static final String TOP_RIGHT_LOTTIE_ID = "36eca156-bc55-414d-9dca-636b62ecf947";

    private static final String VIDEO_RES_ID = "80b48c49-ee86-4281-a95a-647322836735";


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        GatherInfoIntoQuestionMarkParam param = teachEventType.getPerformanceParam(GatherInfoIntoQuestionMarkParam.class);
        if (param == null || param.getKeyInfoCount() == 0) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            log.info("GatherInfoIntoQuestionMarkHandler param is null or keyInfoCount is 0");
            return;
        }

        // set keyInfo2 to keyInfo3 if keyInfo3 is not empty and keyInfo2 is empty
        if(StringUtils.isNotEmpty(param.getKeyInfo3()) && StringUtils.isEmpty(param.getKeyInfo2())){
            param.setKeyInfo2(param.getKeyInfo3());
            param.setKeyInfo3("");
        }

        long duration = TimeUtils.getDuration(time, endTime);
        String routineEndTime = endTime;
        if(duration > 12500){
            routineEndTime = TimeUtils.add(time, 9500);
            super.apply(routineEndTime, endTime, teachEventType, coursewareModel, context);
        }


        // create big routine
        RoutineSegment bigRoutine = SegmentUtil.createBigRoutineSegment(time, routineEndTime, BIG_ROUTINE_RESOURCE_ID, "");
        bigRoutine.playType(1);
        coursewareModel.getRoutineSegment().add(bigRoutine);


        // create lottie
        int paramCount = param.getKeyInfoCount();
        int lottieShowTime = 3000;
        switch (paramCount) {
            case 1:
                coursewareModel.getLottieSegment().add(createLottieSegment(time, TimeUtils.add(time, lottieShowTime), BOTTOM_LOTTIE_ID, param.getKeyInfo1()));
                break;
            case 2:
                coursewareModel.getLottieSegment().add(createLottieSegment(time, TimeUtils.add(time, lottieShowTime), TOP_LEFT_LOTTIE_ID, param.getKeyInfo1()));
                coursewareModel.getLottieSegment().add(createLottieSegment(time, TimeUtils.add(time, lottieShowTime), TOP_RIGHT_LOTTIE_ID, param.getKeyInfo2()));
                break;
            case 3:
                coursewareModel.getLottieSegment().add(createLottieSegment(time, TimeUtils.add(time, lottieShowTime), BOTTOM_LOTTIE_ID, param.getKeyInfo1()));
                coursewareModel.getLottieSegment().add(createLottieSegment(time, TimeUtils.add(time, lottieShowTime), TOP_LEFT_LOTTIE_ID, param.getKeyInfo2()));
                coursewareModel.getLottieSegment().add(createLottieSegment(time, TimeUtils.add(time, lottieShowTime), TOP_RIGHT_LOTTIE_ID, param.getKeyInfo3()));
                break;
            default:
                break;
        }

        // create video
        VideoSegment videoSegment = SegmentUtil.createAlphaVideoSegment(TimeUtils.add(routineEndTime, -3400), routineEndTime, VIDEO_RES_ID, "").turnOnAudio();
        coursewareModel.getVideoSegment().add(videoSegment);
    }

    /**
     * create lottie by lottieId and text
     * @param time start time
     * @param endTime end time
     * @param lottieId lottie id
     * @param text text param
     * @return lottie segment
     */
    private LottieSegment createLottieSegment(String time, String endTime, String lottieId, String text) {
        LottieSegment lottieSegment = SegmentUtil.createLottieSegment(time, endTime, lottieId, "", null);
        lottieSegment.addLottieParam("#Text", "text", text).zIndex(10).volume(40);
        return lottieSegment;
    }


}







