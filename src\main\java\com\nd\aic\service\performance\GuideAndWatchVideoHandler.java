package com.nd.aic.service.performance;

import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.UISegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.performance.param.GuideAndWatchVideoParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

import lombok.RequiredArgsConstructor;

/**
 * 引导观看视频
 */
@RequiredArgsConstructor
@Service
public class GuideAndWatchVideoHandler extends AbstractPerformanceHandler {

    private static final String FIX_LEFT_POSITION_CAMERA = "5ab46e70-b065-43da-b75a-7759f053e0b9";
    private final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private final NdrService ndrService;
    private static final int DEF_DELAY_TIME = 2000;
    private static final int DEF_PAUSE_TIME = 3000;
    private static final int ANIM_TIME = 2000;

    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        getGuideAndWatchVideoParam(teachEventType, true);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        GuideAndWatchVideoParam videoParams = getGuideAndWatchVideoParam(teachEventType, false);
        if (videoParams == null) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // super.apply(time, endTime, teachEventType, coursewareModel, context);
        // 镜头
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, endTime, FIX_LEFT_POSITION_CAMERA, "引导并观看视频").targetId(context.getCharacterInstanceId());
        cameraSegment.targetId(context.getCharacterInstanceId()).setLookAt(true);
        coursewareModel.getCameraSegment().add(cameraSegment);
        context.setCurrentCameraId(cameraSegment.getResourceId());
        // 动作随机
        randomGenerateActionSegment(time, endTime, coursewareModel, context);
        // 获取视频内容
        String video = videoParams.getVideo();
        int delay = videoParams.getDelay() == null ? DEF_DELAY_TIME : videoParams.getDelay();
        int pause = videoParams.getPause() == null ? DEF_PAUSE_TIME : videoParams.getPause();
        String url = ndrService.getResourceUrl(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, video, "source");
        url = String.format("https://aic-demos.sdp.101.com/guide_play_video/guide_play_video.html?videourl=%s&delay=%d&pause=%d", url, delay, pause);
        long st = Math.max(TimeUtils.subMilliseconds(time, delay), 0);
        UISegment uiSegment = SegmentUtil.createWebUISegment(TimeUtils.formatMilliseconds(st), endTime, url, "引导并观看视频");
        coursewareModel.getUiSegment().add(uiSegment);
    }


    private GuideAndWatchVideoParam getGuideAndWatchVideoParam(TeachEventTypeDTO teachEventType, boolean forCalcTime) {
        GuideAndWatchVideoParam videoParams = teachEventType.getPerformanceParam(GuideAndWatchVideoParam.class);
        if (forCalcTime && videoParams != null && StringUtils.isNotBlank(videoParams.getVideo())) {
            String resourceId = videoParams.getVideo();
            Long duration = videoParams.getDuration();
            if (null == duration && UUID_PATTERN.matcher(videoParams.getVideo()).matches()) {
                duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId);
                teachEventType.getRawPerformanceParam().put("duration", duration);
                if (null == duration) {
                    teachEventType.getRawPerformanceParam().put("reason", "资源资源未提供时长，以对白时长为准!");
                }
            }

            // int delay = videoParams.getDelay() == null ? DEF_DELAY_TIME : videoParams.getDelay();
            int pause = videoParams.getPause() == null ? DEF_PAUSE_TIME : videoParams.getPause();
            Long performanceDuration = duration;
            if (null != performanceDuration) {
                performanceDuration = performanceDuration + pause + ANIM_TIME;
            }
            teachEventType.setResourceDuration(performanceDuration);
        }
        return videoParams;
    }
}
