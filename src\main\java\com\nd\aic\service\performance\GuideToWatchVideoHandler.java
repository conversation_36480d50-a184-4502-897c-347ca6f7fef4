package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.common.Transform;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneChangeSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.material.entity.MediaResInfo;
import com.nd.aic.service.performance.param.PlayVideoParam;
import com.nd.aic.util.SegmentUtil;

import com.nd.aic.util.TimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

import lombok.RequiredArgsConstructor;

/**
 * 引导观看视频
 */
@RequiredArgsConstructor
@Service
public class GuideToWatchVideoHandler extends AbstractPerformanceHandler {

    private final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private final NdrService ndrService;

    private static final String WATCH_VIDEO_ROUTINE_ID = "0a09ef3a-4680-48b5-80fd-85c3a0b60750";
    private static final String WATCH_VIDEO_ROUTINE_ID_2 = "c70aca9a-4cc8-4dec-b03f-ad71bcfd1b81";
    private static String VIDEO_PARAM_STR = "Texture-PlaneA-MediaTexture";

    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        PlayVideoParam videoParams = teachEventType.getPerformanceParam(PlayVideoParam.class);
        if (videoParams != null && StringUtils.isNotBlank(videoParams.getVideo())) {
            String resourceId = videoParams.getVideo();
            Long duration = videoParams.getDuration();
            if (null == duration && UUID_PATTERN.matcher(videoParams.getVideo()).matches()) {
                duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId);

                teachEventType.getRawPerformanceParam().put("duration", duration);
                if (null == duration) {
                    teachEventType.getRawPerformanceParam().put("reason", "资源资源未提供时长");
                }
            }
            teachEventType.setResourceDuration(duration);
        }
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        PlayVideoParam videoParams = getPlayVideoParam(teachEventType);
        if (videoParams == null) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 获取视频内容
        String video = videoParams.getVideo();
        // 有2种方案 (临时去掉旧的方案，没有做无限时长的拉伸 ）
        String[] resourceIds = new String[]{WATCH_VIDEO_ROUTINE_ID, WATCH_VIDEO_ROUTINE_ID_2};

        // 随机取一个
        String resourceId = resourceIds[0];

        String videoParamName = "Texture-PlaneA-MediaTexture";
        if(StringUtils.isNotEmpty(videoParams.getPlayType()) && "1".equals(videoParams.getPlayType())){
            resourceId = WATCH_VIDEO_ROUTINE_ID;
            videoParamName = "Texture-PlaneA-MediaTexture";
        } else if (StringUtils.isNotEmpty(videoParams.getPlayType()) && "2".equals(videoParams.getPlayType())) {
            resourceId = WATCH_VIDEO_ROUTINE_ID_2;
            videoParamName = "Texture-Plane-Texture";
        }

        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, resourceId, "引导观看视频").playType(0);
        routineSegment.addProperty(videoParamName, "video", video);
        routineSegment.dependencies(Lists.newArrayList(video));
        if("a1c04877-67b8-4530-8c90-c93399f710be".equals(context.getCurrentSceneId())){
            routineSegment.locationParam(4573.4794333318096, -4576.3799827905768, 1341.7233902737846);
        }
        routineSegment.rotationParam(0, 90.952702864129705, -0);

        // 设置套路视频的时长
        if (null != videoParams.getDuration()) {
            long maxDuration = Math.max(TimeUtils.getDuration(time, endTime), videoParams.getDuration());
            routineSegment.getCustom().fluentPut("Time", maxDuration / 1000.0);
        }
        coursewareModel.getRoutineSegment().add(routineSegment);

        // 结束后回到原地
        Transform transform = getPerformTransform(context, 78.0);
        BirthPointSegment birthPointSegment = SegmentUtil.createBirthPointSegmentByTransform(endTime, transform);
        coursewareModel.getBirthPointSegment().add(birthPointSegment);

        // 不是最后一个教学事件类型，增加转场
        if (!context.isLastTeachEventType(teachEventType)) {
            // 增加转场
            SceneChangeSegment sceneChangeSegment = SegmentUtil.createGlowSceneChangeSegment(endTime, 3, 1);
            coursewareModel.getSceneChangeSegment().add(sceneChangeSegment);
        }

    }


    private PlayVideoParam getPlayVideoParam(TeachEventTypeDTO teachEventType) {
        PlayVideoParam videoParams = teachEventType.getPerformanceParam(PlayVideoParam.class);
        if (videoParams != null && StringUtils.isNotBlank(videoParams.getVideo())) {
            String resourceId = videoParams.getVideo();
            Long duration = videoParams.getDuration();
            if (null == duration && UUID_PATTERN.matcher(videoParams.getVideo()).matches()) {
                duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId);
                teachEventType.getRawPerformanceParam().put("duration", duration);
                if (null == duration) {
                    teachEventType.getRawPerformanceParam().put("reason", "资源资源未提供时长，以对白时长为准");
                }
            }
            teachEventType.setResourceDuration(duration);
        }
        return videoParams;
    }
}
