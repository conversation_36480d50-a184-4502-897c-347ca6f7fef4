package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.HandHeldSignTerminationTaskParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class HandHeldSignTerminationTaskHandler  extends AbstractPerformanceHandler {
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        HandHeldSignTerminationTaskParam handHeldSignTerminationTaskParam = teachEventType.getPerformanceParam(HandHeldSignTerminationTaskParam.class);
        String keyword = StringUtils.defaultString(handHeldSignTerminationTaskParam.getKeyword(), "STOP");
        // 要创建一些镜头
        randomGenerateCameraSegment(time, endTime, coursewareModel, context);

        long duration = TimeUtils.getDuration(time, endTime);
        // 创建角色举牌
        String handHeldEndTime = endTime;
        if (duration > 9800) {
            // 创建角色举牌
            handHeldEndTime = TimeUtils.add(time, 9800);
        }
        //用正则表达式判断是否存在中文字符

        if (StringUtils.length(keyword) >= 3 && keyword.matches(".*[\\u4e00-\\u9fa5].*")) {
            // 判断keyword不全为英文字符，且长度大于等于3
            keyword = keyword.substring(0, 2) + "\n" + keyword.substring(2);
        }
        RoutineSegment characterSegment = SegmentUtil.createCharacterActionSegment(TimeUtils.add(time,100), handHeldEndTime, "28a05fe4-c746-4065-b0a7-3f4880b64512", "手持标志终止任务");
        characterSegment.playType(0).skillType("CharacterAction").targetId("").cTime(TimeUtils.getDuration(time, endTime) / 1000D);
        JSONObject param = new JSONObject()
                .fluentPut("Properties", Lists.newArrayList(
                            new JSONObject()
                                    .fluentPut("name", "FrontText")
                                    .fluentPut("type", "Text")
                                    .fluentPut("value", keyword),
                            new JSONObject()
                                    .fluentPut("name", "BackText")
                                    .fluentPut("type", "Text")
                                    .fluentPut("value", keyword)
                        )
                );
        characterSegment.getCustom().fluentPut("Param", param).fluentPut("Time", TimeUtils.getDuration(time, endTime) / 1000D);
        characterSegment.setParamVersion(1);
        coursewareModel.getRoutineSegment().add(characterSegment);
        if (duration > 9800) {
            // 创建角色举牌
            String startTime = TimeUtils.add(time, 9800);
            super.apply(startTime, endTime, teachEventType, coursewareModel, context);
        }

    }

}
