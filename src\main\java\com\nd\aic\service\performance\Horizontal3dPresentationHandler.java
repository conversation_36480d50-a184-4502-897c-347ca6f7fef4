package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BuffSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.service.performance.param.Horizontal3dPresentationParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.UUIDUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

/**
 * 横图立体呈现配合角色讲解
 */
@AllArgsConstructor
@Service
public class Horizontal3dPresentationHandler extends AbstractPerformanceHandler {

    /**
     * 悬浮画框-方
     */
    private static final String ROUTINE_RES_ID = "019d3b7d-de8f-4a87-bb01-dfa607ec59a8";


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        Horizontal3dPresentationParam param = teachEventType.getPerformanceParam(Horizontal3dPresentationParam.class);
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "横图立体呈现配合角色讲解");
        routineSegment.playType(0);
        routineSegment.addProperty(new JSONObject().fluentPut("name", "Texture01-Plane-Picture")
                .fluentPut("type", "SubMetaRole")
                .fluentPut("value", loadRes(param.getImage1(), param.getVideo1(), time, endTime, coursewareModel)));
        routineSegment.addProperty(new JSONObject().fluentPut("name", "Texture02-Plane-Picture")
                .fluentPut("type", "SubMetaRole")
                .fluentPut("value", loadRes(param.getImage2(), param.getVideo2(), time, endTime, coursewareModel)));
        routineSegment.addProperty(new JSONObject().fluentPut("name", "Texture03-Plane-Picture")
                .fluentPut("type", "SubMetaRole")
                .fluentPut("value", loadRes(param.getImage3(), param.getVideo3(), time, endTime, coursewareModel)));
        routineSegment.addProperty(new JSONObject().fluentPut("name", "Texture04-Plane-Picture")
                .fluentPut("type", "SubMetaRole")
                .fluentPut("value", loadRes(param.getImage4(), param.getVideo4(), time, endTime, coursewareModel)));
        routineSegment.addProperty(new JSONObject().fluentPut("name", "Texture05-Plane-Picture")
                .fluentPut("type", "SubMetaRole")
                .fluentPut("value", loadRes(param.getImage5(), param.getVideo5(), time, endTime, coursewareModel)));
        coursewareModel.getRoutineSegment().add(routineSegment);
        // 随机生成动作
        randomGenerateActionSegment(time, endTime, coursewareModel, context);
        // shotTrack
        int textureCount = 5;
        double perDuration = routineSegment.getDurationSec() / textureCount;
        perDuration = Math.round(perDuration * 1000.0) / 1000.0;
        for (int i = 0; i < textureCount; i++) {
            routineSegment.addShotTrack(i, perDuration);
        }

    }

    private String loadRes(String image, String video, String time, String endTime, CoursewareModel coursewareModel) {
        boolean isVideo = StringUtils.isNotBlank(video);
        String resId = isVideo ? video : image;
        String instanceId = UUIDUtil.upperUuid();
        SceneObjectSegment sceneObjectSegment = new SceneObjectSegment();
        sceneObjectSegment.time("00:00.00").endTime(endTime).resourceId(video).type("ActorObject").instanceId(instanceId);
        sceneObjectSegment.metaRoleComponentName(isVideo ? "VideoMediaRenderTarget" : "ImgSeqRenderTarget").metaRoleType("MetaRoleComponent");
        sceneObjectSegment.addProperty(createVideoObj(resId, false)).dependency(resId);
        coursewareModel.getSceneObjectSegment().add(sceneObjectSegment);

        // 播放板书视频
        BuffSegment playVideoBuff = new BuffSegment();
        playVideoBuff.time(time).endTime(endTime).type("buff").reason("开始Play").cTime(playVideoBuff.getDurationSec());
        playVideoBuff.targetId(instanceId);
        playVideoBuff.putParam("Action", createVideoObj(resId, true)).dependency(resId);
        playVideoBuff.getCustom().fluentPut("name", "MetaRoleBuff.PlayVideo");
        coursewareModel.getBuffSegment().add(playVideoBuff);

        return instanceId;
    }

    private JSONObject createVideoObj(String resourceId, boolean play) {
        return new JSONObject().fluentPut("Resource", resourceId)
                .fluentPut("StartTime", 0)
                .fluentPut("PlayRate", 1)
                .fluentPut("bAutoPlay", play)
                .fluentPut("bLoop", true)
                .fluentPut("SoundVolume", 0.1);
    }

}
