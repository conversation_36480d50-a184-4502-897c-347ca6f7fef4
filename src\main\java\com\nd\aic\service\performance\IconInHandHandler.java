package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 左右手图标表演
 */
@RequiredArgsConstructor
@Service
public class IconInHandHandler extends AbstractPerformanceHandler {

    /**
     * 左右手图标
     */
    private static final String ROUTINE_RES_ID = "01279826-8b35-4908-9063-a81d7064bc23";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        String routineEndTime = endTime;
        // 如果套路的时长太长的话，不扩展
        if (TimeUtils.getDuration(time, endTime) > 8000) {
            routineEndTime = TimeUtils.add(time, 6000);
            super.apply(routineEndTime, endTime, teachEventType, coursewareModel, context);
        }

        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "左右手图标表演");
        coursewareModel.getRoutineSegment().add(routineSegment);

        long offsetTime = TimeUtils.addMilliseconds(time, 1000);
        long offsetEndTime = TimeUtils.subMilliseconds(routineEndTime, 1200);

        // 图标一
        LottieSegment icon1 = new LottieSegment();
        icon1.setTime(TimeUtils.formatMilliseconds(offsetTime));
        icon1.setEndTime(TimeUtils.formatMilliseconds(offsetEndTime));
        icon1.setResourceId("83F8571B3DEA44B48BDE477231D2EB74");
        icon1.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("width", 320).fluentPut("height", 320).fluentPut("top", 380).fluentPut("left", 520).fluentPut("audio", new JSONObject().fluentPut("volume", 40)));
        coursewareModel.getLottieSegment().add(icon1);

        // 图标二
        LottieSegment icon2 = new LottieSegment();
        icon2.setTime(TimeUtils.formatMilliseconds(offsetTime));
        icon2.setEndTime(TimeUtils.formatMilliseconds(offsetEndTime));
        icon2.setResourceId("6CD3BA4BC6624389A8C5399AA71852DF");
        icon2.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("width", 320).fluentPut("height", 320).fluentPut("top", 405).fluentPut("left", 1105).fluentPut("audio", new JSONObject().fluentPut("volume", 40)));
        coursewareModel.getLottieSegment().add(icon2);
    }
}
