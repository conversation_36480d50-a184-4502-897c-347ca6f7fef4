package com.nd.aic.service.performance;


import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.service.performance.param.IdleParam;
import com.nd.aic.util.SegmentUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 空闲时间处理器
 * 该处理器不执行任何操作，仅用于占位
 */
@RequiredArgsConstructor
@Service
public class IdleHandler  extends  AbstractPerformanceHandler{

   // create a default map , key is roleId, value is resourceId
   private static final Map<String, String> DEFAULT_IDLE_RESOURCE_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {{
       // 皮克斯
       put("47b23a4a-f491-42f6-ab20-edeed2cc6a26", "");
       // 霓
       put("77975790-cbc0-4bf3-a62a-59b6b3227c45", "");
       // 白喵
       put("ae0213cd-6e53-4188-9ddb-a41f8fda8674", "81f2d92b-e35b-46d1-8a79-84e281937244");
   }});

   // create a default map, key is standPosition, value is cameraResourceId
    private static final Map<String, String> DEFAULT_CAMERA_RESOURCE_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {{
        /*left*/
        put("left", "8d6ca71f-90cb-47a2-8976-e48f52e2624c");
        /*right*/
        put("right", "ecd75d11-5e42-4596-9963-c9be5fbb2617");
        /*default*/
        put("default", "66faa0de-77c4-4be7-8820-efc7a9916671");
        /*half*/
        put("half_left", "5ab46e70-b065-43da-b75a-7759f053e0b9");
        put("half_right", "abaea04a-07c8-4bb3-9960-1a9ee1fc99f6");
    }});




    /**
     * 预处理
     * @param time 开始时间
     * @param endTime 结束时间
     * @param teachEventType 教学事件类型
     * @param coursewareModel   课件模型
     * @param context 上下文
     */
    @Override
    public void prepare(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.prepare(time, endTime, teachEventType, coursewareModel, context);

    }

    /**
     * 预处理
     * @param teachEventType 教学事件类型
     * @param context 上下文
     */
    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        // 设置空闲资源ID
        IdleParam idleParams = teachEventType.getPerformanceParam(IdleParam.class);

        // 处理时长，如果 idle param 中的时长为空，则使用默认的3000ms
        Long duration = idleParams.getIdleDuration();
        if (duration == null) {
            duration = 3000L;
        }
        teachEventType.setResourceDuration(duration);
    }

    /**
     * 处理空闲时间
     * @param time 开始时间
     * @param endTime 结束时间
     * @param teachEventType 教学事件类型
     * @param coursewareModel   课件模型
     * @param context 上下文
     */
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        IdleParam idleParams = teachEventType.getPerformanceParam(IdleParam.class);
        // No operation
        String idleResourceId = getIdleResourceIdByRoleId(context.getCurrentCharacterResId());
        String idleCameraResourceId = getCameraResourceIdByStandPosition(idleParams.getStandPosition());
        String roleInstanceId = context.getCharacterInstanceId();

        // 处理空闲资源ID
        if(StringUtils.isNotBlank(idleResourceId)){
            // metahuman 的方式
            if (context.getCurrentNpcCharacter() == null){
                coursewareModel.getActionSegment().add(SegmentUtil.createActionSegment(time, endTime, idleResourceId, "空闲动作", "idle"));
            } else{
                coursewareModel.getRoutineSegment().add(SegmentUtil.createActionRoutineSegment(time, endTime, idleResourceId, "待机动作").targetId(roleInstanceId).playType(0));
            }
        }else{
            // 判断前面有没有动作，有的话，则加一个停止时间，如果没有动作，则不加
            if (!coursewareModel.getActionSegment().isEmpty()) {
                ActionSegment lastActionSegment = coursewareModel.getActionSegment().get(coursewareModel.getActionSegment().size() - 1);
                if(StringUtils.isEmpty(lastActionSegment.getEndTime())){
                    coursewareModel.getActionSegment().get(coursewareModel.getActionSegment().size()-1).setEndTime(time);
                }
            }
        }



        if (StringUtils.isNotBlank(roleInstanceId)) {
            coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, idleCameraResourceId, roleInstanceId,""));
        } else {
            coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, idleCameraResourceId, ""));
        }
    }




    /**
     * 根据角色ID获取空闲资源ID
     * @param roleId 角色ID
     * @return 空闲资源ID
     */
    private String getIdleResourceIdByRoleId(String roleId) {
        if (roleId == null) {
            return DEFAULT_IDLE_RESOURCE_MAP.get("01279826-8b35-4908-9063-a81d7064bc23");
        }
        return DEFAULT_IDLE_RESOURCE_MAP.getOrDefault(roleId, DEFAULT_IDLE_RESOURCE_MAP.get("01279826-8b35-4908-9063-a81d7064bc23"));
    }

    /**
     * 根据站位获取摄像机资源ID
     * @param standPosition 站位
     * @return 摄像机资源ID
     */
    private String getCameraResourceIdByStandPosition(String standPosition){

        if (standPosition == null) {
            return DEFAULT_CAMERA_RESOURCE_MAP.get("default");
        }
        return DEFAULT_CAMERA_RESOURCE_MAP.getOrDefault(standPosition, DEFAULT_CAMERA_RESOURCE_MAP.get("default"));
    }
}
