package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.UISegment;
import com.nd.aic.service.performance.param.ImageStackShowParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.JsonUtils;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

/**
 * 引导观看视频
 */
@RequiredArgsConstructor
@Service
public class ImageStackShowHandler extends AbstractPerformanceHandler {

    private static final String FIX_LEFT_POSITION_CAMERA = "5ab46e70-b065-43da-b75a-7759f053e0b9";

    @SneakyThrows
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        ImageStackShowParam stackShowParam = getParam(teachEventType);
        if (CollectionUtils.isEmpty(stackShowParam.getImageList())) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // super.apply(time, endTime, teachEventType, coursewareModel, context);
        // 镜头
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, endTime, FIX_LEFT_POSITION_CAMERA, "多图片叠加展示").targetId(context.getCharacterInstanceId());
        cameraSegment.targetId(context.getCharacterInstanceId()).setLookAt(true);
        coursewareModel.getCameraSegment().add(cameraSegment);
        context.setCurrentCameraId(cameraSegment.getResourceId());
        // 动作随机
        randomGenerateActionSegment(time, endTime, coursewareModel, context);
        // 图片叠加展示
        long t = TimeUtils.getDuration(time, endTime) / stackShowParam.getImageList().size();
        // 获取视频内容
        List<JSONObject> images = stackShowParam.getImageList().stream().map(e -> new JSONObject().fluentPut("url", ImageUtil.getImageUrl(e)).fluentPut("t", t)).collect(Collectors.toList());
        String url = String.format("https://aic-demos.sdp.101.com/flying_images/flying_images_enhanced.html?images=%s", java.net.URLEncoder.encode(JsonUtils.toJson(images), "UTF-8"));
        UISegment uiSegment = SegmentUtil.createWebUISegment(time, endTime, url, "多图片叠加展示");
        coursewareModel.getUiSegment().add(uiSegment);
    }


    private ImageStackShowParam getParam(TeachEventTypeDTO teachEventType) {
        ImageStackShowParam performanceParam = teachEventType.getPerformanceParam(ImageStackShowParam.class);
        performanceParam.getImageList().add(performanceParam.getImage1());
        performanceParam.getImageList().add(performanceParam.getImage2());
        performanceParam.getImageList().add(performanceParam.getImage3());
        performanceParam.getImageList().add(performanceParam.getImage4());
        performanceParam.getImageList().add(performanceParam.getImage5());
        performanceParam.getImageList().add(performanceParam.getImage6());
        performanceParam.getImageList().add(performanceParam.getImage7());
        performanceParam.getImageList().add(performanceParam.getImage8());
        // performanceParam.getImageList().add(performanceParam.getImage9());
        performanceParam.getImageList().removeIf(StringUtils::isBlank);
        return performanceParam;
    }
}
