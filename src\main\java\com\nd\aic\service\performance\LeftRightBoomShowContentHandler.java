package com.nd.aic.service.performance;

import com.nd.aic.enums.LottieParamTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.LeftRightBoomShowContentParam;
import com.nd.aic.util.SegmentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LeftRightBoomShowContentHandler  extends AbstractPerformanceHandler {

    private static final String LEFT_BUBBLE_RES_ID = "72e1c8ea-a2cf-4dbc-ac8a-174ba859483e";
    private static final String RIGHT_BUBBLE_RES_ID = "dae92cfd-e9c2-4796-9ff0-991333aa6a6d";
    private static final String MIDDLE_STAND_CAMERA_RES_ID = "66faa0de-77c4-4be7-8820-efc7a9916671";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        LeftRightBoomShowContentParam param = teachEventType.getPerformanceParam(LeftRightBoomShowContentParam.class);

        if (param == null) {
            log.error("LeftRightBoomShowContentHandler apply param is null");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // add camera
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, MIDDLE_STAND_CAMERA_RES_ID,"表演方案采用固定中景").targetId(context.getCharacterInstanceId()));

        // add actions
        randomGenerateActionSegment(time, endTime, coursewareModel, context);

        // add lottie
        if(StringUtils.isNotEmpty(param.getLeftTitle())){
            String[] times =  getTimeAreaByAliasTexts(param.getLeftTitleAlias(), context, teachEventType);
            LottieSegment lottieSegment = SegmentUtil.createLottieSegment(times[0], times[1], LEFT_BUBBLE_RES_ID, "左气泡表演", null).volume(60);
            lottieSegment.addLottieParam("#Text", LottieParamTypeEnum.TEXT, param.getLeftTitle());
            coursewareModel.getLottieSegment().add(lottieSegment);
        }

        if(StringUtils.isNotEmpty(param.getRightTitle())){
            String[] times =  getTimeAreaByAliasTexts(param.getRightTitleAlias(), context, teachEventType);
            LottieSegment lottieSegment = SegmentUtil.createLottieSegment(times[0], times[1], RIGHT_BUBBLE_RES_ID, "右气泡表演", null).volume(60);
            lottieSegment.addLottieParam("#Text", LottieParamTypeEnum.TEXT, param.getRightTitle());
            coursewareModel.getLottieSegment().add(lottieSegment);
        }

    }


}
