package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.LeftRightKeywordParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 角色驱动左右手关键词表演
 */
@Service
@AllArgsConstructor
@Slf4j
public class LeftRightKeywordHandler extends AbstractPerformanceHandler{

    /**
     * 左手lottie资源参数
     */
    private static final String LEFT_LOTTIE_RES_ID = "107582a1-086f-479f-bc6f-bb1947304881";

    /**
     * 右手lottie资源参数
     */
    private static final String RIGHT_LOTTIE_RES_ID = "5453b789-f077-41b8-b77f-d3992d5ce112";

    /**
     * 大套路资源参数
     */
    private static final String BIG_ROUTINE_RES_ID = "01279826-8b35-4908-9063-a81d7064bc23";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        LeftRightKeywordParam param = teachEventType.getPerformanceParam(LeftRightKeywordParam.class);
        if (param == null || StringUtils.isEmpty(param.getLeft()) || StringUtils.isEmpty(param.getRight())){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        String routineEndTime = endTime;
        // 如果套路的时长太长的话，不扩展,播其他动作
        if (TimeUtils.getDuration(time, endTime) > 8000) {
            routineEndTime = TimeUtils.add(time, 7000);
            super.apply(routineEndTime, endTime, teachEventType, coursewareModel, context);
        }
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, routineEndTime, BIG_ROUTINE_RES_ID, "左右手图标动作和镜头");
        BirthPointSegment currentBirthPoint = context.getCurrentBirthPoint();
        if (currentBirthPoint != null){
            routineSegment.putParam("Rotation",currentBirthPoint.cloneTransform().get("rotation"));
            routineSegment.putParam("Location",currentBirthPoint.cloneTransform().get("location"));
        }



        JSONObject leftJson = new JSONObject();
        JSONObject rightJson = new JSONObject();
        for (int i = 1; i <= 3; i++){
            leftJson.fluentPut("#Text"+i, new JSONObject().fluentPut("type", "text").fluentPut("value", param.getLeft()));
            rightJson.fluentPut("#Text"+i, new JSONObject().fluentPut("type", "text").fluentPut("value", param.getRight()));
        }

        String offsetTime = TimeUtils.formatMilliseconds(TimeUtils.addMilliseconds(time, 1000));
        String offsetEndTime = TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(routineEndTime, 1200));

        //左关键字
        LottieSegment left = SegmentUtil.createLottieSegment(offsetTime, offsetEndTime, LEFT_LOTTIE_RES_ID, "左手关键字", leftJson).volume(40);
        left.addWidthHeightTopLeft(500,400,300,350);
        //右关键字
        LottieSegment right = SegmentUtil.createLottieSegment(offsetTime, offsetEndTime, RIGHT_LOTTIE_RES_ID, "右手关键字", rightJson).volume(40);
        right.addWidthHeightTopLeft(500,400,300,1070);

        coursewareModel.getRoutineSegment().add(routineSegment);
        coursewareModel.getLottieSegment().add(left);
        coursewareModel.getLottieSegment().add(right);
    }

}
