package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.LeftRightShowIconParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 角色驱动左右手图标表演
 */
@Slf4j
@Service
public class LeftRightShowIconHandler extends AbstractPerformanceHandler {

    private static final String LEFT_LOTTIE_RES_ID = "a60c5a2a-19c6-4f5e-ac09-c16eab60f4cb";
    private static final String RIGHT_LOTTIE_RES_ID = "64763ff3-a2e7-4fa6-968d-cd0e128432bf";
    private static final String BIG_ROUTINE_RES_ID = "01279826-8b35-4908-9063-a81d7064bc23";
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        LeftRightShowIconParam param = teachEventType.getPerformanceParam(LeftRightShowIconParam.class);
        if (param == null || StringUtils.isEmpty(param.getLeftIcon() ) || StringUtils.isEmpty(param.getRightIcon())){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        String routineEndTime = endTime;
        // 如果套路的时长太长的话，不扩展,播其他动作
        if (TimeUtils.getDuration(time, endTime) > 8000) {
            routineEndTime = TimeUtils.add(time, 7000);
            super.apply(routineEndTime, endTime, teachEventType, coursewareModel, context);
        }
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, routineEndTime, BIG_ROUTINE_RES_ID, "左右手图标动作和镜头");

        String offsetTime = TimeUtils.formatMilliseconds(TimeUtils.addMilliseconds(time, 1000));
        String offsetEndTime = TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(routineEndTime, 1200));

        LottieSegment LeftLottieSegment = SegmentUtil.createLottieSegment(offsetTime, offsetEndTime, LEFT_LOTTIE_RES_ID, "左手图标表演",new JSONObject());
        LeftLottieSegment.custom(new JSONObject().fluentPut("top",284).fluentPut("left",435));
        LeftLottieSegment.addLottieParam("#Image","image","https://gcdncs.101.com/v0.1/download?dentryId="+param.getLeftIcon());
        coursewareModel.getLottieSegment().add(LeftLottieSegment);

        LottieSegment RightLottieSegment = SegmentUtil.createLottieSegment(offsetTime, offsetEndTime, RIGHT_LOTTIE_RES_ID, "右手图标表演",new JSONObject());
        RightLottieSegment.custom(new JSONObject().fluentPut("top",306).fluentPut("left",1232));
        RightLottieSegment.addLottieParam("#Image","image","https://gcdncs.101.com/v0.1/download?dentryId="+param.getRightIcon());
        coursewareModel.getLottieSegment().add(RightLottieSegment);

;
        coursewareModel.getRoutineSegment().add(routineSegment);
    }
}
