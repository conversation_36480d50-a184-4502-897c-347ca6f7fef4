package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.util.SegmentUtil;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class MiniBoardShowContentByLottieHandler  extends AbstractPerformanceHandler  {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        // 补充动作内容
        randomGenerateActionSegment(time, endTime, coursewareModel, context);

        // 添加镜头
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, "EC4615934C762524B7E048B41B3DF9D4", "使用特定的镜头").targetId(context.getCharacterInstanceId()));
        JSONObject frontContent = new JSONObject().fluentPut("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", "毕业礼物设计"));
        LottieSegment backLottie = SegmentUtil.createLottieSegment(time, endTime, "FE5213AC05074FEB84BE9A0EED3E6804", "小黑板展示内容（二次元）", null).volume(80).zIndex(1);
        LottieSegment frontLottie = SegmentUtil.createLottieSegment(time, endTime, "60AA7CA168074BCBA578C34E59C1E309", "小黑板展示内容（二次元）", frontContent).volume(80).zIndex(2);
        coursewareModel.getLottieSegment().add(backLottie);
        coursewareModel.getLottieSegment().add(frontLottie);
    }
}
