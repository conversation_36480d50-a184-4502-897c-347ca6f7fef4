package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.util.SegmentUtil;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MiniBoardShowContentHandler extends AbstractPerformanceHandler  {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, "B6CB5F64412E12CBD6CA3C81678F9839", "小黑板展示内容（二次元）");

        List<JSONObject> properties = Lists.newArrayList();
        properties.add(new JSONObject().fluentPut("type", "ImgSeq").fluentPut("name","Texture").fluentPut("value", "1E1A0A641F364E00B4633BF71E8C0187"));
        properties.add(new JSONObject().fluentPut("type", "ImgSeq").fluentPut("name","Texture1").fluentPut("value", "39357F3E31814C9E8E25404CD0F12A81"));
        routineSegment.getCustom().fluentPut("Properties", properties);

        coursewareModel.getRoutineSegment().add(routineSegment);
    }
}
