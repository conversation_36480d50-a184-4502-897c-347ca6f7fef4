package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.common.Rotation;
import com.nd.aic.pojo.common.Transform;
import com.nd.aic.pojo.common.Vector3D;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.MultiGroupBoardShowContentParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.web.access.WebInvocationPrivilegeEvaluator;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class MultiGroupBoardShowContentHandler  extends AbstractPerformanceHandler {

    private static final String BIG_ROUTINE_RESOURCE_ID = "c555decc-34c6-4b22-9ce6-1125bf63a937";
    private static final String MIDDLE_STAND_CAMERA = "66faa0de-77c4-4be7-8820-efc7a9916671";
    private static final String LOTTIE_IMAGES_GROUP_1 = "a0ffd8a7-ce7a-4aac-a34c-8e315f2bb42f";
    private static final String LOTTIE_IMAGES_GROUP_2 = "b1475059-f52e-4a81-9804-abbec903ea0a";
    private static final String LOTTIE_IMAGES_GROUP_3 = "efc02506-2af9-4118-9bf8-6c63c9f00985";
    private static final String LOTTIE_IMAGES_GROUP_4 = "e59dd647-70f4-4eab-a163-365cdee91ccf";
    private static final String LOTTIE_IMAGES_GROUP_5 = "49634488-796e-43ff-95d4-69c840e51ace";
    private final WebInvocationPrivilegeEvaluator privilegeEvaluator;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        MultiGroupBoardShowContentParam param = teachEventType.getPerformanceParam(MultiGroupBoardShowContentParam.class);
        if( param == null || param.getGroupCount() == 0){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        long duration = TimeUtils.getDuration(time, endTime);


        // generate action segments
        randomGenerateActionSegment(time, endTime, coursewareModel, context);

        // add middle position camera
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, MIDDLE_STAND_CAMERA, "展示多面板内容").targetId(context.getCharacterInstanceId()));

        // reset the birth point
        BirthPointSegment birthPointSegment = context.getCurrentBirthPoint();
        if (birthPointSegment != null) {
            Transform transform = getPerformTransform(context, 78.0);
            BirthPointSegment birthPoint = SegmentUtil.createBirthPointSegmentByTransform(endTime, transform);
            coursewareModel.getBirthPointSegment().add(birthPoint);
        }
        // add character routine segments
        if(duration > 4500){
            coursewareModel.getRoutineSegment().add(SegmentUtil.createCharacterActionSegment(time, TimeUtils.add(time, 3000), BIG_ROUTINE_RESOURCE_ID, "展示多面板内容").playType(-1));
            // add character action when the duration is longer than character action duration
            randomGenerateActionSegment(TimeUtils.add(time, 3000), endTime, coursewareModel, context);
        } else{
            coursewareModel.getRoutineSegment().add(SegmentUtil.createCharacterActionSegment(time, endTime, BIG_ROUTINE_RESOURCE_ID, "展示多面板内容").playType(1));
        }

        // add lottie segments
        String[][] groups = param.getGroupImages();
        
        // find images in each group, if the group has image then ,use the index of lottie group
        for (int i =0 ; i < groups.length; i++) {
            if (isGroupHasImage(groups[i])) {
                String[] aliasTimeArea = getTimeAreaByAliasTexts(getAliasTextByGroupIndex(i, param), context, teachEventType);
                if(i == 0){
                    aliasTimeArea[0] = TimeUtils.add(time, 1300);
                    aliasTimeArea[1] = endTime;
                }
                LottieSegment lottieSegment = SegmentUtil.createLottieSegment(aliasTimeArea[0], aliasTimeArea[1], getLottieIdByGroupIndex(i), null, null).zIndex(i).volume(80);
                coursewareModel.getLottieSegment().add(lottieSegment);
                for (int j =0; j< groups[i].length; j++) {
                    if (StringUtils.isNotEmpty(groups[i][j])) {
                        lottieSegment.addLottieParam("#Image" + (j+1), "image", ImageUtil.getImageUrl(groups[i][j]));
                    } else {
                        if(j > 0 && StringUtils.isNotEmpty(groups[i][0])){
                            lottieSegment.addLottieParam("#Image" + (j+1), "image", ImageUtil.getImageUrl(groups[i][0]));
                        }
                    }
                }
            }
        }

        if(duration > 3000){
            // hide the roles
            hideCharacter(TimeUtils.add(time,2100), endTime, coursewareModel,context);
        }
    }

    /**
     * get time area by alias texts
     * @param group group
     * @return bool value
     */
    private boolean isGroupHasImage(String[] group){
        for (String image : group) {
            if (StringUtils.isNotEmpty(image)) {
                return true;
            }
        }
        return false;
    }

    /**
     * get lottie id by group index
     * @param index group index
     * @return lottie id
     */
    private String getLottieIdByGroupIndex(int index) {
        switch (index) {
            case 0:
                return LOTTIE_IMAGES_GROUP_1;
            case 1:
                return LOTTIE_IMAGES_GROUP_2;
            case 2:
                return LOTTIE_IMAGES_GROUP_3;
            case 3:
                return LOTTIE_IMAGES_GROUP_4;
            case 4:
                return LOTTIE_IMAGES_GROUP_5;
            default:
                return null;
        }

    }

    /**
     * get alias text
     * @param index index
     * @param param param value
     * @return alias text
     */
    private String getAliasTextByGroupIndex(int index, MultiGroupBoardShowContentParam param){
        switch (index) {
            case 0:
                return param.getAliasContent1();
            case 1:
                return param.getAliasContent2();
            case 2:
                return param.getAliasContent3();
            case 3:
                return param.getAliasContent4();
            case 4:
                return param.getAliasContent5();
            default:
                return null;
        }

    }


}
