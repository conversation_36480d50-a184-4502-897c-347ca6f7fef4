package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 多图立体呈现
 */
@RequiredArgsConstructor
@Service
public class MultiImageDisplayHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        if (null != teachEventType.getPerformanceConfig() && StringUtils.contains(teachEventType.getPerformanceConfig().getDescription(), "图片：傻瓜相机、单反相机、拍立得、胶卷相机")) {
            // 店里目前主要售卖的产品有：傻瓜相机、单反相机、拍立得和胶卷相机。店长要求我们结合顾客需求，分别为他们匹配推荐合适的相机！
            // TODO 多图立体展示新大套路资源
            String resourceId = "B1D7BA6844277BB27096D8BD297AD92C";
            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, resourceId, "多图立体呈现");
            coursewareModel.getRoutineSegment().add(routineSegment);
            return;
        }
        String resourceId = "B6B0FBC346F3D11D10DDE0AFD768FF3D";
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, resourceId, "多图立体呈现");
        coursewareModel.getRoutineSegment().add(routineSegment);
    }
}
