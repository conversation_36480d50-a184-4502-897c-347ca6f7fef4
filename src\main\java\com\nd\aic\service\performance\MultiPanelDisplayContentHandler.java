package com.nd.aic.service.performance;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.MultiPanelDisplayContentParam;
import com.nd.aic.util.AtaWordUtil;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 多版面展示内容
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MultiPanelDisplayContentHandler extends AbstractPerformanceHandler {

    private static final List<String> BIG_ROUTINE_PANEL3 = Lists.newArrayList("9b58492f-7452-4cb1-bf92-de8dc36b04bf", "bd1f771a-cb74-4c48-8fd8-1876aaf03578");
    private static final List<String> BIG_ROUTINE_PANEL4 = Lists.newArrayList("0af216e6-2049-472a-b74f-895dc41c9017", "95616655-7cab-4dfa-bc1e-74c7fc2fd315");
    private static final List<String> BIG_ROUTINE_PANEL5 = Lists.newArrayList("5fa49ac3-be1f-42c6-bf89-06ad7b32958c", "f57a6465-60f6-46a8-8668-6f17a32c6fa0");
    private static final List<String> BIG_ROUTINE_PANEL6 = Lists.newArrayList("e787f97d-064b-450a-98c0-3d59a2035e4a", "4802d37a-641a-44e0-b0e1-815f1fbd76c9");
    private static final Map<Integer, List<String>> BIG_ROUTINE_PANEL_MAP = ImmutableMap.<Integer, List<String>>builder()
            .put(3, BIG_ROUTINE_PANEL3)
            .put(4, BIG_ROUTINE_PANEL4)
            .put(5, BIG_ROUTINE_PANEL5)
            .put(6, BIG_ROUTINE_PANEL6)
            .build();
    private static final String WHITE_CAT_BIG_ROUTINE_PANEL = "d0fedf37-01b6-45b5-8a5a-cde0122a89b5";
    private static final String WHITE_CAT_ACTION_ROUTINE_RES_ID = "092e9312-c860-4e4d-b5ac-2f3b68df7763";

    private static final String NEON_BIG_ROUTINE_PANEL = "f0a8bd80-90da-465a-b01a-b2580699a0ba";
    private final AutomateHandler automateHandler;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useWhiteCat = CharacterUtils.isWhiteCat(context);
        boolean useNeonNew = CharacterUtils.isNeonNew(context);
        MultiPanelDisplayContentParam performanceParam = prepareParam(teachEventType, context, useWhiteCat || useNeonNew);
        if (BooleanUtils.isNotTrue(performanceParam.getIsSatisfy())) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        long startMs = TimeUtils.convertToMilliseconds(time);
        long endMs = TimeUtils.convertToMilliseconds(endTime);
        randomGenerateActionSegment(startMs + 100, endMs, teachEventType.getDialogue(), teachEventType.getDialogueStartPos(), coursewareModel, context);

        List<MultiPanelDisplayContentParam.PanelRes> medias = performanceParam.getPanelResList();
        int panelType = StringUtils.isNotBlank(performanceParam.getText0()) ? 0 : 1;
        String routineResId = BIG_ROUTINE_PANEL_MAP.getOrDefault(medias.size(), BIG_ROUTINE_PANEL3).get(panelType);
        if (useWhiteCat) {
            routineResId = WHITE_CAT_BIG_ROUTINE_PANEL;
            panelType = 1;
        } else if (useNeonNew) {
            routineResId = NEON_BIG_ROUTINE_PANEL;
            panelType = 1;
        }
        // 展示多张图片讲解
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, routineResId, "多版面展示图片讲解");
        routineSegment.playType(0);
        routineSegment.skillType("BigRoutine");
        routineSegment.targetId("");
        routineSegment.cTime(TimeUtils.getDurationSec(time, endTime));

        JSONArray shotTracks = new JSONArray();
        routineSegment.getCustom().fluentPut("shotTrack", shotTracks);
        List<JSONObject> properties = Lists.newArrayList();
        routineSegment.putParam("Properties", properties);
        routineSegment.setParamVersion(1);

        int timeIndex = 0;
        int panelIndex = 0;
        long timeOffset = 0;
        final Params params = new Params();
        params.setExistsHeader(panelType == 0);
        if (useNeonNew || useWhiteCat) {
            params.setAverageDuration(Math.max((int) ((endMs - startMs) / medias.size()) + 1, 5000));
        }
        if (panelType == 0) {
            params.setVideo(null);
            params.setText(performanceParam.getText0());
            params.setTimeOffset(timeOffset);
            params.setTimeOffset(timeIndex++);
            timeOffset = addPanelContent(routineSegment, context, teachEventType, properties, params);
        }
        for (MultiPanelDisplayContentParam.PanelRes panelRes : medias) {
            params.setVideo(panelRes.getVideo());
            params.setVideoType(StringUtils.defaultString(panelRes.getVideoType(), "Video"));
            params.setText(panelRes.getText());
            params.setTimeOffset(timeOffset);
            params.setTimeIndex(timeIndex++);
            params.setPanelIndex(panelIndex++);
            timeOffset = addPanelContent(routineSegment, context, teachEventType, properties, params);
        }
        if (medias.size() == 3) {
            // 显示首个面板内容
            params.setVideo(performanceParam.getVideo1());
            params.setVideoType(StringUtils.defaultString(performanceParam.getVideo1Type(), "Video"));
            params.setText(performanceParam.getText1());
            params.setTimeOffset(0);
            params.setPanelIndex(0);
            params.setPatch(true);
            addPanelContent(routineSegment, context, teachEventType, properties, params);
        }
        if (useWhiteCat) {
            routineSegment.putParam("PauseAtEnd", false);
            routineSegment.putParam("ObjectBindings", Lists.newArrayList(new JSONObject().fluentPut("tag", "Role").fluentPut("id", context.getCharacterInstanceId())));
            if (StringUtils.equals(context.getCurrentSceneId(), "a1c04877-67b8-4530-8c90-c93399f710be")) {
                routineSegment.locationParam(2794, -6416, 95.5);
                routineSegment.rotationParam(0, -101.1, 0);
            } else {
                routineSegment.locationParam(800, 0, -968);
                routineSegment.rotationParam(0, 60.0, 0);
            }
            if (TimeUtils.getDuration(time, endTime) > 30) {
                // https://pms.sdp.101.com/#/track/issue/detail/ee3b21c2-4b98-41fd-afe0-6d084e321a7a
                routineSegment.playType(1);
            }
            // 动作资源
            RoutineSegment actionRoutineSegment = SegmentUtil.createActionRoutineSegment(TimeUtils.add(time, 100), WHITE_CAT_ACTION_ROUTINE_RES_ID, "001_Montage");
            actionRoutineSegment.putParam("ParamVersion", 1);
            actionRoutineSegment.targetId(context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().add(actionRoutineSegment);
        } else if (useNeonNew) {
            routineSegment.bindObject("char", context.getCharacterInstanceId());
            routineSegment/*.putParam("OriginDuration", 147.33)*/.putParam("ParamVersion", 1).putParam("StopAllSkill", false);
            routineSegment.locationParam(800, 0, -968);
            routineSegment.rotationParam(0, 60.0, 0);
            if (TimeUtils.getDuration(time, endTime) > 30) {
                // https://pms.sdp.101.com/#/track/issue/detail/ee3b21c2-4b98-41fd-afe0-6d084e321a7a
                routineSegment.playType(1);
            }
            // 动作资源
            List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(TimeUtils.convertToMilliseconds(time), TimeUtils.convertToMilliseconds(endTime), 8000, 12000, context.getCompatibleActions(), context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().addAll(randomActions);
        } else if (StringUtils.equals(context.getCurrentSceneId(), "a1c04877-67b8-4530-8c90-c93399f710be")) {
            // 卡通城
            // routineSegment.setPositionParam(4915, 4242, 1866);
            routineSegment.rotationParam(0, 90, 0);
            // routineSegment.setScaleParam(1, 1, 1);
        }
        routineSegment.setDependencies(medias.stream().map(MultiPanelDisplayContentParam.PanelRes::getVideo).distinct().collect(Collectors.toList()));
        coursewareModel.getRoutineSegment().add(routineSegment);
    }

    private long addPanelContent(RoutineSegment routineSegment, CoursewareGenerationContext context, TeachEventTypeDTO teachEventType, List<JSONObject> properties, Params params) {
        int sequence = params.getPanelIndex() + 1;
        if (StringUtils.isNotBlank(params.getVideo())) {
            if (params.isPatch()) {
                properties.add(new JSONObject().fluentPut("name", String.format("Texture%02d-PlaneA-MediaTexture", sequence)).fluentPut("type", params.getVideoType()).fluentPut("value", params.getVideo()).fluentPut("Playerid", "0"));
                properties.add(new JSONObject().fluentPut("name", String.format("Texture%02d-PlaneB-MediaTexture", sequence)).fluentPut("type", params.getVideoType()).fluentPut("value", params.getVideo()).fluentPut("Playerid", "0"));
            } else {
                properties.add(new JSONObject().fluentPut("name", String.format("Texture%02d-PlaneA-MediaTexture", sequence)).fluentPut("type", params.getVideoType()).fluentPut("value", params.getVideo()).fluentPut("Playerid", String.valueOf(params.getPanelIndex())));
                properties.add(new JSONObject().fluentPut("name", String.format("Texture%02d-PlaneB-MediaTexture", sequence)).fluentPut("type", params.getVideoType()).fluentPut("value", params.getVideo()).fluentPut("Playerid", String.valueOf(params.getPanelIndex())));
            }
        }
        if (params.isPatch()) {
            return params.getTimeOffset();
        }
        long endTime = getEndTimeByAnnotateText(params.getText(), context, teachEventType);
        if (endTime <= 0) {
            endTime = params.getTimeOffset() + params.getAverageDuration();
        }
        JSONArray shotTracks = routineSegment.getParam().getJSONArray("shotTrack");
        if (shotTracks == null) {
            shotTracks = new JSONArray();
            routineSegment.putParam("shotTrack", shotTracks);
        }
        shotTracks.add(new JSONObject().fluentPut("index", params.getTimeIndex()).fluentPut("duration", (endTime - params.getTimeOffset()) / 1000D));
        return endTime;
    }

    private long getEndTimeByAnnotateText(String annotateText, CoursewareGenerationContext context, TeachEventTypeDTO teachEventType) {
        if (StringUtils.isNotEmpty(annotateText)) {
            // 处理时间问题
            int index = StringUtils.indexOf(teachEventType.getDialogue(), annotateText);
            if (index != -1) {
                // long st = WordTimeUtil.getTextPosTiming(context.getTeachEventWordTimes(), teachEventType.getDialogueStartPos() + index, true);
                long st = TimeUtils.convertToMilliseconds(teachEventType.getTime());
                long et = AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index + annotateText.length(), false);
                return et - st;
            }
        }
        return -1;
    }

    private MultiPanelDisplayContentParam prepareParam(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context, boolean patch) {
        List<MultiPanelDisplayContentParam.PanelRes> panelResList = Lists.newArrayList();
        MultiPanelDisplayContentParam performanceParam = teachEventType.getPerformanceParam(MultiPanelDisplayContentParam.class);
        panelResList.add(getResParam(performanceParam.getVideo1(), performanceParam.getVideo1Type(),
                performanceParam.getImage1(), String.format("%s-%s(%d)", context.getTitle(), "展示多张图片讲解", 1),
                performanceParam.getText1()));
        panelResList.add(getResParam(performanceParam.getVideo2(), performanceParam.getVideo2Type(),
                performanceParam.getImage2(), String.format("%s-%s(%d)", context.getTitle(), "展示多张图片讲解", 2),
                performanceParam.getText2()));
        panelResList.add(getResParam(performanceParam.getVideo3(), performanceParam.getVideo3Type(),
                performanceParam.getImage3(), String.format("%s-%s(%d)", context.getTitle(), "展示多张图片讲解", 3),
                performanceParam.getText3()));
        panelResList.add(getResParam(performanceParam.getVideo4(), performanceParam.getVideo4Type(),
                performanceParam.getImage4(), String.format("%s-%s(%d)", context.getTitle(), "展示多张图片讲解", 4),
                performanceParam.getText4()));
        panelResList.add(getResParam(performanceParam.getVideo5(), performanceParam.getVideo5Type(),
                performanceParam.getImage5(), String.format("%s-%s(%d)", context.getTitle(), "展示多张图片讲解", 5),
                performanceParam.getText5()));
        panelResList.add(getResParam(performanceParam.getVideo6(), performanceParam.getVideo6Type(),
                performanceParam.getImage6(), String.format("%s-%s(%d)", context.getTitle(), "展示多张图片讲解", 6),
                performanceParam.getText6()));

        panelResList.removeIf(Objects::isNull);
        panelResList.removeIf(e -> StringUtils.isEmpty(e.getVideo()));
        List<MultiPanelDisplayContentParam.PanelRes> originalResList = Lists.newArrayList(panelResList);
        performanceParam.setResourceCount(originalResList.size());
        if (patch && !panelResList.isEmpty() && panelResList.size() < 6) {
            // 实验室资源1-4个，用第一个资源补齐
            while (panelResList.size() < 6) {
                MultiPanelDisplayContentParam.PanelRes src = originalResList.get(panelResList.size() % originalResList.size());
                MultiPanelDisplayContentParam.PanelRes panelRes = new MultiPanelDisplayContentParam.PanelRes();
                BeanUtils.copyProperties(src, panelRes);
                panelRes.setPatch(true);
                panelResList.add(panelRes);
            }
        }
        if (panelResList.size() >= 3) {
            performanceParam.setIsSatisfy(true);
        }
        performanceParam.setPanelResList(panelResList);

        return performanceParam;
    }

    private MultiPanelDisplayContentParam.PanelRes getResParam(String video, String videoType, String image, String title, String text) {
        if (StringUtils.isNotBlank(video)) {
            MultiPanelDisplayContentParam.PanelRes panelRes = new MultiPanelDisplayContentParam.PanelRes();
            panelRes.setVideo(video);
            panelRes.setVideoType(StringUtils.defaultString(videoType, "Video"));
            panelRes.setText(text);
            return panelRes;
        }
        if (StringUtils.isNotBlank(image)) {
            try {
                String videoId = automateHandler.image2Video(title, image);
                MultiPanelDisplayContentParam.PanelRes panelRes = new MultiPanelDisplayContentParam.PanelRes();
                panelRes.setVideo(videoId);
                panelRes.setVideoType("Video");
                panelRes.setText(text);
                return panelRes;
            } catch (IOException e) {
                log.error("图片转视频失败", e);
                throw WafI18NException.of("BAD_REQUEST", "图片转视频失败", HttpStatus.BAD_REQUEST, e);
            }
        }
        return null;
    }

    @Data
    private static class Params {
        private boolean existsHeader;
        private String image;
        private String video;
        private String videoType;
        private String text;
        private long timeOffset;
        private int timeIndex;
        private int panelIndex;
        private boolean patch = false;
        private int averageDuration = 5000; // 默认时间间隔5秒
    }

}
