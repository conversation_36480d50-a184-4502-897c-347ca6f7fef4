package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.NeonExplainParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.gaea.client.ApplicationContextUtil;
import com.nd.ndr.model.ResourceMetaTiListViewModel;
import com.nd.sdp.cs.sdk.Dentry;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Pattern;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 2配合视频讲解-SQ_04视频01Neon2
 */
@AllArgsConstructor
@Service
@Slf4j
public class NeonExplainHandler extends AbstractPerformanceHandler {
    private final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private static final String INVALID_FILE_NAME = "\\/:*?<>|}{";
    private final NdrService ndrService;
    private final AutomateHandler automateHandler;

    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        if (!useVideoResource()) {
            return;
        }
        NeonExplainParam performanceParam = teachEventType.getPerformanceParam(NeonExplainParam.class);
        if (performanceParam != null && StringUtils.isNotBlank(performanceParam.getVideo())) {
            String resourceId = performanceParam.getVideo();
            Long duration = performanceParam.getDuration();
            if (null == duration && UUID_PATTERN.matcher(resourceId).matches()) {
                duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId);

                teachEventType.getRawPerformanceParam().put("duration", duration);
                if (null == duration) {
                    duration = 5000L;
                    teachEventType.getRawPerformanceParam().put("reason", "资源资源未提供时长，默认设置为5000ms");
                }
            }
            teachEventType.setResourceDuration(duration);
        }
    }

    protected boolean useVideoResource() {
        return true;
    }

    protected void addRandomActionSegment(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        long startMs = TimeUtils.convertToMilliseconds(time);
        long endMs = TimeUtils.convertToMilliseconds(endTime);
        List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(startMs, endMs, 8000, 12000, context.getCompatibleActions());
        coursewareModel.getRoutineSegment().addAll(randomActions);
    }

    protected NeonExplainParam prepareParam(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context, int paramCount) {
        String title = StringUtils.defaultString(context.getTitle(), UUID.randomUUID().toString());
        for (int i = 0; i < INVALID_FILE_NAME.length(); i++) {
            char c = INVALID_FILE_NAME.charAt(i);
            title = StringUtils.replace(title, String.valueOf(c), "_");
        }
        NeonExplainParam neonExplainParam = teachEventType.getPerformanceParam(NeonExplainParam.class);
        if (paramCount > 1) {
            List<NeonExplainParam.Res> videos = Lists.newArrayList();
            videos.add(getResource(neonExplainParam.getVideo1(), neonExplainParam.getImage1(), title + "_1"));
            videos.add(getResource(neonExplainParam.getVideo2(), neonExplainParam.getImage2(), title + "_2"));
            videos.add(getResource(neonExplainParam.getVideo3(), neonExplainParam.getImage3(), title + "_3"));
            videos.removeIf(Objects::isNull);
            int size = videos.size() ;
            while (videos.size() < paramCount && !videos.isEmpty()) {
                videos.add(videos.get(videos.size() % size));
            }
            neonExplainParam.setResList(videos);
        } else {
            neonExplainParam.setRes(getResource(neonExplainParam.getVideo(), neonExplainParam.getImage(), title));
        }

        return neonExplainParam;
    }

    private NeonExplainParam.Res getResource(String video, String image, String title) {
        if (StringUtils.isNotBlank(video)) {
            return new NeonExplainParam.Res("Video", video);
        }
        if (StringUtils.isNotBlank(image)) {
            try {
                if (UUID_PATTERN.matcher( image).matches()) {
                    MediaService mediaService = ApplicationContextUtil.getApplicationContext().getBean(MediaService.class);
                    Dentry dentry = Dentry.get("aic_service_scontent", image, null);
                    String downloadUrl = String.format("https://cdncs.101.com/v0.1/static/%s", StringUtils.stripStart(dentry.getPath(), "/"));
                    File file = mediaService.download(downloadUrl, dentry.getName());
                    ResourceMetaTiListViewModel meta = ndrService.createImageMeta(dentry.getName(), file, null, NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID);
                    return new NeonExplainParam.Res("ImgSeq", meta.getId());
                }
                log.info("image2Video: {}", image);
                String videoId = automateHandler.image2Video(title, image);
                return new NeonExplainParam.Res("Video", videoId);
            } catch (Exception e) {
                log.error("图片资源入库失败", e);
                throw WafI18NException.of("BAD_REQUEST", "图片资源入库失败", HttpStatus.BAD_REQUEST, e);
            }
        }
        return null;
    }

}
