package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.NeonExplainParam;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 3对着视频讲解-SQ_05图片01Neon2
 */
@Service
public class NeonExplainToVideoHandler extends NeonExplainHandler {
    /**
     * 3对着视频讲解-SQ_05图片01Neon2
     */
    private static final String BIG_ROUTINE_RES_ID = "1424dd3b-c241-4546-a869-51092446d88a";

    public NeonExplainToVideoHandler(NdrService ndrService, AutomateHandler automateHandler) {
        super(ndrService, automateHandler);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useNeon = CharacterUtils.isNeon(context);
        NeonExplainParam performanceParam = prepareParam(teachEventType, context, 1);
        if (useNeon) {
            NeonExplainParam.Res res = performanceParam.getRes();
            if (Objects.nonNull(res)) {
                RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "对着视频讲解（NEON）");
                routineSegment.targetId("");
                routineSegment.putParam("OriginDuration", 7).putParam("ParamVersion", 1).putParam("StopAllSkill", false);
                routineSegment.addProperty("Texture-StaticMeshComponent0-Texture", performanceParam.getRes().getType(), performanceParam.getRes().getValue());
                routineSegment.dependency(performanceParam.getRes().getValue());
                coursewareModel.getRoutineSegment().add(routineSegment);
            }
            addRandomActionSegment(time, endTime, teachEventType, coursewareModel, context);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

}
