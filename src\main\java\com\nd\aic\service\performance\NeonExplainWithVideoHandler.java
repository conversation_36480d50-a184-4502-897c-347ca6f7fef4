package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.NeonExplainParam;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 2配合视频讲解-SQ_04视频01Neon2
 */
@Service
public class NeonExplainWithVideoHandler extends NeonExplainHandler {
    /**
     * 2配合视频讲解-SQ_04视频01Neon2
     */
    private static final String BIG_ROUTINE_RES_ID = "8dd55e20-fdb1-471d-8eab-bdea8aeb65f6";

    public NeonExplainWithVideoHandler(NdrService ndrService, AutomateHandler automateHandler) {
        super(ndrService, automateHandler);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useNeon = CharacterUtils.isNeon(context);
        NeonExplainParam performanceParam = prepareParam(teachEventType, context, 1);
        if (useNeon) {
            NeonExplainParam.Res res = performanceParam.getRes();
            if (Objects.nonNull(res)) {
                RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "配合视频讲解（NEON）");
                routineSegment.targetId("");
                routineSegment.putParam("OriginDuration", 15.2).putParam("ParamVersion", 1).putParam("StopAllSkill", false);
                routineSegment.addProperty("Texture-StaticMeshComponent0-Tex", res.getType(), res.getValue());
                routineSegment.dependency(res.getValue());
                coursewareModel.getRoutineSegment().add(routineSegment);
            }
            addRandomActionSegment(time, endTime, teachEventType, coursewareModel, context);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

}
