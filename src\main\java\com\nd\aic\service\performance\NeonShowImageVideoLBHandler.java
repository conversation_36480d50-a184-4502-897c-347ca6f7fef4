package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.NeonExplainParam;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

/**
 * 8左下方召唤小黑板-展示图片、视频素材SQ_33展示板书01Neon4
 */
@Service
@Slf4j
public class NeonShowImageVideoLBHandler extends NeonExplainHandler {
    /**
     * 8左下方召唤小黑板-展示图片、视频素材SQ_33展示板书01Neon4
     */
    private static final String BIG_ROUTINE_RES_ID = "fec6f19c-0cad-4306-acda-68b5a9932183";

    public NeonShowImageVideoLBHandler(NdrService ndrService, AutomateHandler automateHandler) {
        super(ndrService, automateHandler);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useNeon = CharacterUtils.isNeon(context);
        NeonExplainParam performanceParam = prepareParam(teachEventType, context, 1);
        if (useNeon) {
            NeonExplainParam.Res res = performanceParam.getRes();
            log.info("NeonShowImageVideoLBHandler: {}", res);
            if (Objects.nonNull(res)) {
                RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "左下方召唤小黑板（NEON）");
                routineSegment.targetId("");
                routineSegment.putParam("OriginDuration", 15.2).putParam("ParamVersion", 1).putParam("StopAllSkill", false);
                routineSegment.addProperty("Texture-StaticMeshComponent0-Texture", res.getType(), res.getValue());
                routineSegment.dependency(res.getValue());
                coursewareModel.getRoutineSegment().add(routineSegment);
            }
            addRandomActionSegment(time, endTime, teachEventType, coursewareModel, context);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

}
