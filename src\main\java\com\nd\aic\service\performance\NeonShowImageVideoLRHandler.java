package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.NeonExplainParam;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 7左右展示两个素材-SQ_34展示板书02Neon2
 */
@Slf4j
@Service
public class NeonShowImageVideoLRHandler extends NeonExplainHandler {
    /**
     * 7左右展示两个素材-SQ_34展示板书02Neon2
     */
    private static final String BIG_ROUTINE_RES_ID = "81248845-e6db-4b7b-9d39-2995339a9799";

    public NeonShowImageVideoLRHandler(NdrService ndrService, AutomateHandler automateHandler) {
        super(ndrService, automateHandler);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useNeon = CharacterUtils.isNeon(context);
        NeonExplainParam performanceParam = prepareParam(teachEventType, context, 2);
        if (useNeon) {
            List<NeonExplainParam.Res> resList = performanceParam.getResList();
            log.info("NeonShowImageVideoLRHandler:resList:{}", resList);
            if (CollectionUtils.isNotEmpty(resList)) {
                RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "左右展示两个素材（NEON）");
                routineSegment.targetId("");
                routineSegment.putParam("OriginDuration", 15.4).putParam("ParamVersion", 1).putParam("StopAllSkill", false);
                for (int i = 0; i < resList.size(); i++) {
                    NeonExplainParam.Res res = resList.get(i);
                    routineSegment.addProperty(String.format("Texture%d-StaticMeshComponent0-Texture", i + 1), res.getType(), res.getValue());
                    routineSegment.dependency(res.getValue());
                }
                coursewareModel.getRoutineSegment().add(routineSegment);
            }
            addRandomActionSegment(time, endTime, teachEventType, coursewareModel, context);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

}
