package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.NeonExplainParam;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 5展示三个图片讲解-SQ_33展示板书01Neon3
 */
@Service
public class NeonShowThreeImagesHandler extends NeonExplainHandler {
    /**
     * 5展示三个图片讲解-SQ_33展示板书01Neon3
     */
    private static final String BIG_ROUTINE_RES_ID = "fa5cd5d9-b2a9-452a-a20c-d3d5da68a06e";

    public NeonShowThreeImagesHandler(NdrService ndrService, AutomateHandler automateHandler) {
        super(ndrService, automateHandler);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useNeon = CharacterUtils.isNeon(context);
        NeonExplainParam performanceParam = prepareParam(teachEventType, context, 3);
        if (useNeon) {
            List<NeonExplainParam.Res> resList = performanceParam.getResList();
            if (CollectionUtils.isNotEmpty(resList)) {
                RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "展示三个图片讲解（NEON）");
                routineSegment.targetId("");
                routineSegment.putParam("OriginDuration", 20.4).putParam("ParamVersion", 1).putParam("StopAllSkill", false);
                for (int i = 0; i < resList.size(); i++) {
                    NeonExplainParam.Res res = resList.get(i);
                    routineSegment.addProperty(String.format("Texture%d-StaticMeshComponent0-Texture", i + 1), res.getType(), res.getValue());
                    routineSegment.dependency(res.getValue());
                }
                coursewareModel.getRoutineSegment().add(routineSegment);
            }
            addRandomActionSegment(time, endTime, teachEventType, coursewareModel, context);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

}
