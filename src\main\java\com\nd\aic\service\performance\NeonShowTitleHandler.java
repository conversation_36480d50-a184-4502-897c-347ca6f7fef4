package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.NeonExplainParam;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 4展示标题-SQ_06标题Neon3
 */
@Service
public class NeonShowTitleHandler extends NeonExplainHandler {
    /**
     * 4展示标题-SQ_06标题Neon3
     */
    private static final String BIG_ROUTINE_RES_ID = "0028a863-2e8c-4de1-a175-81812ac600ba";

    public NeonShowTitleHandler(NdrService ndrService, AutomateHandler automateHandler) {
        super(ndrService, automateHandler);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        boolean useNeon = CharacterUtils.isNeon(context);
        NeonExplainParam performanceParam = prepareParam(teachEventType, context, 1);
        if (useNeon) {
            NeonExplainParam.Res res = performanceParam.getRes();
            if (Objects.nonNull(res)) {
                RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "展示标题（NEON）");
                routineSegment.targetId("");
                routineSegment.putParam("OriginDuration", 8.8).putParam("ParamVersion", 1).putParam("StopAllSkill", false);
                routineSegment.addProperty("Texture-StaticMeshComponent0-Texture", res.getType(), res.getValue());
                routineSegment.dependency(res.getValue());
                coursewareModel.getRoutineSegment().add(routineSegment);
            }
            long startMs = TimeUtils.convertToMilliseconds(time);
            long endMs = TimeUtils.convertToMilliseconds(endTime);
            List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(startMs, endMs, 8000, 12000, context.getCompatibleActions());
            coursewareModel.getRoutineSegment().addAll(randomActions);
        } else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

}
