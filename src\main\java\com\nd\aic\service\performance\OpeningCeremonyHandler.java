package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.AppearBookParam;
import com.nd.aic.service.performance.param.OpeningCeremonyParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.UUIDUtil;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Collections;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 开课仪式
 */
@AllArgsConstructor
@Service
@Slf4j
public class OpeningCeremonyHandler extends AbstractPerformanceHandler {

    private static final String BIG_ROUTINE_RES_ID = "de9a108b-fdcc-4c14-9339-ce7b9807d3e1";
    private final MediaService mediaService;
    private final AutomateHandler automateHandler;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        OpeningCeremonyParam openingCeremonyParam = prepareParams(teachEventType, context);
        String videoResId = openingCeremonyParam.getVideo();
        if (StringUtils.isBlank(videoResId)) {
            log.warn("视频资源ID为空，无法生成抛出书本动作， 退化为普通讲解");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        RoutineSegment pointToTv = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "转身指向电视");
        JSONObject param = new JSONObject()
                .fluentPut("Properties", Lists.newArrayList(
                        new JSONObject()
                            .fluentPut("name", "Texture-DianShi_Rectangle001-Texture")
                            .fluentPut("type", "Video")
                            .fluentPut("value", videoResId))
                );
        pointToTv.getCustom()
                .fluentPut("PlayType", 0)
                .fluentPut("SkillType", "BigRoutine")
                .fluentPut("TargetID", "")
                .fluentPut("Time", TimeUtils.getDuration(time, endTime) / 1000f)
                .put("Param", param);
        pointToTv.setParamVersion(1);
        pointToTv.setDependencies(Lists.newArrayList(videoResId));
        coursewareModel.getRoutineSegment().add(pointToTv);
    }

    @SneakyThrows
    private OpeningCeremonyParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        OpeningCeremonyParam performanceParam = teachEventType.getPerformanceParam(OpeningCeremonyParam.class);
        String videoResId = performanceParam.getVideo();
        if (StringUtils.isBlank(videoResId) && StringUtils.isNotBlank(performanceParam.getImage())) {
            // 生成视频
            String downloadUrl = ImageUtil.getImageUrl(performanceParam.getImage());
            File fileInfo = mediaService.download(downloadUrl, performanceParam.getImage());
            List<File> mediaInfo = automateHandler.images2Videos(Collections.singletonList(fileInfo));
            List<ResourceMetaTiListViewModel> resInfos = automateHandler.submitVideo2Ndr(context.getTitle() + teachEventType.getTeachEventType(), mediaInfo);
            videoResId = resInfos.get(0).getId();
            performanceParam.setVideo(videoResId);
        }
        return performanceParam;
    }
}
