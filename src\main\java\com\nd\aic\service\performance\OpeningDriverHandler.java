package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BuffSegment;
import com.nd.aic.pojo.segment.PathFindingSegment;

import org.springframework.stereotype.Service;

/**
 * 小黑板展示
 */
@Service
public class OpeningDriverHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        BuffSegment buffSegment = new BuffSegment();
        buffSegment.setTime(time);
        buffSegment.setEndTime(endTime);
        // buffSegment.setResourceId("79057BB849F00D2A203ECAABD8AD62FA");
        buffSegment.setResourceId("");
        buffSegment.setType("buff");
        buffSegment.setCode("standby");
        buffSegment.setReason("开车");
        buffSegment.setDependencies(Lists.newArrayList("5A29CF6947875E7230B6AF85084D7AB4"));
        // 动漫成样条线 F7480E6C4041EAF4C70438B52EF4112A
        JSONObject custom = new JSONObject().fluentPut("Param", new JSONObject().fluentPut("VehicleId", "1087AF114870DF1A02DE4A975C580F26"))
                // .fluentPut("Mode", "RidingMode")
                .fluentPut("Time", buffSegment.getDuration() / 1000.0)
                .fluentPut("TargetID", "")
                .fluentPut("Name", "Movement.Vehicle");
        buffSegment.setCustom(custom);
        coursewareModel.getBuffSegment().add(buffSegment);

        // 动漫成样条线 F7480E6C4041EAF4C70438B52EF4112A
        PathFindingSegment pathFindingSegment = new PathFindingSegment();
        pathFindingSegment.setTime(time);
        pathFindingSegment.setEndTime(endTime);
        pathFindingSegment.setResourceId("F7480E6C4041EAF4C70438B52EF4112A");
        pathFindingSegment.setReason("开车");
        pathFindingSegment.setCustom(new JSONObject().fluentPut("Speed", 700).fluentPut("Mode", "RidingMode"));
        coursewareModel.getPathFindingSegment().add(pathFindingSegment);
    }
}
