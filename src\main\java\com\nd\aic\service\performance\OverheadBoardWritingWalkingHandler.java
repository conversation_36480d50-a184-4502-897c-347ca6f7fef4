package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.performance.param.OverheadBoardWritingWalkingParam;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class OverheadBoardWritingWalkingHandler extends AbstractPerformanceHandler {

    /**
     * 俯视镜头下板书上前行
     */
    private static final String ROUTINE_RES_ID = "59cedf65-ba3d-4b70-ab64-dfc30a6f70fe";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        OverheadBoardWritingWalkingParam param = teachEventType.getPerformanceParam(OverheadBoardWritingWalkingParam.class);
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "俯视镜头下板书上前行");
        routineSegment.playType(0);
        coursewareModel.getRoutineSegment().add(routineSegment);
        // 背景视频
        if (StringUtils.isNotBlank(param.getVideo())) {
            VideoSegment videoSegment = SegmentUtil.createBackVideoSegment(time, endTime, param.getVideo());
            coursewareModel.getVideoSegment().add(videoSegment);
        }
    }
}
