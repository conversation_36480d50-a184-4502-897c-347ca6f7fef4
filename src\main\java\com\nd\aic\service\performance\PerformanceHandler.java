package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;

public interface PerformanceHandler {

    /**
     * 准备阶段（主要是根据当前表演方案中的素材，计算出表演方案的时间）
     *
     * @param teachEventType 教学事件类型
     * @param context        课件生成上下文
     */
    default void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
    }

    default void prepare(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
    }

    void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context);

    /**
     * 是否需要板书点位
     */
    default boolean requireBoardPoint() {
        return false;
    }

}
