package com.nd.aic.service.performance;

import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.material.MediaAssembleService;
import com.nd.aic.service.material.entity.MediaResInfo;
import com.nd.aic.service.performance.param.PlayVideoParam;
import com.nd.aic.util.Iso8601Utils;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

import lombok.AllArgsConstructor;

/**
 * 播放视频
 */
@AllArgsConstructor
@Service
public class PlayAlphaHandler extends AbstractPerformanceHandler {

    private final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private MediaAssembleService mediaAssembleService;
    private NdrService ndrService;

    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        PlayVideoParam videoParams = teachEventType.getPerformanceParam(PlayVideoParam.class);
        if (videoParams != null && StringUtils.isNotBlank(videoParams.getVideo())) {
            String resourceId = videoParams.getVideo();
            Long duration = videoParams.getDuration();
            if (null == duration && UUID_PATTERN.matcher(videoParams.getVideo()).matches()) {
                duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId);

                teachEventType.getRawPerformanceParam().put("duration", duration);
                if (null == duration) {
                    duration = 5000L;
                    teachEventType.getRawPerformanceParam().put("reason", "资源资源未提供时长，默认设置为5000ms");
                }
            }
            teachEventType.setResourceDuration(duration);
        }
    }

    @Override
    public void prepare(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.prepare(time, endTime, teachEventType, coursewareModel, context);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);
        PlayVideoParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if (performanceParam == null || !performanceParam.getIsSatisfy()) {
            // 没有资源降级为基础表演
            return;
        }

        VideoSegment videoSegment = SegmentUtil.createAlphaVideoSegment(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, 300)), endTime, performanceParam.getVideo(), "播放视频").turnOnAudio();
        coursewareModel.getVideoSegment().add(videoSegment);
    }


    private PlayVideoParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        PlayVideoParam performanceParam = teachEventType.getPerformanceParam(PlayVideoParam.class);
        if (performanceParam != null && !Strings.isEmpty(performanceParam.getVideo())) {
            // 可用
            performanceParam.setIsSatisfy(true);
            return performanceParam;
        } else {
            return null;
        }
    }

}
