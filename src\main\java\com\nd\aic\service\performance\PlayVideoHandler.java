package com.nd.aic.service.performance;

import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.ResourcePackageService;
import com.nd.aic.service.material.MediaAssembleService;
import com.nd.aic.service.material.entity.MediaResInfo;
import com.nd.aic.service.performance.param.PlayVideoParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

import lombok.AllArgsConstructor;

/**
 * 播放视频
 */
@Slf4j
@AllArgsConstructor
@Service
public class PlayVideoHandler extends AbstractPerformanceHandler {

    private final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private MediaAssembleService mediaAssembleService;
    private NdrService ndrService;
    private final ResourcePackageService resourcePackageService;

    @Override

    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        PlayVideoParam videoParams = teachEventType.getPerformanceParam(PlayVideoParam.class);
        if (videoParams != null && StringUtils.isNotBlank(videoParams.getVideo())) {
            String resourceId = videoParams.getVideo();
            Long duration = videoParams.getDuration();
            if (null == duration && UUID_PATTERN.matcher(resourceId).matches()) {
                duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId);

                teachEventType.getRawPerformanceParam().put("duration", duration);
                if (null == duration) {
                    duration = 5000L;
                    teachEventType.getRawPerformanceParam().put("reason", "资源资源未提供时长，默认设置为5000ms");
                }
            }else if (resourceId.startsWith("http")){
                MediaResInfo mediaResInfo = resourcePackageService.submitFrontVideo(resourceId);
                duration = mediaResInfo.getDuration();
                teachEventType.getRawPerformanceParam().put("duration", duration);
                teachEventType.getRawPerformanceParam().put("video", mediaResInfo.getResourceId());
            }
            teachEventType.setResourceDuration(duration);
        } else {
            MediaResInfo mediaResInfo = mediaAssembleService.getMedia(teachEventType, context);
            if (mediaResInfo != null) {
                teachEventType.setResourceDuration(mediaResInfo.getDuration());
            }
        }
    }

    @Override
    public void prepare(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.prepare(time, endTime, teachEventType, coursewareModel, context);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        // 没有资源降级为基础表演
        PlayVideoParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if (performanceParam == null || !performanceParam.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // Long duration = TimeUtils.getDuration(time, endTime);
        // Long videoDuration = performanceParam.getDuration();
        // String videoTime = TimeUtils.formatMilliseconds(Math.max(TimeUtils.subMilliseconds(time, 300), 0));
        // String videoEndTime = endTime;
        // if (videoDuration != null && duration > videoDuration){
        //     videoEndTime = TimeUtils.add(videoTime, videoDuration);
        //     super.apply(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(videoEndTime,100)), endTime, teachEventType, coursewareModel, context);
        // }
        VideoSegment videoSegment = SegmentUtil.createFrontVideoSegment(time, endTime, performanceParam.getVideo(), "播放视频").turnOnAudio();
        if (null != performanceParam.getDuration()) {
            Long milliseconds = TimeUtils.getDuration(time, endTime);
            double rate = performanceParam.getDuration() / milliseconds.doubleValue();
            videoSegment.rate(rate);
        }
        coursewareModel.getVideoSegment().add(videoSegment);
    }


    private PlayVideoParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        PlayVideoParam performanceParam = teachEventType.getPerformanceParam(PlayVideoParam.class);
        if (performanceParam != null && !Strings.isEmpty(performanceParam.getVideo())) {
            // 可用
            performanceParam.setIsSatisfy(true);
            return performanceParam;
        } else {
            return null;
        }
    }

}
