package com.nd.aic.service.performance;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.PointToBlackboardParam;
import com.nd.aic.util.AtaWordUtil;
import com.nd.aic.util.CharacterUtils;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 手搓小黑板
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PointToBlackboardHandler extends AbstractPerformanceHandler {

    private final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private static final String BIG_ROUTINE_RES_ID = "d8494e25-ff36-434c-a42c-b85be2ac55e9";
    private static final String WHITE_CAT_BIG_ROUTINE_RES_ID = "290bcc20-9cc7-4a60-a94e-f0aef975a134";
    private static final String WHITE_CAT_ACTION_ROUTINE_RES_ID = "092e9312-c860-4e4d-b5ac-2f3b68df7763";
    private static final String NEON_BIG_ROUTINE_RES_ID = "3901c00b-7c35-4b3c-916d-a373ee4af58b";
    private final AutomateHandler automateHandler;
    private final NdrService ndrService;
    private final MediaService mediaService;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        PointToBlackboardParam pointToBlackboardParam = prepareParam(teachEventType, context);
        if (StringUtils.isEmpty(pointToBlackboardParam.getVideo1()) || StringUtils.isEmpty(pointToBlackboardParam.getVideo2())) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        // BigRoutine
        boolean useWhiteCat = CharacterUtils.isWhiteCat(context);
        boolean useNeonNew = CharacterUtils.isNeonNew(context);
        // String routineResID = useWhiteCat ? VLIB_BIG_ROUTINE_RES_ID : BIG_ROUTINE_RES_ID;
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "手搓小黑板");
        coursewareModel.getRoutineSegment().add(routineSegment);
        routineSegment.playType(0);
        if (useWhiteCat) {
            routineSegment.setResourceId(WHITE_CAT_BIG_ROUTINE_RES_ID);
            routineSegment.targetId("");
            routineSegment.putParam("PauseAtEnd", false);
            routineSegment.bindObject("TestRole", context.getCharacterInstanceId());
            routineSegment.putParam("Properties", Lists.newArrayList(
                    new JSONObject()
                            .fluentPut("name", "Texture-Plane-Texture")
                            .fluentPut("type", StringUtils.defaultIfBlank(pointToBlackboardParam.getVideo1Type(), "Video"))
                            .fluentPut("Playerid", "0")
                            .fluentPut("value", pointToBlackboardParam.getVideo1())));
            if (StringUtils.equals(context.getCurrentSceneId(), "a1c04877-67b8-4530-8c90-c93399f710be")) {
                routineSegment.locationParam(2794, -6416, 95.5);
                routineSegment.rotationParam(0, -101.1, 0);
            } else {
                routineSegment.locationParam(367, -3, -80);
                routineSegment.rotationParam(0, 90, 0);
            }

            RoutineSegment actionRoutineSegment = SegmentUtil.createActionRoutineSegment(TimeUtils.add(time, 100), WHITE_CAT_ACTION_ROUTINE_RES_ID, "001_Montage");
            actionRoutineSegment.putParam("ParamVersion", 1);
            actionRoutineSegment.targetId(context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().add(actionRoutineSegment);
        } else if (useNeonNew) {
            routineSegment.setResourceId(NEON_BIG_ROUTINE_RES_ID);
            routineSegment.targetId("");
            routineSegment.bindObject("char", context.getCharacterInstanceId());
            routineSegment/*.putParam("OriginDuration", 147.33)*/.putParam("ParamVersion", 1).putParam("StopAllSkill", false);
            routineSegment.locationParam(367, -3, -80);
            routineSegment.rotationParam(0, 90, 0);
            routineSegment.putParam("Properties", Lists.newArrayList(
                    new JSONObject()
                            .fluentPut("name", "Texture-Plane-Texture")
                            .fluentPut("type", StringUtils.defaultIfBlank(pointToBlackboardParam.getVideo1Type(), "Video"))
                            .fluentPut("Playerid", "0")
                            .fluentPut("value", pointToBlackboardParam.getVideo1())));

            List<RoutineSegment> randomActions = PerformanceUtil.randomCreateRoutineActions(TimeUtils.convertToMilliseconds(time), TimeUtils.convertToMilliseconds(endTime), 8000, 12000, context.getCompatibleActions(), context.getCharacterInstanceId());
            coursewareModel.getRoutineSegment().addAll(randomActions);
        } else {

            String video1Type = StringUtils.isEmpty(pointToBlackboardParam.getVideo1Type()) ? "Video" : pointToBlackboardParam.getVideo1Type();
            String video2Type = StringUtils.isEmpty(pointToBlackboardParam.getVideo2Type()) ? "Video" : pointToBlackboardParam.getVideo2Type();

            List<JSONObject> properties = Lists.newArrayList(
                    new JSONObject()
                            .fluentPut("name", "Texture-Plane-Texture")
                            .fluentPut("type", video1Type)
                            .fluentPut("value", pointToBlackboardParam.getVideo1()),
                    new JSONObject()
                            .fluentPut("name", "Texture-Plane-Texture")
                            .fluentPut("type", video1Type)
                            .fluentPut("value", pointToBlackboardParam.getVideo1()),
                    new JSONObject()
                            .fluentPut("name", "Texture3-Plane-Texture")
                            .fluentPut("type", video2Type)
                            .fluentPut("value", pointToBlackboardParam.getVideo2())
            );
            routineSegment.getCustom()
                    .fluentPut("Param", new JSONObject().fluentPut("Properties", properties));
            if (StringUtils.isNotEmpty(pointToBlackboardParam.getText1())) {
                double time1 = getOffsetTimeByAnnotateText(pointToBlackboardParam.getText1(), context, teachEventType);
                if (time1 > 0) {
                    routineSegment.addShotTrack(0, time1);
                }
                // double time2 = getOffsetTimeByAnnotateText(pointToBlackboardParam.getText2(), context, teachEventType);
                long duration = TimeUtils.getDuration(time, endTime);
                double time2 = duration / 1000d - time1;
                if (time2 > 0) {
                    routineSegment.addShotTrack(1, time2);
                }
            } else {
                // 兼容虚拟实现室只传video1参数
                Double sec = TimeUtils.getDurationSec(time, endTime);
                routineSegment.addShotTrack(0, sec);
            }
        }
        routineSegment.setParamVersion(1);
        routineSegment.setDependencies(Lists.newArrayList(Sets.newHashSet(pointToBlackboardParam.getVideo1(), pointToBlackboardParam.getVideo2())));
    }

    private double getOffsetTimeByAnnotateText(String annotateText, CoursewareGenerationContext context, TeachEventTypeDTO teachEventType){
        if(StringUtils.isNotEmpty(annotateText)) {
            // 处理时间问题
            int index = StringUtils.indexOf(teachEventType.getDialogue(), annotateText);
            if (index != -1) {
                // long st = WordTimeUtil.getTextPosTiming(context.getTeachEventWordTimes(), teachEventType.getDialogueStartPos() + index, true);
                long et = AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index + annotateText.length(), false);
                Double sec = TimeUtils.getDurationSec(teachEventType.getTime(), TimeUtils.formatMilliseconds(et));
                return sec != null ? sec : -1;
            }
        }
        return -1.0;
    }

    private PointToBlackboardParam prepareParam(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        PointToBlackboardParam pointToBlackboardParam = teachEventType.getPerformanceParam(PointToBlackboardParam.class);
        if (StringUtils.isEmpty(pointToBlackboardParam.getVideo1()) && StringUtils.isNotEmpty(pointToBlackboardParam.getImage1())) {
            String video = null;
            try {
                video = automateHandler.image2Video(String.format("%s-%s(1)", context.getTitle(), "手搓小黑板"), pointToBlackboardParam.getImage1());
            } catch (IOException e) {
                log.error("图片转视频失败", e);
            }
            pointToBlackboardParam.setVideo1(video);
        }
        if (StringUtils.isEmpty(pointToBlackboardParam.getVideo2()) && StringUtils.isNotEmpty(pointToBlackboardParam.getImage2())) {
            String video = null;
            try {
                video = automateHandler.image2Video(String.format("%s-%s(2)", context.getTitle(), "手搓小黑板"), pointToBlackboardParam.getImage2());
            } catch (IOException e) {
                log.error("图片转视频失败", e);
            }
            pointToBlackboardParam.setVideo2(video);
        }
        if (StringUtils.isNotEmpty(pointToBlackboardParam.getVideo1()) && !UUID_PATTERN.matcher(pointToBlackboardParam.getVideo1()).matches()) {
            try {
                pointToBlackboardParam.setVideo1(url2ResourceId(pointToBlackboardParam.getVideo1()));
            } catch (IOException e) {
                log.error("url转资源ID失败", e);
            }
        }
        if (StringUtils.isNotEmpty(pointToBlackboardParam.getVideo2()) && !UUID_PATTERN.matcher(pointToBlackboardParam.getVideo2()).matches()) {
            try {
                pointToBlackboardParam.setVideo2(url2ResourceId(pointToBlackboardParam.getVideo2()));
            } catch (IOException e) {
                log.error("url转资源ID失败", e);
            }
        }
        if (StringUtils.isEmpty(pointToBlackboardParam.getVideo2())) {
            pointToBlackboardParam.setVideo2(pointToBlackboardParam.getVideo1());
            pointToBlackboardParam.setVideo2Type(pointToBlackboardParam.getVideo1Type());
        }
        return pointToBlackboardParam;
    }

    private String url2ResourceId(String video) throws IOException {
        if (StringUtils.startsWithAny(video, "http://", "https://")) {
            String fileName = String.format("%s.mp4", UUID.nameUUIDFromBytes(video.getBytes(StandardCharsets.UTF_8)));
            File mp4File = mediaService.download(video, fileName, true);
            long duration = mediaService.durationMills(mp4File);
            ResourceMetaTiListViewModel meta = ndrService.createVideoMeta("手搓小黑板资源", mp4File, duration);
            return meta.getId();
        }
        return video;
    }
}
