package com.nd.aic.service.performance;

import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.NdrService;

import com.nd.aic.service.performance.param.RoleShowSymbolParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 角色驱动图标表演
 * 复用贴图表演
 */
@Service
public class RoleShowSymbolHandler extends AbstractPerformanceHandler {

    /**
     * middle stand camera resource id
     */
    private final String middleStandCameraResourceId = "66faa0de-77c4-4be7-8820-efc7a9916671";
    private final NdrService ndrService;

    public RoleShowSymbolHandler(NdrService ndrService) {
        this.ndrService = ndrService;
    }


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        RoleShowSymbolParam performanceParam = teachEventType.getPerformanceParam(RoleShowSymbolParam.class);
        if (performanceParam == null || StringUtils.isEmpty(performanceParam.getSymbolId())) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // generate actions
        randomGenerateActionSegment(time, endTime, coursewareModel, context);

        // add middle camera
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, middleStandCameraResourceId, "使用特定的镜头").targetId(context.getCharacterInstanceId()));

        // get video time
        long videoTime =  ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, performanceParam.getSymbolId());

        // check video time
        if (videoTime > TimeUtils.getDuration(time, endTime)) {
            videoTime = TimeUtils.getDuration(time, endTime);
        }

        // add symbol
        coursewareModel.getLottieSegment().add(SegmentUtil.createLottieSegment(time, TimeUtils.add(time, videoTime), performanceParam.getSymbolId(), "角色驱动图标表演", null));

    }

}
