package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.common.Rotation;
import com.nd.aic.pojo.common.Vector3D;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 角色旋转特效辅助亮相
 */
@Slf4j
@Service
public class RotationEffectsAssistedDebutHandler extends AbstractPerformanceHandler{

    private static final String BIG_ROUTINE_RES_ID = "930f920b-a179-47b9-9fa7-5583ea6d25e5";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        String routineEndTime = endTime;
        if (TimeUtils.getDuration(time, endTime) > 4000){
            routineEndTime = TimeUtils.add(time,4000);
            super.apply(routineEndTime, endTime, teachEventType, coursewareModel, context);
        }
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, routineEndTime, BIG_ROUTINE_RES_ID, "Pose定格").playType(0);
        BirthPointSegment currentBirthPoint = context.getCurrentBirthPoint();
        if (Objects.equals(context.getGlobalScene(), "a1c04877-67b8-4530-8c90-c93399f710be") || currentBirthPoint == null){
            routineSegment.rotationParam(0,0,0);
            routineSegment.locationParam(0,-850,0);
        }else {
            routineSegment.putParam("Rotation",currentBirthPoint.cloneTransform().get("rotation"));
            routineSegment.putParam("Location",currentBirthPoint.cloneTransform().get("location"));
        }


        coursewareModel.getRoutineSegment().add(routineSegment);
    }
}
