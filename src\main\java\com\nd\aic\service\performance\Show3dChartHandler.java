package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.service.performance.param.Show3dChartParam;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class Show3dChartHandler extends AbstractPerformanceHandler {

    /**
     * 3D图表
     */
    private static final String OBJECT_RES_ID = "7779641E4663BF0B260CEC8CAFF11467";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);
        Show3dChartParam param = teachEventType.getPerformanceParam(Show3dChartParam.class);
        if (CollectionUtils.isEmpty(param.getXAxisData()) || CollectionUtils.isEmpty(param.getYAxisData())) {
            return;
        }
        SceneObjectSegment chartObject = new SceneObjectSegment();
        chartObject.time(time).endTime(endTime).resourceId(OBJECT_RES_ID).type("ActorObject").reason("3D图表");
        chartObject.relativeCharacter().location(-30, -80, 60).rotation(0, 0, 0);
        chartObject.addProperty("Percents", ParamPropertyTypeEnum.ARRAY, Lists.newArrayList(param.getYAxisData()));
        chartObject.addProperty("TextDatas", ParamPropertyTypeEnum.ARRAY, Lists.newArrayList(param.getXAxisData()));
        chartObject.addProperty("NeedAnim", ParamPropertyTypeEnum.BOOL, true);
        coursewareModel.getSceneObjectSegment().add(chartObject);
    }
}
