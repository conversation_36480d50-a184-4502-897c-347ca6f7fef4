package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.ShowArtisticTextQuestionKeyInfoParam;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

import java.util.Arrays;

/**
 * 艺术字获取关键信息
 */
@AllArgsConstructor
@Service
public class ShowArtisticTextQuestionKeyInfoHandler extends AbstractPerformanceHandler {


    private static final String middleStandCameraResourceId = "66faa0de-77c4-4be7-8820-efc7a9916671";
//    private static final String actionResourceId = "8f8a0884-08da-4b1b-85e5-608dc8a186f3";

    private static final String leftLottieResourceId = "466b9597-748f-4565-83ef-dda92180a0b0";
    private static final String rightLottieResourceId = "36eca156-bc55-414d-9dca-636b62ecf947";
    private static final String buttonLottieResourceId = "6a827fde-d490-464e-aefe-13a54ec45af3";


    @Override
    public void prepare(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.prepare(time, endTime, teachEventType, coursewareModel, context);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        ShowArtisticTextQuestionKeyInfoParam param = teachEventType.getPerformanceParam(ShowArtisticTextQuestionKeyInfoParam.class);
        if(param == null || (StringUtils.isEmpty(param.getKeyInfo1()) && StringUtils.isEmpty(param.getKeyInfo2()) && StringUtils.isEmpty(param.getKeyInfo3()))) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 基础的动作与镜头
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, middleStandCameraResourceId, "使用固定中间构图的镜头").targetId(context.getCharacterInstanceId()));
        // 使用随机动作
        randomGenerateActionSegment(time, endTime, coursewareModel, context);
//        coursewareModel.getActionSegment().add(SegmentUtil.createActionSegment(time,  actionResourceId, "讲述","动作可以展示艺术字"));

        String[] keyInfos = getKeyInfos(param);


        if (keyInfos.length == 1){
            LottieSegment lottieSegment2 = SegmentUtil.createLottieSegment(time, endTime, buttonLottieResourceId, "艺术字问题",null).volume(70);
            lottieSegment2.getCustom().fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", keyInfos[0])));
            coursewareModel.getLottieSegment().add(lottieSegment2);
        } else if (keyInfos.length == 2){
            // 添加Lottie
            LottieSegment lottieSegment = SegmentUtil.createLottieSegment(time, endTime, leftLottieResourceId, "艺术字问题",null).volume(70).windowId(1);
            LottieSegment lottieSegment1 = SegmentUtil.createLottieSegment(time, endTime, rightLottieResourceId, "艺术字问题",null).volume(70).windowId(2);
            lottieSegment.getCustom().fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", keyInfos[0])));
            lottieSegment1.getCustom().fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", keyInfos[1])));
            coursewareModel.getLottieSegment().add(lottieSegment);
            coursewareModel.getLottieSegment().add(lottieSegment1);
        } else if (keyInfos.length >= 3){
            // 添加Lottie
            LottieSegment lottieSegment = SegmentUtil.createLottieSegment(time, endTime, leftLottieResourceId, "艺术字问题",null).volume(70).windowId(1);
            LottieSegment lottieSegment1 = SegmentUtil.createLottieSegment(time, endTime, rightLottieResourceId, "艺术字问题",null).volume(70).windowId(2);
            LottieSegment lottieSegment2 = SegmentUtil.createLottieSegment(time, endTime, buttonLottieResourceId, "艺术字问题",null).volume(70).windowId(3);
            lottieSegment.getCustom().fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", keyInfos[0])));
            lottieSegment1.getCustom().fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", keyInfos[1])));
            lottieSegment2.getCustom().fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", keyInfos[2])));

            // 增加目标用户
            coursewareModel.getLottieSegment().add(lottieSegment);
            coursewareModel.getLottieSegment().add(lottieSegment1);
            coursewareModel.getLottieSegment().add(lottieSegment2);
        }

    }

    /**
     * do not return the empty string
     * @param param param
     * @return key infos
     */
    private String[] getKeyInfos(ShowArtisticTextQuestionKeyInfoParam param) {
        String[] all = new String[] {param.getKeyInfo1(), param.getKeyInfo2(), param.getKeyInfo3()};
        // do not return the empty string
        return Arrays.stream(all).filter(StringUtils::isNotEmpty).toArray(String[]::new);
    }
}
