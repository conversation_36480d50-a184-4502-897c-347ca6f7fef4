package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.ShowCountdownParam;
import com.nd.aic.util.SegmentUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 展示倒计时
 */
@Service
@AllArgsConstructor
@Slf4j
public class ShowCountdownHandler extends AbstractPerformanceHandler{

    /**
     * 倒计时
     */
    private static final String BIG_ROUTINE_RES_ID = "a1799d30-9c83-43a6-a613-0967b81b3774";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        ShowCountdownParam param = prepareParams(teachEventType,coursewareModel,context);
        if (param==null || !param.getIsSatisfy()){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        //展示倒计时套路
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "展示倒计时");
        routineSegment.playType(0);

        routineSegment.addProperty("InitialTimeMinutes","int",param.getMinutes());
        routineSegment.addProperty("InitialTimeSeconds","int",param.getSeconds());

        coursewareModel.getRoutineSegment().add(routineSegment);
    }
    private ShowCountdownParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context){
        ShowCountdownParam performanceParam = teachEventType.getPerformanceParam(ShowCountdownParam.class);
        if (performanceParam==null){
            return null;
        }
        if (performanceParam.getMinutes()==null){
            performanceParam.setMinutes(0);
        }

        if (performanceParam.getSeconds()==null){
            performanceParam.setIsSatisfy(false);
            return performanceParam;
        }

        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }


}
