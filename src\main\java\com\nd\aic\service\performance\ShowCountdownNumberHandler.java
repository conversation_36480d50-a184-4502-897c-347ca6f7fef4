package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;

import org.springframework.stereotype.Service;

/**
 * 显示倒计时数字
 */
@Service
public class ShowCountdownNumberHandler extends AbstractPerformanceHandler{

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);

        // todo...显示倒计时数字
        // 默认在枚举中配置30秒
    }
}
