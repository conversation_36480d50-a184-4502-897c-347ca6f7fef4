package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.common.Transform;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraFeatureSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.ShowDocumentContentParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 展示文档内容
 */
@Primary
@Service
public class ShowDocumentContentHandler  extends  AbstractPerformanceHandler  {

    private static final String BIG_ROUTINE_RES_ID = "757ee5f6-f0c5-4d5f-8822-642b9020f023";

    private static final String LOTTIE_IMAGE_RES_ID = "cf17deba-b1c4-437b-939d-ce8c9d9a13fe";

    private static final String LOTTIE_TEXT_RES_ID = "d2b02b8f-4131-4c8f-b387-8b6b09c62ef8";


    @Override
    public void prepare(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.prepare(time, endTime, teachEventType, coursewareModel, context);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        ShowDocumentContentParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if(performanceParam == null || !performanceParam.getIsSatisfy()){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 添加动作，延迟一点时间
        randomGenerateActionSegment(TimeUtils.add(time, 100), endTime, coursewareModel, context);

        // 显示文档内容大套路
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "展示文档内容");
        coursewareModel.getRoutineSegment().add(routineSegment);

        if (performanceParam.getText()!=null){
            JSONObject param = new JSONObject();
            // 添加lottie
            param.fluentPut("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getText())).fluentPut("#Text2", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getText()))
                    .fluentPut("#Text3", new JSONObject().fluentPut("type", "text").fluentPut("value", performanceParam.getText()));

            LottieSegment segment =  SegmentUtil.createLottieSegment(TimeUtils.add(time,300), TimeUtils.add(endTime, -200), LOTTIE_TEXT_RES_ID, "显示文档", param).zIndex(1);
            coursewareModel.getLottieSegment().add(segment);
        }


        // 如果是图片，则增加一个lottie
        if(Strings.isNotEmpty(performanceParam.getImage())) {
            LottieSegment lottieSegment = SegmentUtil.createLottieSegment(time, endTime, LOTTIE_IMAGE_RES_ID, "展示文档封面", null).zIndex(10);
            lottieSegment.addLottieParam("#Image", "image", "http://gcdncs.101.com/v0.1/download?dentryId=" + performanceParam.getImage());
            coursewareModel.getLottieSegment().add(lottieSegment);
        }


        Transform transform = getPerformTransform(context, 70.0);
        // 设置一个坐标
        routineSegment.getCustom().fluentPut("Param", new JSONObject().fluentPut("Location", transform.getPosition())
                .fluentPut("Rotation", transform.getRotation())
                .fluentPut("Scale", transform.getScale()));

        // 这个时间段内看向镜头
        CameraFeatureSegment cameraFeatureSegment = SegmentUtil.createLookAtCameraFeatureSegment(TimeUtils.add(time, 100), endTime);
        coursewareModel.getCameraFeatureSegment().add(cameraFeatureSegment);

    }

    private ShowDocumentContentParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        ShowDocumentContentParam performanceParam = teachEventType.getPerformanceParam(ShowDocumentContentParam.class);
        if (performanceParam == null || (StringUtils.isEmpty(performanceParam.getText()) && StringUtils.isEmpty(performanceParam.getImage()))) {
            return null;
        }

        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }

}
