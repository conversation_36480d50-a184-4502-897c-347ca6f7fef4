package com.nd.aic.service.performance;

import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.ShowDocumentCoverParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 展示文档封面
 */
@Service
public class ShowDocumentCoverHandler extends AbstractPerformanceHandler {


    private static final String SHOW_DOCUMENT_COVER = "94495a35-b868-4241-9388-a88ee673a1e7";


    @Override
    public void prepare(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.prepare(time, endTime, teachEventType, coursewareModel, context);
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        ShowDocumentCoverParam performanceParam = teachEventType.getPerformanceParam(ShowDocumentCoverParam.class);
        if (performanceParam == null || StringUtils.isBlank(performanceParam.getText())) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        randomGenerateActionSegment(TimeUtils.add(time, 100), endTime, coursewareModel, context);
        // 展示文档封面
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, SHOW_DOCUMENT_COVER, "展示文档封面").setParamVersion(1).playType(0);
        routineSegment.addProperty("Text", ParamPropertyTypeEnum.TEXT, performanceParam.getText());

        coursewareModel.getRoutineSegment().add(routineSegment);
    }

}
