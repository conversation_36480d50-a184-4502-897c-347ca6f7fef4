package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.ShowKeyContentBothSideParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Primary
@Service
public class ShowKeyContentBothSideHandler extends AbstractPerformanceHandler {


    /**
     * right stand camera
     */
    private static final String rightStandCameraResourceId = "abaea04a-07c8-4bb3-9960-1a9ee1fc99f6";

    /**
     * left board lottie
     */
    private static final String leftBoardLottieResourceId = "04966f65-d16d-4bd8-9be1-1ad7c32599df";

    /**
     * right board lottie
     */
    private static final String rightBoardLottieResourceId = "8871df50-bf54-4f55-9f37-06c8416f9949";

    /**
     * left stand camera
     */
    private static final String leftStandCameraResourceId = "5ab46e70-b065-43da-b75a-7759f053e0b9";


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        // 准备参数
        ShowKeyContentBothSideParam param = prepareParam(teachEventType, context);
        if (param == null || !param.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 随机创建动作
        randomGenerateActionSegment(TimeUtils.add(time, 100), endTime, coursewareModel, context);

        // 添加动作，延迟一点时间
        // 计算整体的表演方案的时间跨度
        long duration = TimeUtils.getDuration(time, endTime);

        // 如果有2段文本需要展示的话
        if (StringUtils.isNotEmpty(param.getContent1()) && StringUtils.isNotEmpty(param.getContent2())) {
            // 如果小于12秒，则采用中间构图的镜头，中间构图+2个板来提示
            // if (duration < 10000L) {
            //     // 如果没有进行时间卡点，则默认使用0~5， 5~10的时间段展示2个板
            //     coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, rightStandCameraResourceId, "采用右侧构图的方式展示关键内容"));
            //
            //     // 增加右侧出现的lottie
            //     LottieSegment leftLottie = SegmentUtil.createLottieSegment(TimeUtils.add(time, 3000), TimeUtils.add(time, 5000), leftBoardLottieResourceId, "展示关键内容", null);
            //     leftLottie.addLottieParam("#Text1", "text", param.getKey1()).addLottieParam("#Text2", "text", param.getContent1()).volume(80).zIndex(1);
            //     coursewareModel.getLottieSegment().add(leftLottie);
            //
            //     // 增加右侧出现的lottie
            //     LottieSegment leftLottie2;
            //     if (TimeUtils.getDuration(TimeUtils.add(time,5000),endTime) > 2000){
            //         leftLottie2 = SegmentUtil.createLottieSegment(TimeUtils.add(time, 5000), endTime, rightBoardLottieResourceId, "展示关键内容", null);
            //     }else {
            //         leftLottie2 = SegmentUtil.createLottieSegment(TimeUtils.add(time, 5000), TimeUtils.add(time,10000), rightBoardLottieResourceId, "展示关键内容", null);
            //     }
            //
            //     leftLottie2.addLottieParam("#Text1", "text", param.getKey2()).addLottieParam("#Text2", "text", param.getContent2()).volume(80).zIndex(1);
            //     coursewareModel.getLottieSegment().add(leftLottie2);
            //
            //
            // } else
            {
                // 如果大于12秒，则采用一前一后的方式，暂时不考虑卡点的信息
                // 如果没有进行时间卡点，则默认使用0~5， 5~10的时间段展示2个板
                String middle = TimeUtils.add(time, duration / 2);
                coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, middle, rightStandCameraResourceId, "采用右侧构图的方式展示关键内容").targetId(context.getCharacterInstanceId()));
                // 增加右侧出现的lottie
                LottieSegment leftLottie = SegmentUtil.createLottieSegment(time, middle, leftBoardLottieResourceId, "展示关键内容", null);
                leftLottie.addLottieParam("#Text1", "text", param.getKey1()).addLottieParam("#Text2", "text", param.getContent1()).volume(80).zIndex(1);
                coursewareModel.getLottieSegment().add(leftLottie);

                // TODO...resid 需要替换真实的镜头线上资源
                coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(middle, endTime, leftStandCameraResourceId, "采用左侧构图的方式展示关键内容").targetId(context.getCharacterInstanceId()));
                LottieSegment rightLottie = SegmentUtil.createLottieSegment(middle, endTime, rightBoardLottieResourceId, "展示关键内容", null);
                rightLottie.addLottieParam("#Text1", "text", param.getKey2()).addLottieParam("#Text2", "text", param.getContent2()).volume(80).zIndex(1);
                coursewareModel.getLottieSegment().add(rightLottie);
            }
        } else {
            // 只有一段文本要展示
            coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, rightStandCameraResourceId, "采用右侧构图的方式展示关键内容").targetId(context.getCharacterInstanceId()));
            // 增加右侧出现的lottie
            LottieSegment leftLottie = SegmentUtil.createLottieSegment(TimeUtils.add(time, 3000), endTime, leftBoardLottieResourceId, "展示关键内容", null);
            leftLottie.addLottieParam("#Text1", "text", param.getKey1()).addLottieParam("#Text2", "text", param.getContent1()).volume(80).zIndex(1);
            coursewareModel.getLottieSegment().add(leftLottie);
        }
    }


    /**
     * 准备参数
     * @param teachEventType 事件类型
     * @param context 上下文
     * @return 参数
     */
    private ShowKeyContentBothSideParam prepareParam(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        ShowKeyContentBothSideParam param = teachEventType.getPerformanceParam(ShowKeyContentBothSideParam.class);
        if (param == null) {
            return  null;
        }

        if (StringUtils.isEmpty(param.getKey1()) && StringUtils.isEmpty(param.getContent1())) {
            if(StringUtils.isEmpty(param.getKey2()) && StringUtils.isEmpty(param.getContent2())) {
                return null;
            }
        }

        param.setIsSatisfy(true);
        return param;
    }
}
