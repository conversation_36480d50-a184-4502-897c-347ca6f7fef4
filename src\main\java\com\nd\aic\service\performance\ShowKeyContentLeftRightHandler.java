package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.ShowKeyContentLeftRightParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
@Slf4j
public class ShowKeyContentLeftRightHandler extends AbstractPerformanceHandler {

    private static final String BIG_ROUTINE_RESOURCE_ID = "d45f583e-abdf-4d53-8c8c-3456ef83801f";
    private final AutomateHandler automateHandler;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        ShowKeyContentLeftRightParam param = prepareParam(teachEventType);
        if (StringUtils.isEmpty(param.getLeftVideo()) && StringUtils.isEmpty(param.getRightVideo())) {
            log.warn("左右展示重点内容参数不完整，退化为普通讲解");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RESOURCE_ID, "展示重点内容左右");

        // segment total duration
        long duration = TimeUtils.getDuration(time, endTime);

        if (duration < 10000) {
            log.warn("展示重点内容左右时间过短，退化为普通讲解");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // if it has left video, least 13 seconds by the param config
        // if it has right video, least 14.5 seconds by the param config
        // only use left video
        if(duration < 15000){
            routineSegment.addProperty("Texture-Plane-Texture", "Video", param.getLeftVideo());
            routineSegment.playType(0).addShotTrack(0, duration / 1000.0);
        } else{
            routineSegment.addProperty("Texture-Plane-Texture", "Video", param.getLeftVideo());
            routineSegment.playType(0).addShotTrack(0, duration / 1000 * 2.0);
            routineSegment.addProperty("Texture2-Plane-Texture", "Video", param.getRightVideo());
            routineSegment.playType(0).addShotTrack(1, duration / 1000 * 2.0);
        }
        coursewareModel.getRoutineSegment().add(routineSegment);
    }


    private ShowKeyContentLeftRightParam prepareParam(TeachEventTypeDTO teachEventType) {
        ShowKeyContentLeftRightParam param = teachEventType.getPerformanceParam(ShowKeyContentLeftRightParam.class);
        try {
            if (StringUtils.isNotEmpty(param.getLeftImage())) {
                String videoNdrId = automateHandler.image2Video("", param.getLeftImage());
                param.setLeftVideo(videoNdrId);
            }
            if (StringUtils.isNotEmpty(param.getRightImage())) {
                String videoNdrId = automateHandler.image2Video("", param.getRightImage());
                param.setRightVideo(videoNdrId);
            }

        } catch (Exception e) {
            log.error("图片转视频失败", e);
        }
        return param;
    }







}
