package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.MultiQuestionParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

/**
 * 显示贴纸（贴图问题表演）
 */

@AllArgsConstructor
@Service
public class ShowMultiQuestionBySticker extends AbstractPerformanceHandler {


    private static final String middle_camera_resource_id = "66faa0de-77c4-4be7-8820-efc7a9916671";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        MultiQuestionParam performanceParam = prepareParams(teachEventType, coursewareModel, context);

        // 参数不满足，走默认逻辑
        if(performanceParam == null || !performanceParam.getIsSatisfy()){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }


        // 加镜头
        CameraSegment camera =  SegmentUtil.createCharacterCameraSegment(time, endTime, middle_camera_resource_id,"使用固定中间构图的镜头").targetId(context.getCharacterInstanceId());
        coursewareModel.getCameraSegment().add(camera);
        randomGenerateActionSegment(time, endTime, coursewareModel, context);

        if(Strings.isNotEmpty(performanceParam.getTitle1()) && Strings.isNotEmpty(performanceParam.getContent1())){
            performanceParam.setIsSatisfy(true);
            // LottieSegment lottieSegment = getLottieSegment(TimeUtils.add(time,300), endTime, 0);
            // coursewareModel.getLottieSegment().add(lottieSegment);
            // 左下猫
            coursewareModel.getLottieSegment().add(SegmentUtil
                    .createLottieSegment(TimeUtils.add(time,300), endTime, "2913d67d-5d0e-4745-ab83-c00c5c88c635", "展示多个问题1", null).zIndex(103)
                    .addLottieParam("#text1", "text", performanceParam.getTitle1())
                    .addLottieParam("#text2", "text", performanceParam.getContent1()));
        }

        if(Strings.isNotEmpty(performanceParam.getTitle2()) && Strings.isNotEmpty(performanceParam.getContent2())){
            performanceParam.setIsSatisfy(true);
            // LottieSegment lottieSegment = getLottieSegment(TimeUtils.add(time,300), endTime, 1);
            // coursewareModel.getLottieSegment().add(lottieSegment);
            // 右下猫
            coursewareModel.getLottieSegment().add(SegmentUtil
                    .createLottieSegment(TimeUtils.add(time,300), endTime, "0e01199b-6b79-4276-b74b-23cb6a685aed", "展示多个问题2", null).zIndex(102)
                    .addLottieParam("#text1", "text", performanceParam.getTitle2())
                    .addLottieParam("#text2", "text", performanceParam.getContent2()));
        }

        if(Strings.isNotEmpty(performanceParam.getTitle3()) && Strings.isNotEmpty(performanceParam.getContent3())){
            performanceParam.setIsSatisfy(true);
            // LottieSegment lottieSegment = getLottieSegment(TimeUtils.add(time,300), endTime, 2);
            // coursewareModel.getLottieSegment().add(lottieSegment);
            // 左上猫
            coursewareModel.getLottieSegment().add(SegmentUtil
                    .createLottieSegment(TimeUtils.add(time,300), endTime, "677f4097-ff58-4417-9072-f35395f4bd90", "展示多个问题3", null).zIndex(101)
                    .addLottieParam("#text1", "text", performanceParam.getTitle3())
                    .addLottieParam("#text2", "text", performanceParam.getContent3()));
        }

        if(Strings.isNotEmpty(performanceParam.getTitle4()) && Strings.isNotEmpty(performanceParam.getContent4())){
            performanceParam.setIsSatisfy(true);
            // LottieSegment lottieSegment = getLottieSegment(TimeUtils.add(time,300), endTime, 3);
            // coursewareModel.getLottieSegment().add(lottieSegment);
            // 右上猫
            coursewareModel.getLottieSegment().add(SegmentUtil
                    .createLottieSegment(TimeUtils.add(time,300), endTime, "6ffc23e4-78ad-47b0-9a2c-b77ba12a9b3f", "展示多个问题4", null).zIndex(100)
                    .addLottieParam("#text1", "text", performanceParam.getTitle4())
                    .addLottieParam("#text2", "text", performanceParam.getContent4()));
        }

    }

    private LottieSegment getLottieSegment(String time, String endTime, int i) {
        LottieSegment catLottie = null;
        switch (i) {
            case 0:
                catLottie = SegmentUtil.createLottieSegment(time, endTime, "3c91886d-7041-41c8-84f2-db00246f74d2", "展示多个问题1", null).zIndex(10);
                break;
            case 1:
                catLottie = SegmentUtil.createLottieSegment(time, endTime, "1297bb49-ad98-417b-944c-cb6e498baa89", "展示多个问题2", null).zIndex(9);
                break;
            case 2:
                catLottie = SegmentUtil.createLottieSegment(time, endTime, "b381b3c5-a2e2-4e30-aa58-29bdf39636f4", "展示多个问题3", null).zIndex(8);
                break;
            default:
                catLottie = SegmentUtil.createLottieSegment(time, endTime, "33303b43-cb56-41d1-85e8-4772bf450c8c", "展示多个问题4", null).zIndex(7);
                break;
        }

        catLottie.getCustom().put("audio", getAudioSetting());


        return catLottie;
    }


    private JSONObject getAudioSetting(){
        return new JSONObject().fluentPut("volume", 70);
    }

    private MultiQuestionParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        MultiQuestionParam performanceParam = teachEventType.getPerformanceParam(MultiQuestionParam.class);
        if(performanceParam == null) {
            return null;
        }
        if(StringUtils.isEmpty(performanceParam.getTitle1()) && StringUtils.isEmpty(performanceParam.getContent1())
            && StringUtils.isEmpty(performanceParam.getTitle2()) && StringUtils.isEmpty(performanceParam.getContent2())
            && StringUtils.isEmpty(performanceParam.getTitle3()) && StringUtils.isEmpty(performanceParam.getContent3())
            && StringUtils.isEmpty(performanceParam.getTitle4()) && StringUtils.isEmpty(performanceParam.getContent4())){
            return null;
        }

        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }

}
