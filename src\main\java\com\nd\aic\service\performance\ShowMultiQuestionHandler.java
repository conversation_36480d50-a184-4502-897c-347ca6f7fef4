package com.nd.aic.service.performance;


import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.MultiQuestionTitleParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ShowMultiQuestionHandler extends AbstractPerformanceHandler{


    private static final String CHARACTER_ACTION_RES_ID = "73dcba56-8696-45ea-b542-088696e81ef8";
    private static final String LOTTIE_RES_ID = "60fb7914-b42f-4cff-801f-027f3896b259";

    // it may be replaced by a variable in the future, when it used by different roles
    private static final String CHARACTER_CAMERA_RES_ID = "66faa0de-77c4-4be7-8820-efc7a9916671";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        // 降级
        MultiQuestionTitleParam performanceParam = prepareParams(teachEventType, coursewareModel, context);
        if (performanceParam == null || !performanceParam.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }


        long characterActionDuration = 3200;
        long duration = TimeUtils.getDuration(time, endTime);
        String characterActionEndTime = TimeUtils.add(time, characterActionDuration);
        String lottieEndTime = endTime;

        if(duration < characterActionDuration){
            // 时长不足，不生成角色动作
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        if (duration > characterActionDuration) {
            // 补上之后的动作
            randomGenerateActionSegment(characterActionEndTime, endTime, coursewareModel, context);
        }

        if(duration > 10000){
            lottieEndTime = TimeUtils.add(time, 10000);
        }


        RoutineSegment routineSegment = SegmentUtil.createCharacterActionSegment(time, characterActionEndTime, CHARACTER_ACTION_RES_ID, "角色展示多个问题").playType(0);
        // 添加角色行为
        coursewareModel.getRoutineSegment().add(routineSegment);
        LottieSegment segment = SegmentUtil.createLottieSegment(TimeUtils.add(time, 1500), lottieEndTime, LOTTIE_RES_ID, "展示多个问题", new JSONObject());
        segment.addLottieParam("#Text1", "text", performanceParam.getText1());
        segment.addLottieParam("#Text2", "text", performanceParam.getText2());


        coursewareModel.getLottieSegment().add(segment);


        // 补上镜头
        coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, CHARACTER_CAMERA_RES_ID, "使用固定中间构图的镜头").targetId(context.getCharacterInstanceId()));

    }

    private MultiQuestionTitleParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        MultiQuestionTitleParam multiQuestionTitleParam = teachEventType.getPerformanceParam(MultiQuestionTitleParam.class);

        if (multiQuestionTitleParam == null) {
            return null;
        }

        if(Strings.isEmpty(multiQuestionTitleParam.getText1()) || Strings.isEmpty(multiQuestionTitleParam.getText2())){
            multiQuestionTitleParam.setIsSatisfy(false);
            return multiQuestionTitleParam;
        }


        multiQuestionTitleParam.setIsSatisfy(true);
        return multiQuestionTitleParam;

    }

}
