package com.nd.aic.service.performance;

import com.nd.aic.enums.RoutineTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.ShowPaperCoverParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

/**
 * 展示纸质材料封面
 */
@AllArgsConstructor
@Service
public class ShowPaperCoverHandler extends AbstractPerformanceHandler {

    /**
     * 打响指
     */
    private static final String CHARACTER_ROUTINE_RES_ID = "2a38bc46-6e0d-492f-82c6-0e514d6c75f9";

    /**
     * 面板
     */
    private static final String SCENE_ROUTINE_RES_ID = "f24d2a1b-553f-4f2e-af35-5b2a194c70d7";

    /**
     * 镜头资源
     */
    private static final String CAMERA_RES_ID = "9c1835a3-d2bc-47f8-91ab-d19347de575c";


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        ShowPaperCoverParam param = teachEventType.getPerformanceParam(ShowPaperCoverParam.class);

        // 镜头
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, endTime, CAMERA_RES_ID, "展示文档封面-镜头套路");
        cameraSegment.setLookAt(true);
        coursewareModel.getCameraSegment().add(cameraSegment);

        RoutineSegment characterRoutineSegment = new RoutineSegment();
        characterRoutineSegment.time(time).endTime(TimeUtils.add(time, 2300)).resourceId(CHARACTER_ROUTINE_RES_ID);
        characterRoutineSegment.type(RoutineTypeEnum.CharacterAction.getName()).reason("打响指");
        characterRoutineSegment.playType(0).skillType(RoutineTypeEnum.CharacterAction.getName()).cTime(characterRoutineSegment.getDurationSec());
        coursewareModel.getRoutineSegment().add(characterRoutineSegment);

        RoutineSegment sceneRoutineSegment = new RoutineSegment();
        sceneRoutineSegment.time(time).endTime(endTime).resourceId(SCENE_ROUTINE_RES_ID);
        sceneRoutineSegment.type(RoutineTypeEnum.SceneAction.getName()).reason("面板");
        sceneRoutineSegment.playType(0).skillType(RoutineTypeEnum.SceneAction.getName()).cTime(characterRoutineSegment.getDurationSec());
        sceneRoutineSegment.addProperty("Texture01-SM_Plan-Param", "ImgSeq", param.getVideo()).dependency(param.getVideo());

        coursewareModel.getRoutineSegment().add(sceneRoutineSegment);

    }
}
