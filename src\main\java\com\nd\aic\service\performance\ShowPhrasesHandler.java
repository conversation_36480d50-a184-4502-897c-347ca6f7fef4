package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.ShowPhrasesParam;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 变成书本
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ShowPhrasesHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        ShowPhrasesParam appearBookParam = prepareParams(teachEventType, context);
        List<String> phrases = appearBookParam.getPhrases();
        if (CollectionUtils.size(phrases) < 3) {
            log.warn("词条内容不足， 退化为普通讲解");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        String resourceId = "cd353d73-e0cb-4d61-86de-8a3edf429ff4";
        switch (phrases.size()) {
            case 3:
                resourceId = "cd353d73-e0cb-4d61-86de-8a3edf429ff4";
                break;
            case 4:
                resourceId = "a4bd3479-6dfd-40fb-af4b-7b94d89f099d";
                break;
            case 5:
                resourceId = "8227ebaf-b79b-4685-bc44-f0a55bda7333";
                break;
            case 6:
                resourceId = "56c5a7e5-3e2c-4d3d-81f4-fdabecbb0466";
                break;
            default:
                break;
        }
        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTime(time);
        routineSegment.setEndTime(endTime);
        routineSegment.setResourceId(resourceId);
        routineSegment.setType("BigRoutine");
        routineSegment.setReason("多词条展示");

        AtomicInteger sequence = new AtomicInteger(1);
        List<JSONObject> properties = phrases.stream()
                .map(
                        e -> new JSONObject()
                                .fluentPut("name", String.format("Text%02d", sequence.getAndIncrement()))
                                .fluentPut("type", "Text")
                                .fluentPut("value", e)
                )
                .collect(Collectors.toList());
        JSONObject params = new JSONObject()
                .fluentPut("Properties", properties);
        routineSegment.setCustom(
                new JSONObject()
                        .fluentPut("PlayType", 0)
                        .fluentPut("SkillType", "BigRoutine")
                        .fluentPut("TargetID", "")
                        .fluentPut("Time", Math.min(routineSegment.getDuration() / 1000.0, 10.066D))
                        .fluentPut("Param", params)
        );
        routineSegment.setParamVersion(1);
        coursewareModel.getRoutineSegment().add(routineSegment);
        long duration = TimeUtils.getDuration(time, endTime);
        if (duration > 4000) {
            long startMs = TimeUtils.addMilliseconds(time, 4000);
            long endMs = Math.min(TimeUtils.convertToMilliseconds(endTime), 10000);
            randomGenerateActionSegment(startMs, endMs, teachEventType.getDialogue(), teachEventType.getDialogueStartPos(), coursewareModel, context);
        }
        if (duration > 10066) {
            super.apply(TimeUtils.add(time, 10000), endTime, teachEventType, coursewareModel, context);
        }
    }

    @SneakyThrows
    private ShowPhrasesParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        ShowPhrasesParam showPhrasesParam = teachEventType.getPerformanceParam(ShowPhrasesParam.class);
        List<String> phrases = Lists.newArrayList();
        phrases.add(showPhrasesParam.getText1());
        phrases.add(showPhrasesParam.getText2());
        phrases.add(showPhrasesParam.getText3());
        phrases.add(showPhrasesParam.getText4());
        phrases.add(showPhrasesParam.getText5());
        phrases.add(showPhrasesParam.getText6());
        phrases.removeIf(StringUtils::isBlank);
        showPhrasesParam.setPhrases(phrases);
        return showPhrasesParam;
    }
}
