package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.SidesBubblesParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.AtaWordUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 左右气泡表演
 */
@RequiredArgsConstructor
@Service
public class SidesBubblesHandler extends AbstractPerformanceHandler {

    /**
     * 左边气泡
     */
    private static final String LEFT_BUBBLES_RES_ID = "33DC42D99C924216AEA82EAB4255734A";
    /**
     * 右边气泡
     */
    private static final String RIGHT_BUBBLES_RES_ID = "2C3DDBF338984D319EAC631F10F11010";


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);
        // 获取左右气泡的表演参数
        SidesBubblesParam param = teachEventType.getPerformanceParam(SidesBubblesParam.class);

        // 初始化左边气泡的时间为开始时间
        String leftBubblesTime = time;

        // 查找对话中第一个文本的位置
        int index = StringUtils.indexOf(teachEventType.getDialogue(), param.getText1());
        if (index != -1) {
            // 如果找到文本，基于单词时间更新左边气泡的时间
            leftBubblesTime = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index, true));
        }

        // 创建左边气泡的Lottie段落，并设置相关参数
        LottieSegment leftBubbles = SegmentUtil.createLottieSegment(leftBubblesTime, endTime, LEFT_BUBBLES_RES_ID, "左边气泡", null).volume(80);
        // 设置左边气泡段落的自定义属性
        leftBubbles.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", param.getText1()))).fluentPut("audio", new JSONObject().fluentPut("volume", 80)));
        // 将左边气泡段落添加到课件模型中
        coursewareModel.getLottieSegment().add(leftBubbles);

        // 检查第二个文本是否不为空
        if (StringUtils.isNotBlank(param.getText2())) {
            // 计算右边气泡的时间为开始和结束时间的中点
            String rightBubblesTime = TimeUtils.add(leftBubblesTime, TimeUtils.getDuration(time, endTime) / 2);
            // 查找对话中第二个文本的位置
            index = StringUtils.indexOf(teachEventType.getDialogue(), param.getText2());
            if (index != -1) {
                // 如果找到文本，基于单词时间更新右边气泡的时间
                rightBubblesTime = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index, true));
            }

            // 创建右边气泡的Lottie段落，并设置相关参数
            LottieSegment rightBubbles = SegmentUtil.createLottieSegment(rightBubblesTime, endTime, RIGHT_BUBBLES_RES_ID, "右边气泡", null).volume(80);
            // 设置右边气泡段落的自定义属性
            leftBubbles.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", param.getText2()))).fluentPut("audio", new JSONObject().fluentPut("volume", 80)));
            // 将右边气泡段落添加到课件模型中
            coursewareModel.getLottieSegment().add(rightBubbles);
        }
    }
}
