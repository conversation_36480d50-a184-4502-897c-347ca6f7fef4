package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.performance.param.SlantedBoardWritingWalkingParam;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

import java.util.Objects;

@RequiredArgsConstructor
@Service
public class SlantedBoardWritingWalkingHandler extends AbstractPerformanceHandler {

    /**
     * 斜角度板书上前行
     */
    private static final String ROUTINE_RES_ID = "6bace7ca-e5a3-4044-904c-6c5a486518b6";

    /**
     * 起步走的时长
     */
    private static final double WALK_START_DURATION_SEC = 4.266;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        if (Objects.equals(context.getGlobalScene(), "a1c04877-67b8-4530-8c90-c93399f710be")){
            SlantedBoardWritingWalkingParam param = teachEventType.getPerformanceParam(SlantedBoardWritingWalkingParam.class);
            RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "斜角度板书上前行");
            routineSegment.playType(0).addShotTrack(0, WALK_START_DURATION_SEC).addShotTrack(1, routineSegment.getDurationSec());
            coursewareModel.getRoutineSegment().add(routineSegment);
            // 背景视频
            if (StringUtils.isNotBlank(param.getVideo())) {
                VideoSegment videoSegment = SegmentUtil.createBackVideoSegment(time, endTime, param.getVideo());
                coursewareModel.getVideoSegment().add(videoSegment);
            }
        }else {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }
}
