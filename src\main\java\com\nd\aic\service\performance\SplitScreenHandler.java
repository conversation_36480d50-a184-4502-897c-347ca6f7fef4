package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.common.Rotation;
import com.nd.aic.pojo.common.Transform;
import com.nd.aic.pojo.common.Vector3D;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.UUIDUtil;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 分屏表演
 */
@RequiredArgsConstructor
@Service
public class SplitScreenHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        // 创建导览图
        String instanceId = UUIDUtil.upperUuid();
        SceneObjectSegment sceneObjectSegment = new SceneObjectSegment();
        sceneObjectSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, 100)));
        sceneObjectSegment.setEndTime(endTime);
        sceneObjectSegment.setResourceId("CC59ADAB42B31FC56B12F385F76D6424");
        sceneObjectSegment.setType("ActorObject");
        sceneObjectSegment.setReason("创建导览图");
        JSONObject location = new JSONObject().fluentPut("x", 648.5441067).fluentPut("y", -3764.814512).fluentPut("z", 300.1437736);
        JSONObject rotation = new JSONObject().fluentPut("pitch", 0).fluentPut("yaw", -50.45712).fluentPut("roll", 0);
        JSONObject scale = new JSONObject().fluentPut("x", 0.4).fluentPut("y", 0.4).fluentPut("z", 0.4);
        sceneObjectSegment.setCustom(new JSONObject().fluentPut("type", "ActorObject").fluentPut("instanceID", instanceId).fluentPut("location", location).fluentPut("rotation", rotation).fluentPut("scale", scale));
        coursewareModel.getSceneObjectSegment().add(sceneObjectSegment);

        // 导览图下落1.6s
        RoutineSegment fallRoutineSegment = new RoutineSegment();
        fallRoutineSegment.setTime(time);
        fallRoutineSegment.setEndTime(TimeUtils.formatMilliseconds(TimeUtils.convertToMilliseconds(time) + 1600));
        fallRoutineSegment.setResourceId("AF2C860A4549922733C07AB472A713E6");
        fallRoutineSegment.setType("CharacterAction");
        fallRoutineSegment.setReason("导览图下落");
        fallRoutineSegment.setCustom(new JSONObject().fluentPut("PlayType", 1).fluentPut("SkillType", "CharacterAction").fluentPut("TargetID", instanceId).fluentPut("Time", fallRoutineSegment.getDuration() / 1000.0));
        coursewareModel.getRoutineSegment().add(fallRoutineSegment);

        RoutineSegment rotateRoutineSegment = new RoutineSegment();
        rotateRoutineSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.convertToMilliseconds(time) + 1600));
        rotateRoutineSegment.setEndTime(endTime);
        rotateRoutineSegment.setResourceId("960B37784018A3824A41C68272443554");
        rotateRoutineSegment.setType("CharacterAction");
        rotateRoutineSegment.setReason("导览图旋转");
        JSONObject rotateRoutineSegmentParam = new JSONObject().fluentPut("Rotation", new JSONObject().fluentPut("pitch", 0).fluentPut("yaw", -50).fluentPut("roll", 0));
        rotateRoutineSegment.setCustom(new JSONObject().fluentPut("PlayType", 1).fluentPut("SkillType", "CharacterAction").fluentPut("Param", rotateRoutineSegmentParam).fluentPut("TargetID", instanceId).fluentPut("Time", rotateRoutineSegment.getDuration() / 1000.0));
        coursewareModel.getRoutineSegment().add(rotateRoutineSegment);

        // Lottie 导览图分割框
        LottieSegment itemSegment = new LottieSegment();
        itemSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.convertToMilliseconds(time) + 1600));
        itemSegment.setEndTime(endTime);
        itemSegment.setResourceId("C2C1AE4AE9944850852E4F2C61D0CF00");
        itemSegment.setCustom(new JSONObject().fluentPut("windowId", "1"));
        coursewareModel.getLottieSegment().add(itemSegment);

        // 镜头
        String resourceId = "0E323E8946702BF97AD0189E5222CA91";
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, null, resourceId, "导览图镜头").targetId(context.getCharacterInstanceId());
        coursewareModel.getCameraSegment().add(cameraSegment);


        // 创建出生点信息
        Transform transform = new Transform().position(new Vector3D(980.0520428680748, -3764.814512702413,   265.1437736225587)).rotation(new Rotation(0, -50.45712280, 0)).scale(new Vector3D(1,1,1));
        coursewareModel.getBirthPointSegment().add(SegmentUtil.createBirthPointSegmentByTransform(time, transform));


    }
}
