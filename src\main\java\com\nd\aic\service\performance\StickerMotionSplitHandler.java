package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * Emoji表演
 */
@RequiredArgsConstructor
@Service
public class StickerMotionSplitHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        long timeOffset = TimeUtils.addMilliseconds(time, 4000);
        if (StringUtils.isNotBlank(teachEventType.getPerformanceConfig().getLottieImageUrl())) {
            // 图片
            LottieSegment lottieSegment = new LottieSegment();
            lottieSegment.setTimeInMilliseconds(timeOffset);
            lottieSegment.setEndTime(endTime);
            lottieSegment.setResourceId("515E5B76F0EE415ABC043D7F2959F943");
            lottieSegment.setType("LottieScreen");
            JSONObject params = new JSONObject().fluentPut("#Image", new JSONObject().fluentPut("type", "image").fluentPut("value", teachEventType.getPerformanceConfig().getLottieImageUrl()));
            lottieSegment.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("audio", new JSONObject().fluentPut("volume", 40)).fluentPut("params", params));
            coursewareModel.getLottieSegment().add(lottieSegment);
        } else {
            // 毕业季图片
            LottieSegment lottieSegment = new LottieSegment();
            lottieSegment.setTimeInMilliseconds(timeOffset);
            lottieSegment.setEndTime(endTime);
            lottieSegment.setResourceId("D18F0BBBEC0A4834AF19D0DDB807761E");
            lottieSegment.setType("LottieScreen");
            lottieSegment.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("audio", new JSONObject().fluentPut("volume", 40)));
            coursewareModel.getLottieSegment().add(lottieSegment);

            // 毕业季图片标题
            LottieSegment titleLottieSegment = new LottieSegment();
            titleLottieSegment.setTimeInMilliseconds(timeOffset);
            titleLottieSegment.setEndTime(endTime);
            titleLottieSegment.setResourceId("47C40A66407546659853083510ECB3F0");
            titleLottieSegment.setType("LottieScreen");
            titleLottieSegment.setCustom(new JSONObject().fluentPut("windowId", "1").fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", "毕业礼物设计"))).fluentPut("audio", new JSONObject().fluentPut("volume", 40)));
            coursewareModel.getLottieSegment().add(titleLottieSegment);
        }

        // 镜头
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, null, "EC4615934C762524B7E048B41B3DF9D4", "毕业季礼物设计镜头").targetId(context.getCharacterInstanceId());
        cameraSegment.setLookAt(true);
        coursewareModel.getCameraSegment().add(cameraSegment);

    }
}
