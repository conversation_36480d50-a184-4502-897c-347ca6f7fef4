package com.nd.aic.service.performance;

import com.nd.aic.common.NdrConstant;
import com.nd.aic.controller.NdrController;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.performance.param.StickShowParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 贴图表演
 */
@Service
@Primary
public class StickerShowHandler extends AbstractPerformanceHandler {

    private static final String middleStandCameraResourceId = "66faa0de-77c4-4be7-8820-efc7a9916671";
    private static final String leftStandCameraResourceId = "5ab46e70-b065-43da-b75a-7759f053e0b9";
    private static final String rightStandCameraResourceId = "abaea04a-07c8-4bb3-9960-1a9ee1fc99f6";
    private final NdrService ndrService;

    StickerShowHandler(NdrService ndrService) {
        this.ndrService = ndrService;
    }


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        StickShowParam param = prepareParam(teachEventType);
        if (param == null || !param.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // add action segments
        randomGenerateActionSegment(time, endTime, coursewareModel, context);

        // set camera segment
        switch (param.getStandPosition()){
            case "LEFT":
                coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, leftStandCameraResourceId,"选用固定左侧镜头").targetId(context.getCharacterInstanceId()));
                break;
            case "RIGHT":
                coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, rightStandCameraResourceId,"选用固定右侧镜头").targetId(context.getCharacterInstanceId()));
                break;
            default:
                coursewareModel.getCameraSegment().add(SegmentUtil.createCharacterCameraSegment(time, endTime, middleStandCameraResourceId,"选用固定中间镜头").targetId(context.getCharacterInstanceId()));
                break;
        }



        if( StringUtils.isNotEmpty(param.getAlphaVideo())){
            Long duration = 0L;//
             try{
                 duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, param.getAlphaVideo());
             }catch (Exception e){
                 duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_COURSEWARE_CONTAINER_ID, param.getAlphaVideo());
             } finally {
                 if(duration == null || duration == 0) {
                     duration = 5000L;
                 }
             }


            // get rate
            double rate =  getRate(time, endTime, duration);
            if(rate != 1){
                coursewareModel.getVideoSegment().add(SegmentUtil.createAlphaVideoSegment(time, endTime, param.getAlphaVideo(), "").rate(rate).turnOnAudio());
            } else {
                coursewareModel.getVideoSegment().add(SegmentUtil.createAlphaVideoSegment(time, TimeUtils.add(time, duration), param.getAlphaVideo(), "").rate(1.0).turnOnAudio());
            }
        } else if(StringUtils.isNotEmpty(param.getImgSeq())){
            coursewareModel.getGifSegment().add(SegmentUtil.createFrontImgSeqSegment(time, endTime, param.getImgSeq(), ""));
        } else if(StringUtils.isNotEmpty(param.getLottie())){
            coursewareModel.getLottieSegment().add(SegmentUtil.createLottieSegment(time, endTime, param.getLottie(), "", null));
        }
    }


    /**
     * prepare params
     * @param teachEventType teach event type
     * @return StickShowParam
     */
    private StickShowParam prepareParam(TeachEventTypeDTO teachEventType) {

        StickShowParam param = teachEventType.getPerformanceParam(StickShowParam.class);
        if (param == null) {
         return null;
        }

        if(StringUtils.isEmpty(param.getAlphaVideo()) && StringUtils.isEmpty(param.getImgSeq()) &&
                StringUtils.isEmpty(param.getLottie()) && StringUtils.isEmpty(param.getStandPosition())){
            return null;
        }

        param.setIsSatisfy(true);
        return  param;
    }



    private double getRate(String time, String endTime, Long duration){
        if (duration == null) {
            return 1;
        }
        double rate  = duration/ (double) TimeUtils.getDuration(time, endTime);
        if(rate > 1.3){
            rate = 1;
        }
        return rate;
    }

}
