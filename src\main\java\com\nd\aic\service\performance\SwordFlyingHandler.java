package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.ObjectSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.service.material.SplineAssembleService;
import com.nd.aic.service.material.entity.SplineResInfo;
import com.nd.aic.util.SegmentUtil;

import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.UUIDUtil;
import com.nd.sg.sdk.sync.client.SgCenterClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import springfox.documentation.spring.web.ObjectMapperConfigurer;

import java.util.Collections;

import static com.nd.aic.enums.PerformanceTypeEnum.SWORD_FLYING;

/**
 * 御剑飞行
 */
@AllArgsConstructor
@Slf4j
@Service
public class SwordFlyingHandler extends AbstractPerformanceHandler {

    private static final String ROUTINE_RES_ID = "4eef49a2-a847-41d2-a71f-ceff6de22f44";
    private static final String SWORD_VEHICLE_RES_ID = "516f4931-952b-4773-992e-003d27117f9d";
    private static final String CAMERA_FIRST= "82f0c58b-9948-4264-a36b-6300b9fec0da";
    private static final String CAMERA_SECOND= "82f0c58b-9948-4264-a36b-6300b9fec0da";

    private SplineAssembleService splineAssembleService;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        long duration = TimeUtils.getDuration(time, endTime);
        if(duration < SWORD_FLYING.getDuration()){
            log.warn("御剑飞行表演时长不足，无法生成表演方案");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        SplineResInfo splineResInfo = splineAssembleService.findSplineByContext(context, teachEventType);
        if(splineResInfo == null){
            log.warn("未找到样条线资源，无法生成御剑飞行表演方案");
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 增加样条线
        String splineInstanceId = UUIDUtil.upperUuid();
        SceneObjectSegment swordVehicle = SegmentUtil.createSceneObjectSegment(time, endTime, splineResInfo.getResourceId(), "MetaSpline", "御剑载具");
        swordVehicle.instanceId(splineInstanceId);
        swordVehicle.rotation(0,0,0).location(0,0,0);
        swordVehicle.dependencies(Collections.singleton(splineResInfo.getResourceId()));
        coursewareModel.getSceneObjectSegment().add(swordVehicle);

        // 增加载具
        String vehicleInstanceId = UUIDUtil.upperUuid();
        ObjectSegment objectSegment = SegmentUtil.createObjectSegment(time, endTime, SWORD_VEHICLE_RES_ID, "Vehicle");
        objectSegment.setInstanceId(vehicleInstanceId);
        objectSegment.getCustom().fluentPut("IsDefaultBirthPoint", false).fluentPut("BirthPointLocation", new JSONObject().fluentPut("x", 0).fluentPut("y", 0).fluentPut("z", 0))
                .fluentPut("BirthPointRotation", new JSONObject().fluentPut("pitch", 0).fluentPut("yaw", 0).fluentPut("roll", 0));
        objectSegment.dependency(SWORD_VEHICLE_RES_ID);
        coursewareModel.getObjects().add(objectSegment);

        // 增加大套路
        RoutineSegment routineSegment  = SegmentUtil.createBigRoutineSegment(TimeUtils.add(time,200), endTime, ROUTINE_RES_ID, "御剑飞行");
        routineSegment.bindObject("MetaRole.vehicle1", vehicleInstanceId);
        routineSegment.bindObject("MetaRole.spline01", splineInstanceId);
        coursewareModel.getRoutineSegment().add(routineSegment);
    }
}
