package com.nd.aic.service.performance;

import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.tts.AudioSynthesisResult;
import com.nd.aic.service.AudioResourceService;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 教学互动
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TeachingInteractionHandler extends AbstractPerformanceHandler {

    private final AudioResourceService audioResourceService;

    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        // 互动方案句末增加1秒的静默时间（仅当有对白时生效）
        if (!context.isLastTeachEventType(teachEventType)) {
            teachEventType.setSilenceDuration(1000L);
        }
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        InteractionDTO interaction = teachEventType.getInteraction();
        if (null == interaction) {
            return;
        }
        if (BooleanUtils.isTrue(interaction.getNeedSpeak())) {
            TtsSegment ttsSegment = new TtsSegment().setText(interaction.getQuestion())
                    .setVoiceId(context.getCoursewareGenerationReq().getVoiceId())
                    .setVoiceSpeed(context.getCoursewareGenerationReq().getVoiceSpeed());
            AudioSynthesisResult result = audioResourceService.mergeTts(ttsSegment);
            FileUtils.deleteQuietly(result.getAudioFile());
            audioResourceService.mergeTts(ttsSegment);
            interaction.setSpeakUrl(result.getAudioUrl());
            Integer delay = interaction.getSpeakDelay();
            if (delay == null) {
                delay = 300;
            }
            interaction.setSpeakDelay(delay);
        }
        long triggerTime;
        if (!context.isLastTeachEventType(teachEventType)) {
            triggerTime = TimeUtils.subMilliseconds(endTime, 500);
        } else {
            triggerTime = TimeUtils.addMilliseconds(endTime, 500);
        }

        if (context.getLastInteractionTime() != null && triggerTime - context.getLastInteractionTime() < 150) {
            triggerTime += 150;
        }
        interaction.setTriggerTime(triggerTime);
        context.setLastInteractionTime(triggerTime);
    }

}
