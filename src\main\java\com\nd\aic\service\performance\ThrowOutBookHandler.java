package com.nd.aic.service.performance;

import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.ThrowOutBookParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.SegmentUtil;

import com.nd.ndr.model.ResourceMetaTiListViewModel;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

/**
 * 抛出书本
 * 双手向上托举，变出书本
 */
@RequiredArgsConstructor
@Service
public class ThrowOutBookHandler extends AbstractPerformanceHandler {

    private final MediaService mediaService;
    private final AutomateHandler automateHandler;

    // res id online
    private static final String BIG_ROUTINE_RESOURCE_ID = "fdc73de8-1f6f-447c-a29a-c019ba5398b9";
    private static final String PARAM_STR = "Texture-SM_Book-MediaTexture";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        ThrowOutBookParam throwOutBookParam = prepareParams(teachEventType, coursewareModel, context);
        if (throwOutBookParam == null || StringUtils.isEmpty(throwOutBookParam.getVideo())) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RESOURCE_ID, "抛出书本");
        routineSegment.addProperty(PARAM_STR, ParamPropertyTypeEnum.VIDEO, throwOutBookParam.getVideo()).playType(0);
        routineSegment.setDependencies(Collections.singletonList(throwOutBookParam.getVideo()));
        coursewareModel.getRoutineSegment().add(routineSegment);
    }


    /**
     * 准备参数
     *
     * @param teachEventType  教学事件
     * @param coursewareModel 课件模型
     * @param context         课件生成上下文
     * @return 板书讲解参数
     */
    private ThrowOutBookParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        // 获取参数
        ThrowOutBookParam performanceParam = teachEventType.getPerformanceParam(ThrowOutBookParam.class);

        // 如果参数没有，则降级到最基础的表演
        if (performanceParam == null || (StringUtils.isEmpty(performanceParam.getVideo()) && StringUtils.isEmpty(performanceParam.getImage()))) {
            return performanceParam;
        }

        if (Strings.isEmpty(performanceParam.getVideo())) {
            try {
                String downloadUrl = "";
                // 判断 performanceParam.getImage() 这个是否是 uuid 的格式
                downloadUrl = ImageUtil.getImageUrl(performanceParam.getImage());
                // 生成视频
                File fileInfo = mediaService.download(downloadUrl, UUID.randomUUID().toString());
                List<File> mediaInfo = automateHandler.images2Videos(Collections.singletonList(fileInfo));
                List<ResourceMetaTiListViewModel> resInfos = automateHandler.submitVideo2Ndr(coursewareModel.getTitle() + teachEventType.getTeachEventType(), mediaInfo);
                if (resInfos != null && !resInfos.isEmpty()) {
                    performanceParam.setVideo(resInfos.get(0).getId());
                } else {
                    log.print("通过图片生成视频失败");
                    return performanceParam;
                }
            } catch (IOException | InterruptedException e) {
                log.print("通过图片生成视频失败");
                return performanceParam;
            }
        }

        // 参数满足要求
        performanceParam.setIsSatisfy(true);
        return performanceParam;
    }
}
