package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.performance.param.ThrowOutTopicParam;
import com.nd.aic.util.SegmentUtil;

import com.nd.aic.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;


/**
 * 抛出课题
 *
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ThrowOutTopicHandler extends AbstractPerformanceHandler {

    private static final String BIG_ROUTINE_RES_ID = "084590b4-42d1-4ce0-a622-095325b95ada";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {


        // 创建视频区域
        ThrowOutTopicParam throwOutTopicParam = teachEventType.getPerformanceParam(ThrowOutTopicParam.class);

        // use the base action if the video is empty
        if(StringUtils.isEmpty(throwOutTopicParam.getAlphaVideo())){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        // 抛出课题时长
        double videoDuration = 9942;
        // 适配的时长为 8~11 秒，小于6秒的，使用默认表演，大于10秒的，在10秒之后使用默认表演
        double duration = TimeUtils.getDuration(time, endTime);
        if(duration < 8000) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            log.info("ThrowOutTopicHandler 抛出课题时长小于6秒，使用默认表演");
            return;
        }

        String  realEndTime = endTime;
        if(duration > 10000) {
            // 超过11秒的，截取11秒之后的部分
            realEndTime = TimeUtils.add(time, 10000);
            super.apply(realEndTime, endTime, teachEventType, coursewareModel, context);
        }


        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, realEndTime, BIG_ROUTINE_RES_ID, "抛出课题");
        // 坐标
        BirthPointSegment birthPointSegment = context.getCurrentBirthPoint();
        JSONObject param;
        if (birthPointSegment != null && birthPointSegment.getCustom() != null) {
            param = new JSONObject().fluentPut("Location", birthPointSegment.getCustom().get("location"))
                    .fluentPut("Rotation", birthPointSegment.getCustom().get("rotation"));
        } else {
            // 临时写死一个坐标
            param = new JSONObject().fluentPut("Location", new JSONObject().fluentPut("X", 2938.274498).fluentPut("Y", -708.174826).fluentPut("Z", 0))
                    .fluentPut("Rotation", new JSONObject().fluentPut("pitch", 0).fluentPut("yaw", 18.743698).fluentPut("roll", 0));
        }
        routineSegment.getCustom().put("Param", param);
        coursewareModel.getRoutineSegment().add(routineSegment);


        String videoId = "EE203BE0C84841E59AD50262A3DA40D6";
        if (StringUtils.isNotBlank(throwOutTopicParam.getAlphaVideo())) {
            videoId = throwOutTopicParam.getAlphaVideo();
        }
        VideoSegment videoSegment = SegmentUtil.createAlphaVideoSegment(time, realEndTime, videoId, "抛出课题").turnOnAudio();
        videoSegment.getCustom().fluentPut("MediaRate", videoDuration / videoSegment.getDuration());

        coursewareModel.getVideoSegment().add(videoSegment);
    }
}
