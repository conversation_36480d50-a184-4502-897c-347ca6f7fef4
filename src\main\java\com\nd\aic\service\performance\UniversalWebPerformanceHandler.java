package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.UISegment;
import com.nd.aic.service.performance.param.UniversalWebParam;
import com.nd.gaea.util.WafJsonMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 通用WEB表演处理器
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UniversalWebPerformanceHandler extends AbstractPerformanceHandler {

    private static final String WEB_PLAYER_BASE_URL = "https://ai-courseware-2d-player.sdp.101.com/#/load?preload=";

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        try {
            // 解析参数
            UniversalWebParam param = parseParameters(teachEventType);
            if (param == null || StringUtils.isBlank(param.getTemplate())) {
                log.warn("通用WEB表演参数不满足要求，降级为基础表演");
                super.apply(time, endTime, teachEventType, coursewareModel, context);
                return;
            }

            // 构建脚本数据
            UISegment uiSegment = createMetaUISegment(time, endTime, param, context);
            coursewareModel.getUiSegment().add(uiSegment);

            log.info("通用WEB表演方案执行成功，模板: {}", param.getTemplate());

        } catch (Exception e) {
            log.error("通用WEB表演方案执行失败，降级为基础表演", e);
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

    /**
     * 解析前端传入的动态参数
     */
    private UniversalWebParam parseParameters(TeachEventTypeDTO teachEventType) {
        Map<String, Object> rawParams = teachEventType.getRawPerformanceParam();
        if (rawParams == null || rawParams.isEmpty()) {
            return null;
        }

        UniversalWebParam param = new UniversalWebParam();
        
        // 提取基本参数
        param.setTemplate((String) rawParams.get("template"));
        param.setDuration((Integer) rawParams.get("duration"));
        
        // 提取动态参数（排除基本参数）
        Map<String, Object> dynamicParams = new HashMap<>(rawParams);
        dynamicParams.remove("template");
        dynamicParams.remove("duration");
        param.setDynamicParams(dynamicParams);

        return param;
    }

    /**
     * 创建MetaUI类型的segment
     */
    private UISegment createMetaUISegment(String time, String endTime, UniversalWebParam param, CoursewareGenerationContext context) {
        UISegment uiSegment = new UISegment();
        uiSegment.setTime(time);
        uiSegment.setEndTime(endTime);
        uiSegment.setResourceId("B03D1B624BB9E93771DD5C85E46757FB"); // 固定的资源ID
        uiSegment.setType("MetaUI");
        uiSegment.setReason("web表演方案接入");
        uiSegment.setter("instanceId", UUID.randomUUID().toString());

        // 构建custom数据
        JSONObject custom = new JSONObject();
        
        // 构建Properties
        List<JSONObject> properties = new ArrayList<>();
        JSONObject urlProperty = new JSONObject();
        urlProperty.put("name", "Url");
        urlProperty.put("value", buildPlayerUrl(param));
        urlProperty.put("type", "string");
        properties.add(urlProperty);
        custom.put("Properties", properties);

        // 构建Parameters
        JSONObject parameters = new JSONObject();
        parameters.put("template", param.getTemplate());
        parameters.put("config", buildConfig(param));
        custom.put("Parameters", parameters);

        uiSegment.setCustom(custom);
        return uiSegment;
    }

    /**
     * 构建播放器URL
     */
    private String buildPlayerUrl(UniversalWebParam param) {
        try {
            // 构建preload数据
            List<UniversalWebParam.PreloadData> preloadList = buildPreloadData(param);
            String preloadJson = WafJsonMapper.toJson(preloadList);
            String encodedPreload = URLEncoder.encode(preloadJson, StandardCharsets.UTF_8.toString());
            
            return WEB_PLAYER_BASE_URL + encodedPreload;
        } catch (Exception e) {
            log.error("构建播放器URL失败", e);
            return WEB_PLAYER_BASE_URL;
        }
    }

    /**
     * 构建preload数据（仅包含image和video类型的资源）
     */
    private List<UniversalWebParam.PreloadData> buildPreloadData(UniversalWebParam param) {
        List<UniversalWebParam.PreloadData> preloadList = new ArrayList<>();
        UniversalWebParam.PreloadData preloadData = new UniversalWebParam.PreloadData();
        preloadData.setTemplate(param.getTemplate());
        
        List<UniversalWebParam.ResourceData> resources = new ArrayList<>();
        
        // 遍历动态参数，提取image和video类型的资源
        if (param.getDynamicParams() != null) {
            for (Map.Entry<String, Object> entry : param.getDynamicParams().entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                
                if (paramValue instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> paramMap = (Map<String, Object>) paramValue;
                    String type = (String) paramMap.get("type");
                    String value = (String) paramMap.get("value");
                    
                    if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)) {
                        if ("image".equals(type) || "video".equals(type)) {
                            UniversalWebParam.ResourceData resource = new UniversalWebParam.ResourceData();
                            resource.setName(paramName);
                            resource.setType(type);
                            resource.setUrl(value);
                            resource.setStartTime(0);
                            resource.setFullLoad(false);
                            resources.add(resource);
                        }
                    }
                }
            }
        }
        
        preloadData.setResources(resources);
        preloadList.add(preloadData);
        return preloadList;
    }

    /**
     * 构建config数据
     */
    private JSONObject buildConfig(UniversalWebParam param) {
        JSONObject config = new JSONObject();
        
        // 设置持续时间
        config.put("duration", param.getDuration() != null ? param.getDuration() : 10);
        
        // 构建resources数组
        List<JSONObject> resources = new ArrayList<>();
        
        if (param.getDynamicParams() != null) {
            for (Map.Entry<String, Object> entry : param.getDynamicParams().entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                
                if (paramValue instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> paramMap = (Map<String, Object>) paramValue;
                    String type = (String) paramMap.get("type");
                    String value = (String) paramMap.get("value");
                    
                    if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)) {
                        JSONObject resource = new JSONObject();
                        resource.put("name", paramName);
                        resource.put("type", type);
                        resource.put("startTime", 0);
                        
                        // 根据类型设置不同的字段
                        if ("image".equals(type) || "video".equals(type)) {
                            resource.put("url", value);
                        } else if ("text".equals(type)) {
                            resource.put("content", value);
                        }
                        
                        // 添加事件
                        List<JSONObject> events = new ArrayList<>();
                        JSONObject event = new JSONObject();
                        event.put("event", "show");
                        event.put("startTime", 0);
                        events.add(event);
                        resource.put("events", events);
                        
                        resources.add(resource);
                    }
                }
            }
        }
        
        config.put("resources", resources);
        return config;
    }
}
