package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.UISegment;
import com.nd.aic.service.performance.param.UniversalWebParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.UUIDUtil;
import com.nd.gaea.util.WafJsonMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;


import java.io.IOException;
import java.util.*;

/**
 * 通用WEB表演处理器
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UniversalWebPerformanceHandler extends AbstractPerformanceHandler {



    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        try {
            // 解析参数
            UniversalWebParam param = parseParameters(teachEventType);
            if (param == null || StringUtils.isBlank(param.getTemplate())) {
                log.warn("通用WEB表演参数不满足要求，降级为基础表演");
                super.apply(time, endTime, teachEventType, coursewareModel, context);
                return;
            }

            // 构建脚本数据
            UISegment uiSegment = createMetaUISegment(time, endTime, param, context);
            coursewareModel.getUiSegment().add(uiSegment);

            log.info("通用WEB表演方案执行成功，模板: {}", param.getTemplate());

        } catch (Exception e) {
            log.error("通用WEB表演方案执行失败，降级为基础表演", e);
            super.apply(time, endTime, teachEventType, coursewareModel, context);
        }
    }

    /**
     * 解析前端传入的动态参数
     */
    private UniversalWebParam parseParameters(TeachEventTypeDTO teachEventType) {
        Map<String, Object> rawParams = teachEventType.getRawPerformanceParam();
        if (rawParams == null || rawParams.isEmpty()) {
            return null;
        }

        UniversalWebParam param = new UniversalWebParam();

        // 解析嵌套的参数结构
        Map<String, Object> flatParams = new HashMap<>();

        for (Map.Entry<String, Object> entry : rawParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 解析嵌套结构，提取最终的value和type
            ParameterInfo paramInfo = extractParameterInfo(value);
            if (paramInfo != null) {
                if ("template".equals(key)) {
                    param.setTemplate(paramInfo.getValue());
                } else {
                    flatParams.put(key, paramInfo);
                }
            }
        }

        param.setDynamicParams(flatParams);
        return param;
    }

    /**
     * 提取参数信息，处理简单的value和type结构
     */
    private ParameterInfo extractParameterInfo(Object value) {
        if (value == null) {
            return null;
        }

        // 如果是Map，直接提取value和type
        if (value instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> valueMap = (Map<String, Object>) value;

            // 检查是否包含value和type字段
            if (valueMap.containsKey("value") && valueMap.containsKey("type")) {
                String finalValue = (String) valueMap.get("value");
                String type = (String) valueMap.get("type");
                return new ParameterInfo(finalValue, type);
            }
        }

        return null;
    }

    /**
     * 参数信息内部类
     */
    private static class ParameterInfo {
        private final String value;
        private final String type;

        public ParameterInfo(String value, String type) {
            this.value = value;
            this.type = type;
        }

        public String getValue() { return value; }
        public String getType() { return type; }
    }

    /**
     * 将UUID转换为可下载的URL
     * @param value 原始值（可能是UUID或HTTP URL）
     * @param type 资源类型（image、video、text）
     * @return 转换后的URL
     */
    private String convertToDownloadUrl(String value, String type) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        // 如果已经是HTTP URL，直接返回
        if (value.startsWith("http://") || value.startsWith("https://")) {
            return value;
        }

        // 如果是text类型，不需要转换
        if ("text".equals(type)) {
            return value;
        }

        // 对于image和video类型，检查是否为UUID格式并转换
        if (("image".equals(type) || "video".equals(type)) && UUIDUtil.isUuid(value)) {
            if ("image".equals(type)) {
                // 使用项目中已有的ImageUtil工具处理图片UUID
                return ImageUtil.getImageUrl(value);
            } else {
                // 对于视频，使用项目中统一的下载URL格式（参考MediaService.mp4ToMp3等方法）
                return "https://gcdncs.101.com/v0.1/download?dentryId=" + value;
            }
        }

        return value;
    }

    /**
     * 从参数中获取layer值，默认为Front
     */
    private String getLayerFromParams(UniversalWebParam param) {
        if (param.getDynamicParams() != null) {
            Object layerParam = param.getDynamicParams().get("layer");
            if (layerParam instanceof ParameterInfo) {
                ParameterInfo layerInfo = (ParameterInfo) layerParam;
                return StringUtils.defaultIfBlank(layerInfo.getValue(), "Front");
            }
        }
        return "Front";
    }

    /**
     * 构建交互URL（固定地址）
     */
    private String buildInteractionUrl() {
        return "https://ai-courseware-interaction.sdp.101.com/?platform=UE&url=https%3a%2f%2faic-lottie.sdp.101.com%2f%23%2flottie%2fhome#/common-iframe";
    }

    /**
     * 创建Property对象
     */
    private JSONObject createProperty(String name, String type, String value) {
        JSONObject property = new JSONObject();
        property.put("name", name);
        property.put("type", type);
        property.put("value", value);
        return property;
    }

    /**
     * 构建LoadMsg - 对应原来的preload数据
     */
    private String buildLoadMsg(String time, String endTime, UniversalWebParam param) throws IOException {
        // 构建preload数据（仅包含image和video类型的资源）
        List<UniversalWebParam.PreloadData> preloadList = buildPreloadData(param);
        return WafJsonMapper.toJson(preloadList);
    }

    /**
     * 构建PlayMsg - 对应原来的parameters数据
     */
    private String buildPlayMsg(String time, String endTime, UniversalWebParam param) throws IOException {
        JSONObject parameters = new JSONObject();
        parameters.put("template", param.getTemplate());
        parameters.put("config", buildConfig(param));
        return WafJsonMapper.toJson(parameters);
    }

    /**
     * 构建preload数据（仅包含image和video类型的资源）
     */
    private List<UniversalWebParam.PreloadData> buildPreloadData(UniversalWebParam param) {
        List<UniversalWebParam.PreloadData> preloadList = new ArrayList<>();
        UniversalWebParam.PreloadData preloadData = new UniversalWebParam.PreloadData();
        preloadData.setTemplate(param.getTemplate());

        List<UniversalWebParam.ResourceData> resources = new ArrayList<>();

        if (param.getDynamicParams() != null) {
            for (Map.Entry<String, Object> entry : param.getDynamicParams().entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();

                // 跳过layer参数
                if ("layer".equals(paramName)) {
                    continue;
                }

                if (paramValue instanceof ParameterInfo) {
                    ParameterInfo paramInfo = (ParameterInfo) paramValue;
                    String type = paramInfo.getType();
                    String value = paramInfo.getValue();

                    if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)) {
                        if ("image".equals(type) || "video".equals(type)) {
                            UniversalWebParam.ResourceData resource = new UniversalWebParam.ResourceData();
                            resource.setName(paramName);
                            resource.setType(type);
                            resource.setUrl(convertToDownloadUrl(value, type));
                            resource.setStartTime(0);
                            resource.setFullLoad(false);
                            resources.add(resource);
                        }
                    }
                }
            }
        }

        preloadData.setResources(resources);
        preloadList.add(preloadData);
        return preloadList;
    }

    /**
     * 构建config数据
     */
    private JSONObject buildConfig(UniversalWebParam param) {
        JSONObject config = new JSONObject();
        List<JSONObject> resources = new ArrayList<>();

        if (param.getDynamicParams() != null) {
            for (Map.Entry<String, Object> entry : param.getDynamicParams().entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();

                // 跳过layer参数
                if ("layer".equals(paramName)) {
                    continue;
                }

                if (paramValue instanceof ParameterInfo) {
                    ParameterInfo paramInfo = (ParameterInfo) paramValue;
                    String type = paramInfo.getType();
                    String value = paramInfo.getValue();

                    if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(value)) {
                        JSONObject resource = new JSONObject();
                        resource.put("name", paramName);
                        resource.put("type", type);
                        resource.put("startTime", 0);

                        // 根据类型设置不同的字段
                        if ("image".equals(type) || "video".equals(type)) {
                            resource.put("url", convertToDownloadUrl(value, type));
                        } else if ("text".equals(type)) {
                            resource.put("content", convertToDownloadUrl(value, type));
                        }

                        // 添加事件
                        List<JSONObject> events = new ArrayList<>();
                        JSONObject event = new JSONObject();
                        event.put("event", "show");
                        event.put("startTime", 0);
                        events.add(event);
                        resource.put("events", events);

                        resources.add(resource);
                    }
                }
            }
        }

        config.put("resources", resources);
        return config;
    }

    /**
     * 创建MetaUI类型的segment
     */
    private UISegment createMetaUISegment(String time, String endTime, UniversalWebParam param, CoursewareGenerationContext context) throws IOException {
        UISegment uiSegment = new UISegment();
        uiSegment.setTime(time);
        uiSegment.setEndTime(endTime);
        uiSegment.setResourceId("19561FB904B543368E8FD82DC930F25C"); // 新的固定资源ID
        uiSegment.setType("MetaUI");
        uiSegment.setReason("web表演方案接入");

        // 设置perform数据（固定）
        JSONObject perform = new JSONObject();
        perform.put("name", "commondWeb-Reload");
        uiSegment.setPerform(perform);

        // 设置layer（从参数中获取，默认为Front）
        String layer = getLayerFromParams(param);
        uiSegment.setter("layer", layer);

        // 构建custom数据
        JSONObject custom = new JSONObject();
        custom.put("instanceID", UUID.randomUUID().toString().replace("-", ""));

        // 构建Param数据
        JSONObject paramData = new JSONObject();
        paramData.put("Url", buildInteractionUrl());
        paramData.put("LoadMsg", buildLoadMsg(time, endTime, param));
        paramData.put("PlayMsg", buildPlayMsg(time, endTime, param));
        paramData.put("layer", "Background");

        // 构建Properties（固定数据）
        List<JSONObject> properties = new ArrayList<>();
        properties.add(createProperty("ShouldWaitForLoad", "bool", "true"));
        properties.add(createProperty("ShouldShowCloseBtn", "bool", "true"));
        paramData.put("Properties", properties);

        custom.put("Param", paramData);
        uiSegment.setCustom(custom);

        return uiSegment;
    }




}
