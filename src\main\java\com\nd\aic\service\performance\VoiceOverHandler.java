package com.nd.aic.service.performance;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.performance.param.VoiceOverParam;
import com.nd.aic.util.ImageUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.ndr.model.ResourceMetaTiListViewModel;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Random;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 话外音
 */
@AllArgsConstructor
@Service
@Slf4j
public class VoiceOverHandler extends AbstractPerformanceHandler {

    private static final String HORI_LOTTIE = "df444cae-4153-494c-94e4-cc5239065096";
    private static final String FIX_LEFT_POSITION_CAMERA = "5ab46e70-b065-43da-b75a-7759f053e0b9";

    private static final String buttonLottieResourceId = "6a827fde-d490-464e-aefe-13a54ec45af3";
    private final MediaService mediaService;
    private final NdrService ndrService;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        VoiceOverParam voiceOverParam = teachEventType.getPerformanceParam(VoiceOverParam.class);
        log.info("话外音参数: {}", voiceOverParam);
        long duration = TimeUtils.getDuration(time, endTime);
        long videoDuration = duration;
        if (StringUtils.isEmpty(voiceOverParam.getVideo()) && StringUtils.isNotEmpty(voiceOverParam.getImage())) {
            String imageUrl = ImageUtil.getImageUrl(voiceOverParam.getImage());
            try {
                File imageFile = mediaService.download(imageUrl, ImageUtil.getFileName(imageUrl));
                Random r = new Random();
                boolean l2rOrT2b = r.nextBoolean();
                int t2b = r.nextInt(3) - 1;
                int l2r = t2b == 0 ? r.nextBoolean() ? 1 : -1 : r.nextInt(3) - 1;
                File videoFile = mediaService.image2Video(imageFile, duration, l2r, r.nextInt(3) - 1, false);
                videoDuration = mediaService.durationMills(videoFile);
                ResourceMetaTiListViewModel meta = ndrService.createVideoMeta(String.format("%s_画外音", context.getTitle()), videoFile, duration);
                voiceOverParam.setVideo(meta.getId());
            } catch (IOException | InterruptedException e) {
                throw WafI18NException.of("FILE_DOWNLOAD_FAILED", "画外音资源下载失败", HttpStatus.INTERNAL_SERVER_ERROR, e);
            }
        }
        // 4c92d7ed-f973-4a42-8977-aca4417a3671 喇叭
        String video = StringUtils.defaultString(voiceOverParam.getVideo(), "f83dae04-0124-4cea-b3cd-2df8cb4a37ab");
        VideoSegment videoSegment = SegmentUtil.createFrontVideoSegment(time, endTime, video, "话外音");
        Long milliseconds = TimeUtils.getDuration(time, endTime);
        double rate = videoDuration / milliseconds.doubleValue();
        videoSegment.rate(rate);
        videoSegment.dependencies(Lists.newArrayList(video));
        coursewareModel.getVideoSegment().add(videoSegment);

        if (StringUtils.isNotBlank(voiceOverParam.getKeyword())) {
            LottieSegment lottieSegment = SegmentUtil.createLottieSegment(time, endTime, buttonLottieResourceId, "画外音关键字展示",null).volume(70);
            lottieSegment.getCustom().fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", voiceOverParam.getKeyword())));
            coursewareModel.getLottieSegment().add(lottieSegment);
        }

        long startMs = TimeUtils.convertToMilliseconds(time);
        long endMs = TimeUtils.convertToMilliseconds(endTime);
        if (0 == startMs) {
            // 补个镜头，防止视频加载来不及竖屏
            randomGenerateCameraSegment(startMs, endMs, coursewareModel, context);
        }
    }
}


