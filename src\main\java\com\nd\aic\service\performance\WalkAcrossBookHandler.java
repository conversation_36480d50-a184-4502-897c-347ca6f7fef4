package com.nd.aic.service.performance;

import com.nd.aic.common.NdrConstant;
import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.ResourcePackageService;
import com.nd.aic.service.material.entity.MediaResInfo;
import com.nd.aic.service.performance.param.WalkAcrossBookParam;
import com.nd.aic.util.SegmentUtil;

import com.nd.ndr.model.ResourceMetaTiListViewModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.regex.Pattern;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class WalkAcrossBookHandler extends AbstractPerformanceHandler {

    private final ResourcePackageService resourcePackageService;

    private static final String ROUTINE_ID = "033825f2-0e76-4ec3-a893-f24da1e2723a";

    private static final String PARAM_NAME_VIDEO = "Texture-Plane-Texture";

    private static final String BACK_VIDEO_LIGHT_RES_ID = "6833199d-c154-40d9-a399-2dea9da77fcb";

    private final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
    private final NdrService ndrService;

    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        WalkAcrossBookParam param = teachEventType.getPerformanceParam(WalkAcrossBookParam.class);
        BirthPointSegment birthPointSegment = context.getCurrentBirthPoint();
        if (param != null && StringUtils.isNotBlank(param.getVideo())) {
            String resourceId = param.getVideo();
            Long duration = null;
            if (UUID_PATTERN.matcher(param.getVideo()).matches()) {
                duration = ndrService.getResourceDuration(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId);

                teachEventType.getRawPerformanceParam().put("duration", duration);
                if (null == duration) {
                    teachEventType.getRawPerformanceParam().put("reason", "资源资源未提供时长");
                }
            }else if (resourceId.startsWith("http")){
                MediaResInfo mediaResInfo = resourcePackageService.submitFrontVideo(resourceId);
                duration = mediaResInfo.getDuration();
                teachEventType.getRawPerformanceParam().put("duration", duration);
                teachEventType.getRawPerformanceParam().put("video", mediaResInfo.getResourceId());
            }
            teachEventType.setResourceDuration(duration);
        }
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        WalkAcrossBookParam param = teachEventType.getPerformanceParam(WalkAcrossBookParam.class);
        if(param == null || StringUtils.isEmpty(param.getVideo())){
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }
        // add routine
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_ID, "书本前横向走过").setParamVersion(1).playType(0);
        routineSegment.addProperty(PARAM_NAME_VIDEO, ParamPropertyTypeEnum.VIDEO, param.getVideo()).dependency(param.getVideo());
        coursewareModel.getRoutineSegment().add(routineSegment);

        // add light routine
        coursewareModel.getLightSegment().add(SegmentUtil.createLightSegment(time, endTime, BACK_VIDEO_LIGHT_RES_ID, "背景视频补光"));

    }
}
