package com.nd.aic.service.performance;

import com.nd.aic.enums.ParamPropertyTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.WalkAndFlipCardsParam;
import com.nd.aic.util.SegmentUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 横向行走翻牌讲解
 */
@RequiredArgsConstructor
@Service
public class WalkAndFlipCardsHandler extends AbstractPerformanceHandler {

    /**
     * 横向行走翻牌讲解
     */
    private static final String ROUTINE_RES_ID = "2dbf7b03-85b8-4d3d-885c-8e38ec0c3d1b";

    private static final double ROUTINE_MAX_DURATION_SEC = 50.0;

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        WalkAndFlipCardsParam walkAndFlipCardsParam = teachEventType.getPerformanceParam(WalkAndFlipCardsParam.class);
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, ROUTINE_RES_ID, "横向行走翻牌讲解");
        Double durationSec = routineSegment.getDurationSec();
        if (durationSec != null && durationSec > ROUTINE_MAX_DURATION_SEC) {
            routineSegment.playType(1);
        } else {
            routineSegment.playType(0);
        }
        // 参数
        if (StringUtils.isNotBlank(walkAndFlipCardsParam.getTexture1())) {
            routineSegment.addProperty("Texture-Plane-Texture", ParamPropertyTypeEnum.VIDEO, walkAndFlipCardsParam.getTexture1());
            routineSegment.dependency(walkAndFlipCardsParam.getTexture1());
        }
        if (StringUtils.isNotBlank(walkAndFlipCardsParam.getTexture2())) {
            routineSegment.addProperty("Texture2-Plane-Texture", ParamPropertyTypeEnum.VIDEO, walkAndFlipCardsParam.getTexture2());
            routineSegment.dependency(walkAndFlipCardsParam.getTexture2());
        }
        if (StringUtils.isNotBlank(walkAndFlipCardsParam.getTexture3())) {
            routineSegment.addProperty("Texture3-Plane-Texture", ParamPropertyTypeEnum.VIDEO, walkAndFlipCardsParam.getTexture3());
            routineSegment.dependency(walkAndFlipCardsParam.getTexture3());
        }
        coursewareModel.getRoutineSegment().add(routineSegment);
    }
}
