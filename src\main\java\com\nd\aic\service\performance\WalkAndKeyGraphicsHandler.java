package com.nd.aic.service.performance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.enums.LottieParamTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.WalkAndKeyGraphicsParam;
import com.nd.aic.util.AtaWordUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.nd.aic.util.TimeUtils.getDurationSec;

/**
 * 角色横向行走关键图文辅助讲解
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WalkAndKeyGraphicsHandler extends AbstractPerformanceHandler {
    /**
     * 左右手关键字表演（左）LOTTIE资源参数
     */
    private static final String LEFT_LOTTIE_RES_ID = "107582a1-086f-479f-bc6f-bb1947304881";

    /**
     * 右手关键字表演（右）LOTTIE资源参数
     */
    private static final String RIGHT_LOTTIE_RES_ID = "5453b789-f077-41b8-b77f-d3992d5ce112";

    /**
     * 横向行走关键图文辅助讲解 大套路资源参数
     */
    private static final String BIG_ROUTINE_RES_ID = "c4267a05-ed49-49c8-81cc-53476218febd";
    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        WalkAndKeyGraphicsParam param = prepareParams(teachEventType, coursewareModel, context);
        if (param == null || !param.getIsSatisfy()) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }

        //设置大套路轨道
        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_RES_ID, "横向行走关键图文辅助").playType(0);
        routineSegment.rotationParam(-0,90,0);
        routineSegment.scaleParam(1,1,1);
        routineSegment.locationParam(5568,-3422,10);

        String lottieEndTime = endTime;
        String lottieTime = time;
        for (int i = 1; i <= 5; i++){
            //设置lottie轨道
            if (StringUtils.isNotEmpty(param.get("text"+i))){
                int index = StringUtils.indexOf(teachEventType.getDialogue(), param.get("text"+i));
                if (index != -1) {
                    lottieTime = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index, true));
                }
                //套路间隔
                long duration = (long)(Double.parseDouble(param.get("duration"+i))*1000);
                //每个lottie结束时间就是套路的每个分镜结束的时间
                lottieEndTime = TimeUtils.add(lottieTime,duration);
                //先右在左
                if (i%2 != 0){
                    LottieSegment lottieSegment = SegmentUtil.createLottieSegment(lottieTime, lottieEndTime, RIGHT_LOTTIE_RES_ID, "左右手关键字表演（右）", new JSONObject());
                    for (int j = 1; j <=3; j++){
                        lottieSegment.addLottieParam("#Text"+j, LottieParamTypeEnum.TEXT,param.get("text"+i));
                    }
                    coursewareModel.getLottieSegment().add(lottieSegment);
                }else {
                    LottieSegment lottieSegment = SegmentUtil.createLottieSegment(lottieTime, lottieEndTime, LEFT_LOTTIE_RES_ID, "左右手关键字表演（左）", new JSONObject())
                            .addWidthHeightTopLeft(500,400,230,1050);
                    for (int j = 1; j <=3; j++){
                        lottieSegment.addLottieParam("#Text"+j, LottieParamTypeEnum.TEXT,param.get("text"+i));
                    }
                    coursewareModel.getLottieSegment().add(lottieSegment);
                }

                routineSegment.addShotTrack(i-1,Double.valueOf(param.get("duration"+i)));
            }
        }
        coursewareModel.getRoutineSegment().add(routineSegment);
    }

    private WalkAndKeyGraphicsParam prepareParams(TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        WalkAndKeyGraphicsParam param = teachEventType.getPerformanceParam(WalkAndKeyGraphicsParam.class);
        if (param == null) {
            return null;
        }
        for (int i = 1; i <= 5; i++){
            String durationField = "duration" + i;
            String duration = param.get(durationField);

            if (StringUtils.isNotEmpty(duration)) {
                //通过引用的文本，计算出引用的时间
                String[] timeAreaByAliasTexts = getTimeAreaByAliasTexts(duration, context, teachEventType);
                //获取格式为 "06:68.00" 的时长，单位为秒
                Double durationSec = getDurationSec(timeAreaByAliasTexts[0], timeAreaByAliasTexts[1]);
                if (durationSec != null && durationSec > 0)  {
                        param.set(durationField, durationSec.toString());
                } else {
                    param.set(durationField, "5");
                }
            }else {
                param.set(durationField, "5");
            }
        }
        param.setIsSatisfy(true);
        return param;
    }
}
