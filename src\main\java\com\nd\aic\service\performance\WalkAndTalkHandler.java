package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.util.SegmentUtil;

import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

/**
 * 边走边说话
 */
@RequiredArgsConstructor
@Service
public class WalkAndTalkHandler extends AbstractPerformanceHandler {

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        // 边走边说话动作
        ActionSegment actionSegment = SegmentUtil.createActionSegment(time, endTime, "f22116ba-948e-4b8f-a61f-4c9983906e61", "往前边走边说");
        coursewareModel.getActionSegment().add(actionSegment);
        // 边走边说话镜头
        String resourceId = "B48865E7456068AF774E8985BD141B07";
        CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(time, endTime, resourceId, "配合边走边说的镜头").targetId(context.getCharacterInstanceId());
        cameraSegment.setLookAt(true);
        coursewareModel.getCameraSegment().add(cameraSegment);
    }
}
