package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.automate.AutomateHandler;
import com.nd.aic.service.performance.param.WalkForwardMultiBoardShowParam;
import com.nd.aic.util.SegmentUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@AllArgsConstructor
public class WalkForwardMultiBoardShowHandler extends AbstractPerformanceHandler {


    private static final String BIG_ROUTINE_ID = "919f0512-a9f5-41f2-bac0-46b3b9cd13c8";
    private static final String LEFT_IMAGE_PARAM = "Texture-Plane-Texture";
    private static final String RIGHT_IMAGE_PARAM = "Texture2-Plane-Texture";
    private final AutomateHandler automateHandler;


    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {

        WalkForwardMultiBoardShowParam param = preparePram(teachEventType);
        //只有卡通城能用
        if (param == null || !StringUtils.equals(context.getCurrentSceneId(), "a1c04877-67b8-4530-8c90-c93399f710be")) {
            super.apply(time, endTime, teachEventType, coursewareModel, context);
            return;
        }


        RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(time, endTime, BIG_ROUTINE_ID, "向前行走多版面展示内容").playType(0).setParamVersion(1);
        if (StringUtils.isNotEmpty(param.getVideo1())) {
            Map<String, Object> property = new HashMap<>();
            property.put("Playerid", "0");
            routineSegment.addProperty(LEFT_IMAGE_PARAM, "Video", param.getVideo1(), property);
            routineSegment.dependency(param.getVideo1());

        }
        if (StringUtils.isNotEmpty(param.getVideo2())) {
            Map<String, Object> property = new HashMap<>();
            property.put("Playerid", "1");
            routineSegment.addProperty(RIGHT_IMAGE_PARAM, "Video", param.getVideo2(), property);
            routineSegment.dependency(param.getVideo2());
        }
        routineSegment.locationParam(2400.367262, -57.269844, -2);
        routineSegment.rotationParam(0, 105.356955, 0);
        routineSegment.scaleParam(1, 1, 1);


        // String[] timeArea = getTimeAreaByAliasTexts(param.getAliasContent(), context, teachEventType);
        // long duration = TimeUtils.getDuration(time, timeArea[0]);
        // if(!timeArea[0].equals(time)) {
        //     routineSegment.addShotTrack(0, TimeUtils.getDuration(time, timeArea[0]) / 1000.0);
        // }
        routineSegment.addShotTrack(0, 0.001);

        coursewareModel.getRoutineSegment().add(routineSegment);
    }


    private WalkForwardMultiBoardShowParam preparePram(TeachEventTypeDTO teachEventType) {
        WalkForwardMultiBoardShowParam param = teachEventType.getPerformanceParam(WalkForwardMultiBoardShowParam.class);

        if (StringUtils.isEmpty(param.getImage1()) && StringUtils.isEmpty(param.getVideo1()) && StringUtils.isEmpty(param.getImage2()) && StringUtils.isEmpty(param.getVideo2())) {
            return null;
        }

        try {
            if (StringUtils.isEmpty(param.getVideo1()) && StringUtils.isNotEmpty(param.getImage1())) {
                param.setVideo1(automateHandler.image2Video(teachEventType.getPerformanceType(), param.getImage1()));
            }
            if (StringUtils.isEmpty(param.getVideo2()) && StringUtils.isNotEmpty(param.getImage2())) {
                param.setVideo2(automateHandler.image2Video(teachEventType.getPerformanceType(), param.getImage2()));
            }
        } catch (Exception ex) {
            log.error("image2Video error", ex);
            return null;
        }


        return param;
    }

}
