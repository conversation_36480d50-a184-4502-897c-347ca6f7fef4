package com.nd.aic.service.performance;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.service.material.MediaAssembleService;
import com.nd.aic.service.material.entity.MediaResInfo;
import com.nd.aic.util.SegmentUtil;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;

@AllArgsConstructor
@Service
public class WorkflowShowHandler extends AbstractPerformanceHandler  {

    private final MediaAssembleService mediaAssembleService;

    @Override
    public void beforePrepare(TeachEventTypeDTO teachEventType, CoursewareGenerationContext context) {
        super.beforePrepare(teachEventType, context);
        MediaResInfo mediaResInfo = mediaAssembleService.getMedia(teachEventType, context);
        if (mediaResInfo != null) {
            teachEventType.setResourceDuration(mediaResInfo.getDuration());
        }
    }

    @Override
    public void apply(String time, String endTime, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        super.apply(time, endTime, teachEventType, coursewareModel, context);

        MediaResInfo mediaResInfo =  mediaAssembleService.getMedia(teachEventType, context);
        // 增加视频播放
        coursewareModel.getVideoSegment().add(SegmentUtil.createAlphaVideoSegment(time, endTime, mediaResInfo.getResourceId(), mediaResInfo.getReason()).turnOnAudio());
    }
}
