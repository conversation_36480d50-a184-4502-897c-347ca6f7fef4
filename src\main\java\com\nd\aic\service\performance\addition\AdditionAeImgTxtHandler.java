package com.nd.aic.service.performance.addition;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.performance.param.AdditionAeImgTxtParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AdditionAeImgTxtHandler extends AbstractPerformanceAdditionHandler {

    @Value("${ioa.ai.aippt.tools.invalidFilename:\\/:*?<>|}")
    private String invalidFilename;

    private final NdrService ndrService;
    private final MediaService mediaService;

    public AdditionAeImgTxtHandler(NdrService ndrService, MediaService mediaService) {
        super();
        this.ndrService = ndrService;
        this.mediaService = mediaService;
    }

    public void apply(Integer time, Integer endTime, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        AdditionAeImgTxtParam additionAeImgTxtParam = performanceAdditionDTO.getParamObject(AdditionAeImgTxtParam.class);
        if (null == additionAeImgTxtParam || StringUtils.isEmpty(additionAeImgTxtParam.getVideoUrl())) {
            return;
        }
        String videoResourceId;
        try {
            videoResourceId = buildVideoResource(performanceAdditionDTO, additionAeImgTxtParam.getVideoUrl());
        } catch (RuntimeException | IOException e) {
            log.info("AE imgTxt effect failed", e);
            return;
        }
        Integer minDuration = performanceAdditionDTO.getMinDuration();
        if (minDuration == null) {
            minDuration = 3000;
        }
        if (endTime - time < minDuration) {
            endTime = time + minDuration;
        }
        String st = TimeUtils.formatMilliseconds(time);
        String et = TimeUtils.formatMilliseconds(endTime);

        String reason = String.format("%s-%s-%s", performanceAdditionDTO.getResourceName(), performanceAdditionDTO.getText(), performanceAdditionDTO.getReason());
        VideoSegment videoSegment = SegmentUtil.createAlphaVideoSegment(st, et, videoResourceId, reason);
        videoSegment.rate(1);
        videoSegment.setDependencies(Lists.newArrayList(videoResourceId));
        coursewareModel.getVideoSegment().add(videoSegment);

        // boolean useWhiteCat = StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.WHITE_CAT.getId());
        // String routineResID = useWhiteCat ? VLIB_BIG_ROUTINE_RES_ID : BIG_ROUTINE_RES_ID;
        // RoutineSegment routineSegment = SegmentUtil.createBigRoutineSegment(st, et, routineResID, reason);
        // coursewareModel.getRoutineSegment().add(routineSegment);
        // routineSegment.playType(0);
        // if (useWhiteCat) {
        //     routineSegment.targetId("");
        //     routineSegment.putParam("PauseAtEnd", false);
        //     routineSegment.bindObject("TestRole", context.getCharacterInstanceId());
        //     routineSegment.putParam("Properties", Lists.newArrayList(
        //             new JSONObject().fluentPut("name", "Texture-Plane-Texture").fluentPut("type", "Video").fluentPut("Playerid", "0").fluentPut("value", videoResourceId),
        //             new JSONObject().fluentPut("name", "IsUseAlphaVideo").fluentPut("type", "float").fluentPut("value", "1")
        //     ));
        //
        //     RoutineSegment actionRoutineSegment = SegmentUtil.createActionRoutineSegment(TimeUtils.add(st, 100), VLIB_ACTION_ROUTINE_RES_ID, "001_Montage");
        //     actionRoutineSegment.putParam("ParamVersion", 1);
        //     actionRoutineSegment.targetId(context.getCharacterInstanceId());
        //     coursewareModel.getRoutineSegment().add(actionRoutineSegment);
        // } else {
        //     List<JSONObject> properties = Lists.newArrayList(
        //             new JSONObject()
        //                     .fluentPut("name", "Texture-Plane-Texture")
        //                     .fluentPut("type", "Video")
        //                     .fluentPut("value", videoResourceId),
        //             new JSONObject()
        //                     .fluentPut("name", "Texture-Plane-Texture")
        //                     .fluentPut("type", "Video")
        //                     .fluentPut("value", videoResourceId),
        //             new JSONObject()
        //                     .fluentPut("name", "Texture3-Plane-Texture")
        //                     .fluentPut("type", "Video")
        //                     .fluentPut("value", videoResourceId),
        //             new JSONObject().fluentPut("name", "IsUseAlphaVideo").fluentPut("type", "float").fluentPut("value", "1")
        //     );
        //     routineSegment.getCustom()
        //             .fluentPut("Param", new JSONObject().fluentPut("Properties", properties));
        // }
        // routineSegment.setDependencies(Lists.newArrayList(videoResourceId));
        // routineSegment.setParamVersion(1);
    }

    private String buildVideoResource(PerformanceAdditionDTO performanceAdditionDTO, String videoUrl) throws IOException {
        File videoFile = mediaService.download(videoUrl, String.format("%s.mp4", UUID.nameUUIDFromBytes(videoUrl.getBytes(StandardCharsets.UTF_8))), true);
        String title = performanceAdditionDTO.getText();
        for (int i = 0; i < invalidFilename.length(); i++) {
            char c = invalidFilename.charAt(i);
            title = StringUtils.replace(title, String.valueOf(c), "-");
        }
        long duration = mediaService.durationMills(videoFile);
        return ndrService.createVideoMeta(title, videoFile, duration).getId();
    }

}
