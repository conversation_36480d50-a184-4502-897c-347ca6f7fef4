package com.nd.aic.service.performance.addition;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.client.AIHubFeignClient;
import com.nd.aic.client.pojo.aihub.AIHubTaskId;
import com.nd.aic.client.pojo.aihub.AIHubTaskReq;
import com.nd.aic.client.pojo.aihub.AIHubTaskResult;
import com.nd.aic.client.pojo.karaoke.CmdSubmitReq;
import com.nd.aic.common.UserHandler;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.CameraResourceDTO;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.VideoSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.ResourcePackageService;
import com.nd.aic.service.performance.param.AdditionAeTextParam;
import com.nd.aic.util.JsonUtils;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AdditionAeTextHandler extends AbstractPerformanceAdditionHandler {

    @Value("${ioa.ai.aippt.tools.invalidFilename:\\/:*?<>|}")
    private String invalidFilename;

    private final NdrService ndrService;
    private final MediaService mediaService;
    private final AIHubFeignClient aiHubFeignClient;
    private final ResourcePackageService resourcePackageService;

    public AdditionAeTextHandler(NdrService ndrService, MediaService mediaService, ResourcePackageService resourcePackageService, AIHubFeignClient aiHubFeignClient) {
        super();
        this.ndrService = ndrService;
        this.mediaService = mediaService;
        this.resourcePackageService = resourcePackageService;
        this.aiHubFeignClient = aiHubFeignClient;
    }

    public void apply(Integer time, Integer endTime, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        if (BooleanUtils.isTrue(context.getDisableAdditionAes())) {
            // 禁用AE增强表演
            return;
        }
        List<CameraSegment> cameraSegmentList = PerformanceUtil.findOverlayCameraSegments(coursewareModel.getCameraSegment(), time, endTime);
        CameraSegment cameraSegment = null;
        if (CollectionUtils.isEmpty(cameraSegmentList)) {
            cameraSegment = coursewareModel.getCameraSegment().get(coursewareModel.getCameraSegment().size() - 1);
        } else {
            cameraSegment = bestMatchCameraSegment(time, endTime, cameraSegmentList);
        }
        if (TimeUtils.convertToMilliseconds(cameraSegment.getTime()) > time.longValue()) {
            time = Math.toIntExact(TimeUtils.convertToMilliseconds(cameraSegment.getTime()));
        }

        Integer minDuration = performanceAdditionDTO.getMinDuration();
        if (minDuration == null) {
            minDuration = 3000;
        }
        if (endTime - time < minDuration) {
            endTime = time + minDuration;
        }
        if (null != cameraSegment.getEndTime() && TimeUtils.convertToMilliseconds(cameraSegment.getEndTime()) < endTime) {
            endTime = Math.toIntExact(TimeUtils.convertToMilliseconds(cameraSegment.getEndTime()));
        }
        String st = TimeUtils.formatMilliseconds(time);
        String et = TimeUtils.formatMilliseconds(endTime);

        String reason = String.format("%s-%s-%s", performanceAdditionDTO.getResourceName(), performanceAdditionDTO.getText(), performanceAdditionDTO.getReason());
        CameraSegment finalCameraSegment = cameraSegment;
        List<CameraResourceDTO> cameras = context.getCoursewareResourceConfig().getCharacterCameras();
        CameraResourceDTO cameraInfo = cameras.stream().filter(e -> StringUtils.equals(e.getId(), finalCameraSegment.getResourceId())).findFirst().orElse(null);
        if (cameraInfo == null) {
            return;
        }
        RectCfg rect = calcRect(cameraInfo);
        try {
            String videoResourceId = buildAlphaVideoResource(performanceAdditionDTO, rect);
            VideoSegment videoSegment = SegmentUtil.createAlphaVideoSegment(st, et, videoResourceId, reason + JsonUtils.toJson(rect));
            videoSegment.setDependencies(Lists.newArrayList(videoResourceId));
            coursewareModel.getVideoSegment().add(videoSegment);
        } catch (RuntimeException e) {
            // AE智能体失败，禁用后续AE增强表演
            log.error("AE text effect failed", e);
            context.setDisableAdditionAes(true);
        }
    }

    @SneakyThrows
    private String buildAlphaVideoResource(PerformanceAdditionDTO performanceAdditionDTO, RectCfg rectCfg) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        String videoUrl = generateVideoByAIHub(performanceAdditionDTO, rectCfg);
        log.info("Generate video by AIHub cost {}", stopwatch.stop());
        File videoFile = mediaService.download(videoUrl, String.format("%s.mp4", UUID.nameUUIDFromBytes(videoUrl.getBytes(StandardCharsets.UTF_8))), true);
        String title = performanceAdditionDTO.getText();
        for (int i = 0; i < invalidFilename.length(); i++) {
            char c = invalidFilename.charAt(i);
            title = StringUtils.replace(title, String.valueOf(c), "_");
        }
        long duration = mediaService.durationMills(videoFile);
        // File frontFile = resourcePackageService.packageAlphaVideo(videoFile, title);
        return ndrService.createVideoMeta(title, videoFile, duration).getId();
    }

    private String generateVideoByAIHub(PerformanceAdditionDTO performanceAdditionDTO, RectCfg rectCfg) {
        CmdSubmitReq cmdSubmitReq = new CmdSubmitReq();
        cmdSubmitReq.getParamsObj().setTemplateId(performanceAdditionDTO.getResourceId());
        AdditionAeTextParam additionAeTextParam = performanceAdditionDTO.getParamObject(AdditionAeTextParam.class);
        cmdSubmitReq.getParamsObj().getParams().putAll(additionAeTextParam.getTexts());
        cmdSubmitReq.getParamsObj().getParams().put("position_x", String.valueOf(rectCfg.getLeft()));
        cmdSubmitReq.getParamsObj().getParams().put("position_y", String.valueOf(rectCfg.getTop()));
        // cmdSubmitReq.getParamsObj().getParams().put("scale_x", String.format("%.02f", rectCfg.getWidth() * 1.0f / 1920));
        // cmdSubmitReq.getParamsObj().getParams().put("scale_y", String.format("%.02f", rectCfg.getHeight() * 1.0f / 1080));
        cmdSubmitReq.getParamsObj().getParams().put("scale_x", "1");
        cmdSubmitReq.getParamsObj().getParams().put("scale_y", "1");
        cmdSubmitReq.getParamsObj().getParams().put("rotate", String.valueOf(0));

        String userId = StringUtils.defaultString(UserHandler.getUser(null), AIHubFeignClient.DEFAULT_USER_ID);
        AIHubTaskReq aiHubTaskReq = new AIHubTaskReq();
        aiHubTaskReq.setQuery("null");
        aiHubTaskReq.setInputs(Maps.newHashMap());
        aiHubTaskReq.getInputs().put("cmd", "cmd_text_effects_generate");
        aiHubTaskReq.getInputs().put("param", JsonUtils.toJson(cmdSubmitReq.getParamsObj()));
        aiHubTaskReq.setUserId(userId);
        AIHubTaskId taskId = aiHubFeignClient.createTextEffectGenerateTask(userId, aiHubTaskReq);
        log.info("Submit task {} with data {}", performanceAdditionDTO.getText(), JsonUtils.toJson(cmdSubmitReq.getParamsObj()));

        while (true) {
            AIHubTaskResult aiHubTaskResult = aiHubFeignClient.queryTextEffectGenerateTask(taskId.getAsyncTaskId());
            log.info("Query task {} result: {}", performanceAdditionDTO.getText(), JsonUtils.toJson(aiHubTaskResult));
            if (StringUtils.equals(aiHubTaskResult.getStatus(), "running") || StringUtils.equals(aiHubTaskResult.getStatus(), "init")) {
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException ignore) {
                }
                continue;
            }

            AIHubTaskResult.Answer answer = AIHubTaskResult.getAnswerOrRaise(aiHubTaskResult);
            log.info("AIHub task result: {}", JsonUtils.toJson(answer));
            VideoResult videoResult = JsonUtils.parseObject(answer.getData().getOutputs().get("result"), VideoResult.class);
            if (StringUtils.isEmpty(videoResult.getUrl())) {
                throw WafI18NException.of("AIHUB_TASK_FAILED", "AE视频渲染失败: " + videoResult.getError(), HttpStatus.BAD_REQUEST);
            }
            return videoResult.getUrl();
        }
    }

    private RectCfg calcRect(CameraResourceDTO cameraInfo) {
        int width, height, left, top;
        if (StringUtils.contains(cameraInfo.getComposition(), "左")) {
            width = 1080;
            height = 720;
            left = 0;
            top = 0;
        } else if (StringUtils.contains(cameraInfo.getComposition(), "右")) {
            width = 1080;
            height = 720;
            left = 1920 - 1080;
            top = 0;
        } else {
            width = 640;
            height = 720;
            if (new Random().nextBoolean()) {
                // left
                left = 0;
            } else {
                // right
                left = 1920 - width;
            }
            top = (1080 - height) / 2;
        }
        // 锚点在中心
        RectCfg rect = new RectCfg();
        rect.setLeft(left);
        rect.setTop(top);
        rect.setWidth(width);
        rect.setHeight(height);
        return rect;
    }

    private CameraSegment bestMatchCameraSegment(int st, int et, List<CameraSegment> cameraSegmentList) {
        if (cameraSegmentList.size() == 1) {
            return cameraSegmentList.get(0);
        }

        if (cameraSegmentList.size() == 2) {
            long st1 = Math.max(st, TimeUtils.convertToMilliseconds(cameraSegmentList.get(0).getTime()));
            long et1 = TimeUtils.convertToMilliseconds(cameraSegmentList.get(0).getEndTime());
            long dur1 = et1 - st1;

            long st2 = Math.max(st, TimeUtils.convertToMilliseconds(cameraSegmentList.get(1).getTime()));
            long et2 = et;
            long dur2 = et2 - st2;
            return dur2 > dur1 ? cameraSegmentList.get(1) : cameraSegmentList.get(0);
        } else {
            return cameraSegmentList.get(1);
        }
    }

    @Data
    static class RectCfg {
        private int width;
        private int height;
        private int left;
        private int top;
        private int anchorX;
        private int anchorY;

        public RectCfg() {
            this.width = 1920;
            this.height = 1080;
            this.left = 0;
            this.top = 0;
        }

        public int getAnchorX() {
            return getLeft() + getWidth() / 2;
        }

        public int getAnchorY() {
            return getTop() + getHeight() / 2;
        }
    }

    @Data
    public static class VideoResult {
        private String url;
        private String error;
    }
}
