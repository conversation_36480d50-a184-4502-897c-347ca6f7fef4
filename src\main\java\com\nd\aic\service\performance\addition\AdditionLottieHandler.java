package com.nd.aic.service.performance.addition;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.CameraResourceDTO;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.util.PerformanceUtil;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.gaea.util.WafJsonMapper;
import com.nd.ndr.model.DownloadUrlViewModel;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.security.access.method.P;

public abstract class AdditionLottieHandler extends AbstractPerformanceAdditionHandler {

    private final NdrService ndrService;
    private final MediaService mediaService;

    private static final Map<String, String> CAMERA_COMPOSITION_MAPPING = Maps.newHashMap();

    static {
        CAMERA_COMPOSITION_MAPPING.put("b5e809b4-6ced-41b7-8bc2-34bc910fe104", "居中偏右");
        CAMERA_COMPOSITION_MAPPING.put("右", "右");
        CAMERA_COMPOSITION_MAPPING.put("中", "中");
    }

    public AdditionLottieHandler(NdrService ndrService, MediaService mediaService) {
        super();
        this.ndrService = ndrService;
        this.mediaService = mediaService;
    }

    public LottieSegment generateLottie(Integer time, Integer endTime, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        List<CameraSegment> cameraSegmentList = PerformanceUtil.findOverlayCameraSegments(coursewareModel.getCameraSegment(), time, endTime);
        CameraSegment cameraSegment = null;
        boolean[] isLeftRightOrClose = {false};
        if (CollectionUtils.isEmpty(cameraSegmentList)) {
            cameraSegment = coursewareModel.getCameraSegment().get(coursewareModel.getCameraSegment().size() - 1);
        } else {
            cameraSegment = bestMatchCameraSegment(time, endTime, cameraSegmentList);
        }
        if (TimeUtils.convertToMilliseconds(cameraSegment.getTime()) > time.longValue()) {
            time = Math.toIntExact(TimeUtils.convertToMilliseconds(cameraSegment.getTime()));
        }

        Integer minDuration = performanceAdditionDTO.getMinDuration();
        if (minDuration == null) {
            minDuration = 5000;
        }
        if (endTime - time < minDuration) {
            endTime = time + minDuration;
        }
        if (endTime - time > 8000){
            endTime = time + 8000;
        }
        if (null != cameraSegment.getEndTime() && TimeUtils.convertToMilliseconds(cameraSegment.getEndTime()) < endTime) {
            endTime = Math.toIntExact(TimeUtils.convertToMilliseconds(cameraSegment.getEndTime()));
            time = Math.max(endTime - minDuration, (int) TimeUtils.convertToMilliseconds(cameraSegment.getTime()));
        }
        String st = TimeUtils.formatMilliseconds(time);
        String et = TimeUtils.formatMilliseconds(endTime);
        String reason = String.format("%s-%s-%s", performanceAdditionDTO.getResourceName(), performanceAdditionDTO.getText(), performanceAdditionDTO.getReason());
        LottieSegment lottieSegment = SegmentUtil.createLottieSegment(st, et, performanceAdditionDTO.getResourceId(), reason, new JSONObject());
        // lottieSegment.addWidthHeightTopLeft(910, 694, 60, 1150);
        lottieSegment.setDependencies(Lists.newArrayList(performanceAdditionDTO.getResourceId()));
        List<CameraResourceDTO> cameras = context.getCoursewareResourceConfig().getCharacterCameras();
        CameraSegment finalCameraSegment = cameraSegment;
        cameras.stream().filter(e -> StringUtils.equals(e.getId(), finalCameraSegment.getResourceId())).findFirst().ifPresent(cameraInfo -> {
            LottieCfg lottieCfg = parseRectangle(performanceAdditionDTO.getResourceId());
            if (null == lottieCfg) {
                return;
            }
            boolean isImage = lottieSegment.getReason().startsWith("展示图片");
            LottieCfg rect = suggestLottieCfg(lottieCfg, cameraInfo, isImage);
            lottieSegment.addWidthHeightTopLeft(rect.getWidth(), rect.getHeight(), rect.getTop(), rect.getLeft());
            isLeftRightOrClose[0] = StringUtils.containsAny(cameraInfo.getName(), "左", "右")
                    || cameraInfo.getShotSize().equals("近景");
        });

        //同一时间内如果角色镜头不是居中，则随机舍弃一个表演方案。
        List<LottieSegment> lottieSegmentList = coursewareModel.getLottieSegment();
        if (!lottieSegmentList.isEmpty() && isLeftRightOrClose[0]) {
            int lastIndex = lottieSegmentList.size() - 1;
            LottieSegment lastSegment = lottieSegmentList.get(lastIndex);
            long duration = TimeUtils.getDuration(lastSegment.getEndTime(), st);
            if (duration < 0){
                if ( new Random().nextBoolean()){
                    lottieSegmentList.remove(lottieSegmentList.size() - 1);
                    lottieSegmentList.add(lottieSegment);
                }
            }else {
                lottieSegmentList.add(lottieSegment);
            }
        }else {
            lottieSegmentList.add(lottieSegment);
        }

        return lottieSegment;
    }

    private LottieCfg suggestLottieCfg(LottieCfg lottieCfg, CameraResourceDTO cameraInfo,boolean isImage) {
        int width, height, left, top;
        int maxWidth = cameraInfo.getShotSize().equals("近景") ? 540 : 640;
        width = Math.min(lottieCfg.width, maxWidth);
        height = (int) (lottieCfg.height * 1.0f / lottieCfg.width * width);
        top = (1080 - height) / 2;
        if (StringUtils.contains(cameraInfo.getName(), "左")||cameraInfo.getShotSize().equals("近景")) {
            left = (1920 - maxWidth) + (maxWidth - width) / 2;
            // 防止靠右边界
            left -= 10;
        } else if (StringUtils.contains(cameraInfo.getName(), "右")) {
            left = (maxWidth - width) / 2;
            left += 10;
            // 防止靠左边界
        } else {
            if (isImage) {
                left = (1920 - maxWidth) + (maxWidth - width) / 2;
                // 防止靠右边界
                left -= 10;
            } else {
                left = (maxWidth - width) / 2;
                // 防止靠左边界
                left += 10;
            }
        }
        LottieCfg rect = new LottieCfg();
        rect.setTop(top);
        rect.setLeft(left);
        rect.setWidth(width);
        rect.setHeight(height);
        return rect;
    }

    @SneakyThrows
    private LottieCfg parseRectangle(String lottieResId) {
        Map<String, DownloadUrlViewModel> urlViewModelMap = ndrService.getResourceUrlModel(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_COURSEWARE_CONTAINER_ID, lottieResId, "source");
        DownloadUrlViewModel downloadUrlViewModel = urlViewModelMap.get("source");
        String url = "https://" + downloadUrlViewModel.getHost() + downloadUrlViewModel.getUrl();
        url = StringUtils.replace(url, "gcdncs.101.com", "cdncs.101.com");
        url = StringUtils.substringBefore(url, "?");
        String filename = String.format("%s.zip", lottieResId);
        File file = mediaService.download(url, filename, true);

        try (ZipInputStream zis = new ZipInputStream(Files.newInputStream(file.toPath()))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (entry.getName().endsWith("lottie.cfg")) {
                    return WafJsonMapper.getMapper().readValue(zis, LottieCfg.class);
                }
            }
        }
        return null;
    }

    private CameraSegment bestMatchCameraSegment(int st, int et, List<CameraSegment> cameraSegmentList) {
        if (cameraSegmentList.size() == 1) {
            return cameraSegmentList.get(0);
        }

        if (cameraSegmentList.size() == 2) {
            long st1 = Math.max(st, TimeUtils.convertToMilliseconds(cameraSegmentList.get(0).getTime()));
            long et1 = TimeUtils.convertToMilliseconds(cameraSegmentList.get(0).getEndTime());
            long dur1 = et1 - st1;

            long st2 = Math.max(st, TimeUtils.convertToMilliseconds(cameraSegmentList.get(1).getTime()));
            long et2 = et;
            long dur2 = et2 - st2;
            return dur2 > dur1 ? cameraSegmentList.get(1) : cameraSegmentList.get(0);
        } else {
            return cameraSegmentList.get(1);
        }
    }

    @Data
    static class LottieCfg {
        private int width;
        private int height;
        private int left;
        private int top;
    }
}
