package com.nd.aic.service.performance.addition;

import com.nd.aic.entity.TimeArea;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.performance.param.AdditionLottieImageParam;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class AdditionLottieImageHandler extends AdditionLottieHandler {

    public AdditionLottieImageHandler(NdrService ndrService, MediaService mediaService) {
        super(ndrService, mediaService);
    }

    @Override
    public void apply(Integer time, Integer endTime, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        try {
            AdditionLottieImageParam additionLottieImageParam = performanceAdditionDTO.getParamObject(AdditionLottieImageParam.class);
            LottieSegment lottieSegment = generateLottie(time, endTime, performanceAdditionDTO, coursewareModel, context);
            if (null != additionLottieImageParam.getImages()) {
                additionLottieImageParam.getImages().forEach((k, v) -> lottieSegment.addLottieParam(k, "image", v));
            }
            // 移除时间重复的图片增强表演
            TimeArea timeArea = new TimeArea(TimeUtils.convertToMilliseconds(lottieSegment.getTime()), TimeUtils.convertToMilliseconds(lottieSegment.getEndTime()));
            if (null != context.getLatestAdditionImageTimeArea() && context.getLatestAdditionImageTimeArea().overlaps(timeArea)) {
                coursewareModel.getLottieSegment().remove(lottieSegment);
            } else {
                // 记录最后的图片增加表演时间范围
                context.setLatestAdditionImageTimeArea(timeArea);
            }
        } catch (RuntimeException e) {
            log.error("AdditionLottieImageHandler apply error", e);
        }
    }
}
