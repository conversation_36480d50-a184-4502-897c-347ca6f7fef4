package com.nd.aic.service.performance.addition;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.performance.param.AdditionLottieTextParam;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;


@Component
@Slf4j
public class AdditionLottieTextHandler extends AdditionLottieHandler {

    public AdditionLottieTextHandler(NdrService ndrService, MediaService mediaService) {
        super(ndrService, mediaService);
    }

    @Override
    public void apply(Integer time, Integer endTime, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        try {
            LottieSegment lottieSegment = generateLottie(time, endTime, performanceAdditionDTO, coursewareModel, context);
            AdditionLottieTextParam additionLottieTextParam = performanceAdditionDTO.getParamObject(AdditionLottieTextParam.class);
            if (null != additionLottieTextParam.getTexts()) {
                additionLottieTextParam.getTexts().forEach((k, v) -> lottieSegment.addLottieParam(k, "text", v));
            }
        } catch (RuntimeException e) {
            log.error("AdditionLottieTextHandler apply error", e);
        }
    }

}
