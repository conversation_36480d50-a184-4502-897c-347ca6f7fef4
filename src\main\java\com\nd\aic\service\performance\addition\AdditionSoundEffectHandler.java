package com.nd.aic.service.performance.addition;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.common.NdrConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.AudioSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.MediaService;
import com.nd.aic.service.NdrService;
import com.nd.aic.service.performance.param.AdditionLottieTextParam;
import com.nd.aic.service.performance.param.AdditionSoundEffectParam;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;
import com.nd.ndr.model.DownloadUrlViewModel;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;


@Component
@Slf4j
public class AdditionSoundEffectHandler extends AbstractPerformanceAdditionHandler {

    private final NdrService ndrService;
    private final MediaService mediaService;

    public AdditionSoundEffectHandler(NdrService ndrService, MediaService mediaService) {
        super();
        this.ndrService = ndrService;
        this.mediaService = mediaService;
    }

    @Override
    public void apply(Integer time, Integer endTime, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        log.info("AdditionSoundEffectHandler apply: {}", performanceAdditionDTO);
        AdditionSoundEffectParam additionSoundEffectParam = performanceAdditionDTO.getParamObject(AdditionSoundEffectParam.class);
        if (null != additionSoundEffectParam) {
            String align = additionSoundEffectParam.getAlign();
            if (StringUtils.isNotEmpty(align)) {
                Long duration = duration(performanceAdditionDTO.getResourceId());
                if (StringUtils.equals(align, "同时开始") && null != duration) {
                    endTime = time + duration.intValue();
                } else if (StringUtils.equals(align, "同时结束") && null != duration) {
                    time = endTime - duration.intValue();
                }
            }
        }
        String st = TimeUtils.formatMilliseconds(time);
        String et = TimeUtils.formatMilliseconds(endTime);
        String resourceId = performanceAdditionDTO.getResourceId();
        String resourceName = performanceAdditionDTO.getResourceName();
        AudioSegment audioSegment = SegmentUtil.createAudioSegment(st, et, resourceId, "Sfx", 0.7f);
        audioSegment.setReason(performanceAdditionDTO.getText());
        audioSegment.setDependencies(Lists.newArrayList(resourceId));
        coursewareModel.getAudioSegment().add(audioSegment);
    }

    private Long duration(String resourceId) {
        try {
            Map<String, DownloadUrlViewModel> urlViewModelMap = ndrService.getResourceUrlModel(NdrConstant.AIMV_TENANT_ID, NdrConstant.AIMV_MATERIAL_CONTAINER_ID, resourceId, "source");
            DownloadUrlViewModel downloadUrlViewModel = urlViewModelMap.get("source");
            String url = "https://" + downloadUrlViewModel.getHost() + downloadUrlViewModel.getUrl();
            url = StringUtils.replace(url, "gcdncs.101.com", "cdncs.101.com");
            url = StringUtils.substringBefore(url, "?");
            String filename = String.format("%s.mp3", resourceId);
            File file = mediaService.download(url, filename, true);
            return mediaService.durationMills(file);
        } catch (Exception e) {
            return null;
        }
    }

}
