package com.nd.aic.service.performance.addition;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.AtmosphereSegment;
import com.nd.aic.service.performance.param.SeasonChangeAdditionParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class SeasonChangeAdditionHandler extends AbstractPerformanceAdditionHandler {

    @Override
    public void apply(Integer startMs, Integer endMs, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        SeasonChangeAdditionParam seasonChangeAdditionParam = performanceAdditionDTO.getParamObject(SeasonChangeAdditionParam.class);
        String skyResId = StringUtils.trim(seasonChangeAdditionParam.getSkyResId());
        if (StringUtils.isBlank(skyResId)) {
            return;
        }
        AtmosphereSegment seasonSegment = new AtmosphereSegment();
        seasonSegment.time(startMs).endTime(endMs).resourceId(skyResId).type("Sky");
        coursewareModel.getAtmosphereSegment().add(seasonSegment);
    }
}
