package com.nd.aic.service.performance.addition;

import com.nd.aic.common.SegmentPerformConstant;
import com.nd.aic.enums.RoutineTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.TextBubbleAdditionParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class TextBubbleAdditionHandler extends AbstractPerformanceAdditionHandler {

    /**
     * 资源ID
     */
    private static final String RES_ID = "6A550E73453DC76AE685E4862C575DD9";

    @Override
    public void apply(Integer startMs, Integer endMs, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        TextBubbleAdditionParam param = performanceAdditionDTO.getParamObject(TextBubbleAdditionParam.class);
        if (StringUtils.isBlank(param.getTexts())) {
            return;
        }
        RoutineSegment sceneRoutineSegment = new RoutineSegment();
        sceneRoutineSegment.time(startMs).endTime(endMs).resourceId(RES_ID);
        sceneRoutineSegment.type(RoutineTypeEnum.SceneAction.getName()).reason("文字气泡");
        sceneRoutineSegment.perform(SegmentPerformConstant.TEXT_BUBBLE).performOption("texts", StringUtils.split(param.getTexts(), ","));
        sceneRoutineSegment.playType(1)
                .skillType(RoutineTypeEnum.SceneAction.getName())
                .cTime(sceneRoutineSegment.getDurationSec())
                .relativeCharacterInParam()
                .locationParam(-200, -100, 0);
        coursewareModel.getRoutineSegment().add(sceneRoutineSegment);
    }

}
