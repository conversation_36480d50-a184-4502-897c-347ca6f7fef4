package com.nd.aic.service.performance.addition;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.service.performance.param.TextDisplayAdditionParam;

import org.springframework.stereotype.Service;

@Service
public class TextDisplayAdditionHandler extends AbstractPerformanceAdditionHandler {

    @Override
    public void apply(Integer startMs, Integer endMs, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        TextDisplayAdditionParam param = performanceAdditionDTO.getParamObject(TextDisplayAdditionParam.class);
        LottieSegment lottieSegment = new LottieSegment();
        lottieSegment.time(startMs);
        if (param.getDuration() != null) {
            lottieSegment.endTime(startMs + param.getDuration());
        } else {
            lottieSegment.endTime(endMs);
        }
        lottieSegment.resourceId(param.getTextTypeId()).type("LottieScreen");
        lottieSegment.addLottieParam("#Text", "text", param.getText());
        lottieSegment.addLottieParam("#Text1", "text", param.getText());
        lottieSegment.addLottieParam("#Text2", "text", param.getText());
        lottieSegment.addLottieParam("#Text3", "text", param.getText());
        coursewareModel.getLottieSegment().add(lottieSegment);
    }
}
