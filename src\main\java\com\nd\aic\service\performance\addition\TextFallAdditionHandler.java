package com.nd.aic.service.performance.addition;

import com.nd.aic.common.SegmentPerformConstant;
import com.nd.aic.enums.RoutineTypeEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.service.performance.param.TextFallAdditionParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class TextFallAdditionHandler extends AbstractPerformanceAdditionHandler {

    /**
     * 资源ID
     */
    private static final String RES_ID = "6B4504D5435B502DE7929A9C4323C4DD";

    @Override
    public void apply(Integer startMs, Integer endMs, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        TextFallAdditionParam param = performanceAdditionDTO.getParamObject(TextFallAdditionParam.class);
        if (StringUtils.isBlank(param.getTexts())) {
            return;
        }
        RoutineSegment sceneRoutineSegment = new RoutineSegment();
        sceneRoutineSegment.time(startMs).endTime(endMs).resourceId(RES_ID);
        sceneRoutineSegment.type(RoutineTypeEnum.SceneAction.getName()).reason("文字下落");
        sceneRoutineSegment.perform(SegmentPerformConstant.TEXT_FALL).performOption("texts", StringUtils.split(param.getTexts(), ","));
        sceneRoutineSegment.playType(1)
                .skillType(RoutineTypeEnum.SceneAction.getName())
                .cTime(sceneRoutineSegment.getDurationSec())
                .relativeCharacterInParam()
                .locationParam(-650, 180, 0);
        coursewareModel.getRoutineSegment().add(sceneRoutineSegment);
    }
}
