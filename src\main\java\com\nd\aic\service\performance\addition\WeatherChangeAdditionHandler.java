package com.nd.aic.service.performance.addition;

import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceAdditionDTO;
import com.nd.aic.pojo.segment.AtmosphereSegment;
import com.nd.aic.service.performance.param.WeatherChangeAdditionParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class WeatherChangeAdditionHandler extends AbstractPerformanceAdditionHandler {

    @Override
    public void apply(Integer startMs, Integer endMs, PerformanceAdditionDTO performanceAdditionDTO, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        WeatherChangeAdditionParam param = performanceAdditionDTO.getParamObject(WeatherChangeAdditionParam.class);
        String resId = StringUtils.trim(param.getWeatherResId());
        if (StringUtils.isBlank(resId)) {
            return;
        }
        AtmosphereSegment weatherSegment = new AtmosphereSegment();
        weatherSegment.time(startMs).endTime(endMs).resourceId(resId).type("Weather");
        coursewareModel.getAtmosphereSegment().add(weatherSegment);
    }
}
