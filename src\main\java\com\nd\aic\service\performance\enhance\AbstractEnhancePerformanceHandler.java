package com.nd.aic.service.performance.enhance;

import com.nd.aic.enums.StandPositionEnum;
import com.nd.aic.pojo.dto.CameraResourceDTO;
import com.nd.aic.pojo.dto.PerformanceEnhanceDTO;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.util.PerformanceEnhanceUtils;
import com.nd.aic.util.TimeUtils;
import com.nd.aic.util.AtaWordUtil;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public abstract class AbstractEnhancePerformanceHandler {

    public void enhance(TeachEventTypeDTO teachEventType, PerformanceEnhanceDTO performanceEnhance, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        int index = StringUtils.indexOf(teachEventType.getDialogue(), performanceEnhance.getAnnoText());
        if (-1 == index) {
            return;
        }
        String st = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index, true));
        String et = TimeUtils.formatMilliseconds(AtaWordUtil.getWordTiming(context.getAtaWords(), teachEventType.getDialogueStartPos() + index + performanceEnhance.getAnnoText().length(), false));
        apply(st, et, teachEventType, performanceEnhance, coursewareModel, context);
    }

    protected abstract void apply(String st, String et, TeachEventTypeDTO teachEventType, PerformanceEnhanceDTO performanceEnhance, CoursewareModel coursewareModel, CoursewareGenerationContext context);

    protected int[] mappingCoordinate(String st, TeachEventTypeDTO teachEventType, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        StandPositionEnum standPosition = getPositionFromCameraSegment(teachEventType.getTime(), st, coursewareModel.getCameraSegment(), context);
        List<String> candidatePosition = PerformanceEnhanceUtils.mappingCandidatePosition(standPosition);
        return PerformanceEnhanceUtils.mappingCoordinate(candidatePosition.get(0));
    }

    protected StandPositionEnum getPositionFromCameraSegment(String time, String st, List<CameraSegment> cameraSegments, CoursewareGenerationContext context) {
        // 筛选当前表演方案范围内的镜头片段
        long startMills = TimeUtils.convertToMilliseconds(time);
        cameraSegments = cameraSegments.stream().filter(segment -> TimeUtils.convertToMilliseconds(segment.getTime()) >= startMills).collect(Collectors.toList());
        if (cameraSegments.isEmpty()) {
            return null;
        }
        // 获取当前表演方案开始时间之前的最近的镜头片段
        CameraSegment cameraSegment = null;
        for (int i = cameraSegments.size() - 1; i >= 0; i--) {
            if (TimeUtils.convertToMilliseconds(cameraSegments.get(i).getTime()) <= TimeUtils.convertToMilliseconds(st)) {
                cameraSegment = cameraSegments.get(i);
                break;
            }
        }
        if (cameraSegment == null) {
            return null;
        }
        String cameraResource = cameraSegment.getResourceId();
        CameraResourceDTO cameraResourceDTO = context.getCoursewareResourceConfig().getCharacterCameras().stream().filter(e -> StringUtils.equals(cameraResource, e.getId())).findFirst().orElse(null);
        if (cameraResourceDTO == null) {
            return null;
        }
        return cameraResourceDTO.getPosition();
    }

}
