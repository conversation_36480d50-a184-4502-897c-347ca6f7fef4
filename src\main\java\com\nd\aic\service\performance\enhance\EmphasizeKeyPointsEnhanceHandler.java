package com.nd.aic.service.performance.enhance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceEnhanceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Component;

@Component
public class EmphasizeKeyPointsEnhanceHandler extends AbstractEnhancePerformanceHandler {

    @Override
    public void apply(String st, String et, TeachEventTypeDTO teachEventType, PerformanceEnhanceDTO performanceEnhance, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        int[] coordinate = mappingCoordinate(st, teachEventType, coursewareModel, context);

        String keyword = performanceEnhance.getAnnoText();
        JSONObject lottieParam = new JSONObject();
        lottieParam.fluentPut("#Text1", new JSONObject().fluentPut("type", "text").fluentPut("value", keyword))
                .fluentPut("#Text2", new JSONObject().fluentPut("type", "text").fluentPut("value", keyword))
                .fluentPut("#Text3", new JSONObject().fluentPut("type", "text").fluentPut("value", keyword));
        lottieParam.fluentPut("windowId", "1").fluentPut("width", 640).fluentPut("height", 360).fluentPut("top", coordinate[1] - 180).fluentPut("left", coordinate[0] - 320);

        if (TimeUtils.getDuration(st, et) < 3000) {
            et = TimeUtils.add(st, 3000);
        }
        LottieSegment lottieSegment = SegmentUtil.createLottieSegment(st, et, "f9e1667d-45b3-4d24-b4c1-3da80bafda43", "强调重点", lottieParam);
        coursewareModel.getLottieSegment().add(lottieSegment);
    }
}
