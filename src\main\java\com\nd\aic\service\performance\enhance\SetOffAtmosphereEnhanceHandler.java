package com.nd.aic.service.performance.enhance;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.enums.StandPositionEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.PerformanceEnhanceDTO;
import com.nd.aic.pojo.dto.TeachEventTypeDTO;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.util.PerformanceEnhanceUtils;
import com.nd.aic.util.SegmentUtil;
import com.nd.aic.util.TimeUtils;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SetOffAtmosphereEnhanceHandler extends AbstractEnhancePerformanceHandler {

    @Override
    public void apply(String st, String et, TeachEventTypeDTO teachEventType, PerformanceEnhanceDTO performanceEnhance, CoursewareModel coursewareModel, CoursewareGenerationContext context) {
        StandPositionEnum standPosition = getPositionFromCameraSegment(teachEventType.getTime(), st, coursewareModel.getCameraSegment(), context);
        List<String> candidatePosition = PerformanceEnhanceUtils.mappingCandidatePosition(standPosition);
        int[] coordinate = PerformanceEnhanceUtils.mappingCoordinate(candidatePosition.get(0));

        if (TimeUtils.getDuration(st, et) < 2700) {
            et = TimeUtils.add(st, 2700);
        }
        JSONObject lottieObject = new JSONObject();
        lottieObject.fluentPut("windowId", "1").fluentPut("width", 360).fluentPut("height", 360).fluentPut("top", coordinate[1] - 180).fluentPut("left", coordinate[0] - 180);
        // ".\WindowsClient\ElectronResCache\lottie\6CD3BA4BC6624389A8C5399AA71852DF\images\img_4.png"
        lottieObject.fluentPut("#Image", new JSONObject().fluentPut("type", "image").fluentPut("value", "https://gcdncs.101.com/v0.1/download?dentryId=0d911b9b-9314-431f-a3cb-fcabcccda33b"));
        LottieSegment lottieSegment = SegmentUtil.createLottieSegment(st, et, "C015975D2E3545499421786C51400DE1", "图片信息", lottieObject);
        coursewareModel.getLottieSegment().add(lottieSegment);
    }
}
