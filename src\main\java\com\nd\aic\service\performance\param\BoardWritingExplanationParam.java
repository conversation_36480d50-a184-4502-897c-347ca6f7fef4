package com.nd.aic.service.performance.param;


import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BoardWritingExplanationParam extends AbstractPerformanceParam {

    private String image;

    private String video;

    private String reason;

    private Integer pptIndex;

    /**
     * 全屏板书的内容
     */
    private String showPpt1;
    private Integer pptType1;
    private String showPpt2;
    private Integer pptType2;
    private String showPpt3;
    private Integer pptType3;
}
