package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BookFlippingBrowsingParam extends AbstractPerformanceParam {

    private String texture1;
    private String texture2;
    private String texture3;
    private String texture4;
    private String texture5;
    private String texture6;
    private String texture7;
    private String texture8;

    /**
     * 第二页到第三页触发点
     */
    private Integer trigger2;
    /**
     * 第三页到第四页触发点
     */
    private Integer trigger3;
    /**
     * 第四页到第五页触发点
     */
    private Integer trigger4;

}
