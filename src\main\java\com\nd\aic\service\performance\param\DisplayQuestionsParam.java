package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.lang.reflect.Field;

@EqualsAndHashCode(callSuper = true)
@Data
public class DisplayQuestionsParam extends AbstractPerformanceParam{
    /**
     *  版面主题
     */
    private String forumtopic;
    /**
     *  问题标题
     */
    private String questiontitle;
    /**
     *  选项A,B,C,D
     */
    private String optionsa;
    private String optionsb;
    private String optionsc;
    private String optionsd;

    // 动态获取字段值
    public String get(String fieldName) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return (String) field.get(this);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法获取字段值: " + fieldName, e);
        }
    }

    // 动态设置字段值
    public void set(String fieldName, String value) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(this, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法设置字段值: " + fieldName, e);
        }
    }
}
