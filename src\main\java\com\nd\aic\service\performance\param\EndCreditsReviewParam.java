package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 展示人物传记
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EndCreditsReviewParam  extends AbstractPerformanceParam{
    /**
     * 左侧人物图像
     */
    private String image1;
    /**
     * 背景
     */
    private String image2;
    /**
     * 人物介绍的主标题 最多13个字
     */
    private String texture1 ;

    /**
     * 人物介绍的副标题 最多9个字
     */
    private String texture2 ;

    /**
     * 人物介绍的签名
     */
    private String texture3 ;
}
