package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.lang.reflect.Field;

/**
 * 悬浮画框
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FloatingFrameParam extends AbstractPerformanceParam{
    /**
     * 图像
     */
    private String picture1;
    /**
     * 视频资源，直接上传的ndr视频id
     */
    private String video1;
    /**
     * 切换时间
     */
    private String duration1;

    private String picture2;
    private String video2;
    private String duration2;

    private String picture3;
    private String video3;
    private String duration3;

    private String picture4;
    private String video4;
    private String duration4;

    private String picture5;
    private String video5;
    private String duration5;


    // 动态获取字段值
    public String get(String fieldName) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return (String) field.get(this);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法获取字段值: " + fieldName, e);
        }
    }

    // 动态设置字段值
    public void set(String fieldName, String value) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(this, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法设置字段值: " + fieldName, e);
        }
    }
}
