package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * 关键信息汇聚成问号
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatherInfoIntoQuestionMarkParam extends AbstractPerformanceParam {

    private String keyInfo1;

    private String keyInfo2;

    private String keyInfo3;

    public int getKeyInfoCount(){
        int count = 0;
        if(StringUtils.isNotEmpty(keyInfo1)){
            count++;
        }
        if(StringUtils.isNotEmpty(keyInfo2)){
            count++;
        }
        if(StringUtils.isNotEmpty(keyInfo3)){
            count++;
        }
        return count;
    }
}
