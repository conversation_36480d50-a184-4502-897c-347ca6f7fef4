package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GuideAndWatchVideoParam extends AbstractPerformanceParam {
    /**
     * 视频资源，直接上传的ndr视频id
     */
    private String video;

    private Long duration;

    private String reason;

    private String playType;
    // 是指多久飞入视频封面
    private Integer delay;
    // 是指飞入后，多久开始播放视频
    private Integer pause;
}
