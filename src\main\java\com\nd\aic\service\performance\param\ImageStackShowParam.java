package com.nd.aic.service.performance.param;

import com.google.common.collect.Lists;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImageStackShowParam extends AbstractPerformanceParam {
    private String image1;
    private String image2;
    private String image3;
    private String image4;
    private String image5;
    private String image6;
    private String image7;
    private String image8;
    // private String image9;
    private List<String> imageList = Lists.newArrayList();
}
