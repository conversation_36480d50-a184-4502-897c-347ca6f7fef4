package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MultiGroupBoardShowContentParam extends AbstractPerformanceParam {

    // 5 group image, max 5 images in a group
    // create single params for each image, do not use string[] or array
    // group is not necessary, but it is better to have
    private String group1Image1;
    private String group1Image2;
    private String group1Image3;
    private String group1Image4;
    private String group1Image5;
    private String aliasContent1;

    private String group2Image1;
    private String group2Image2;
    private String group2Image3;
    private String group2Image4;
    private String group2Image5;
    private String aliasContent2;


    private String group3Image1;
    private String group3Image2;
    private String group3Image3;
    private String group3Image4;
    private String group3Image5;
    private String aliasContent3;


    private String group4Image1;
    private String group4Image2;
    private String group4Image3;
    private String group4Image4;
    private String group4Image5;
    private String aliasContent4;


    private String group5Image1;
    private String group5Image2;
    private String group5Image3;
    private String group5Image4;
    private String group5Image5;
    private String aliasContent5;

    // create a function, get the groups that have images
    // group images may appear in any one
    public int getGroupCount() {
        int count = 0;
        if (group1Image1 != null || group1Image2 != null || group1Image3 != null || group1Image4 != null || group1Image5 != null) {
            count++;
        }
        if (group2Image1 != null || group2Image2 != null || group2Image3 != null || group2Image4 != null || group2Image5 != null) {
            count++;
        }
        if (group3Image1 != null || group3Image2 != null || group3Image3 != null || group3Image4 != null || group3Image5 != null) {
            count++;
        }
        if (group4Image1 != null || group4Image2 != null || group4Image3 != null || group4Image4 != null || group4Image5 != null) {
            count++;
        }
        if (group5Image1 != null || group5Image2 != null || group5Image3 != null || group5Image4 != null || group5Image5 != null) {
            count++;
        }
        return count;
    }

    // create a function, return string[][], to count the images in each group
    public String[][] getGroupImages() {
        String[][] groups = new String[5][5];
        int count = 0;
        if (group1Image1 != null) {
            groups[0][count++] = group1Image1;
        }
        if (group1Image2 != null) {
            groups[0][count++] = group1Image2;
        }
        if (group1Image3 != null) {
            groups[0][count++] = group1Image3;
        }
        if (group1Image4 != null) {
            groups[0][count++] = group1Image4;
        }
        if (group1Image5 != null) {
            groups[0][count++] = group1Image5;
        }
        count = 0;
        if (group2Image1 != null) {
            groups[1][count++] = group2Image1;
        }
        if (group2Image2 != null) {
            groups[1][count++] = group2Image2;
        }
        if (group2Image3 != null) {
            groups[1][count++] = group2Image3;
        }
        if (group2Image4 != null) {
            groups[1][count++] = group2Image4;
        }
        if (group2Image5 != null) {
            groups[1][count++] = group2Image5;
        }
        count = 0;
        if (group3Image1 != null) {
            groups[2][count++] = group3Image1;
        }
        if (group3Image2 != null) {
            groups[2][count++] = group3Image2;
        }
        if (group3Image3 != null) {
            groups[2][count++] = group3Image3;
        }
        if (group3Image4 != null) {
            groups[2][count++] = group3Image4;
        }
        if (group3Image5 != null) {
            groups[2][count++] = group3Image5;
        }
        count = 0;
        if (group4Image1 != null) {
            groups[3][count++] = group4Image1;
        }
        if (group4Image2 != null) {
            groups[3][count++] = group4Image2;
        }
        if (group4Image3 != null) {
            groups[3][count++] = group4Image3;
        }
        if (group4Image4 != null) {
            groups[3][count++] = group4Image4;
        }
        if (group4Image5 != null) {
            groups[3][count++] = group4Image5;
        }
        count = 0;
        if (group5Image1 != null) {
            groups[4][count++] = group5Image1;
        }
        if (group5Image2 != null) {
            groups[4][count++] = group5Image2;
        }
        if (group5Image3 != null) {
            groups[4][count++] = group5Image3;
        }
        if (group5Image4 != null) {
            groups[4][count++] = group5Image4;
        }

        if (group5Image5 != null) {
            groups[4][count++] = group5Image5;
        }
        return groups;
    }

}
