package com.nd.aic.service.performance.param;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
public class MultiPanelDisplayContentParam extends AbstractPerformanceParam {
    private String text0;

    private String text1;
    private String image1;
    private String video1;
    private String video1Type;

    private String text2;
    private String image2;
    private String video2;
    private String video2Type;

    private String text3;
    private String image3;
    private String video3;
    private String video3Type;

    private String text4;
    private String image4;
    private String video4;
    private String video4Type;

    private String text5;
    private String image5;
    private String video5;
    private String video5Type;

    private String text6;
    private String image6;
    private String video6;
    private String video6Type;

    @Deprecated
    private List<String> medias;
    @JsonIgnore
    private List<PanelRes> panelResList;

    private int resourceCount;

    @Data
    @Accessors(chain = true)
    public static class PanelRes {
        private String video;
        private String videoType;
        private String text;
        private boolean patch;
    }
}
