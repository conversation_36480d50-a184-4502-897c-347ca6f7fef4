package com.nd.aic.service.performance.param;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@EqualsAndHashCode(callSuper = true)
@Data
public class NeonExplainParam extends AbstractPerformanceParam {

    /**
     * 视频资源，直接上传的ndr视频id
     */
    private String video;

    private String image;

    private Long duration;

    private String reason;

    private String playType;

    private String image1;

    private String image2;

    private String image3;

    private String video1;

    private String video2;

    private String video3;

    private Res res;
    private List<Res> resList;

    @Getter
    public static class Res {
        private final String type;
        private final String value;
        public Res(String type, String value) {
            this.type = type;
            this.value = value;
        }
    }
}
