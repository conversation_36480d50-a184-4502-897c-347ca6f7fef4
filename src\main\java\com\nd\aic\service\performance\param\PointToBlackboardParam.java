package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 手搓小黑板
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PointToBlackboardParam extends AbstractPerformanceParam {
    private String text1;
    private String image1;
    private String video1;
    private String video1Type;

    private String text2;
    private String image2;
    private String video2;
    private String video2Type;
}
