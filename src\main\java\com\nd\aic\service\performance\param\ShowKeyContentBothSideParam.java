package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 展示关键内容双面参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShowKeyContentBothSideParam extends AbstractPerformanceParam {


    private String key1;
    private String content1;
    /**
     * 第二个关键字卡点文本
     */
    private String aliasKey1;

    private String key2;
    private String content2;
    /**
     * 第二个关键字卡点文本
     */
    private String aliasKey2;


}
