package com.nd.aic.service.performance.param;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ShowPhrasesParam extends AbstractPerformanceParam {

    private String text1;
    private String text2;
    private String text3;
    private String text4;
    private String text5;
    private String text6;

    private List<String> phrases;
}
