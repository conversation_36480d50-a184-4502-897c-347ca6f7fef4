package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 通用WEB表演参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UniversalWebParam extends AbstractPerformanceParam {
    
    /**
     * 模板名称
     */
    private String template;
    
    /**
     * 表演持续时间（秒）
     */
    private Integer duration;
    
    /**
     * 动态参数Map，包含所有前端传入的参数
     * 格式：{"参数名": {"value": "值", "type": "image/video/text"}}
     */
    private Map<String, Object> dynamicParams;
    
    /**
     * 资源数据类，用于内部处理
     */
    @Data
    public static class ResourceData {
        private String name;
        private String type;
        private String url;
        private String content;
        private Integer startTime = 0;
        private boolean fullLoad = false;
    }
    
    /**
     * 预加载数据类
     */
    @Data
    public static class PreloadData {
        private String template;
        private java.util.List<ResourceData> resources;
    }
}
