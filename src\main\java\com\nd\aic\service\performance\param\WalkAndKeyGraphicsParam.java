package com.nd.aic.service.performance.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.lang.reflect.Field;

@EqualsAndHashCode(callSuper = true)
@Data
public class WalkAndKeyGraphicsParam extends AbstractPerformanceParam{
    /**
     * 关键字
     */
    private String text1;
    private String text2;
    private String text3;
    private String text4;
    private String text5;

    /**
     * 关键字引用的文本信息（出现的时间点）
     */
    private String duration1;
    private String duration2;
    private String duration3;
    private String duration4;
    private String duration5;

    // 动态获取字段值
    public String get(String fieldName) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return (String) field.get(this);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法获取字段值: " + fieldName, e);
        }
    }

    // 动态设置字段值
    public void set(String fieldName, String value) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(this, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法设置字段值: " + fieldName, e);
        }
    }
}
