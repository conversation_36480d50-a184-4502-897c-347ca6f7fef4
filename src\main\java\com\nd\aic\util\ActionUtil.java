package com.nd.aic.util;

import com.nd.aic.enums.RoutineTypeEnum;
import com.nd.aic.pojo.SentenceTiming;
import com.nd.aic.pojo.dto.ActionResourceDTO;
import com.nd.aic.pojo.dto.CoursewareResourceDTO;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.RoutineSegment;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public final class ActionUtil {

    public static final String ACTION_IDLE = "idle";
    public static final String ACTION_SPEAK = "speak";
    public static final String ACTION_POINT_TO_LEFT = "point_to_left";
    public static final String ACTION_POINT_TO_RIGHT = "point_to_right";
    public static final String ACTION_KEYWORD = "keyword";

    public static List<ActionSegment> genActionSegments(List<SentenceTiming> sentenceTimings, List<ActionSegment> globalActionSegments, List<ActionResourceDTO> actionResList) {
        List<ActionSegment> actionSegments = new ArrayList<>();
        List<ActionResourceDTO> keywordActions = actionResList.stream()
                .filter(actionRes -> StringUtils.equalsIgnoreCase(ACTION_KEYWORD, actionRes.getType()))
                .collect(Collectors.toList());
        for (ActionResourceDTO keywordAction : keywordActions) {
            List<String> words = keywordAction.getWords();
            if (CollectionUtils.isEmpty(words)) {
                continue;
            }
            // TODO 关键字
            for (String word : words) {
                for (SentenceTiming sentenceTiming : sentenceTimings) {
                    if (StringUtils.contains(sentenceTiming.getText(), word)) {

                        // Integer startTime = AtaWordUtil.getWordTiming(words, startPos, true);
                        // Integer endTime = AtaWordUtil.getWordTiming(words, endPos, false);

                        ActionSegment actionSegment = new ActionSegment().resourceId(keywordAction.getId())
                                .type(keywordAction.getType())
                                .reason(keywordAction.getName());
                        // actionSegments.add(actionSegment);
                        // actionSegment.setActionRes(keywordAction);
                        // actionSegment.setStartTime(sentenceTiming.getStartTime());
                        // actionSegment.setEndTime(sentenceTiming.getEndTime());
                        // actionSegments.add(actionSegment);
                    }
                }
            }
        }

        for (SentenceTiming sentenceTiming : sentenceTimings) {
            Integer startTime = sentenceTiming.getStartTime();
            Integer endTime = sentenceTiming.getEndTime();
            if (startTime == null || endTime == null) {
                continue;
            }
            double sentenceDuration = sentenceTiming.getDurationSec();
            if (sentenceDuration <= 3) {
                continue;
            }
            List<ActionSegment> existingActionSegments = new ArrayList<>();
            if (globalActionSegments != null) {
                existingActionSegments.addAll(globalActionSegments);
            }
            existingActionSegments.addAll(actionSegments);
            actionSegments.addAll(genActionSegments(startTime, endTime - 500, ACTION_SPEAK, actionResList, existingActionSegments));
        }
        return actionSegments;
    }

    public static List<ActionSegment> genActionSegments(long startTime, long endTime, String actionType, List<ActionResourceDTO> actionResList, List<ActionSegment> existingActionSegments) {
        List<ActionResourceDTO> filteredTypeActions = actionResList.stream()
                .filter(actionRes -> StringUtils.equalsIgnoreCase(actionType, actionRes.getType()))
                .collect(Collectors.toList());
        List<ActionSegment> actionSegments = new ArrayList<>();
        if (CollectionUtils.isEmpty(filteredTypeActions)) {
            return actionSegments;
        }
        int minSplit = 2000;
        int maxSplit = 20000;
        // 定义动作间隔时间为0.5秒（500毫秒）
        final long actionInterval = 500;
        long current = startTime;
        while (current < endTime) {
            // 计算剩余时间
            long remainingTime = endTime - current;

            // 筛选出动作时长小于剩余时间的动作资源
            List<ActionResourceDTO> availableActions = filteredTypeActions.stream().filter(actionRes -> {
                if (actionRes.getDuration() != null) {
                    return (long) (actionRes.getDuration() * 1000) <= remainingTime;
                }
                // 如果没有指定时长，则默认可用
                return true;
            }).collect(Collectors.toList());

            // 如果没有合适的动作资源，则退出循环
            if (CollectionUtils.isEmpty(availableActions)) {
                break;
            }

            // 减少重复出现动作
            Map<String, Integer> scoreMap = new HashMap<>(16);
            for (ActionResourceDTO availableAction : availableActions) {
                int score = 0;
                for (ActionSegment actionSegment : existingActionSegments) {
                    if (StringUtils.equals(availableAction.getId(), actionSegment.getResourceId())) {
                        score -= 10;
                    }
                }
                for (ActionSegment actionSegment : actionSegments) {
                    if (StringUtils.equals(availableAction.getId(), actionSegment.getResourceId())) {
                        score -= 20;
                    }
                }
                scoreMap.put(availableAction.getId(), score);
            }
            // 最大分值
            int maxScore = Collections.max(scoreMap.values());
            List<String> maxScoreActionIds = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : scoreMap.entrySet()) {
                if (entry.getValue() == maxScore) {
                    maxScoreActionIds.add(entry.getKey());
                }
            }

            List<ActionResourceDTO> maxScoreActions = availableActions.stream()
                    .filter(obj -> maxScoreActionIds.contains(obj.getId()))
                    .collect(Collectors.toList());
            CoursewareResourceDTO action = maxScoreActions.get(RandomUtils.nextInt(0, maxScoreActions.size()));

            long actionDuration;
            if (action.getDuration() != null) {
                actionDuration = (long) (action.getDuration() * 1000);
            } else {
                // 对于未指定时长的动作，确保不超过剩余时间
                actionDuration = Math.min(minSplit + RandomUtils.nextInt(0, maxSplit - minSplit + 1), remainingTime);
            }
            long split = current + actionDuration;
            ActionSegment actionSegment = new ActionSegment().time(current)
                    .endTime(split)
                    .resourceId(action.getId())
                    .type(actionType)
                    .reason(action.getName());
            actionSegments.add(actionSegment);
            if (split >= endTime || (endTime - split) < minSplit) {
                break;
            }
            // 添加0.5秒间隔
            current = split + actionInterval;
        }
        return actionSegments;
    }

    public static RoutineSegment genIdleActionSegment(List<ActionResourceDTO> actionResList) {
        List<ActionResourceDTO> idleActions = actionResList.stream()
                .filter(actionRes -> StringUtils.equalsIgnoreCase(ACTION_IDLE, actionRes.getType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idleActions)) {
            return null;
        }
        ActionResourceDTO idleAction = idleActions.get(0);
        return new RoutineSegment().time(0)
                .resourceId(idleAction.getId())
                .type(idleAction.getType())
                .reason(idleAction.getName())
                .playType(0)
                .skillType(RoutineTypeEnum.Animation.getName())
                .putParam("IsIdleAnim", true);
    }

}
