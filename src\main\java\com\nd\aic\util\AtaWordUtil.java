package com.nd.aic.util;

import com.nd.aic.pojo.AtaUtteranceWithPos;
import com.nd.aic.pojo.AtaWordWithPos;
import com.nd.aic.pojo.SentenceTiming;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public final class AtaWordUtil {

    /**
     * 根据位置计算时间
     */
    public static Integer getWordTimingByUtterances(List<AtaUtteranceWithPos> ataUtterances, int pos, boolean start) {
        List<AtaWordWithPos> words = ataUtterances.stream()
                .map(AtaUtteranceWithPos::getWords)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        return getWordTiming(words, pos, start);
    }

    /**
     * 根据位置计算时间
     */
    public static Integer getWordTiming(List<AtaWordWithPos> ataWords, int pos, boolean start) {
        if (CollectionUtils.isEmpty(ataWords) || pos < 0) {
            return 0;
        }
        // 最后一个词
        AtaWordWithPos lastWord = ataWords.get(ataWords.size() - 1);
        // 超出最后一个词的范围
        if (pos > lastWord.getPos()) {
            return start ? lastWord.getStartTime() : lastWord.getEndTime();
        }
        // 完全命中
        for (AtaWordWithPos word : ataWords) {
            if (pos == word.getPos()) {
                return start ? word.getStartTime() : word.getEndTime();
            }
        }
        // 未命中，可能是行尾的标点符号
        for (int i = ataWords.size() - 1; i >= 0; i--) {
            if (pos > ataWords.get(i).getPos()) {
                return ataWords.get(i).getEndTime();
            }
        }
        return 0;
    }

    /**
     * 将文本按句号和问号切分，并计算每句话的开始时间和结束时间
     *
     * @param text  原始文本
     * @param words 所有单词及其位置信息
     * @return 包含每句话及其时间信息的列表
     */
    public static List<SentenceTiming> splitTextAndCalculateTiming(String text, int textStartPos, List<AtaWordWithPos> words) {
        if (StringUtils.isBlank(text) || CollectionUtils.isEmpty(words)) {
            return new ArrayList<>();
        }

        // 定义分隔符的正则：中英文的句号、问号、感叹号
        String regex = "[。？！.?!]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);

        List<SentenceTiming> sentences = new ArrayList<>();
        int lastEnd = 0;

        // 查找所有分隔符
        while (matcher.find()) {
            int delimiterPos = matcher.end();
            processSentence(text, words, sentences, textStartPos, lastEnd, delimiterPos);
            lastEnd = delimiterPos;
        }

        // 处理最后一部分文本（如果没有以分隔符结尾）
        if (lastEnd < text.length()) {
            processSentence(text, words, sentences, textStartPos, lastEnd, text.length());
        }

        return sentences;
    }

    /**
     * 处理单个句子，计算时间并添加到结果列表
     */
    private static void processSentence(String text, List<AtaWordWithPos> words, List<SentenceTiming> sentences, int textStartPos, int startIndex, int endIndex) {
        // 获取原始句子
        String originalSentence = text.substring(startIndex, endIndex);

        // 获取处理后的句子（去除空白字符）
        String cleanedSentence = originalSentence.trim();

        if (cleanedSentence.isEmpty()) {
            return;
        }

        // 计算句子中第一个非空白字符的位置
        int firstNonWhitespaceIndex = findFirstNonWhitespaceIndex(originalSentence);

        // 计算句子中最后一个非空白字符的位置
        int lastNonWhitespaceIndex = findLastNonWhitespaceIndex(originalSentence);

        // 计算有意义字符的实际位置
        int startPos = textStartPos + startIndex + firstNonWhitespaceIndex;
        int endPos = textStartPos + startIndex + lastNonWhitespaceIndex;

        // 获取句子的开始和结束时间
        Integer startTime = getWordTiming(words, startPos, true);
        Integer endTime = getWordTiming(words, endPos, false);

        sentences.add(SentenceTiming.builder().text(cleanedSentence).startTime(startTime).endTime(endTime).startPos(startPos).endPos(endPos).build());
    }

    /**
     * 查找字符串中第一个非空白字符的索引
     */
    private static int findFirstNonWhitespaceIndex(String str) {
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return i;
            }
        }
        return 0;
    }

    /**
     * 查找字符串中最后一个非空白字符的索引
     */
    private static int findLastNonWhitespaceIndex(String str) {
        for (int i = str.length() - 1; i >= 0; i--) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return i;
            }
        }
        return str.length() - 1;
    }
}
