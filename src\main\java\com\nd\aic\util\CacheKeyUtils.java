package com.nd.aic.util;

import com.google.common.collect.Lists;

import com.nd.aic.client.pojo.tts.ChildTtsSegment;
import com.nd.aic.client.pojo.tts.TtsSegment;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.audio.AudioReq;
import com.nd.aic.pojo.audio.AudioReqSegment;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CacheKeyUtils {

    private static final Pattern INVALID_CHARS = Pattern.compile("^[\\p{Punct}\\s，。！？；：“”‘’（）《》〈〉【】、——……·]+$");

    public static String audioCacheKey(AudioReq audioReq) {
        String defVoiceId = StringUtils.defaultString(audioReq.getVoiceType());
        Float defVoiceSpeed = audioReq.getVoiceSpeed() == null ? 1 : audioReq.getVoiceSpeed();
        StringBuilder builder = new StringBuilder();
        // 发音人
        builder.append(defVoiceId).append("\n");
        // 语速
        builder.append(defVoiceSpeed).append("\n");
        for (AudioReqSegment segment : audioReq.getSegments()) {
            // 教学活动
            builder.append(StringUtils.defaultString(segment.getTeachingActivity(), "NULL")).append("\t");
            // 音频时长要求
            builder.append(segment.getDuration() == null ? 0 : segment.getDuration()).append("\t");
            // 对话稿
            builder.append(StringUtils.defaultString(segment.getDialogue(), "NULL")).append("\n");
            // 发音标注
            Object tone = MapUtils.getObject(segment.getPronunciationDict(), "tone");
            if (!CollectionUtils.sizeIsEmpty(tone)) {
                builder.append(JsonUtils.toJson(tone));
            }
            // 句尾静音时长
            if (segment.getSilenceDuration() != null) {
                builder.append(segment.getSilenceDuration()).append("\n");
            }

            List<ChildTtsSegment> childSegments = segment.getTtsSegments();
            String childSegmentContent = appendTtsSegments(segment.getDialogue(), childSegments, defVoiceId, defVoiceSpeed);
            builder.append(childSegmentContent).append("\n");
        }
        return UUID.nameUUIDFromBytes(builder.toString().getBytes(StandardCharsets.UTF_8)).toString().trim();
    }

    private static String appendTtsSegments(String text, List<ChildTtsSegment> childSegments, String defVoiceId, Float defVoiceSpeed) {
        StringBuilder builder = new StringBuilder();
        // // 发音标注
        // Object tone = MapUtils.getObject(pronunciationDict, "tone");
        // if (!CollectionUtils.sizeIsEmpty(tone)) {
        //     builder.append(JsonUtils.toJson(tone)).append("\n");
        // }

        if (CollectionUtils.isNotEmpty(childSegments)) {
            // 拷贝一份，避免排序后影响原始数据
            childSegments = Lists.newArrayList(childSegments);
            for (ChildTtsSegment childSegment : childSegments) {
                int idx = StringUtils.indexOf(text, childSegment.getText());
                childSegment.setIndex(idx);
            }
            childSegments.sort(Comparator.comparingInt(ChildTtsSegment::getIndex));

            for (TtsSegment childSegment : childSegments) {
                builder.append("[");
                // 发音人
                builder.append(StringUtils.defaultString(childSegment.getVoiceId(), defVoiceId)).append("\n")
                        // 语速
                        .append(null == childSegment.getVoiceSpeed() ? defVoiceSpeed : childSegment.getVoiceSpeed()).append("\n")
                        // 对话稿
                        .append(childSegment.getText());
                // 发音标注
                Object tone = MapUtils.getObject(childSegment.getPronunciationDict(), "tone");
                if (!CollectionUtils.sizeIsEmpty(tone)) {
                    builder.append(JsonUtils.toJson(tone));
                }
                builder.append("]");
            }
        }
        return builder.toString();
    }

    public static String audioCacheKey(TtsSegment ttsSegment) {
        String defVoiceId = StringUtils.defaultString(ttsSegment.getVoiceId(), "default");
        Float defVoiceSpeed = null == ttsSegment.getVoiceSpeed() ? 1 : ttsSegment.getVoiceSpeed();

        StringBuilder builder = new StringBuilder("mergeTts:");
        builder.append(defVoiceId).append("\n")
                .append(defVoiceSpeed).append("\n")
                .append(ttsSegment.getText());
        Object tone = MapUtils.getObject(ttsSegment.getPronunciationDict(), "tone");
        if (!CollectionUtils.sizeIsEmpty(tone)) {
            builder.append(JsonUtils.toJson(tone)).append("\n");
        }

        List<ChildTtsSegment> childSegments = ttsSegment.getTtsSegments();
        String childContents = appendTtsSegments(ttsSegment.getText(), childSegments, defVoiceId, defVoiceSpeed);
        builder.append(childContents);
        return UUID.nameUUIDFromBytes(builder.toString().getBytes(StandardCharsets.UTF_8)).toString().trim();
    }

    public static String audioCacheKey(String text, TtsSegment ttsSegment) {
        StringBuilder builder = new StringBuilder("cacheableTts:")
                .append(StringUtils.defaultString(ttsSegment.getVoiceId(), "default")).append("\n")
                .append(null == ttsSegment.getVoiceSpeed() ? 1 : ttsSegment.getVoiceSpeed()).append("\n")
                .append(text);
        Object tone = MapUtils.getObject(ttsSegment.getPronunciationDict(), "tone");
        if (!CollectionUtils.sizeIsEmpty(tone)) {
            builder.append(JsonUtils.toJson(tone));
        }
        return UUID.nameUUIDFromBytes(builder.toString().getBytes(StandardCharsets.UTF_8)).toString().trim();
    }

    public static String audioCacheKey(CoursewareModel coursewareModel) {
        StringBuilder builder = new StringBuilder("arkit:")
                .append(StringUtils.defaultString(coursewareModel.getResourceId(), "default")).append("\n")
                .append(CollectionUtils.isEmpty(coursewareModel.getCharacterSegment()) ? "default" :coursewareModel.getCharacterSegment().get(0).getResourceId());
        return UUID.nameUUIDFromBytes(builder.toString().getBytes(StandardCharsets.UTF_8)).toString().trim();
    }

    public static List<TtsSegment> splitSegment(TtsSegment ttsSegment) {
        if (CollectionUtils.isEmpty(ttsSegment.getTtsSegments())) {
            return Lists.newArrayList(ttsSegment);
        } else {
            List<ChildTtsSegment> childSegments = Lists.newArrayList();
            for (ChildTtsSegment childSegment : ttsSegment.getTtsSegments()) {
                int idx = StringUtils.indexOf(ttsSegment.getText(), childSegment.getText());
                childSegment.setIndex(idx);
                if (StringUtils.isEmpty(childSegment.getVoiceId())) {
                    childSegment.setVoiceId(ttsSegment.getVoiceId());
                }
                childSegments.add(childSegment);
            }
            log.info("childSegments before valid size: {}", childSegments.size());
            childSegments.removeIf(childSegment -> childSegment.getIndex() == -1);
            log.info("childSegments after valid size: {}", childSegments.size());
            childSegments.sort(Comparator.comparingInt(ChildTtsSegment::getIndex));

            List<TtsSegment> segments = Lists.newArrayList();
            int startIndex = 0;
            TtsSegment latestSegment = null;
            for (ChildTtsSegment childSegment : childSegments) {
                // 批注前的子段
                if (childSegment.getIndex() > startIndex) {
                    String text = StringUtils.substring(ttsSegment.getText(), startIndex, childSegment.getIndex());
                    if (StringUtils.isBlank(text) || INVALID_CHARS.matcher(text).matches()) {
                        if (null != latestSegment) {
                            latestSegment.setText(latestSegment.getText() + text);
                        }
                    } else {
                        TtsSegment segment = new TtsSegment();
                        segment.setText(text);
                        segment.setVoiceId(ttsSegment.getVoiceId());
                        segment.setVoiceSpeed(ttsSegment.getVoiceSpeed());
                        segment.setPronunciationDict(ttsSegment.getPronunciationDict());
                        segments.add(segment);
                    }
                }
                // 批注的子段
                TtsSegment segment = new TtsSegment();
                segment.setText(childSegment.getText());
                segment.setVoiceId(childSegment.getVoiceId());
                segment.setVoiceSpeed(childSegment.getVoiceSpeed());
                segment.setPronunciationDict(childSegment.getPronunciationDict());
                segment.setEmotion(childSegment.getEmotion());
                segments.add(segment);
                startIndex = childSegment.getIndex() + childSegment.getText().length();
                latestSegment = segment;
                // 最后一个子段
                if (childSegments.indexOf(childSegment) == childSegments.size() - 1) {
                    String text = StringUtils.substring(ttsSegment.getText(), startIndex);
                    if (StringUtils.isBlank(text) || INVALID_CHARS.matcher(text).matches()) {
                        latestSegment.setText(latestSegment.getText() + text);
                        break;
                    }
                    segment = new TtsSegment();
                    segment.setText(text);
                    segment.setVoiceId(ttsSegment.getVoiceId());
                    segment.setVoiceSpeed(ttsSegment.getVoiceSpeed());
                    segment.setPronunciationDict(ttsSegment.getPronunciationDict());
                    segments.add(segment);
                }
            }

            return segments;
        }
    }
}
