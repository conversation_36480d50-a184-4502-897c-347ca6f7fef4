package com.nd.aic.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.nd.aic.enums.CameraAngleEnum;
import com.nd.aic.pojo.dto.CameraResourceDTO;
import com.nd.aic.pojo.segment.CameraFeatureSegment;
import com.nd.aic.pojo.segment.CameraSegment;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public final class CameraUtil {

    private final static Map<String, Integer> CAMERA_SIZE_VALUE_MAP = Maps.newHashMap();

    private final static Map<String, Integer[]> CAMERA_SIZE_NEXT = Maps.newHashMap();

    private final static Map<String, Integer[]> BOARD_CAMERA_SIZE_NEXT = Maps.newHashMap();

    static {
        CAMERA_SIZE_VALUE_MAP.put("大远景", 0);
        CAMERA_SIZE_VALUE_MAP.put("远景", 0);
        CAMERA_SIZE_VALUE_MAP.put("大全景", 1);
        CAMERA_SIZE_VALUE_MAP.put("全景", 1);
        CAMERA_SIZE_VALUE_MAP.put("中景", 2);
        CAMERA_SIZE_VALUE_MAP.put("中近景", 2);
        CAMERA_SIZE_VALUE_MAP.put("近景", 3);
        CAMERA_SIZE_VALUE_MAP.put("特写", 4);
        CAMERA_SIZE_VALUE_MAP.put("全屏", 5);

        CAMERA_SIZE_NEXT.put("大远景", new Integer[]{1, 2, 3, 4});
        CAMERA_SIZE_NEXT.put("远景", new Integer[]{1, 2, 3, 4});
        CAMERA_SIZE_NEXT.put("大全景", new Integer[]{0, 2, 3, 4});
        CAMERA_SIZE_NEXT.put("全景", new Integer[]{0, 2, 3, 4});
        CAMERA_SIZE_NEXT.put("中景", new Integer[]{0, 1, 2});
        CAMERA_SIZE_NEXT.put("中近景", new Integer[]{0, 1, 2});
        CAMERA_SIZE_NEXT.put("近景", new Integer[]{0, 1});
        CAMERA_SIZE_NEXT.put("特写", new Integer[]{0, 1});

        BOARD_CAMERA_SIZE_NEXT.put("大远景", new Integer[]{1, 2});
        BOARD_CAMERA_SIZE_NEXT.put("远景", new Integer[]{0, 1, 2, 3, 4});
        BOARD_CAMERA_SIZE_NEXT.put("大全景", new Integer[]{0});
        BOARD_CAMERA_SIZE_NEXT.put("全景", new Integer[]{0});
        BOARD_CAMERA_SIZE_NEXT.put("中景", new Integer[]{0});
        BOARD_CAMERA_SIZE_NEXT.put("中近景", new Integer[]{0});
        BOARD_CAMERA_SIZE_NEXT.put("近景", new Integer[]{0, 1});
        BOARD_CAMERA_SIZE_NEXT.put("特写", new Integer[]{0, 1});
    }

    @SuppressWarnings("AlibabaMethodTooLong")
    public static List<Long> allocateCuts(long startTime, long endTime, int minDuration, int maxDuration, List<Long> dialogueTimes) {
        List<Long> cuts = new ArrayList<>();
        // 无效时间区间
        if (startTime >= endTime) {
            return cuts;
        }

        List<Long> sortedDialogues = new ArrayList<>(dialogueTimes);
        Collections.sort(sortedDialogues);
        // 过滤掉不在时间区间内的对白时间点
        List<Long> validDialogues = new ArrayList<>();
        for (long time : sortedDialogues) {
            if (time > startTime && time < endTime) {
                validDialogues.add(time);
            }
        }

        int n = validDialogues.size();
        // 当前处理到的对白时间点索引
        int i = 0;
        long currentStart = startTime;

        while (currentStart < endTime) {
            // 最大切镜时间
            long maxPossibleEnd = currentStart + maxDuration;
            // 最小切镜时间
            long minPossibleEnd = currentStart + minDuration;

            // 寻找下一个合适的对白时间点
            int j = i;
            long bestCut = -1;
            while (j < n && validDialogues.get(j) <= maxPossibleEnd) {
                if (validDialogues.get(j) >= minPossibleEnd) {
                    bestCut = validDialogues.get(j);
                }
                j++;
            }

            if (bestCut != -1) {
                // 是否是最后一个
                boolean isLast = bestCut >= endTime || (endTime - bestCut) < minDuration;
                bestCut = isLast ? endTime : bestCut;
                // 找到符合条件的对白时间点
                cuts.add(bestCut);
                currentStart = bestCut;
                // 移动到下一个未处理的对白时间点
                while (i < n && validDialogues.get(i) <= bestCut) {
                    i++;
                }
            } else {
                // 没有符合条件的对白时间点
                if (i < n) {
                    long nextDialogue = validDialogues.get(i);
                    if (nextDialogue > maxPossibleEnd) {
                        // 对白时间点超出最大时间，切割到最大时间
                        long cut = Math.min(currentStart + (minDuration + RandomUtils.nextInt(0, maxDuration - minDuration + 1)), endTime);
                        if (cut > currentStart) {
                            cuts.add(cut);
                            currentStart = cut;
                        }
                    } else {
                        // 合并对白时间点直到满足最小时间或超过最大时间
                        long mergedEnd = nextDialogue;
                        long mergedDuration = mergedEnd - currentStart;
                        int k = i;
                        while (k < n && mergedDuration < minDuration) {
                            k++;
                            if (k < n) {
                                long nextSegment = validDialogues.get(k) - mergedEnd;
                                if (mergedDuration + nextSegment > maxDuration) {
                                    break;
                                }
                                mergedDuration += nextSegment;
                                mergedEnd = validDialogues.get(k);
                            }
                        }
                        if (mergedDuration >= minDuration) {
                            // 是否是最后一个
                            boolean isLast = mergedEnd >= endTime || (endTime - mergedEnd) < minDuration;
                            mergedEnd = isLast ? endTime : mergedEnd;
                            cuts.add(mergedEnd);
                            currentStart = mergedEnd;
                            i = k + 1;
                        } else {
                            // 合并后仍不足，切割到最小时间
                            long cut = Math.min(currentStart + minDuration, endTime);
                            // 是否是最后一个
                            boolean isLast = endTime - cut < minDuration;
                            cut = isLast ? endTime : cut;
                            if (cut > currentStart) {
                                cuts.add(cut);
                                currentStart = cut;
                            }
                        }
                    }
                } else {
                    // 无更多对白时间点，处理剩余时间
                    long remaining = endTime - currentStart;
                    if (remaining <= 0) {
                        break;
                    }
                    long cut = currentStart + (minDuration + RandomUtils.nextInt(0, maxDuration - minDuration + 1));
                    // 是否是最后一个
                    boolean isLast = cut >= endTime || (endTime - cut) < minDuration;
                    cut = isLast ? endTime : cut;
                    if (cut - currentStart >= minDuration || remaining < minDuration) {
                        cuts.add(cut);
                        currentStart = cut;
                    }
                }
            }
        }

        return cuts;
    }

    /**
     * 生成镜头
     *
     * @param startTime     开始时间（毫秒）
     * @param endTime       结束时间（毫秒）
     * @param cameraResList 镜头资源列表
     * @param dialogueTimes 对白时间点列表
     */
    public static List<CameraSegment> genCameraSegments(long startTime, long endTime, boolean board, int minDuration, int maxDuration, List<CameraResourceDTO> cameraResList, List<Long> dialogueTimes, List<CameraSegment> globalCameraSegments, boolean isOpening, boolean isEnding) {
        List<CameraSegment> cameraSegments = Lists.newArrayList();
        if (CollectionUtils.isEmpty(cameraResList)) {
            return cameraSegments;
        }
        // 按时间排序
        SegmentUtil.sortByTime(globalCameraSegments);
        CameraResourceDTO prevCameraRes = null;
        CameraResourceDTO prevPrevCameraRes = null;
        // 获取上一个和上上个镜头资源
        if (CollectionUtils.isNotEmpty(globalCameraSegments)) {
            CameraSegment lastCameraSegment = globalCameraSegments.get(globalCameraSegments.size() - 1);
            prevCameraRes = cameraResList.stream()
                    .filter(item -> StringUtils.equals(item.getId(), lastCameraSegment.getResourceId()))
                    .findFirst()
                    .orElse(null);
            if (globalCameraSegments.size() > 1) {
                CameraSegment prevLastCameraSegment = globalCameraSegments.get(globalCameraSegments.size() - 2);
                prevPrevCameraRes = cameraResList.stream()
                        .filter(item -> StringUtils.equals(item.getId(), prevLastCameraSegment.getResourceId()))
                        .findFirst()
                        .orElse(null);
            }
        }

        List<Long> cuts = allocateCuts(startTime, endTime, minDuration, maxDuration, dialogueTimes);
        if (CollectionUtils.isEmpty(cuts)) {
            return cameraSegments;
        }
        long start = startTime;
        for (int i = 0; i < cuts.size(); i++) {
            Long cut = cuts.get(i);
            CameraSegment cameraSegment = new CameraSegment();
            cameraSegment.time(start);
            cameraSegment.endTime(cut);
            CameraResourceDTO selectedCameraRes = selectCamera(cameraResList, prevCameraRes, prevPrevCameraRes, board, i == 0, isOpening && i == 0, isEnding && i == cuts.size() - 1);
            cameraSegment.setResourceId(selectedCameraRes.getId());
            List<String> reasons = Lists.newArrayList(selectedCameraRes.getShotSize(), selectedCameraRes.getMoveType(), selectedCameraRes.getPan());
            if (BooleanUtils.isTrue(selectedCameraRes.getOpening())) {
                reasons.add("开场");
            }
            cameraSegment.type(selectedCameraRes.getName()).reason(StringUtils.join(reasons, "---"));

            cameraSegment.setPan(selectedCameraRes.getPan());
            cameraSegment.setShotSize(selectedCameraRes.getShotSize());
            cameraSegment.playType(1).skillType("CharacterCamera");

            start = cut;
            cameraSegments.add(cameraSegment);
            prevPrevCameraRes = prevCameraRes;
            prevCameraRes = selectedCameraRes;
        }
        return cameraSegments;
    }

    /**
     * 选择镜头
     *
     * @param cameraResList 镜头资源列表
     * @param prevCameraRes 上一个镜头
     */
    @SuppressWarnings("AlibabaMethodTooLong")
    private static CameraResourceDTO selectCamera(List<CameraResourceDTO> cameraResList, CameraResourceDTO prevCameraRes, CameraResourceDTO prevPrevCameraRes, boolean board, boolean first, boolean isOpening, boolean isEnding) {
        Map<String, Integer> scoreMap = new HashMap<>(16);
        Map<String, Integer[]> nextSizeMap = board ? BOARD_CAMERA_SIZE_NEXT : CAMERA_SIZE_NEXT;
        for (CameraResourceDTO resourceDTO : cameraResList) {
            // 分值计算
            int score = 0;
            // 避免线性单调变化
            int dir = 0;
            if (prevCameraRes != null && prevPrevCameraRes != null) {
                Integer prevSizeValue = CAMERA_SIZE_VALUE_MAP.get(prevCameraRes.getShotSize());
                Integer prevPrevSizeValue = CAMERA_SIZE_VALUE_MAP.get(prevPrevCameraRes.getShotSize());
                if (prevSizeValue != null && prevPrevSizeValue != null) {
                    dir = prevSizeValue - prevPrevSizeValue;
                }
            }

            // 开场镜头
            if (BooleanUtils.isTrue(resourceDTO.getOpening())) {
                if (isOpening) {
                    score += 1000;
                } else {
                    score -= 1000;
                }
            }
            // 结尾镜头
            else if (BooleanUtils.isTrue(resourceDTO.getEnding())) {
                if (isEnding) {
                    score += 1000;
                } else {
                    score -= 1000;
                }
            }

            // 如果是板书讲解的第一个镜头
            if (board && first) {
                Integer sizeValue = CAMERA_SIZE_VALUE_MAP.get(resourceDTO.getShotSize());
                if (sizeValue != null && sizeValue == 0) {
                    score += 500;
                }
            }

            if (prevCameraRes != null) {
                // 相同镜头互斥
                if (StringUtils.equals(resourceDTO.getId(), prevCameraRes.getId())) {
                    score -= 10000;
                }

                if (prevPrevCameraRes != null && StringUtils.equals(resourceDTO.getId(), prevPrevCameraRes.getId())) {
                    score -= 150;
                }

                Integer sizeValue = CAMERA_SIZE_VALUE_MAP.get(resourceDTO.getShotSize());
                Integer prevSizeValue = CAMERA_SIZE_VALUE_MAP.get(prevCameraRes.getShotSize());

                // 在规定的景别中
                Integer[] bestSize = nextSizeMap.get(prevCameraRes.getShotSize());
                if (ArrayUtils.contains(bestSize, sizeValue)) {
                    score += 200;
                }

                // 相同景别互斥
                if (sizeValue != null && sizeValue.equals(prevSizeValue)) {
                    score -= 100;
                }

                // 相同运镜互斥
                if (StringUtils.isNotBlank(resourceDTO.getMoveType()) && StringUtils.isNotBlank(prevCameraRes.getMoveType()) && StringUtils.equals(resourceDTO.getMoveType(), prevCameraRes.getMoveType())) {
                    score -= 100;
                }

                if (dir > 0) {
                    // 镜头变大时，不允许变小
                    if (sizeValue != null && prevSizeValue != null && sizeValue > prevSizeValue) {
                        score -= 10;
                    }
                } else if (dir < 0) {
                    // 镜头变小时，不允许变大
                    if (sizeValue != null && prevSizeValue != null && sizeValue < prevSizeValue) {
                        score -= 10;
                    }
                }
            }
            scoreMap.put(resourceDTO.getId(), score);
        }

        // 最高分值的摄像机
        List<String> maxScoreCameraIds = new ArrayList<>();
        if (!scoreMap.isEmpty()) {
            int maxScore = Collections.max(scoreMap.values());
            for (Map.Entry<String, Integer> entry : scoreMap.entrySet()) {
                if (entry.getValue() == maxScore) {
                    maxScoreCameraIds.add(entry.getKey());
                }
            }
        }

        List<CameraResourceDTO> filteredCameraResList = cameraResList.stream()
                .filter(obj -> maxScoreCameraIds.contains(obj.getId()))
                .collect(Collectors.toList());
        return RandomResUtil.selectRandomResByWeight(filteredCameraResList, CameraResourceDTO::getWeight);
    }

    /**
     * 创建看向
     */
    public static List<CameraFeatureSegment> createLookAtFeature(List<CameraSegment> cameraSegments) {
        List<CameraFeatureSegment> cameraFeatureSegments = new ArrayList<>();
        if (CollectionUtils.isEmpty(cameraSegments)) {
            return cameraFeatureSegments;
        }
        for (CameraSegment cameraSegment : cameraSegments) {
            if (BooleanUtils.isFalse(cameraSegment.getLookAt())) {
                continue;
            }
            if (!BooleanUtils.isTrue(cameraSegment.getLookAt()) && !StringUtils.equalsIgnoreCase(cameraSegment.getPan(), CameraAngleEnum.FRONT.getName())) {
                continue;
            }
            CameraFeatureSegment cameraFeatureSegment = SegmentUtil.createLookAtCameraFeatureSegment(cameraSegment.getTime(), cameraSegment.getEndTime());
            cameraFeatureSegments.add(cameraFeatureSegment);
        }
        return cameraFeatureSegments;
    }

    public static void main(String[] args) {
        List<Long> dialogueTimes = Lists.newArrayList(7000L, 12000L, 23000L, 45000L, 50000L, 59000L, 118000L);
        List<Long> cuts = CameraUtil.allocateCuts(0, 120000, 5000, 10000, dialogueTimes);
        System.out.println("切镜时间点: " + cuts);
    }

}
