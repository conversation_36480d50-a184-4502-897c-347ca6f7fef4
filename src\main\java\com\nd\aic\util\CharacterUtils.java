package com.nd.aic.util;

import com.nd.aic.enums.CharacterEnum;
import com.nd.aic.pojo.CoursewareGenerationContext;

import org.apache.commons.lang3.StringUtils;

public class CharacterUtils {

    public static boolean isWhiteCat(CoursewareGenerationContext context) {
        return StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.WHITE_CAT.getId());
    }

    public static boolean isNeon(CoursewareGenerationContext context) {
        return StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.CHIBI.getId());
    }

    public static boolean isNeonNew(CoursewareGenerationContext context) {
        return StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.NEON.getId())
                || StringUtils.equals(context.getCurrentCharacterResId(), CharacterEnum.NEON2.getId());
    }

    public static boolean isNeonNewOrWhiteCat(CoursewareGenerationContext context) {
        return isWhiteCat(context) || isNeonNew(context);
    }
}
