package com.nd.aic.util;

import com.nd.aic.common.SegmentPerformConstant;
import com.nd.aic.pojo.CoursewareGenerationContext;
import com.nd.aic.pojo.CoursewareModel;
import com.nd.aic.pojo.dto.ScenePositionResourceDTO;
import com.nd.aic.pojo.segment.BirthPointSegment;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public final class CoursewareModelHelper {

    /**
     * 更换点位加转场
     */
    public static void changesScenePosition(String time, ScenePositionResourceDTO scenePositionRes, final CoursewareModel model, final CoursewareGenerationContext context) {
        if (scenePositionRes == null) {
            return;
        }
        BirthPointSegment currentBirthPointSegment = context.getCurrentBirthPoint();
        // 更换的点位和当前点位一致，不再更换
        if (currentBirthPointSegment != null && StringUtils.equals(scenePositionRes.getType(), currentBirthPointSegment.getType())) {
            return;
        }
        BirthPointSegment newBirthPoint = new BirthPointSegment();
        newBirthPoint.time(time)
                .type(scenePositionRes.getType())
                .custom(scenePositionRes.getCustom())
                .setBoardTransform(scenePositionRes.cloneBoardTransform());
        model.getBirthPointSegment().add(newBirthPoint);
        int rnd = RandomUtils.nextInt(0, 2);
        if (rnd > 0) {
            // 转场
            long transitionMinStartMs = 2000;
            if (TimeUtils.convertToMilliseconds(time) > transitionMinStartMs) {
                model.getSceneChangeSegment().add(PerformanceUtil.randomCreateTransitionEffect(time));
            }
        } else {
            // 传送特效
            newBirthPoint.perform(SegmentPerformConstant.BIRTH_POINT_TRANSPORT);
            newBirthPoint.performOption("showEffect", true);
        }
        context.setCurrentBirthPoint(newBirthPoint);
    }

    /**
     * 随机更换点位加转场
     */
    public static void randomChangesScenePosition(String time, boolean boardPoint, final CoursewareModel model, final CoursewareGenerationContext context) {
        List<ScenePositionResourceDTO> birthPointRes = context.getCoursewareResourceConfig().getScenePositionsMap().get(context.getCurrentSceneId());
        if (CollectionUtils.isEmpty(birthPointRes)) {
            return;
        }
        List<ScenePositionResourceDTO> filteredBirthPointRes = birthPointRes.stream()
                .filter(item -> boardPoint == (item.getBoardTransform() != null))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredBirthPointRes)) {
            return;
        }
        ScenePositionResourceDTO birthPoint = filteredBirthPointRes.get(RandomUtils.nextInt(0, filteredBirthPointRes.size()));
        changesScenePosition(time, birthPoint, model, context);
    }
}
