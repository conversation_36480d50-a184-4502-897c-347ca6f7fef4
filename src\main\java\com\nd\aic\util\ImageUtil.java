package com.nd.aic.util;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

public class ImageUtil {


    public static String getImageUrl(String imageId) {

        if(StringUtils.isEmpty(imageId)){
            return "";
        }

        if(UUIDUtil.isUuid(imageId)){
            return "https://gcdncs.101.com/v0.1/download?dentryId=" + imageId;
        }

        return imageId;
    }

    public static String getFileName(String  imageId){
        if(StringUtils.isEmpty(imageId)){
            return UUIDUtil.upperUuid();
        }
        // get file name , file name is the last part of the url, full url is like https://gcdncs.101.com/v0.1/static/edu_product/esp/assets/3310e3c2-0f9b-40ef-a234-65d5009b2d8b.pkg/标题_test_内容_深度挖掘_对配角进行深度挖掘_可以增加故事的厚重感_甚至可以为配角开设支线剧情.jpeg
        String filename = imageId;
        if(imageId.contains("/")){
            filename = imageId.substring(imageId.lastIndexOf("/") + 1);
        }
        try {
            File file = new File(filename);
            // 验证文件名是否合法
            if (file.createNewFile()) {
                file.delete();
            }
        } catch (IOException e) {
            filename = UUID.nameUUIDFromBytes(imageId.getBytes(StandardCharsets.UTF_8)).toString();
        }

        return filename;
    }
}
