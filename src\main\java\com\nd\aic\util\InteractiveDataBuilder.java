package com.nd.aic.util;

import com.nd.aic.base.exception.WafI18NException;
import com.nd.aic.pojo.interactive.InteractiveInput;
import com.nd.aic.pojo.interactive.InteractiveData;

import org.springframework.http.HttpStatus;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.UUID;

import lombok.Getter;

public class InteractiveDataBuilder {

    private static final Map<String, String> BIND_AUDIO_MEDIA_TARGET = new HashMap<String, String>() {{
        put("answerno", "//gcdncs.101.com/v0.1/static/eb_aic/Assets/%E7%AD%94%E9%94%99%E4%BA%86%EF%BC%8C%E7%BB%A7%E7%BB%AD%E5%8A%A0%E6%B2%B9%E5%93%A6.mp3");
        put("answeryes", "//gcdncs.101.com/v0.1/static/eb_aic/Assets/%E7%AD%94%E5%AF%B9%E4%BA%86%EF%BC%8C%E7%9C%9F%E6%A3%92.mp3");
    }};

    @Getter
    public enum InteractiveType {
        TRUE_OR_FALSE("判断题"),
        CHOICES("选择题"),
        QUESTION("问答题"),
        NOT_FOUND("NOT_FOUND"),
        ;

        private final String type;

        InteractiveType(String type) {
            this.type = type;
        }

        public static InteractiveType fromType(String type) {
            return Arrays.stream(InteractiveType.values())
                    .filter(interactiveType -> interactiveType.type.equals(type))
                    .findFirst()
                    .orElse(NOT_FOUND);
        }
    }

    private static final Map<Integer, String> LABEL_CHOICES_MAP = new HashMap<>();
    static {
        LABEL_CHOICES_MAP.put(0, "A");
        LABEL_CHOICES_MAP.put(1, "B");
        LABEL_CHOICES_MAP.put(2, "C");
        LABEL_CHOICES_MAP.put(3, "D");
    }

    private static final Map<Integer, String> LABEL_TOF_MAP = new HashMap<>();
    static {
        LABEL_TOF_MAP.put(0, "correct");
        LABEL_TOF_MAP.put(1, "wrong");
    }

    public static InteractiveData buildInteractiveData(InteractiveInput data) {
        String id = UUID.randomUUID().toString();
        InteractiveType type = InteractiveType.fromType(data.getType());
        InteractiveData newData = type == InteractiveType.QUESTION ? createQaInteractiveData(id, data.getQuestion()) : createDefaultInteractiveData(id, data.getQuestion());

        newData.setId(UUID.randomUUID().toString());
        newData.getActiveEvents().get(0).setId(UUID.randomUUID().toString());
        newData.getActiveEvents().get(0).getCustomArgs().setPrompt(data.getQuestion());
        newData.getActiveEvents().get(0).setTimeAnchor(data.getTriggerTime() / 1000.0);

        if (type == InteractiveType.QUESTION) {
            newData.setInteractiveType("empty");
            return newData;
        } else if (type == InteractiveType.CHOICES || type == InteractiveType.TRUE_OR_FALSE) {
            newData.getResources().get(0).setIdentifier(id);

            boolean isChoices = type == InteractiveType.CHOICES;
            List<InteractiveData.ResourceDataChoice> choices = IntStream.range(0, data.getOptions().size())
                    .mapToObj(index -> {
                        InteractiveData.ResourceDataChoice choice = new InteractiveData.ResourceDataChoice();
                        choice.setCorrect(data.getOptions().get(index).isCorrect());
                        choice.setIdentifier(UUID.randomUUID().toString());
                        choice.setImage(null);
                        choice.setLabel(isChoices ? LABEL_CHOICES_MAP.get(index) : LABEL_TOF_MAP.get(index));
                        choice.setText(data.getOptions().get(index).getText());
                        return choice;
                    })
                    .collect(Collectors.toList());

            newData.getResources().get(0).getData().setChoices(choices);

            if (isChoices) {
                newData.getActiveEvents().get(0).getCustomArgs().setQtiCode("$RE0202");
                newData.getResources().get(0).setQuestionCode("$RE0202");
                InteractiveData.ChoicesLabel choicesLabel = new InteractiveData.ChoicesLabel();
                choicesLabel.setId("letter");
                choicesLabel.setPatternText("A B C D");
                newData.getResources().get(0).getCustomProperties().setChoicesLabel(choicesLabel);
            }
            return newData;
        }
        throw  WafI18NException.of("INTERACTIVE_NOT_SUPPORT", "互动类型错误", HttpStatus.NOT_IMPLEMENTED);
    }

    private static InteractiveData createDefaultInteractiveData(String id, String prompt) {
        InteractiveData interactive = new InteractiveData();
        interactive.setActiveEvents(Collections.singletonList(createDefaultInteractiveEvent(id, prompt)));
        interactive.setResources(Collections.singletonList(createDefaultInteractiveResource(id)));
        return interactive;
    }

    private static InteractiveData createQaInteractiveData(String id, String prompt) {
        InteractiveData interactive = new InteractiveData();
        InteractiveData.ActiveEvent event = createDefaultInteractiveEvent(id, prompt);
        event.setType("voice");
        event.setInteractiveType("empty");
        Map<String, List<InteractiveData.ActiveEventAction>> backActions = new HashMap<>();
        backActions.put("default", Collections.singletonList(new InteractiveData.ActiveEventAction("play", "", null)));
        event.setBackActions(backActions);
        // custom args
        InteractiveData.ActiveEventCustomArgs customArgs = new InteractiveData.ActiveEventCustomArgs();
        customArgs.setCoreType("NONE");
        customArgs.setPrompt("12312312");
        customArgs.setVoiceType("qa");
        event.setCustomArgs(customArgs);
        interactive.setActiveEvents(Collections.singletonList(event));
        return interactive;
    }

    // 生成默认的互动数据（选择题/判断题）
    private static InteractiveData.ActiveEvent createDefaultInteractiveEvent(String id, String prompt) {
        InteractiveData.ActiveEvent event = new InteractiveData.ActiveEvent();
        // 视频ndrid
        event.setMediaTarget("");
        // 插入时间点
        event.setTimeAnchor(0);
        // ID
        event.setId(id);
        // back_actions
        Map<String, List<InteractiveData.ActiveEventAction>> backActions = new HashMap<>();
        backActions.put("true", Collections.singletonList(new InteractiveData.ActiveEventAction("play", "", null)));
        backActions.put("false", Collections.singletonList(new InteractiveData.ActiveEventAction("play", "", null)));
        event.setBackActions(backActions);
        // type
        event.setType("qti");
        // custom args
        InteractiveData.ActiveEventCustomArgs customArgs = new InteractiveData.ActiveEventCustomArgs();
        customArgs.setNeedTimeout(true);
        customArgs.setPrompt(prompt);
        customArgs.setId(id);
        customArgs.setIsRecycle(false);
        customArgs.setQtiType("custom");
        customArgs.setQtiCode("$RE0203");
        customArgs.setAnsweryes(Collections.singletonList(new InteractiveData.Answer("$id1", BIND_AUDIO_MEDIA_TARGET.get("answeryes"))));
        customArgs.setAnswerno(Collections.singletonList(new InteractiveData.Answer("$id1", BIND_AUDIO_MEDIA_TARGET.get("answerno"))));
        customArgs.getAnsweryes().get(0).setId(id);
        customArgs.getAnswerno().get(0).setId(id);
        event.setCustomArgs(customArgs);
        // 前端替换， 问答empty 其他'track'
        event.setInteractiveType("track");
        // 互动时长 默认给30
        event.setTimeLimit(30);

        return event;
    }

    private static InteractiveData.Resource createDefaultInteractiveResource(String id) {
        InteractiveData.Resource resource = new InteractiveData.Resource();
        resource.setIdentifier(id);
        resource.setType("question");
        resource.setQuestionCode("$RE0203");
        resource.setData(new InteractiveData.ResourceData());
        // customProperties 默认一个空的choice_labels
        InteractiveData.ResourceCustomArgs customProperties = new InteractiveData.ResourceCustomArgs();
        resource.setCustomProperties(customProperties);
        return resource;
    }

}