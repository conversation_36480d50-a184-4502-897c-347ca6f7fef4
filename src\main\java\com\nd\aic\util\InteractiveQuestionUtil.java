package com.nd.aic.util;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.entity.VideoInteraction;
import com.nd.aic.pojo.dto.InteractionDTO;
import com.nd.gaea.util.WafJsonMapper;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import lombok.SneakyThrows;

public class InteractiveQuestionUtil {

    @SneakyThrows
    public static VideoInteraction buildVideoInteraction(List<InteractionDTO> interactions) {
        if (CollectionUtils.isEmpty(interactions)) {
            return null;
        }
        VideoInteraction videoInteraction = new VideoInteraction();
        List<VideoInteraction.ActiveEvent> activeEvents = new ArrayList<>();
        List<JSONObject> resources = new ArrayList<>();
        for (InteractionDTO interaction : interactions) {
            if (StringUtils.isBlank(interaction.getQuestion()) || StringUtils.isBlank(interaction.getType()) && interaction.getTriggerTime() == null) {
                continue;
            }
            String eventIdBuilder = interaction.getQuestion() + interaction.getType() + WafJsonMapper.toJson(interaction.getOptions()) + interaction.getNeedSpeak();
            String eventId = UUID.nameUUIDFromBytes(eventIdBuilder.getBytes(StandardCharsets.UTF_8)).toString();

            String questionId = UUID.randomUUID().toString();
            VideoInteraction.ActiveEvent activeEvent = buildActiveEvent(eventId, questionId, interaction);
            if (activeEvent != null) {
                activeEvents.add(activeEvent);
            }
            JSONObject resource = buildResource(questionId, interaction);
            if (resource != null) {
                resources.add(resource);
            }
        }

        videoInteraction.setActiveEvents(activeEvents);
        videoInteraction.setResources(resources);
        return videoInteraction;
    }

    private static VideoInteraction.ActiveEvent buildActiveEvent(String eventId, String questionId, InteractionDTO interaction) {
        String questionType = interaction.getType();
        if (StringUtils.contains(questionType, "选择") || StringUtils.contains(questionType, "判断")) {
            return buildChoiceOrTrueFalseActiveEvent(eventId, questionId, questionType, interaction);
        } else if (StringUtils.contains(questionType, "问答")) {
            return buildAnswerActiveEvent(eventId, interaction);
        }
        return null;
    }

    private static JSONObject buildResource(String questionId, InteractionDTO interaction) {
        String questionType = interaction.getType();
        if (StringUtils.contains(questionType, "选择") || StringUtils.contains(questionType, "判断")) {
            return buildChoiceOrTrueFalseResource(questionId, questionType, interaction);
        }
        return null;
    }

    private static VideoInteraction.ActiveEvent buildChoiceOrTrueFalseActiveEvent(String eventId, String questionId, String questionType, InteractionDTO interaction) {
        List<InteractionDTO.Option> options = interaction.getOptions();
        if (CollectionUtils.isEmpty(options)) {
            return null;
        }
        VideoInteraction.ActiveEvent activeEvent = new VideoInteraction.ActiveEvent();
        activeEvent.setId(eventId);
        activeEvent.setTimeAnchor(interaction.getTriggerTime() / 1000.0);
        activeEvent.setType("qti");
        activeEvent.setTimeLimit(30);
        activeEvent.setInteractiveType("track");
        JSONObject backActions = new JSONObject(true);
        backActions.put("true", Lists.newArrayList(new JSONObject().fluentPut("type", "play")));
        backActions.put("false", Lists.newArrayList(new JSONObject().fluentPut("type", "play")));
        activeEvent.setter("back_actions", backActions);
        JSONObject customArgs = new JSONObject(true).fluentPut("id", questionId)
                .fluentPut("needTimeout", true)
                .fluentPut("needSpeak", interaction.getNeedSpeak())
                .fluentPut("speakUrl", interaction.getSpeakUrl())
                .fluentPut("speakDelay", interaction.getSpeakDelay())
                .fluentPut("is_recycle", false)
                .fluentPut("prompt", interaction.getQuestion())
                .fluentPut("qti_type", "custom")
                .fluentPut("qti_code", StringUtils.contains(questionType, "选择") ? "$RE0202" : "$RE0203")
                .fluentPut("answeryes", Lists.newArrayList(new JSONObject(true).fluentPut("id", questionId)
                        .fluentPut("bind_audio_media_target", "//gcdncs.101.com/v0.1/static/eb_aic/Assets/%E7%AD%94%E5%AF%B9%E4%BA%86%EF%BC%8C%E7%9C%9F%E6%A3%92.mp3")))
                .fluentPut("answerno", Lists.newArrayList(new JSONObject(true).fluentPut("id", questionId)
                        .fluentPut("bind_audio_media_target", "//gcdncs.101.com/v0.1/static/eb_aic/Assets/%E7%AD%94%E9%94%99%E4%BA%86%EF%BC%8C%E7%BB%A7%E7%BB%AD%E5%8A%A0%E6%B2%B9%E5%93%A6.mp3")));
        activeEvent.setCustomArgs(customArgs);
        return activeEvent;
    }

    private static VideoInteraction.ActiveEvent buildAnswerActiveEvent(String eventId, InteractionDTO interaction) {
        VideoInteraction.ActiveEvent activeEvent = new VideoInteraction.ActiveEvent();
        activeEvent.setId(eventId);
        activeEvent.setTimeAnchor(interaction.getTriggerTime() / 1000.0);
        activeEvent.setType("voice");
        activeEvent.setTimeLimit(30);
        activeEvent.setInteractiveType("empty");
        JSONObject backActions = new JSONObject(true);
        backActions.put("default", Lists.newArrayList(new JSONObject().fluentPut("type", "play")));
        activeEvent.setter("back_actions", backActions);
        JSONObject customArgs = new JSONObject(true).fluentPut("core_type", "NONE")
                .fluentPut("prompt", interaction.getQuestion())
                .fluentPut("voice_type", "qa");
        activeEvent.setCustomArgs(customArgs);
        return activeEvent;
    }

    private static JSONObject buildChoiceOrTrueFalseResource(String questionId, String questionType, InteractionDTO interaction) {
        List<InteractionDTO.Option> options = interaction.getOptions();
        if (CollectionUtils.isEmpty(options)) {
            return null;
        }
        JSONObject resource = new JSONObject(true);
        resource.fluentPut("identifier", questionId)
                .fluentPut("type", "question")
                .fluentPut("question_code", StringUtils.contains(questionType, "选择") ? "$RE0202" : "$RE0203");
        List<JSONObject> choices = new ArrayList<>();
        String[] labels;
        if (StringUtils.contains(questionType, "判断")) {
            labels = new String[]{"correct", "wrong"};
            if (options.size() != 2) {
                return null;
            }
        } else {
            labels = new String[]{"A", "B", "C", "D"};
            if (options.size() != 4) {
                return null;
            }
        }
        for (int i = 0; i < options.size(); i++) {
            InteractionDTO.Option option = options.get(i);
            choices.add(new JSONObject(true).fluentPut("identifier", UUID.randomUUID().toString())
                    .fluentPut("label", labels[i])
                    .fluentPut("text", option.getText())
                    .fluentPut("correct", option.getCorrect()));
        }
        resource.fluentPut("data", new JSONObject().fluentPut("choices", choices));
        return resource;
    }

}
