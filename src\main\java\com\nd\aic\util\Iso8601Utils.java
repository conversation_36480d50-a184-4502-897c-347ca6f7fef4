package com.nd.aic.util;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Iso8601Utils {

    private static final Pattern DURATION_PATTERN = Pattern.compile("^DURATION:(\\d+(\\.\\d+)?)$");

    // 将毫秒转换为ISO 8601时间间隔格式（只有时间部分）
    public static String millisecondsToIso8601(long milliseconds) {
        Duration duration = Duration.ofMillis(milliseconds);
        // 使用Duration的toString()方法，它符合ISO8601格式
        String iso8601 = duration.toString();

        // 处理只有分钟和秒的情况，去掉小时部分
        if (milliseconds < 3600000) {
            iso8601 = iso8601.replaceAll("PT(\\d+)H", ""); // 去掉小时部分
        }

        // 特殊处理0分钟0秒的情况为PT0S
        if (iso8601.equals("PT0S")) {
            return "PT0S";
        }

        return iso8601;
    }

    // 将ISO 8601时间间隔格式转换为毫秒（只有时间部分）
    public static long iso8601ToMilliseconds(String iso8601) {
        Duration duration = Duration.parse(iso8601);
        return duration.toMillis();
    }

    public static Long parseResourceDuration(List<String> labels, String optSecond) {
        if (CollectionUtils.isEmpty(labels)) {
            return null;
        }
        for (String label : labels) {
            Matcher matcher = DURATION_PATTERN.matcher(label);
            if (matcher.matches()) {
                // 提取出数字部分
                String duration = matcher.group(1);
                return Float.valueOf(duration).longValue();
            }
        }
        if (StringUtils.isNotBlank(optSecond)) {
            return iso8601ToMilliseconds(optSecond);
        }
        return null;
    }

    public static void main(String[] args) {
        long milliseconds = 65051L; // 1分钟5秒
        String iso8601 = millisecondsToIso8601(milliseconds);
        System.out.println("Milliseconds: " + milliseconds + " -> ISO 8601: " + iso8601);

        long parsedMilliseconds = iso8601ToMilliseconds(iso8601);
        System.out.println("ISO 8601: " + iso8601 + " -> Milliseconds: " + parsedMilliseconds);
    }
}
