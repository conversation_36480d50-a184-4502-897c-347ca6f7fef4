package com.nd.aic.util;

import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.introspect.BeanPropertyDefinition;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.nd.aic.entity.flow.OriginalRequirement;
import com.nd.gaea.util.WafJsonMapper;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.SneakyThrows;

public class JsonUtils {

    @SneakyThrows
    public static <T> List<T> parseList(String json, Class<T> clazz) {
        CollectionType type = WafJsonMapper.getMapper().getTypeFactory().constructCollectionType(List.class, clazz);
        return WafJsonMapper.getMapper().readValue(json, type);
    }

    @SneakyThrows
    public static <T> T parseObject(Object object, Class<T> clazz) {
        if (object == null) {
            return null;
        }
        String json;
        if (object instanceof String) {
            json = (String) object;
        } else {
            json = WafJsonMapper.toJson(object);
        }
        return WafJsonMapper.parse(json, clazz);
    }

    @SneakyThrows
    public static String toJson(Object object) {
        return WafJsonMapper.toJson(object);
    }

    public static Set<String> getJacksonKeys(Class<?> cls) {
        ObjectMapper mapper = WafJsonMapper.getMapper();
        // SerializationConfig config = mapper.getSerializationConfig();
        BeanDescription beanDesc = mapper.getSerializationConfig()
                .introspect(mapper.getTypeFactory().constructType(cls));

        return beanDesc.findProperties().stream()
                .map(BeanPropertyDefinition::getName)
                .collect(Collectors.toSet());
    }
}
