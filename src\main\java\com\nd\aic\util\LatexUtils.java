package com.nd.aic.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.client.pojo.asr.AtaUtterance;
import com.nd.aic.client.pojo.asr.AtaWord;
import com.nd.aic.client.pojo.asr.LatexContent;
import com.nd.aic.client.pojo.asr.LatexMapping;
import com.nd.aic.common.BizConstant;
import com.nd.aic.common.BizHandler;
import com.nd.aic.service.FastGptRetryService;
import com.nd.gaea.client.ApplicationContextUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class LatexUtils {

    private static final Pattern LATEX_PATTERN = Pattern.compile("\\$\\$(.*?)\\$\\$", Pattern.DOTALL);
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("'`([^`']+?)`'");
    private static final String PLACEHOLDER_PREFIX = "'`";
    private static final String PLACEHOLDER_SUFFIX = "`'";
    private static final String PLACEHOLDER_FORMATER = PLACEHOLDER_PREFIX + "%s" + PLACEHOLDER_SUFFIX;
    private static final String FAST_GPT_KEY = "fastgpt-peIAGTZZohxmYQyc7jXJ9Vy3nKPTbM8uQl9geUlByIqj9AdIaFsMBApKaYQDdv1";

    public static boolean enableLatex() {
        return ApplicationContextUtil.getApplicationContext().getEnvironment().getProperty("ENABLE_LATEX", Boolean.class, false);
    }

    public static LatexContent parseLatexContent(String text) {
        LatexContent latexContent = new LatexContent();
        latexContent.setInput(text);

        List<String> latexList = LatexUtils.findLatex(text);
        if (enableLatex() && StringUtils.equals(BizHandler.getBizFlag(), BizConstant.BIZ_FLAG_VLAB) && CollectionUtils.isNotEmpty(latexList)) {
            List<LatexMapping> latexMappings = LatexUtils.latexPlaceholderMapping(latexList);
            String output = replaceLatexPlaceholder(text, convertLatexMap(latexMappings));
            latexContent.setLatexMappings(latexMappings);
            latexContent.setOutput(output);
        } else {
            latexContent.setOutput(text);
        }

        return latexContent;
    }

    public static Map<String, String> convertLatexMap(List<LatexMapping> latexMappings) {
        if (CollectionUtils.isEmpty(latexMappings)) {
            return null;
        }
        return latexMappings.stream().collect(Collectors.toMap(LatexMapping::getLatex, LatexMapping::getText, (k1, k2) -> k1, java.util.HashMap::new));
    }

    private static List<String> findLatex(String text) {
        List<String> latexList = Lists.newArrayList();
        if (StringUtils.isBlank(text)) {
            return latexList;
        }
        // 匹配latex的正则表达式
        Matcher matcher = LATEX_PATTERN.matcher(text);
        while (matcher.find()) {
            latexList.add(matcher.group());
        }
        return latexList;
    }

    private static List<LatexMapping> latexPlaceholderMapping(List<String> latexList) {
        if (CollectionUtils.isEmpty(latexList)) {
            return Lists.newArrayList();
        }
        FastGptRetryService fastGptRetryService = ApplicationContextUtil.getApplicationContext().getBean(FastGptRetryService.class);
        Function<String, List<LatexMapping>> applyFunction = data -> {
            List<String> placeholders = JsonUtils.parseList(data, String.class);
            if (placeholders.size() != latexList.size()) {
                throw new RuntimeException("Latex数量和占位符数量不一致");
            }
            List<LatexMapping> latexMappings = Lists.newArrayList();
            for (int i = 0; i < placeholders.size(); i++) {
                LatexMapping mapping = new LatexMapping(latexList.get(i), String.format(PLACEHOLDER_FORMATER, placeholders.get(i)));
                latexMappings.add(mapping);
            }
            return latexMappings;
        };
        return fastGptRetryService.fastGptCallForJsonResult(JsonUtils.toJson(latexList), FAST_GPT_KEY, null, applyFunction);
    }

    private static String replaceLatexPlaceholder(String text, Map<String, String> latexMap) {
        if (StringUtils.isBlank(text) || MapUtils.isEmpty(latexMap)) {
            return text;
        }
        for (Map.Entry<String, String> entry : latexMap.entrySet()) {
            text = StringUtils.replace(text, entry.getKey(), entry.getValue());
        }

        return text;
    }

    private static List<String> findLatexPlaceholder(String text) {
        List<String> placeholders = Lists.newArrayList();
        if (StringUtils.isBlank(text)) {
            return placeholders;
        }
        // 匹配latex的正则表达式
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(text);
        while (matcher.find()) {
            placeholders.add(matcher.group());
        }
        return placeholders;
    }

    /**
     * 将朗读内容还原（'`朗读内容`'）为latex公式（$$公式$$）
     * @param text 带`朗读内容`'的内容
     * @param latexMap 占位内容与公式的映射，KEY是公式
     * */
    public static String revertLatexPlaceholder(String text, Map<String, String> latexMap) {
        if (StringUtils.isBlank(text) || MapUtils.isEmpty(latexMap)) {
            return text;
        }
        for (Map.Entry<String, String> entry : latexMap.entrySet()) {
            text = StringUtils.replace(text, entry.getValue(), entry.getKey());
        }

        return text;
    }

    /**
     * 合并句子中分散的分散的word(latex部分)为一个word
     * @param ataSentence 打轴返回的句子
     * */
    public static List<AtaWord> mergeLatexWords(AtaUtterance ataSentence) {
        List<String> placeholders = findLatexPlaceholder(ataSentence.getText());
        if (placeholders.isEmpty()) {
            return ataSentence.getWords();
        }

        List<AtaWord> words = ataSentence.getWords();
        List<AtaWord> result = Lists.newArrayList();

        int wordIndex = 0;
        while (wordIndex < words.size()) {
            boolean matched = false;

            for (String placeholder : placeholders) {
                int matchEnd = tryMatchWords(words, wordIndex, placeholder);
                if (matchEnd != -1) {
                    // 匹配成功：合并 wordIndex 到 matchEnd-1 的 word
                    int endIndex = matchEnd - 1;
                    int startTime = words.get(wordIndex).getStartTime();
                    int endTime = words.get(endIndex).getEndTime();

                    AtaWord merged = new AtaWord();
                    merged.setText(placeholder);
                    merged.setStartTime(startTime);
                    merged.setEndTime(endTime);
                    merged.setMerged(true);
                    result.add(merged);

                    wordIndex = matchEnd;
                    matched = true;
                    break;
                }
            }

            if (!matched) {
                result.add(words.get(wordIndex));
                wordIndex++;
            }
        }

        return result;
    }

    // 尝试从 words[i] 开始是否能拼出 targetString，匹配成功则返回匹配结束位置（不包含）
    private static int tryMatchWords(List<AtaWord> words, int startIndex, String targetString) {
        targetString = StringUtils.replace(targetString, " ", "");
        StringBuilder sb = new StringBuilder();
        for (int j = startIndex; j < words.size(); j++) {
            sb.append(StringUtils.trim(words.get(j).getText()));
            if (!targetString.startsWith(sb.toString())) {
                return -1;
            }
            if (sb.toString().equals(targetString)) {
                return j + 1;
            }
        }
        return -1;
    }
}
