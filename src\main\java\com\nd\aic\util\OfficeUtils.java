package com.nd.aic.util;

import com.google.common.collect.Lists;

import com.nd.aic.base.exception.WafI18NException;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.sl.usermodel.Placeholder;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFNotes;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTextShape;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.http.HttpStatus;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OfficeUtils {

    public static String readWordContent(File file) {
        StringBuilder builder = new StringBuilder();
        try (FileInputStream fis = new FileInputStream(file); XWPFDocument document = new XWPFDocument(fis)) {

            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph para : paragraphs) {
                builder.append(para.getText()).append("\n");
            }
        } catch (IOException e) {
            log.info("Error reading DOCX file: {}", e.getMessage());
            throw WafI18NException.of("BAD_REQUEST", "无法读取Word文档内容", HttpStatus.BAD_REQUEST, e);
        }

        return builder.toString().trim();
    }

    public static List<String> parsePptxNotes(File pptxFile) throws IOException {
        List<String> pptxNotes = Lists.newArrayList();

        InputStream is = Files.newInputStream(pptxFile.toPath());
        XMLSlideShow xmlSlideShow = new XMLSlideShow(is);
        is.close();
        // 获取幻灯片
        List<XSLFSlide> slides = xmlSlideShow.getSlides();
        for (int slideIndex = 0; slideIndex < slides.size(); slideIndex++) {
            StringBuilder builder = new StringBuilder();

            XSLFSlide slide = slides.get(slideIndex);
            XSLFNotes notes = slide.getNotes();
            if (notes != null) {
                List<XSLFShape> shapes = notes.getShapes();
                for (XSLFShape shape : shapes) {
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape textShape = (XSLFTextShape) shape;
                        if (null != textShape.getTextType() && Placeholder.SLIDE_NUMBER.equals(textShape.getTextType())) {
                            continue;
                        }
                        String text = textShape.getText();
                        log.info("Notes: {}  {}", slideIndex, text);
                        if (StringUtils.equals(text, "null")) {
                            text = null;
                        }
                        if (StringUtils.isNotBlank(text)) {
                            builder.append(text.trim()).append("\n");
                        }
                    }
                }
            } else {
                log.info("No notes found for this slide.");
            }
            pptxNotes.add(builder.toString().trim());
        }
        return pptxNotes;
    }

    public static void main(String[] args) {
        File pptxFile = new File("D:\\Users\\admin\\Downloads\\TEST.docx");
        String content = readWordContent(pptxFile);
        System.out.println(content);
    }
}
