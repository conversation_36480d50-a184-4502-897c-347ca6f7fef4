package com.nd.aic.util;

import com.google.common.collect.Lists;
import com.nd.aic.enums.StandPositionEnum;
import java.util.List;

public class PerformanceEnhanceUtils {

    public static List<String> mappingCandidatePosition(String position) {
        // 根据position给出空白区域，屏幕大小为1920*1080，区域以九宫格划分九块；如position=LEFT，则可选的位置是中上、中中、中下、右上、右中、右下六个区域
        switch (position) {
            case "LEFT":
                return Lists.newArrayList("middle_top", "middle_center", "middle_bottom", "right_top", "right_center", "right_bottom");
            case "MIDDLE":
                return Lists.newArrayList("left_top", "left_center", "left_bottom", "right_top", "right_center", "right_bottom");
            case "RIGHT":
                return Lists.newArrayList("left_top", "left_center", "left_bottom", "middle_top", "middle_center", "middle_bottom");
            default:
                // return Lists.newArrayList();
                return Lists.newArrayList("left_center", "middle_center", "right_center");
        }
    }

    public static List<String> mappingCandidatePosition(StandPositionEnum positionEnum) {
        if (null == positionEnum) {
            // 默认位置是中间
            return Lists.newArrayList("left_center", "right_center");
        }
        // 根据positionEnum给出空白区域，屏幕大小为1920*1080，区域以九宫格划分九块；如positionEnum=LEFT，则可选的位置是中上、中中、中下、右上、右中、右下六个区域
        switch (positionEnum) {
            case LEFT:
            case MOVE_LEFT:
                return Lists.newArrayList("middle_top", "middle_center", "middle_bottom", "right_top", "right_center", "right_bottom");
            case MIDDLE:
                return Lists.newArrayList("left_top", "left_center", "left_bottom", "right_top", "right_center", "right_bottom");
            case RIGHT:
            case MOVE_RIGHT:
                return Lists.newArrayList("left_top", "left_center", "left_bottom", "middle_top", "middle_center", "middle_bottom");
            default:
                return Lists.newArrayList("left_center", "right_center");
        }
    }

    public static int[] mappingCoordinate(String position) {
        // 根据positionEnum给出空白区域，屏幕大小为1920*1080，区域以九宫格划分九块；如positionEnum=LEFT，则可选的位置是中上、中中、中下、右上、右中、右下六个区域；坐标取保区域中心点
        switch (position) {
            case "left_top":
                return new int[]{640, 360};
            case "left_center":
                return new int[]{640, 540};
            case "left_bottom":
                return new int[]{640, 720};
            case "middle_top":
                return new int[]{960, 360};
            case "middle_bottom":
                return new int[]{960, 720};
            case "right_top":
                return new int[]{1280, 360};
            case "right_center":
                return new int[]{1280, 540};
            case "right_bottom":
                return new int[]{1280, 720};
            case "middle_center":
            default:
                return new int[]{960, 540};
        }
    }
}
