package com.nd.aic.util;

import com.nd.aic.pojo.CameraConstraint;
import com.nd.aic.pojo.dto.ActionResourceDTO;
import com.nd.aic.pojo.dto.CameraResourceDTO;
import com.nd.aic.pojo.dto.CoursewareResourceDTO;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.BaseSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneChangeSegment;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public final class PerformanceUtil {

    /**
     * 随机镜头
     */
    @Deprecated
    public static List<CameraSegment> randomCreateCameras(long start, long end, int minSplit, int maxSplit, List<CameraResourceDTO> characterCameraRes, boolean isOpening, boolean isEnding, final String lastCameraId, List<CameraConstraint> constraints) {
        List<CameraSegment> cameraSegments = new ArrayList<>();
        if (CollectionUtils.isEmpty(characterCameraRes)) {
            return cameraSegments;
        }
        // 获取上一个镜头资源
        CameraResourceDTO prevCameraRes = characterCameraRes.stream()
                .filter(item -> StringUtils.equals(item.getId(), lastCameraId))
                .findFirst()
                .orElse(null);
        long current = start;
        while (current < end) {
            CameraResourceDTO finalPrevCameraRes = prevCameraRes;
            long split = current + minSplit + RandomUtils.nextInt(0, maxSplit - minSplit + 1);
            boolean isLast = split >= end || (end - split) < minSplit;
            long cameraEnd = isLast ? end : split;
            // 应用镜头约束
            List<CameraResourceDTO> filteredCharacterCameraRes = applyCameraConstraints(current, cameraEnd, characterCameraRes, constraints);
            // 按规则过滤镜头
            filteredCharacterCameraRes = filteredCharacterCameraRes.stream().filter(item -> {
                if (finalPrevCameraRes != null) {
                    // 同镜头互斥
                    if (StringUtils.equals(item.getId(), finalPrevCameraRes.getId())) {
                        return false;
                    }
                    // 同运镜互斥
                    if (StringUtils.isNotBlank(item.getMoveType()) && StringUtils.isNotBlank(finalPrevCameraRes.getMoveType()) && StringUtils.equals(item.getMoveType(), finalPrevCameraRes.getMoveType())) {
                        return false;
                    }
                    // 景别不同直接可用
                    if (StringUtils.isBlank(item.getShotSize()) || StringUtils.isBlank(finalPrevCameraRes.getShotSize()) || !StringUtils.equals(item.getShotSize(), finalPrevCameraRes.getShotSize())) {
                        return StringUtils.isNotBlank(item.getId());
                    }
                    // 景别相同，镜头结束角度和镜头开始角度要不一样
                    // if (StringUtils.isNotBlank(item.getStartAngle()) && StringUtils.isNotBlank(finalPrevCameraRes.getEndAngle()) && StringUtils.equals(item.getStartAngle(), finalPrevCameraRes.getEndAngle())) {
                    //     return false;
                    // }

                    return StringUtils.isNotBlank(item.getId());
                }
                return true;
            }).collect(Collectors.toList());
            // 无可用镜头
            if (CollectionUtils.isEmpty(filteredCharacterCameraRes)) {
                filteredCharacterCameraRes = characterCameraRes.stream()
                        .filter(item -> finalPrevCameraRes == null || !StringUtils.equals(item.getId(), finalPrevCameraRes.getId()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(filteredCharacterCameraRes)) {
                return cameraSegments;
            }
            // 开场镜头
            CameraResourceDTO rndResource = null;
            if (isOpening && current == start) {
                List<CameraResourceDTO> openingCameraList = filteredCharacterCameraRes.stream()
                        .filter(camera -> BooleanUtils.isTrue(camera.getOpening()))
                        .collect(Collectors.toList());
                rndResource = CollectionUtils.isNotEmpty(openingCameraList) ? openingCameraList.get(RandomUtils.nextInt(0, openingCameraList.size())) : null;
            }
            // 结尾镜头
            else if (isEnding && isLast) {
                List<CameraResourceDTO> endingCameraList = filteredCharacterCameraRes.stream()
                        .filter(camera -> BooleanUtils.isTrue(camera.getEnding()))
                        .collect(Collectors.toList());
                rndResource = CollectionUtils.isNotEmpty(endingCameraList) ? endingCameraList.get(RandomUtils.nextInt(0, endingCameraList.size())) : null;
            }
            if (rndResource == null) {
                // 根据权重选择镜头
                rndResource = RandomResUtil.selectRandomResByWeight(filteredCharacterCameraRes, CameraResourceDTO::getWeight);
            }
            if (rndResource == null) {
                return cameraSegments;
            }
            String resourceId = rndResource.getId();
            CameraSegment cameraSegment = SegmentUtil.createCharacterCameraSegment(TimeUtils.formatMilliseconds(current), TimeUtils.formatMilliseconds(cameraEnd), resourceId, rndResource.getName());
            cameraSegment.setShotSize(rndResource.getShotSize());
            cameraSegment.setPan(rndResource.getPan());
            cameraSegment.setLookAt(rndResource.getLookAt());
            cameraSegments.add(cameraSegment);
            prevCameraRes = rndResource;
            current = cameraEnd;
        }
        return cameraSegments;
    }

    @Deprecated
    private static List<CameraResourceDTO> applyCameraConstraints(long start, long end, List<CameraResourceDTO> cameras, List<CameraConstraint> constraints) {
        List<CameraResourceDTO> filteredCameras = new ArrayList<>();
        for (CameraResourceDTO camera : cameras) {
            if (CollectionUtils.isEmpty(constraints)) {
                filteredCameras.add(camera);
            } else {
                boolean isMatch = true;
                for (CameraConstraint constraint : constraints) {
                    if (!overlaps(start, end, constraint)) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(constraint.getShotType())) {
                        if (!StringUtils.contains(constraint.getShotType(), camera.getShotSize())) {
                            isMatch = false;
                        }
                    }
                    if (isMatch && StringUtils.isNotBlank(constraint.getShotComposition())) {
                        if (!StringUtils.contains(constraint.getShotComposition(), camera.getComposition())) {
                            isMatch = false;
                        }
                    }
                }
                if (isMatch) {
                    filteredCameras.add(camera);
                }
            }
        }
        return filteredCameras;
    }

    @Deprecated
    private static boolean overlaps(long start, long end, CameraConstraint constraint) {
        if (constraint.getStartMs() == null || constraint.getEndMs() == null) {
            return false;
        }
        return start <= constraint.getEndMs() && end >= constraint.getStartMs();
    }

    /**
     * 随机动作
     */
    public static List<ActionSegment> randomCreateActions(long start, long end, int minSplit, int maxSplit, List<ActionResourceDTO> actions) {
        List<ActionSegment> actionSegments = new ArrayList<>();
        if (CollectionUtils.isEmpty(actions)) {
            return actionSegments;
        }
        long current = start;
        while (current < end) {
            CoursewareResourceDTO action = actions.get(RandomUtils.nextInt(0, actions.size()));

            long actionDuration;
            if (action.getDuration() != null) {
                actionDuration = (long) (action.getDuration() * 1000);
            } else {
                actionDuration = minSplit + RandomUtils.nextInt(0, maxSplit - minSplit + 1);
            }
            long split = current + actionDuration;
            if (split >= end || (end - split) < minSplit) {
                split = end;
            }
            ActionSegment actionSegment = SegmentUtil.createActionSegment(TimeUtils.formatMilliseconds(current), TimeUtils.formatMilliseconds(split), action.getId(), action.getName());
            actionSegments.add(actionSegment);
            current = split;
        }
        return actionSegments;
    }

    public static List<RoutineSegment> randomCreateRoutineActions(long start, long end, int minSplit, int maxSplit, List<ActionResourceDTO> actions, String target) {
        List<RoutineSegment> routineSegments = randomCreateRoutineActions(start, end, minSplit, maxSplit, actions);
        routineSegments.forEach(e -> {
            e.setParamVersion(1);
            e.targetId(target);
        });
        return routineSegments;
    }

    /**
     * 随机套路动作
     */
    public static List<RoutineSegment> randomCreateRoutineActions(long start, long end, int minSplit, int maxSplit, List<ActionResourceDTO> actions) {
        List<RoutineSegment> actionSegments = new ArrayList<>();
        if (CollectionUtils.isEmpty(actions)) {
            return actionSegments;
        }
        long current = start;
        while (current < end) {
            CoursewareResourceDTO action = actions.get(RandomUtils.nextInt(0, actions.size()));
            RoutineSegment actionSegment = SegmentUtil.createActionRoutineSegment(TimeUtils.formatMilliseconds(current), action.getId(), action.getName());
            actionSegments.add(actionSegment);
            long actionDuration;
            if (action.getDuration() != null) {
                actionDuration = (long) (action.getDuration() * 1000);
            } else {
                actionDuration = minSplit + RandomUtils.nextInt(0, maxSplit - minSplit + 1);
            }
            long split = current + actionDuration;
            if (split >= end) {
                break;
            }
            current = split;
        }
        return actionSegments;
    }

    /**
     * 随机转场
     */
    public static SceneChangeSegment randomCreateTransitionEffect(String time) {
        int rndNum = RandomUtils.nextInt(0, 5);
        switch (rndNum) {
            case 0:
                return SegmentUtil.createCircularMaskSceneChangeSegment(time, 1, 0.5f);
            case 1:
                return SegmentUtil.createGlowSceneChangeSegment(time, 1, 0.5f);
            case 2:
                return SegmentUtil.createSplitScreenSceneChangeSegment(time, 1, 0.5f);
            case 3:
                return SegmentUtil.createShatterEffectSceneChangeSegment(time, 1, 0.5f);
            case 4:
                return SegmentUtil.createTransectSceneChangeSegment(time, 1, 0.5f);
            default:
        }
        return SegmentUtil.createCircularMaskSceneChangeSegment(time, 1, 0.5f);
    }


    /**
     * 根据时间范围查找片段
     */
    public static <T extends BaseSegment<T>> List<T> findSegmentsBetween(final List<T> segments, long startMs, long endMs) {
        if (CollectionUtils.isEmpty(segments)) {
            return new ArrayList<>();
        }
        List<T> result = new ArrayList<>();
        for (T segment : segments) {
            long segmentStartMs = TimeUtils.convertToMilliseconds(segment.getTime());
            if (segmentStartMs >= startMs && segmentStartMs < endMs) {
                result.add(segment);
            }
        }
        return result;
    }

    public static List<CameraSegment> findOverlayCameraSegments(final List<CameraSegment> segments, long startMs, long endMs) {
        if (CollectionUtils.isEmpty(segments)) {
            return new ArrayList<>();
        }
        List<CameraSegment> result = new ArrayList<>();
        for (CameraSegment segment : segments) {
            long segmentStartMs = TimeUtils.convertToMilliseconds(segment.getTime());
            long segmentEndMs = TimeUtils.convertToMilliseconds(segment.getEndTime());
            if (startMs >= segmentEndMs || endMs <= segmentStartMs) {
                continue;
            }
            result.add(segment);
        }
        return result;
    }
}
