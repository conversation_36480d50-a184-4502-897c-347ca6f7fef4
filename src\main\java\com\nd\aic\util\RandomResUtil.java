package com.nd.aic.util;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;

import java.util.List;
import java.util.function.Function;

public class RandomResUtil {

    public static <T> T selectRandomResByWeight(List<T> items, Function<T, Integer> weightExtractor) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        // 计算权重总和
        int totalWeight = 0;
        for (T item : items) {
            Integer weightVal = weightExtractor.apply(item);
            int weight = weightVal != null ? weightVal : 1;
            totalWeight += weight;
        }
        // 生成一个随机数
        int randomNumber = RandomUtils.nextInt(0, totalWeight);
        // 遍历数组，根据权重选择对象
        double currentWeightSum = 0;
        for (T item : items) {
            Integer weightVal = weightExtractor.apply(item);
            int weight = weightVal != null ? weightVal : 1;
            currentWeightSum += weight;
            if (randomNumber < currentWeightSum) {
                return item;
            }
        }
        // 如果没有返回对象（理论上不会发生）
        return items.get(0);
    }
}
