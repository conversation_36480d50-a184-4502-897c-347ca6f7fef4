package com.nd.aic.util;

import java.util.concurrent.Callable;

public class RetryUtil {

    /**
     * 执行带有重试机制的方法
     *
     * @param task       要执行的任务，使用Callable封装
     * @param maxRetries 最大重试次数（不包括首次尝试）
     * @return 任务执行成功的结果
     * @throws Exception 当所有尝试都失败时抛出最后一次异常
     */
    public static <T> T executeWithRetry(Callable<T> task, int maxRetries) throws Exception {
        if (maxRetries < 0) {
            throw new IllegalArgumentException("最大重试次数不能小于0");
        }

        Exception lastException = null;

        // 总尝试次数 = 首次执行 + 重试次数
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return task.call();
            } catch (Exception e) {
                lastException = e;
                Thread.sleep(calculateBackoff(attempt));
            }
        }

        throw lastException;
    }

    /**
     * 退避时间计算
     */
    private static long calculateBackoff(int attempt) {
        // 指数退避，最大60秒
        return (long) Math.min(1000 * Math.pow(2, attempt), 60000);
    }
}
