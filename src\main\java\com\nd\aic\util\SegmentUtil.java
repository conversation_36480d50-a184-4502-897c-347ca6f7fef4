package com.nd.aic.util;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSONObject;
import com.nd.aic.enums.RoutineTypeEnum;
import com.nd.aic.pojo.common.Transform;
import com.nd.aic.pojo.segment.ActionSegment;
import com.nd.aic.pojo.segment.ArkitFaceSegment;
import com.nd.aic.pojo.segment.AudioSegment;
import com.nd.aic.pojo.segment.BaseSegment;
import com.nd.aic.pojo.segment.BgmSegment;
import com.nd.aic.pojo.segment.BirthPointSegment;
import com.nd.aic.pojo.segment.BuffSegment;
import com.nd.aic.pojo.segment.CameraFeatureSegment;
import com.nd.aic.pojo.segment.CameraSegment;
import com.nd.aic.pojo.segment.CharacterSegment;
import com.nd.aic.pojo.segment.ClothingSegment;
import com.nd.aic.pojo.segment.GifSegment;
import com.nd.aic.pojo.segment.LightSegment;
import com.nd.aic.pojo.segment.LottieSegment;
import com.nd.aic.pojo.segment.ObjectSegment;
import com.nd.aic.pojo.segment.RoutineSegment;
import com.nd.aic.pojo.segment.SceneChangeSegment;
import com.nd.aic.pojo.segment.SceneObjectSegment;
import com.nd.aic.pojo.segment.SceneSegment;
import com.nd.aic.pojo.segment.UISegment;
import com.nd.aic.pojo.segment.VideoSegment;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public final class SegmentUtil {

    /**
     * segments 根据时间正序
     */
    public static void sortByTime(List<? extends BaseSegment<?>> segments) {
        segments.sort((a, b) -> {
            if (StringUtils.isBlank(a.getTime())) {
                return -1;
            }
            if (StringUtils.isBlank(b.getTime())) {
                return 1;
            }
            return Long.compare(TimeUtils.convertToMilliseconds(a.getTime()), TimeUtils.convertToMilliseconds(b.getTime()));
        });
    }

    /**
     * 创建角色镜头
     */
    public static CameraSegment createCharacterCameraSegment(String time, String endTime, String resourceId, String reason) {
        CameraSegment cameraSegment = new CameraSegment();
        cameraSegment.setTime(time);
        if (StringUtils.isNotBlank(endTime)) {
            cameraSegment.setEndTime(endTime);
        }
        cameraSegment.setResourceId(resourceId);
        cameraSegment.setNdrResourceId(resourceId);
        cameraSegment.setType("角色镜头");
        cameraSegment.setReason(reason);
        cameraSegment.playType(1).skillType("CharacterCamera");
        return cameraSegment;
    }

    /**
     * 创建场景镜头
     */
    public static CameraSegment createSceneCameraSegment(String time, String endTime, String resourceId) {
        CameraSegment cameraSegment = new CameraSegment();
        cameraSegment.setTime(time);
        if (StringUtils.isNotBlank(endTime)) {
            cameraSegment.setEndTime(endTime);
        }
        cameraSegment.setResourceId(resourceId);
        cameraSegment.setType("空镜");
        cameraSegment.playType(1).skillType("SceneCamera");
        return cameraSegment;
    }



    /**
     * 创建角色镜头
     *
     * @param time     开始时间
     * @param endTime  结束时间
     * @param resourceId 资源id
     * @param targetId 目标id
     * @param reason   原因
     * @return 镜头
     */
    public static CameraSegment createCharacterCameraSegment(String time, String endTime, String resourceId, String targetId, String reason) {
        CameraSegment cameraSegment = new CameraSegment();
        cameraSegment.setTime(time);
        if (StringUtils.isNotBlank(endTime)) {
            cameraSegment.setEndTime(endTime);
        }
        cameraSegment.setResourceId(resourceId);
        cameraSegment.setNdrResourceId(resourceId);
        cameraSegment.setType("角色镜头");
        cameraSegment.setReason(reason);
        // 增加镜头的targetId
        cameraSegment.targetId(targetId);
        cameraSegment.playType(1).skillType("CharacterCamera");
        return cameraSegment;
    }

    /**
     * 创建动作
     */
    public static ActionSegment createActionSegment(String time, String endTime, String resourceId, String type) {
        ActionSegment actionSegment = new ActionSegment();
        actionSegment.setTime(time);
        actionSegment.setEndTime(endTime);
        actionSegment.setResourceId(resourceId);
        actionSegment.setType(type);
        actionSegment.setCode("talk");
        return actionSegment;
    }


    /**
     * 创建动作
     */
    public static ActionSegment createActionSegment(String time, String endTime, String resourceId, String type, String reason) {
        ActionSegment actionSegment = new ActionSegment();
        actionSegment.setTime(time);
        actionSegment.setEndTime(endTime);
        actionSegment.setResourceId(resourceId);
        actionSegment.setType(type);
        actionSegment.setReason(reason);
        actionSegment.setCode("talk");
        return actionSegment;
    }

    /**
     * 创建看向镜头
     */
    public static CameraFeatureSegment createLookAtCameraFeatureSegment(String time, String endTime) {
        CameraFeatureSegment cameraFeatureSegment = new CameraFeatureSegment();
        cameraFeatureSegment.setTime(time);
        cameraFeatureSegment.setEndTime(endTime);
        cameraFeatureSegment.setType("look_at_target");
        cameraFeatureSegment.setReason("看向镜头");
        cameraFeatureSegment.setCustom(new JSONObject().fluentPut("TargetType", "Camera"));
        return cameraFeatureSegment;
    }

    /**
     * 创建镜头跟随的特性
     *
     * @param time     开始时间
     * @param end_time 结束时间
     * @return 镜头特性
     */
    public static CameraFeatureSegment createCameraFollowFeatureSegment(String time, String end_time, String reason) {
        CameraFeatureSegment cameraFeatureSegment = new CameraFeatureSegment();
        cameraFeatureSegment.setTime(time);
        cameraFeatureSegment.setEndTime(end_time);
        cameraFeatureSegment.setType("camera_follow");
        cameraFeatureSegment.setReason(StringUtils.isEmpty(reason) ? "镜头跟随" : reason);
        cameraFeatureSegment.setCustom(null);
        return cameraFeatureSegment;
    }

    /**
     * 创建转场-指定转场资源方式
     */
    public static SceneChangeSegment createGeneralSceneChangeSegment(String time, String resourceId, String name, double durationSec, float offsetSec) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, (long) (offsetSec * 1000))));
        sceneChangeSegment.setResourceId(resourceId);
        sceneChangeSegment.setType(name);
        sceneChangeSegment.playType(1).skillType("ScreenEffect").cTime(durationSec);
        sceneChangeSegment.putParam("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "SceneCapture")));
        return sceneChangeSegment;
    }

    /**
     * 创建转场-横推切镜
     */
    public static SceneChangeSegment createTransectSceneChangeSegment(String time, double durationSec, float offsetSec) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, (long) (offsetSec * 1000))));
        sceneChangeSegment.setResourceId("509d3d16-cc93-4d87-8b8d-78bf7850006e");
        sceneChangeSegment.setType("横推切镜");
        sceneChangeSegment.playType(1).skillType("ScreenEffect").cTime(durationSec);
        sceneChangeSegment.putParam("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "SceneCapture")));
        return sceneChangeSegment;
    }

    /**
     * 创建转场-圆形遮罩
     */
    public static SceneChangeSegment createCircularMaskSceneChangeSegment(String time, double durationSec, float offsetSec) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, (long) (offsetSec * 1000))));
        sceneChangeSegment.setResourceId("8a66cd2b-b978-4063-8777-ea3f3494f009");
        sceneChangeSegment.setType("圆形遮罩");
        sceneChangeSegment.playType(1).skillType("ScreenEffect").cTime(durationSec);
        sceneChangeSegment.putParam("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "SceneCapture")));
        return sceneChangeSegment;
    }

    /**
     * 创建转场-泛光
     */
    public static SceneChangeSegment createGlowSceneChangeSegment(String time, double durationSec, float offsetSec) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, (long) (offsetSec * 1000))));
        sceneChangeSegment.setResourceId("f958dea6-11a3-4f3a-9542-90a2a1fda75a");
        sceneChangeSegment.setType("泛光");
        sceneChangeSegment.playType(1).skillType("ScreenEffect").cTime(durationSec);
        sceneChangeSegment.putParam("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "SceneCapture")));
        return sceneChangeSegment;
    }


    /**
     * 创建转场-画面分割
     */
    public static SceneChangeSegment createSplitScreenSceneChangeSegment(String time, double durationSec, float offsetSec) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, (long) (offsetSec * 1000))));
        sceneChangeSegment.setResourceId("5ff811b5-8f6e-48cb-bc51-f5aa9a3a70fd");
        sceneChangeSegment.setType("画面分割");
        sceneChangeSegment.playType(1).skillType("ScreenEffect").cTime(durationSec);
        sceneChangeSegment.putParam("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "SceneCapture")));
        return sceneChangeSegment;
    }

    /**
     * 创建转场-破碎效果
     */
    public static SceneChangeSegment createShatterEffectSceneChangeSegment(String time, double durationSec, float offsetSec) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, (long) (offsetSec * 1000))));
        sceneChangeSegment.setResourceId("443cebd5-4b46-463c-8081-909cbcb974dc");
        sceneChangeSegment.setType("破碎效果");
        sceneChangeSegment.playType(1).skillType("ScreenEffect").cTime(durationSec);
        sceneChangeSegment.putParam("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "SceneCapture")));
        return sceneChangeSegment;
    }

    /**
     * 创建转场-云雾转场
     */
    public static LottieSegment createCloudMistTransitionLottieSegment(String time) {
        long startMs = TimeUtils.subMilliseconds(time, 800);
        return SegmentUtil.createLottieSegment(TimeUtils.formatMilliseconds(startMs), TimeUtils.formatMilliseconds(startMs + 1700), "b39cf060-4bb3-41f7-a203-5bae45fbf18f", "云雾转场", null);
    }

    public static SceneChangeSegment randomCreateTransitionEffect(String time) {
        int rndNum = RandomUtils.nextInt(0, 5);
        switch (rndNum) {
            case 0:
                // "8a66cd2b-b978-4063-8777-ea3f3494f009" 圆形遮罩
                return SegmentUtil.createCircularMaskSceneChangeSegment(time, 2, 1f);
            case 1:
                // "f958dea6-11a3-4f3a-9542-90a2a1fda75a" 泛光
                return SegmentUtil.createGlowSceneChangeSegment(time, 2, 1f);
            case 2:
                // "5ff811b5-8f6e-48cb-bc51-f5aa9a3a70fd" 画面分割
                return SegmentUtil.createSplitScreenSceneChangeSegment(time, 2, 1f);
            case 3:
                // "443cebd5-4b46-463c-8081-909cbcb974dc" 破碎效果
                return SegmentUtil.createShatterEffectSceneChangeSegment(time, 2, 1f);
            case 4:
                // "509d3d16-cc93-4d87-8b8d-78bf7850006e" 横推切镜
                return SegmentUtil.createTransectSceneChangeSegment(time, 2, 1f);
            default:
        }
        return SegmentUtil.createCircularMaskSceneChangeSegment(time, 2, 1f);
    }

    /**
     * 创建视频全屏板书的屏幕物资
     *
     * @param time    开始时间
     * @param endTime 结束时间
     * @param reason  原因
     * @return 屏幕特效轨道
     */
    public static SceneChangeSegment createBoardFullScreenSceneChangeSegment(String time, String endTime, String reason) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(time);
        sceneChangeSegment.setEndTime(endTime);
        sceneChangeSegment.setResourceId("50D753EB4C2E4D7EBB086B8671E96F85");
        sceneChangeSegment.setNdrResourceId("50D753EB4C2E4D7EBB086B8671E96F85");
        sceneChangeSegment.setReason(reason);
        sceneChangeSegment.setType("全屏视频");
        sceneChangeSegment.setCustom(new JSONObject().fluentPut("PlayType", 1)
                .fluentPut("SkillType", "ScreenEffect")
                .fluentPut("Time", sceneChangeSegment.getDurationSec())
                .fluentPut("TargetID", "")
                .fluentPut("Param", new JSONObject().fluentPut("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "CurrentVideo")))));
        return sceneChangeSegment;
    }

    /**
     * 获取灯光
     *
     * @param time       时间
     * @param endTime    结束时间
     * @param resourceId 资源id
     * @param reason     原因
     * @return 灯光
     */
    public static LightSegment createLightSegment(String time, String endTime, String resourceId, String reason) {
        LightSegment lightSegment = new LightSegment();
        lightSegment.setTime(time);
        lightSegment.setEndTime(endTime);
        lightSegment.setResourceId(resourceId);
        lightSegment.setReason(reason);
        return lightSegment;
    }


    /**
     * 创建转场-流光
     */
    public static SceneChangeSegment createLightStreakSceneChangeSegment(String time, int durationSec, float offsetSec) {
        SceneChangeSegment sceneChangeSegment = new SceneChangeSegment();
        sceneChangeSegment.setTime(TimeUtils.formatMilliseconds(TimeUtils.subMilliseconds(time, (long) (offsetSec * 1000))));
        sceneChangeSegment.setResourceId("74460c76-eba9-4bd4-aa7d-6dc309fd48d2");
        sceneChangeSegment.setType("流光");
        JSONObject param = new JSONObject().fluentPut("InputInfos", Lists.newArrayList(new JSONObject().fluentPut("InputType", "SceneCapture")));
        sceneChangeSegment.setCustom(new JSONObject().fluentPut("PlayType", 1)
                .fluentPut("SkillType", "ScreenEffect")
                .fluentPut("Time", durationSec)
                .fluentPut("Param", param));
        return sceneChangeSegment;
    }

    /**
     * 创建 Lottie
     */
    public static LottieSegment createLottieSegment(String time, String endTime, String resourceId, String reason, JSONObject lottieParam) {
        LottieSegment lottieSegment = new LottieSegment();
        lottieSegment.setTime(time);
        lottieSegment.setEndTime(endTime);
        lottieSegment.setType("LottieScreen");
        lottieSegment.setReason(reason);
        lottieSegment.setResourceId(resourceId);
        lottieSegment.setCustom(new JSONObject().fluentPut("windowId", 1).fluentPut("params", lottieParam));
        return lottieSegment;
    }


    /**
     * 创建字幕Lottie
     */
    public static LottieSegment createSubtitleLottieSegment(String time, String endTime, String text) {
        LottieSegment lottieSegment = new LottieSegment();
        lottieSegment.setTime(time);
        lottieSegment.setEndTime(endTime);
        lottieSegment.setType("LottieScreen");
        lottieSegment.setResourceId("AA18925A059C481494A3B547AC437504");
        lottieSegment.setCustom(new JSONObject().fluentPut("windowId", 1)
                .fluentPut("params", new JSONObject().fluentPut("#Text", new JSONObject().fluentPut("type", "text").fluentPut("value", text))));
        return lottieSegment;
    }

    /**
     * 创建场景
     */
    public static SceneSegment createSceneSegment(String time, String endTime, String resourceId) {
        SceneSegment sceneSegment = new SceneSegment();
        sceneSegment.setTime(time);
        sceneSegment.setEndTime(endTime);
        sceneSegment.setResourceId(resourceId);
        sceneSegment.setReason("场景");
        return sceneSegment;
    }

    public static BgmSegment createBGMSegment(String time, String endTime, String resourceId) {
        BgmSegment bgmSegment = new BgmSegment();
        bgmSegment.setTime(time);
        bgmSegment.setEndTime(endTime);
        bgmSegment.setResourceId(resourceId);
        bgmSegment.setReason("背景音乐");
        bgmSegment.setCustom(new JSONObject().fluentPut("volume", 80));
        return bgmSegment;
    }

    public static AudioSegment createAudioSegment(String time, String endTime, String resourceId, String soundGroup, Float volume) {
        AudioSegment audioSegment = new AudioSegment();
        audioSegment.setTime(time);
        audioSegment.setEndTime(endTime);
        audioSegment.setType("控制端暂时没用");
        audioSegment.setResourceId(resourceId);
        audioSegment.setReason("播放音频");
        audioSegment.soundType("2D").targetType("Camera").distance(0).location(0, 0, 0).offset(0).soundGroup(soundGroup).volume(volume);
        audioSegment.setDependencies(Lists.newArrayList(resourceId));
        return audioSegment;
    }

    /**
     * 创建角色
     */
    public static CharacterSegment createCharacterSegment(String time, String endTime, String resourceId, boolean isHide) {
        CharacterSegment characterSegment = new CharacterSegment();
        characterSegment.setTime(time);
        if (StringUtils.isNotBlank(endTime)) {
            characterSegment.setEndTime(endTime);
        }
        if (isHide) {
            characterSegment.setType("hide");
        }
        characterSegment.setResourceId(resourceId);
        characterSegment.setCustom(new JSONObject().fluentPut("noLoadingUI", true).fluentPut("getType", "1").fluentPut("type", "Role"));
        return characterSegment;
    }

    /**
     * 创建角色显示或者隐藏
     */
    public static CharacterSegment createToggleCharacterSegment(String time, boolean isHide) {
        CharacterSegment characterSegment = new CharacterSegment();
        characterSegment.setTime(time);
        characterSegment.setType(isHide ? "hide" : "show");
        return characterSegment;
    }

    /**
     * 创建服装
     */
    public static ClothingSegment createClothingSegment(String time, String endTime, String resourceId, String reason) {
        ClothingSegment clothingSegment = new ClothingSegment();
        clothingSegment.setTime(time);
        clothingSegment.setEndTime(endTime);
        clothingSegment.setResourceId(resourceId);
        clothingSegment.setReason(reason);
        return clothingSegment;
    }

    /**
     * 创建出生点
     */
    public static BirthPointSegment createBirthPointSegment(String time, String type, JSONObject custom) {
        BirthPointSegment birthPointSegment = new BirthPointSegment();
        birthPointSegment.setTime(time);
        birthPointSegment.setType(type);
        birthPointSegment.setCustom(custom);
        return birthPointSegment;
    }

    /**
     * 根据transform创建出生点
     *
     * @param time      时间
     * @param transform transform
     * @return 出生点
     */
    public static BirthPointSegment createBirthPointSegmentByTransform(String time, Transform transform) {
        BirthPointSegment birthPointSegment = new BirthPointSegment();
        birthPointSegment.setTime(time);
        birthPointSegment.setReason("自定义出生点");
        birthPointSegment.setResourceId(null);
        birthPointSegment.setType("自定义出生点");
        birthPointSegment.setCustom(transform.toJSONObject().fluentPut("SkillType", "CharacterMove").fluentPut("name", "自定义出生点"));
        return birthPointSegment;
    }


    /**
     * 创建透明视频
     */
    public static VideoSegment createAlphaVideoSegment(String time, String endTime, String resourceId, String reason) {
        VideoSegment videoSegment = new VideoSegment();
        videoSegment.setTime(time);
        videoSegment.setEndTime(endTime);
        videoSegment.setResourceId(resourceId);
        videoSegment.setType("AlphaVideo");
        videoSegment.setReason(reason);
        videoSegment.setCustom(new JSONObject().fluentPut("Model", "Play").fluentPut("Time", videoSegment.getDuration() / 1000.0));
        videoSegment.perform("video-preload");
        return videoSegment;
    }

    /**
     * 创建背景视频
     */
    public static VideoSegment createBackVideoSegment(String time, String endTime, String resourceId) {
        VideoSegment videoSegment = new VideoSegment();
        videoSegment.setTime(time);
        videoSegment.setEndTime(endTime);
        videoSegment.setResourceId(resourceId);
        videoSegment.setType("BackVideo");
        videoSegment.setCustom(new JSONObject().fluentPut("Model", "Play").fluentPut("Time", videoSegment.getDuration() / 1000.0));
        videoSegment.perform("video-preload");
        return videoSegment;
    }

    /**
     * 创建前景视频
     */
    public static VideoSegment createFrontVideoSegment(String time, String endTime, String resourceId) {
        VideoSegment videoSegment = new VideoSegment();
        videoSegment.setTime(time);
        videoSegment.setEndTime(endTime);
        videoSegment.setResourceId(resourceId);
        videoSegment.setType("FrontVideo");
        videoSegment.perform("video-preload");
        return videoSegment;
    }


    /**
     * 创建前景视频
     */
    public static VideoSegment createFrontVideoSegment(String time, String endTime, String resourceId, String reason) {
        VideoSegment videoSegment = new VideoSegment();
        videoSegment.setTime(time);
        videoSegment.setEndTime(endTime);
        videoSegment.setResourceId(resourceId);
        videoSegment.setReason(reason);
        videoSegment.setType("FrontVideo");
        videoSegment.perform("video-preload");
        return videoSegment;
    }


    /**
     * 创建前景视频
     */
    public static GifSegment createFrontImgSeqSegment(String time, String endTime, String resourceId, String reason) {
        GifSegment gifSegment = new GifSegment();
        gifSegment.setTime(time);
        gifSegment.setEndTime(endTime);
        gifSegment.setResourceId(resourceId);
        gifSegment.setReason(reason);
        gifSegment.setType("FrontImgSeq");
        return gifSegment;
    }


    /**
     * 创建前景视频
     */
    public static GifSegment createGifSegment(String time, String endTime, String resourceId, String reason) {
        GifSegment gifSegment = new GifSegment();
        gifSegment.setTime(time);
        gifSegment.setEndTime(endTime);
        gifSegment.setResourceId(resourceId);
        gifSegment.setReason(reason);
        gifSegment.setType("gif");
        return gifSegment;
    }

    /**
     * 创建大套路
     */
    public static RoutineSegment createBigRoutineSegment(String time, String endTime, String resourceId, String reason) {
        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTime(time);
        routineSegment.setEndTime(endTime);
        routineSegment.setResourceId(resourceId);
        routineSegment.setType(RoutineTypeEnum.BigRoutine.getName());
        routineSegment.setReason(reason);
        routineSegment.playType(1).skillType(RoutineTypeEnum.BigRoutine.getName()).cTime(routineSegment.getDurationSec());
        // https://pms.sdp.101.com/#/track/issue/detail/385c7465-b10a-4db6-9c17-799883cbb47c 此BUG要求增加StopAllSkill参数
        // StopAllSkill会引发新问题-20250506渲染問題已修復
        // routineSegment.putParam("StopAllSkill", false);
        return routineSegment;
    }

    /**
     * 创建大套路（动作的套路）
     */
    public static RoutineSegment createActionRoutineSegment(String time, String resourceId, String reason) {
        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTime(time);
        routineSegment.setResourceId(resourceId);
        routineSegment.setType(RoutineTypeEnum.Animation.getName());
        routineSegment.setReason(reason);
        routineSegment.playType(0).skillType(RoutineTypeEnum.Animation.getName());
        return routineSegment;
    }

    /**
     * 创建大套路（动作的套路）
     */
    public static RoutineSegment createActionRoutineSegment(String time, String endTime, String resourceId, String reason) {
        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTime(time);
        routineSegment.setEndTime(endTime);
        routineSegment.setResourceId(resourceId);
        routineSegment.setType(RoutineTypeEnum.Animation.getName());
        routineSegment.setReason(reason);
        routineSegment.playType(0).skillType(RoutineTypeEnum.Animation.getName());
        return routineSegment;
    }

    /**
     * 创建单独对象轨道
     */
    public static ObjectSegment createObjectSegment(String time, String endTime, String resourceId, String type) {
        ObjectSegment objectSegment = new ObjectSegment();
        objectSegment.setTime(time);
        objectSegment.setEndTime(endTime);
        objectSegment.setResourceId(resourceId);
        objectSegment.setType(type);
        return objectSegment;
    }

    /**
     * 创建场景物体
     *
     * @param time       时间
     * @param endTime    结束时间
     * @param resourceId 资源id
     * @param reason     原因
     * @return 场景物体
     */
    public static SceneObjectSegment createSceneObjectSegment(String time, String endTime, String resourceId, String type, String reason) {
        SceneObjectSegment sceneObjectSegment = new SceneObjectSegment();
        sceneObjectSegment.setTime(time);
        sceneObjectSegment.setEndTime(endTime);
        sceneObjectSegment.setType(type);
        sceneObjectSegment.setResourceId(resourceId);
        sceneObjectSegment.setReason(reason);
        sceneObjectSegment.setCustom(new JSONObject().fluentPut("MetaRoleType", type));
        return sceneObjectSegment;
    }

    /**
     * 设置套路参数
     */
    public static SceneObjectSegment createSetParamSegment(String time, String endTime, String reason, String instanceId, JSONObject custom) {
        SceneObjectSegment routineSegment = new SceneObjectSegment();
        routineSegment.setTime(time);
        routineSegment.setEndTime(endTime);
        routineSegment.setType(RoutineTypeEnum.Setting.getName());
        routineSegment.setReason(reason);
        routineSegment.setCustom(custom);
        routineSegment.instanceId(instanceId);
        return routineSegment;
    }

    /**
     * 创建角色套路
     *
     * @param time       时间
     * @param endTime    结束时间
     * @param resourceId 资源id
     * @param reason     原因
     * @return 角色套路
     */
    public static RoutineSegment createCharacterActionSegment(String time, String endTime, String resourceId, String reason) {
        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTime(time);
        routineSegment.setEndTime(endTime);
        routineSegment.setResourceId(resourceId);
        routineSegment.setType(RoutineTypeEnum.CharacterAction.getName());
        routineSegment.setReason(reason);
        routineSegment.playType(1).skillType(RoutineTypeEnum.CharacterAction.getName()).cTime(routineSegment.getDurationSec());
        return routineSegment;
    }

    /**
     * 创建表情轨道
     *
     * @param time       时间
     * @param endTime    结束时间
     * @param resourceId 资源id
     * @param reason     原因
     * @return 表情轨道
     */
    public static ArkitFaceSegment createArkitFaceSegment(String time, String endTime, String resourceId, String reason) {
        ArkitFaceSegment arkitFaceSegment = new ArkitFaceSegment();
        arkitFaceSegment.setTime(time);
        arkitFaceSegment.setEndTime(endTime);
        arkitFaceSegment.setResourceId(resourceId);
        arkitFaceSegment.setReason(reason);
        arkitFaceSegment.playType(1).cTime(arkitFaceSegment.getDurationSec()).skillType(RoutineTypeEnum.Animation.getName());
        return arkitFaceSegment;
    }


    /**
     * create arkit face segment using routine segment
     *
     * @param time       start time
     * @param endTime    end time
     * @param resourceId resource id
     * @param reason     reason
     * @return routine segment
     */
    public static RoutineSegment createArkitFaceSegmentUsingRoutine(String time, String endTime, String resourceId, String reason) {
        RoutineSegment routineSegment = new RoutineSegment();
        routineSegment.setTime(time);
        routineSegment.setEndTime(endTime);
        routineSegment.setResourceId(resourceId);
        routineSegment.setType(RoutineTypeEnum.Animation.getName());
        routineSegment.setReason(reason);
        routineSegment.playType(1).skillType(RoutineTypeEnum.Animation.getName()).cTime(routineSegment.getDurationSec());
        return routineSegment;
    }

    /**
     * 创建Buff轨道
     *
     * @param time     时间
     * @param endTime  结束时间
     * @param targetId 绑定id
     * @param reason   原因
     * @return Buff轨道
     */
    public static BuffSegment createBuffSegment(String time, String endTime, String targetId, String reason, JSONObject custom) {
        BuffSegment buffSegment = new BuffSegment();
        buffSegment.setTime(time);
        buffSegment.setEndTime(endTime);
        buffSegment.setType("buff");
        buffSegment.setReason(reason);
        buffSegment.setCustom(custom);
        buffSegment.targetId(targetId);
        buffSegment.cTime(buffSegment.getDurationSec());
        return buffSegment;
    }

    public static UISegment createSubtitleUISegment(String time, String endTime, String text) {
        UISegment segment = new UISegment();
        segment.setTime(time);
        segment.setEndTime(endTime);
        segment.setType("MetaUI");
        if (LatexUtils.enableLatex()) {
            // 支持LATEX字幕
            segment.setResourceId("47BC4FB34E1DEF0BBC3D98BE8A664598");
            segment.perform("webbrowser-effect");
        } else {
            segment.setResourceId("10DEEA23455E0CB977D55AA776F18CF4");
        }
        segment.instanceId(UUIDUtil.upperUuid());
        segment.putParam("Properties", Lists.newArrayList(new JSONObject().fluentPut("name", "MainText").fluentPut("type", "string").fluentPut("value", text)));
        return segment;
    }

    public static UISegment createWebUISegment(String time, String endTime, String url, String reason) {
        UISegment segment = new UISegment();
        segment.setReason(reason);
        segment.setTime(time);
        segment.setEndTime(endTime);
        segment.setType("MetaUI");
        segment.setResourceId("928a7d34-8ba4-48d2-95dd-24d3b6c2c0c1");
        segment.getCustom().put("Layer", "Front");
        segment.instanceId(UUIDUtil.upperUuid());
        segment.putParam("Properties", Lists.newArrayList(
                new JSONObject().fluentPut("name", "Url").fluentPut("type", "string").fluentPut("value", url),
                new JSONObject().fluentPut("name", "ShouldWaitForLoad").fluentPut("type", "bool").fluentPut("value", "false")
        ));
        return segment;
    }
}
