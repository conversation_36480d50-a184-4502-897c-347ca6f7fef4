package com.nd.aic.util;

import com.nd.aic.client.pojo.asr.AtaResult;
import com.nd.aic.client.pojo.asr.AtaUtterance;
import com.nd.aic.client.pojo.asr.AtaWord;
import com.nd.aic.common.BizConstant;
import com.nd.aic.common.BizHandler;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class SubtitleUtils {



    private static final Pattern BREAK_PUNCTUATION_PATTERN = Pattern.compile("[，,。.！!？?；;：:]");

    public static String genLrcContent(AtaResult ataResult) {
        return genLrcContent(ataResult, false);
    }

    public static String genLrcContent(AtaResult ataResult, boolean withEndTime) {
        StringBuilder builder = new StringBuilder();
        List<AtaUtterance> utterances = ataResult.getUtterances();
        Map<String, String> latexMap = LatexUtils.convertLatexMap(ataResult.getLatexMappings());

        final int maxWords = 35;
        final int maxCharsPerLine = 70; // 每行最大字符数
        int latestUtteranceTime = 0;

        for (AtaUtterance utterance : utterances) {
            String text = StringUtils.trim(utterance.getText());
            List<AtaWord> mergedWords = null == latexMap ? utterance.getWords() : LatexUtils.mergeLatexWords(utterance);

            // 插入空白时间点行（超过 1 秒间隔）
            if (utterance.getStartTime() - latestUtteranceTime > 1000) {
                builder.append("[").append(TimeUtils.formatMilliseconds(latestUtteranceTime)).append("] \n");
            }

            // 判断是否需要断句（间隔超过 200ms）或者 英文字幕过长
            if (mergedWords.size() > maxWords || isEnglishTextTooLong(text, maxCharsPerLine)) {
                SubtitleSegment segment = new SubtitleSegment(latexMap);
                for (AtaWord word : mergedWords) {
                    if (segment.shouldBreak(word) || segment.contentLength() >= maxCharsPerLine) {
                        writeSegmentLine(builder, segment, withEndTime);
                        segment.reset();
                    }
                    segment.addWord(word);
                }
                if (!segment.isEmpty()) {
                    writeSegmentLine(builder, segment, withEndTime, utterance.getEndTime());
                }
            } else {
                // 短句整句处理
                builder.append("[").append(TimeUtils.formatMilliseconds(utterance.getStartTime())).append("]");
                if (withEndTime) {
                    builder.append("[").append(TimeUtils.formatMilliseconds(utterance.getEndTime())).append("]");
                }
                String finalText = LatexUtils.revertLatexPlaceholder(text, latexMap);
                builder.append(replaceSubtitleContent(finalText)).append("\n");
            }

            latestUtteranceTime = utterance.getEndTime();
        }

        return builder.toString();
    }

    private static boolean isEnglishTextTooLong(String text, int maxChars) {
        // 判断是否包含英文字符
        if (!text.matches(".*[a-zA-Z].*")) {
            return false;
        }
        // 去除标点符号后的实际长度
        String cleanText = text.replaceAll("[\\p{P}\\s]", "");
        return cleanText.length() > maxChars;
    }

    private static void writeSegmentLine(StringBuilder builder, SubtitleSegment segment, boolean withEndTime) {
        writeSegmentLine(builder, segment, withEndTime, segment.getSegmentEndTime());
    }

    private static void writeSegmentLine(StringBuilder builder, SubtitleSegment segment, boolean withEndTime, Integer actualEndTime) {
        builder.append("[")
                .append(TimeUtils.formatMilliseconds(segment.getSegmentStartTime()))
                .append("]");
        if (withEndTime && actualEndTime != null) {
            builder.append("[")
                    .append(TimeUtils.formatMilliseconds(actualEndTime))
                    .append("]");
        }
        builder.append(replaceSubtitleContent(segment.getContent())).append("\n");
    }

    /**
     * 生成ass文件
     */
    public static String genAssContent(AtaResult ataResult) {
        StringBuilder builder = new StringBuilder();
        List<AtaUtterance> utterances = ataResult.getUtterances();

        builder.append("[Script Info]").append("\n");
        builder.append("; This is an Advanced Sub Station Alpha v4+ script.").append("\n");
        builder.append("Title: 字幕").append("\n");
        builder.append("ScriptType: v4.00+").append("\n");
        builder.append("PlayDepth: 0").append("\n");
        builder.append("ScaledBorderAndShadow: Yes").append("\n");
        builder.append("WrapStyle: 3").append("\n");
        builder.append("\n");

        builder.append("[V4+ Styles]").append("\n");
        builder.append("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding")
                .append("\n");
        builder.append("Style: Default,思源宋体 CN,21.5,&H00000000,&H0000FFFF,&H00FFFFFF,&H000000FF,-1,0,0,0,100,100,0,0,1,1.0,0,2,10,10,18,1")
                .append("\n");
        builder.append("\n");

        builder.append("[Events]").append("\n");
        builder.append("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text").append("\n");

        final int maxLineWords = 23;
        // int latestTime = 0;
        for (AtaUtterance utterance : utterances) {
            String text = StringUtils.trim(utterance.getText());
            String filteredText = replaceSubtitleContent(text);
            if (StringUtils.isBlank(filteredText)) {
                continue;
            }
            if (StringUtils.length(filteredText) > maxLineWords) {
                List<AtaWord> words = utterance.getWords();
                Integer startTime = null;
                StringBuilder content = new StringBuilder();
                for (int i = 0; i < words.size(); i++) {
                    AtaWord word = words.get(i);
                    String wordText = word.getText();
                    content.append(replaceSubtitleContent(wordText));
                    if (startTime == null) {
                        startTime = word.getStartTime();
                    }
                    if (containsBreakPunctuation(wordText)) {
                        builder.append(String.format("Dialogue: 0,%s,%s,Default,,0,0,0,,%s", TimeUtils.formatMilliseconds4Ass(startTime), TimeUtils.formatMilliseconds4Ass(word.getEndTime()), content))
                                .append("\n");
                        startTime = null;
                        content = new StringBuilder();
                    } else if (StringUtils.length(content) >= 10 & i < words.size() - 1) {
                        AtaWord nextWord = words.get(i + 1);
                        // 两个字的停顿超过200ms
                        if (Math.abs(nextWord.getStartTime() - word.getEndTime()) > 200) {
                            builder.append(String.format("Dialogue: 0,%s,%s,Default,,0,0,0,,%s", TimeUtils.formatMilliseconds4Ass(startTime), TimeUtils.formatMilliseconds4Ass(word.getEndTime()), content))
                                    .append("\n");
                            startTime = null;
                            content = new StringBuilder();
                        }
                    }
                }
                if (startTime != null && StringUtils.isNotBlank(content.toString())) {
                    builder.append(String.format("Dialogue: 0,%s,%s,Default,,0,0,0,,%s", TimeUtils.formatMilliseconds4Ass(startTime), TimeUtils.formatMilliseconds4Ass(utterance.getEndTime()), content))
                            .append("\n");
                }
            } else {
                builder.append(String.format("Dialogue: 0,%s,%s,Default,,0,0,0,,%s", TimeUtils.formatMilliseconds4Ass(utterance.getStartTime()), TimeUtils.formatMilliseconds4Ass(utterance.getEndTime()), filteredText))
                        .append("\n");
            }
        }
        return builder.toString();
    }

    /**
     * 替换字幕中的标点符号
     */
    private static String replaceSubtitleContent(String subtitle) {
        if (StringUtils.isBlank(subtitle)) {
            return subtitle;
        }
        if (StringUtils.equals(BizHandler.getBizFlag(), BizConstant.BIZ_FLAG_VLAB)) {
            return subtitle.replaceAll("[、，；。？！：“”「」【】《》（）]", " ").trim();
        } else {
            return subtitle.replaceAll("[、，；。？！：.,!?;:()“”'「」【】{}\\[\\]<>《》（）+/]", " ").trim();
        }
    }

    /**
     * 判断文本中是否包含可换行的标点符号
     *
     * @param text 文本
     * @return 是否包含标点符号
     */
    private static boolean containsBreakPunctuation(String text) {
        Matcher matcher = BREAK_PUNCTUATION_PATTERN.matcher(text);
        return matcher.find();
    }

    static class SubtitleSegment {
        // 字幕打轴前提取的latex映射
        private final Map<String, String> latexMap;
        // 字幕段落内容
        private final StringBuilder content;

        private Integer startTime = null;
        private Integer latestEndTime = null;

        SubtitleSegment(Map<String, String> latexMap) {
            this.latexMap = latexMap;
            this.content = new StringBuilder();
        }

        public void addWord(AtaWord word) {
            if (startTime == null) {
                startTime = word.getStartTime();
            }
            String text = word.isMerged() ? LatexUtils.revertLatexPlaceholder(word.getText(), latexMap) : word.getText();
            content.append(text);
            latestEndTime = word.getEndTime();
        }

        public boolean shouldBreak(AtaWord nextWord) {
            return latestEndTime != null && nextWord.getStartTime() - latestEndTime > 200;
        }

        public boolean isEmpty() {
            return content.length() == 0;
        }

        public void reset() {
            content.setLength(0);
            startTime = null;
            latestEndTime = null;
        }

        public String getContent() {
            return content.toString();
        }

        public Integer getSegmentStartTime() {
            return startTime;
        }

        public Integer getSegmentEndTime() {
            return latestEndTime;
        }

        public int contentLength() {
            return content.length();
        }
    }


}
