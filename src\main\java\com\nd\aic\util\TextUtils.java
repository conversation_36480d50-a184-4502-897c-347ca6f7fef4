package com.nd.aic.util;

import com.google.common.collect.Lists;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Pattern;

public class TextUtils {

    private static final Pattern BREAK_PATTERN = Pattern.compile("<#[+-]?(\\d+(\\.\\d+)?|\\.\\d+)#>");
    private static final List<String> SEPARATORS = Lists.newArrayList("。", "！", "？", "，", ".", "!", "?", ",");

    public static List<String> splitText(String text, int maxWordLength) {
        List<String> textSegments = Lists.newArrayList();
        int start = 0;
        while (start < text.length()) {
            String str = text.substring(start, Math.min(start + maxWordLength, text.length()));
            String segment;
            // 截取的长度不够MAX_WORDS，说明已到文本结尾
            if (str.length() < maxWordLength) {
                segment = str;
                start += str.length();
            } else {
                int index = -1;
                for (String separator : SEPARATORS) {
                    index = str.lastIndexOf(separator);
                    if (-1 != index) {
                        break;
                    }
                }
                if (-1 == index) {
                    throw new RuntimeException("文本中没有找到分隔符，无法拆分配音内容");
                }

                segment = text.substring(start, start + index + 1);
                start += index + 1;
            }
            textSegments.add(segment);
        }
        return textSegments;
    }

    public static String clearBreak(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        return BREAK_PATTERN.matcher(text).replaceAll("");
    }
}
