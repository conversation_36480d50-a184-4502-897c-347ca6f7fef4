package com.nd.aic.util;

import com.google.common.collect.Lists;

import com.nd.aic.pojo.segment.BaseSegment;
import com.nd.aic.pojo.segment.BgmSegment;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.Getter;

public class TimeRangeUtils {
    public static List<BgmSegment> splitBgmSegments(List<BgmSegment> segments, List<TimeRange> ranges) {
        if (CollectionUtils.isEmpty(ranges)) {
            return segments;
        }
        List<TimeRangeHolder<BgmSegment>> segmentHolders = new ArrayList<>();
        for (BgmSegment segment : segments) {
            TimeRangeHolder<BgmSegment> holder = new TimeRangeHolder<>(TimeUtils.convertToMilliseconds(segment.getTime()), TimeUtils.convertToMilliseconds(segment.getEndTime()), segment);
            segmentHolders.add(holder);
        }
        List<TimeRangeHolder<BgmSegment>> overlapHolders = new ArrayList<>();
        for (TimeRange range : ranges) {
            TimeRangeHolder<BgmSegment> holder = new TimeRangeHolder<>(range.getStart(), range.getEnd(), null);
            overlapHolders.add(holder);
        }

        List<TimeRangeHolder<BgmSegment>> result = Lists.newArrayList();
        for (TimeRangeHolder<BgmSegment> overlapHolder : overlapHolders) {
            result = Lists.newArrayList();
            for (TimeRangeHolder<BgmSegment> segmentHolder : segmentHolders) {
                List<TimeRangeHolder<BgmSegment>> splitResult = segmentHolder.splitByOverlaps(overlapHolder);
                result.addAll(splitResult);
            }
            segmentHolders = result;
        }

        return result.stream().map(e -> {
            // 通过反射复制e.value对象
            BgmSegment value = e.value;
            @SuppressWarnings("unchecked")
            BgmSegment t = new BgmSegment();
            BeanUtils.copyProperties(value, t, "id");
            t.setTime(TimeUtils.formatMilliseconds(e.start));
            t.setEndTime(TimeUtils.formatMilliseconds(e.end));
            return t;
        }).collect(Collectors.toList());
    }

    private static <T extends BaseSegment<T>> List<TimeRangeHolder<T>> splitSegments(List<TimeRangeHolder<T>> segmentHolders, TimeRangeHolder<T> overlapHolder) {
        List<TimeRangeHolder<T>> result = Lists.newArrayList();
        for (TimeRangeHolder<T> segmentHolder : segmentHolders) {
            List<TimeRangeHolder<T>> nonOverlappingParts = segmentHolder.splitByOverlaps(overlapHolder);
            result.addAll(nonOverlappingParts);
        }

        return result;
    }

    @Getter
    public static class TimeRange {
        private final long start;
        private final long end;

        public TimeRange(long start, long end) {
            this.start = start;
            this.end = end;
        }
    }

    @Getter
    private static class TimeRangeHolder<T> {
        private final long start;
        private final long end;
        private final T value;

        public TimeRangeHolder(long start, long end, T value) {
            if (start >= end) {
                throw new IllegalArgumentException("Start must be less than end.");
            }
            this.start = start;
            this.end = end;
            this.value = value;
        }

        /**
         * 将当前对象分割为非重叠部分
         */
        public List<TimeRangeHolder<T>> splitByOverlaps(TimeRangeHolder<T> other) {
            List<TimeRangeHolder<T>> result = new ArrayList<>();

            // 如果没有重叠，直接返回当前对象
            if (this.end <= other.start || this.start >= other.end) {
                result.add(TimeRangeHolder.this);
                return result;
            }

            // 处理非重叠部分
            if (this.start < other.start) {
                result.add(new TimeRangeHolder<>(this.start, other.start, this.value));
            }

            if (this.end > other.end) {
                result.add(new TimeRangeHolder<>(other.end, this.end, this.value));
            }

            return result;
        }

        @Override
        public String toString() {
            return "TimeRangeHolder{" + "start=" + start + ", end=" + end + ", value=" + value + '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TimeRangeHolder<?> that = (TimeRangeHolder<?>) o;
            return start == that.start && end == that.end && Objects.equals(value, that.value);
        }

        @Override
        public int hashCode() {
            return Objects.hash(start, end, value);
        }

    }

    public static void main(String[] args) {
        String input = "[\n" + "    {\n" + "      \"time\": \"00:00.00\",\n" + "      \"end_time\": \"02:16.67\",\n" + "      \"resource_id\": \"c8c9d65e-8642-4ea0-8f83-d4953adfabab\",\n" + "      \"reason\": \"背景音乐\",\n" + "      \"custom\": {\n" + "        \"volume\": 40\n" + "      },\n" + "      \"id\": \"ac160d0b-33d8-406a-8fcc-aa0e751d6273\",\n" + "      \"ti_flag\": \"source\"\n" + "    },\n" + "    {\n" + "      \"time\": \"02:16.67\",\n" + "      \"end_time\": \"02:31.00\",\n" + "      \"resource_id\": \"c8c9d65e-8642-4ea0-8f83-d4953adfabab\",\n" + "      \"reason\": \"背景音乐\",\n" + "      \"custom\": {\n" + "        \"volume\": 40\n" + "      },\n" + "      \"id\": \"23d3d15d-6cb3-4f3a-9e7c-bb62b856cff1\",\n" + "      \"ti_flag\": \"source\"\n" + "    }\n" + "  ]";
        List<BgmSegment> bgmSegments = JsonUtils.parseList(input, BgmSegment.class);
        TimeRange range1 = new TimeRange(10, 20);
        TimeRange range2 = new TimeRange(15, 25);

        List<BgmSegment> result = splitBgmSegments(bgmSegments, Lists.newArrayList(range1, range2));
        result.forEach(e -> System.out.println(e.getTime() + " - " + e.getEndTime()));
    }
}
