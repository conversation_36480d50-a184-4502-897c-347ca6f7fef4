package com.nd.aic.util;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.time.Duration;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class TimeUtils {

    public static String formatIso8601DateString(Date date) {
        String pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
        return DateFormatUtils.format(date, pattern);
    }

    public static String formatIso8601DateString(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return formatIso8601DateString(new Date(timestamp));
    }

    /**
     * 将"00:06:68" 转换为 "06:68.00"
     */
    public static String timeConvert(String time, String defaultTime) {
        String regex = "^\\d{1,2}:\\d{2}:\\d{1,2}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(time);
        if (!matcher.matches()) {
            return defaultTime;
        }
        String[] splitTime = time.split(":");
        int hours = Integer.parseInt(splitTime[0]);
        int minutes = Integer.parseInt(splitTime[1]);
        String seconds = splitTime[2];

        // 将小时转换为分钟并加上原分钟数
        int totalMinutes = hours * 60 + minutes;
        return String.format("%02d:%s.00", totalMinutes, seconds);
    }


    /**
     * 将 "06:68.00" 转换为毫秒
     */
    public static long convertToMilliseconds(String time) {
        // 拆分时间字符串
        String[] parts = time.split(":");
        String minutesPart = parts[0];
        String secondsPart = parts[1];

        // 进一步拆分秒和毫秒部分
        String[] secondsParts = secondsPart.split("\\.");
        String seconds = secondsParts[0];
        String millisecondsPart = secondsParts.length > 1 ? secondsParts[1] : "0";

        // 解析成整数
        int minutes = Integer.parseInt(minutesPart);
        int secs = Integer.parseInt(seconds);
        int millis = Integer.parseInt(millisecondsPart);

        // 计算总的毫秒数 如果是三位，就不要乘以10了
        return ((long) minutes * 60 * 1000) + (secs * 1000L) + (millis >= 100 ? millis : (millis * 10L));
    }

    /**
     * 将 毫秒 转换为 "06:68.00"
     */
    public static String formatMilliseconds(long milliseconds) {
        long minutes = (milliseconds / 1000) / 60;
        long seconds = (milliseconds / 1000) % 60;
        long millis = (milliseconds % 1000) / 10;
        return String.format("%02d:%02d.%02d", minutes, seconds, millis);
    }

    /**
     * 将 毫秒 转换为 "06:68:05"
     */
    public static String formatMilliseconds4Duration(long seconds) {
        Duration duration = Duration.ofSeconds(seconds);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long secs = duration.getSeconds() % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, secs);
    }

    /**
     * 将 毫秒 转换为 "0:06:68.05"
     */
    public static String formatMilliseconds4Ass(long milliseconds) {
        Duration duration = Duration.ofMillis(milliseconds);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long secs = duration.getSeconds() % 60;
        long ms = milliseconds % 1000 / 10;
        return String.format("%d:%02d:%02d.%02d", hours, minutes, secs, ms);
    }

    /**
     * 获取格式为 "06:68.00" 的时长，单位为毫秒
     */
    public static Long getDuration(String startTime, String endTime) {
        if (startTime == null || endTime == null) {
            return null;
        }
        return TimeUtils.convertToMilliseconds(endTime) - TimeUtils.convertToMilliseconds(startTime);
    }

    /**
     * 获取格式为 "06:68.00" 的时长，单位为秒
     */
    public static Double getDurationSec(String startTime, String endTime) {
        Long duration = getDuration(startTime, endTime);
        return duration != null ? duration / 1000.0 : null;
    }

    public static long subMilliseconds(String time, long milliseconds) {
        long timeInMilliseconds = convertToMilliseconds(time);
        return timeInMilliseconds - milliseconds;
    }

    public static long addMilliseconds(String time, long milliseconds) {
        long timeInMilliseconds = convertToMilliseconds(time);
        return timeInMilliseconds + milliseconds;
    }

    public static String add(String time, long milliseconds) {
        long timeInMilliseconds = convertToMilliseconds(time);
        return formatMilliseconds(timeInMilliseconds + milliseconds);
    }
}
