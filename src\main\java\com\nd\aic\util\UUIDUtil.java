package com.nd.aic.util;

import java.util.UUID;

public final class UUIDUtil {

    public static String upperUuid() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    public static boolean isUuid(String uuid) {
        if (uuid == null) {
            return false;
        }
        // 正则表达式用于匹配标准UUID格式
        return uuid.matches("^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$");
    }
}
