package com.nd.aic.util;

import com.google.common.collect.Lists;

import com.nd.aic.pojo.flow.XlsxTeachingActivity;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class XlsxUtils {

    public static List<XlsxTeachingActivity> parseTeachActivityList(File file) {
        List<XlsxTeachingActivity> activities = Lists.newArrayList();

        try (FileInputStream fis = new FileInputStream(file); Workbook workbook = new XSSFWorkbook(fis)) {
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

            Iterator<Row> rowIterator = sheet.iterator();
            int rowIndex = 0;

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();

                if (rowIndex++ < 2) {
                    // 跳过表头（第一行）
                    continue;
                }

                XlsxTeachingActivity activity = new XlsxTeachingActivity();

                Cell cell1 = row.getCell(0); // 教学活动
                Cell cell2 = row.getCell(1); // 教学事件
                Cell cell3 = row.getCell(2); // 演讲稿
                Cell cell4 = row.getCell(3); // 表演方案
                Cell cell5 = row.getCell(4); // 素材

                if (cell1 != null) {
                    cell1.setCellType(CellType.STRING);
                    activity.setActivity(cell1.getStringCellValue());
                }
                if (StringUtils.isEmpty(activity.getActivity())) {
                    continue;
                }
                if (cell2 != null) {
                    cell2.setCellType(CellType.STRING);
                    activity.setEvent(cell2.getStringCellValue());
                }
                if (cell3 != null) {
                    cell3.setCellType(CellType.STRING);
                    activity.setScript(cell3.getStringCellValue());
                }
                if (cell4 != null) {
                    cell4.setCellType(CellType.STRING);
                    activity.setPerformancePlan(cell4.getStringCellValue());
                }
                if (cell5 != null) {
                    cell5.setCellType(CellType.STRING);
                    activity.setMaterial(cell5.getStringCellValue());
                }


                activities.add(activity);
            }

        } catch (IOException e) {
            log.error("parse teaching activities from excel fail", e);
            throw new RuntimeException(e);
        }

        return activities;
    }
}
