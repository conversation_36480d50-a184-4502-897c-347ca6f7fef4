(dp0
S'param_dict'
p1
(dp2
S'norm_type'
p3
S'clip_0to1'
p4
sS'score_clip'
p5
(lp6
F0.0
aF100.0
asS'C'
p7
F4.0
sS'nu'
p8
F0.9
sS'gamma'
p9
F0.05
ssS'model_dict'
p10
(dp11
S'feature_dict'
p12
(dp13
S'VMAF_feature'
p14
(lp15
S'vif_scale0'
p16
aS'vif_scale1'
p17
aS'vif_scale2'
p18
aS'vif_scale3'
p19
aS'adm2'
p20
aS'motion'
p21
assg3
S'linear_rescale'
p22
sg5
g6
sS'feature_names'
p23
(lp24
S'VMAF_feature_adm2_score'
p25
aS'VMAF_feature_motion_score'
p26
aS'VMAF_feature_vif_scale0_score'
p27
aS'VMAF_feature_vif_scale1_score'
p28
aS'VMAF_feature_vif_scale2_score'
p29
aS'VMAF_feature_vif_scale3_score'
p30
asS'intercepts'
p31
(lp32
F-0.01818181818181818
aF-1.9715077269616803
aF-0.03671084490620134
aF-0.08203910092775053
aF-0.2858058491124272
aF-0.480167543060334
aF-0.7764756920485143
asS'model_type'
p33
S'LIBSVMNUSVR'
p34
sS'model'
p35
NsS'slopes'
p36
(lp37
F0.009454545454545453
aF2.9715077269616805
aF0.05246778812837607
aF1.0820390945805258
aF1.2858061763913162
aF1.4801682848883448
aF1.7764765384047878
ass.