(dp0
S'param_dict'
p1
(dp2
S'C'
p3
F4.0
sS'norm_type'
p4
S'none'
p5
sS'score_clip'
p6
(lp7
F0.0
aF100.0
asS'custom_clip_0to1_map'
p8
(dp9
S'VMAF_feature_adm_scale0_score'
p10
(lp11
F0.0
aF0.5
assS'nu'
p12
F0.9
sS'gamma'
p13
F0.05
ssS'model_dict'
p14
(dp15
g4
g5
sg6
g7
sS'feature_names'
p16
(lp17
S'VMAF_feature_adm2_score'
p18
aS'VMAF_feature_motion_score'
p19
aS'VMAF_feature_vif_scale0_score'
p20
aS'VMAF_feature_vif_scale1_score'
p21
aS'VMAF_feature_vif_scale2_score'
p22
aS'VMAF_feature_vif_scale3_score'
p23
asS'model_type'
p24
S'LIBSVMNUSVR'
p25
sS'model'
p26
NsS'feature_dict'
p27
(dp28
S'VMAF_feature'
p29
(lp30
S'vif_scale0'
p31
aS'vif_scale1'
p32
aS'vif_scale2'
p33
aS'vif_scale3'
p34
aS'adm2'
p35
aS'motion'
p36
asss.