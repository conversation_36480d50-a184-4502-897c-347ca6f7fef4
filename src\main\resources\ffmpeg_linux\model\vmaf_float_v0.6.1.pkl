(dp0
S'param_dict'
p1
(dp2
S'C'
p3
F4.0
sS'score_transform'
p4
(dp5
S'p2'
p6
F-0.00705305
sS'out_gte_in'
p7
S'true'
p8
sS'p0'
p9
F1.70674692
sS'p1'
p10
F1.72643844
ssS'norm_type'
p11
S'clip_0to1'
p12
sS'score_clip'
p13
(lp14
F0.0
aF100.0
asS'nu'
p15
F0.9
sS'gamma'
p16
F0.04
ssS'model_dict'
p17
(dp18
S'model'
p19
Nsg4
g5
sg11
S'linear_rescale'
p20
sg13
g14
sS'feature_names'
p21
(lp22
S'VMAF_feature_adm2_score'
p23
aS'VMAF_feature_motion2_score'
p24
aS'VMAF_feature_vif_scale0_score'
p25
aS'VMAF_feature_vif_scale1_score'
p26
aS'VMAF_feature_vif_scale2_score'
p27
aS'VMAF_feature_vif_scale3_score'
p28
asS'intercepts'
p29
(lp30
F-0.3092981927591963
aF-1.7993968597186747
aF-0.003017198086831897
aF-0.1728125095425364
aF-0.5294309090081222
aF-0.7577185792093722
aF-1.083428597549764
asS'model_type'
p31
S'LIBSVMNUSVR'
p32
sS'slopes'
p33
(lp34
F0.012020766332648465
aF2.8098077502505414
aF0.06264407466686016
aF1.222763456258933
aF1.5360318811084146
aF1.7620864995501058
aF2.08656468286432
asS'feature_dict'
p35
(dp36
S'VMAF_feature'
p37
(lp38
S'vif_scale0'
p39
aS'vif_scale1'
p40
aS'vif_scale2'
p41
aS'vif_scale3'
p42
aS'adm2'
p43
aS'motion2'
p44
asss.