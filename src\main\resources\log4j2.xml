<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <properties>
        <property name="logPath">logs</property>
        <property name="pattern">%d{yyyy-MM-dd HH:mm:ss.SSS} # %thread # %-5level # %logger{80}:%L # TraceID=%X{TraceID},,%msg%n</property>
        <property name="sdp.env">${sys:sdp.env}</property>
    </properties>
    <Appenders>
        <!--输出到控制台 -->
        <Console name="ConsoleLog" target="SYSTEM_OUT">
            <!--只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） -->
            <ThresholdFilter level="TRACE" onMatch="ACCEPT" onMismatch="DENY"/>
            <!--输出日志的格式，引用自定义模板 PATTERN -->
            <PatternLayout pattern="${pattern}"/>
        </Console>
        <!--输出到文件 -->
        <!-- 把error等级记录到文件 一般不用 -->
        <RollingRandomAccessFile name="ErrorLog" fileName="${logPath}/err.log" filePattern="${logPath}/%d{yyyy-MM-dd}-%i.err.gz">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/><!-- 每1天归档一次,依据设置的精度 -->
            </Policies>
        </RollingRandomAccessFile>
        <!--输出到循环日志，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
        <RollingRandomAccessFile name="RollingFileLog" fileName="${logPath}/log.log" filePattern="${logPath}/%d{yyyy-MM-dd}-%i.log.gz">
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/><!-- 每1天归档一次,依据设置的精度 -->
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <AsyncLogger name="com.nd.gaea.log" level="INFO" additivity="false">
            <AppenderRef ref="RollingFileLog"/>
            <AppenderRef ref="ErrorLog"/>
            <AppenderRef ref="ConsoleLog"/><!--开发时可以打开,生产时请关闭,否由elk会采集两遍,因为catalina.out中也会输出-->
        </AsyncLogger>
        <Root level="INFO">
            <AppenderRef ref="RollingFileLog"/>
            <AppenderRef ref="ErrorLog"/>
            <AppenderRef ref="ConsoleLog"/><!--开发时可以打开,生产时请关闭,否由elk会采集两遍,因为catalina.out中也会输出-->
        </Root>
        <!-- 第三方日志系统 -->
        <!--过滤掉spring和mybatis的一些无用的DEBUG信息，也可以在spring boot 的logging.level.org.springframework=FATAL设置-->
        <logger name="org.springframework" level="INFO"/>
        <logger name="org.mybatis" level="INFO"/>
        <logger name="org.apache.http" level="INFO"/>
        <logger name="com.netflix" level="INFO"/>
        <logger name="org.hibernate" level="INFO"/>
    </Loggers>
</Configuration>
